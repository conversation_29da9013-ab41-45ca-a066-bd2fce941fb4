﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertEntityDAToBusinessWTM
    {

        private static IDALService.IWTMComponentService wtmCompService =
            AppFramework.ServiceBus.ServiceLocator.GetService<IDALService.IWTMComponentService>();

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 机组型号表
        /// </summary>
        /// <param name="_entity">WTModel</param>
        /// <returns></returns>
        public static WTCMSLive.BusinessEntity.WindTurbineModel ConvertWindTurbineModel(WTCMSLive.Entity.Models.WTModel _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.BusinessEntity.WindTurbineModel windTurbineModel = new WTCMSLive.BusinessEntity.WindTurbineModel();

            windTurbineModel.TurbineModel = _entity.WindTurbineModel;
            windTurbineModel.StructureType = (EnumTurModelStructureType)(int.Parse(_entity.StructureType));
            windTurbineModel.StructureDiagram = _entity.StructureDiagram;
            windTurbineModel.Manufactory = _entity.Manufactory;
            windTurbineModel.RatedPower = float.Parse(_entity.FactedPower.ToString());
            windTurbineModel.Description = _entity.Description;
            windTurbineModel.BladeNum = Convert.ToInt32(_entity.BladeNum);

            return windTurbineModel;
        }

        /// <summary>
        /// 机组型号表列表
        /// </summary>
        /// <param name="_list"></param>
        /// <returns></returns>
        public static List<WTCMSLive.BusinessEntity.WindTurbineModel> ConvertWindTurbineModelList(List<WTCMSLive.Entity.Models.WTModel> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<WTCMSLive.BusinessEntity.WindTurbineModel> turModelList = new List<BusinessEntity.WindTurbineModel>();
            
            foreach (WTCMSLive.Entity.Models.WTModel item in _list)
            {
                WTCMSLive.BusinessEntity.WindTurbineModel turModel = ConvertWindTurbineModel(item);

                turModelList.Add(turModel);
            }
            return turModelList;
        }

    }
}
