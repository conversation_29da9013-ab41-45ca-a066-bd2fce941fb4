﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 模块枚举
    /// </summary>
    public enum ModuleName
    {
        /*配置页面*/
        [Description("机组监测数据模块")]
        TurbineMonitoring = 1,
        [Description("测量定义模块")]
        MeaDefition = 2,
        [Description("报警定义模块")]
        Warn = 3,
        [Description("设备树模块")]
        Devtree = 4,
        [Description("主控模块")]
        MCS = 5,
        [Description("采集单元模块")]
        DAUManage = 6,
        [Description("晃度仪模块")]
        SVM = 7,
        [Description("油液模块")]
        Oil = 8,
        [Description("风场配置")]
        WindPark = 10,
        /* 前台数据展示页面*/
        [Description("采集单元监测")]
        DAUDetail = 9,
        [Description("晃度仪监测")]
        SVMDetail = 11,
        [Description("监视特征值")]
        SupervisoryEV = 12,
        /* 用户管理页面*/
        [Description("用户管理")]
        UserManage = 13,
        [Description("角色管理")]
        RoleManage = 14,
        [Description("波形下载")]
        WaveDownLoad = 15,
        [Description("倾角仪监测")]
        ACCDetail = 16,
        /*集控中心用户表示*/
        [Description("集控中心")]
        CentralizedControl = 99
    }
}
