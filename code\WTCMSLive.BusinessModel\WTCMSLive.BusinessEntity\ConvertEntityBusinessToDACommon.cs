﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertEntityBusinessToDACommon
    {
        /// <summary>
        /// 系统配置表
        /// </summary>
        /// <param name="_entity">Global</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysConfiguration ConvertGlobal(Global _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysConfiguration _SysConfiguration = new WTCMSLive.Entity.Models.SysConfiguration();

            _SysConfiguration.DatabaseVersion = _entity.DatabaseVersion;
            _SysConfiguration.SystemState = _entity.SystemState;

            return _SysConfiguration;
        }

        /// <summary>
        /// 系统运行日志
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysRunningLog ConvertSysRunningLog(LogEntity _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysRunningLog _SysRunningLog = new WTCMSLive.Entity.Models.SysRunningLog();
            _SysRunningLog.GUID = System.Guid.NewGuid().ToString("N");
            _SysRunningLog.EventTime = _entity.LogTime;
            _SysRunningLog.LogContent = _entity.OperationDescription;
            _SysRunningLog.LogTitle = _entity.OperationDescription;
            _SysRunningLog.LogType = short.Parse(_entity.LogDB.ToString());
            _SysRunningLog.Operator = _entity.UserName;
            
            return _SysRunningLog;
        }

        /// <summary>
        /// 机组运行日志
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.WindTurbineRunLog ConvertWindTurbineRunLog(WindTurbineRunLog _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            WTCMSLive.Entity.Models.WindTurbineRunLog _runLog = new Entity.Models.WindTurbineRunLog();
            _runLog.WindTurbineID = _entity.WindTurbineID;
            _runLog.EventTime = _entity.EventTime;
            _runLog.AlarmDegree = _entity.AlarmDegree;
            char[] logTile=_entity.LogTitle.ToCharArray();
            if (logTile.Count() > 200)
            {
                logTile = logTile.Take(200).ToArray();
            }
            _runLog.LogTitle = new string(logTile);
            return _runLog;
        }

        /// <summary>
        /// 查询条件实体
        /// </summary>
        /// <param name="_entity">LogQueryCondition</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysLogQueryCondition ConvertSysLogQueryCondition(LogQueryCondition _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysLogQueryCondition _SysLogQueryCondition = new WTCMSLive.Entity.Models.SysLogQueryCondition();

            _SysLogQueryCondition.EndTime = _entity.EndTime;
            _SysLogQueryCondition.KeyWords = _entity.KeyWords;
            _SysLogQueryCondition.UserName = _entity.UserName;
            _SysLogQueryCondition.StartTime = _entity.StartTime;
            _SysLogQueryCondition.LogType = _entity.LogDB;
            
            return _SysLogQueryCondition;
        }

        /// <summary>
        /// 用户表
        /// </summary>
        /// <param name="_entity">User</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysUser ConvertSysUser(User _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysUser _SysUser = new WTCMSLive.Entity.Models.SysUser();

            _SysUser.PassWord = _entity.PassWord;
            _SysUser.UserID = _entity.UserID;
            _SysUser.UserName = _entity.UserName;
            _SysUser.UserState = _entity.UserState;
            _SysUser.Email = _entity.Email;
            _SysUser.Phone = _entity.Phone;
            _SysUser.SysRoles.Add(ConvertEntityBusinessToDACommon.ConvertSysRole(_entity.UserRole));

            return _SysUser;
        }

        /// <summary>
        /// 角色表
        /// </summary>
        /// <param name="_entity">Role</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysRole ConvertSysRole(Role _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysRole _SysRole = new WTCMSLive.Entity.Models.SysRole();

            _SysRole.RoleID = Convert.ToInt32(_entity.RoleID);
            _SysRole.RoleName = _entity.RoleName;
            
            foreach (Function item in _entity.Functions)
            {
                _SysRole.SysFunctions.Add(ConvertEntityBusinessToDACommon.ConvertSysFunction(item));
            }

            foreach (Module item in _entity.Modules)
            {
                _SysRole.SysModules.Add(ConvertEntityBusinessToDACommon.ConvertSysModule(item));
            }

            _SysRole.IsSystemRole = (sbyte)(_entity.IsSystemRole == true ? 1 : 0);

            return _SysRole;
        }

        /// <summary>
        /// 权限表
        /// </summary>
        /// <param name="_entity">Function</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysFunction ConvertSysFunction(Function _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysFunction _SysFunction = new WTCMSLive.Entity.Models.SysFunction();

            _SysFunction.FunctionID = Convert.ToInt32(_entity.FunctionID);
            _SysFunction.FunctionName = _entity.FunctionName;
            _SysFunction.ModuleID = Convert.ToInt32(_entity.ModuleID);

            return _SysFunction;
        }

        /// <summary>
        /// 模块表
        /// </summary>
        /// <param name="_entity">Module</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.SysModule ConvertSysModule(Module _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.SysModule _SysModule = new WTCMSLive.Entity.Models.SysModule();

            _SysModule.ModuleID = Convert.ToInt32(_entity.ModuleID);
            _SysModule.ModuleName = _entity.ModuleName;

            foreach (Function item in _entity.ModuleFunctions)
            {
                _SysModule.SysFunctions.Add(ConvertEntityBusinessToDACommon.ConvertSysFunction(item));
            }

            return _SysModule;
        }



        /// <summary>
        /// 主控配置表
        /// </summary>
        /// <param name="_entity">Template</param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.MCSystem ConvertMCSystem(MCS _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.MCSystem _McsSystem = new WTCMSLive.Entity.Models.MCSystem();

            _McsSystem.WindTurbineID = _entity.WindTurbineID;
            _McsSystem.MCSIP = _entity.MCSIP;
            _McsSystem.MCSPort = _entity.MCSPort;

            return _McsSystem;
        }
        
        /// <summary>
        /// DAU单元采集表
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.DAUnit ConvertDau(DataSourceV2 _entity)
        {
            _entity = _entity == null ? null : _entity;
            WTCMSLive.Entity.Models.DAUnit _DAUnit = new Entity.Models.DAUnit();
            _DAUnit.WindTurbineID = _entity.AssocWindTurbineID;
            _DAUnit.DAUnitName = _entity.Name;
            _DAUnit.IPAddress = _entity.IP;
            _DAUnit.DataAcquisitionInterval = _entity.DataAcquisitionInterval;
            _DAUnit.TrendSaveInterval = _entity.TrendSaveInterval;
            _DAUnit.Enable =Convert.ToInt16(_entity.IsAvailable);
            return _DAUnit;
        }
    }
}
