﻿using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class TIMModel : ModbusUnit
    {

        public TimCalibration TimUnit { get; set; }

        //测量定义拓展
        public MeasDefinition_Ex Mdfex { get; set; }
        //测量定义
        public ModbusDef Mdfmodbus { get; set; }


        public string DauName { get; set; }
        public string MeasName { get; set; }

        //测点位置
        public string loclist { get; set; }
        public string modbusType { get; set; }

        // 截面
        public string SectionName { get; set; }
        // 部件
        public string CompName { get; set; }

        // 特征值
        public string evList { get; set; }
    }

    
}