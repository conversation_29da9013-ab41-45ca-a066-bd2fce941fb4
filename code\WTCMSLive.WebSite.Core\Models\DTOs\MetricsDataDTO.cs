namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// 性能数据存储DTO
    /// </summary>
    public class MetricsDataDTO
    {
        /// <summary>
        /// 时间戳（Unix毫秒）
        /// </summary>
        public long Timestamp { get; set; }

        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public double Cpu { get; set; }

        /// <summary>
        /// 内存使用量（MB）
        /// </summary>
        public double Memory { get; set; }

        /// <summary>
        /// 磁盘读取速率（KiB/s）
        /// </summary>
        public double DiskRead { get; set; }

        /// <summary>
        /// 磁盘写入速率（KiB/s）
        /// </summary>
        public double DiskWrite { get; set; }
    }

    /// <summary>
    /// 性能数据文件信息DTO
    /// </summary>
    public class MetricsFileInfoDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime { get; set; }

        /// <summary>
        /// 数据记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 数据开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 数据结束时间
        /// </summary>
        public DateTime EndTime { get; set; }
    }

    /// <summary>
    /// 性能数据查询请求DTO
    /// </summary>
    public class MetricsQueryRequestDTO
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 数据采样间隔（秒），用于数据压缩
        /// </summary>
        public int? SampleInterval { get; set; }
    }

    /// <summary>
    /// 性能数据下载响应DTO
    /// </summary>
    public class MetricsDownloadResponseDTO
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// 文件内容类型
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// 文件数据
        /// </summary>
        public byte[] FileData { get; set; }

        /// <summary>
        /// 数据记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 数据时间范围
        /// </summary>
        public string TimeRange { get; set; }
    }
}
