import{C as o,bM as n,bN as a,bO as c,bP as i,bQ as h,bR as l,bS as u,bT as p,bU as d,bV as D,bW as A,bX as U,bY as y,bZ as L,b_ as C,b$ as f,c0 as w,c1 as b,c2 as m,c3 as q,c4 as B,c5 as G,c6 as S,c7 as P,c8 as N,c9 as k,ca as v,cb as R,cc as V}from"./index-D9CxWmlM.js";import{a as s}from"./tools-DZBuE28U.js";const M=o("collectionUnitConfig",{state:()=>({dAUTypeList:[],dAUList:[],channelNumList:[],rotSpdMeasLocList:[],measLocList:[],measLocProcessList:[],collectionUnitList:[],timeWaveDefList:[],dAUOptionList:[],voltageCurrentWaveDefList:[],wordConditionMeasLocList:[]}),actions:{reset(){this.$reset()},async fetchGetDAUType(t={}){try{const e=await V(t);let r=s(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.dAUTypeList=r,e}catch(e){console.error("请求报错:",e)}},async fetchGetDAUList(t={}){try{const e=await R(t);return this.dAUOptionList=s(e,{label:"dauName",value:"dauID"},{nother:!0}),this.dAUList=e,e}catch(e){console.error("请求报错:",e)}},async fetchBatchAddDAU(t={}){try{return await v(t)}catch(e){console.error("请求报错:",e)}},async fetchDAUEditDAU(t={}){try{return await k(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchDeleteDAU(t={}){try{return await N(t)}catch(e){console.error("请求报错:",e)}},async fetchChangeDAUStateBat(t={}){try{return await P(t)}catch(e){console.error("请求报错:",e)}},async fetchChangeDAUState(t={}){try{return await S(t)}catch(e){console.error("请求报错:",e)}},async fetchDAUGetDAUVibList(t={}){try{return await G(t)||[]}catch(e){console.error("请求报错:",e)}},async fetchGetDAUProcessList(t={}){try{return await B(t)||[]}catch(e){console.error("请求报错:",e)}},async fetchGetRotSpeedList(t={}){try{return await q(t)||[]}catch(e){console.error("请求报错:",e)}},async fetchGetworkConditionList(t={}){try{return await m(t)}catch(e){console.error("请求报错:",e)}},async fetchGetRotSpdMeasLocList(t={}){try{const e=await b(t);let r=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.rotSpdMeasLocList=r,r}catch(e){console.error("请求报错:",e)}},async fetchGetWordConditionMeasLoc(t={}){try{const e=await w(t);let r=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.wordConditionMeasLocList=r,r}catch(e){console.error("请求报错:",e)}},async fetchGetDAUVibChannelNumList(t={}){try{const e=await f(t);let r=s(e,{loopSelf:!0},{nother:!0});return this.channelNumList=r,r}catch(e){console.error("请求报错:",e)}},async fetchGetmeasLocList(t={}){try{const e=await C(t);let r=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.measLocList=r,r}catch(e){console.error("请求报错:",e)}},async fetchGetmeasLocProcessList(t={}){try{const e=await L(t);let r=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.measLocProcessList=r,r}catch(e){console.error("请求报错:",e)}},async fetchBatchAddVibChannels(t={}){try{return await y(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchAddProcessChannels(t={}){try{return await U(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchDeleteVibChannels(t={}){try{return await A(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchDeleteProcessChannels(t={}){try{return await D(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchEditVibChannels(t={}){try{return await d(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchEditProcessChannel(t={}){try{return await p(t)}catch(e){console.error("请求报错:",e)}},async fetchAddWorkConditionChannels(t={}){try{return await u(t)}catch(e){console.error("请求报错:",e)}},async fetchEditWorkConditionChannel(t={}){try{return await l(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchDeleteWorkConditionChannels(t={}){try{return await h(t)}catch(e){console.error("请求报错:",e)}},async fetchAddRotSpeedChannels(t={}){try{return await i(t)}catch(e){console.error("请求报错:",e)}},async fetchEditRotSpeedChannel(t={}){try{return await c(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchDeleteRotSpeedChannels(t={}){try{return await a(t)}catch(e){console.error("请求报错:",e)}},async fetchBatchUpdateDAUNetwork(t={}){try{return await n(t)}catch(e){console.error("请求报错:",e)}},reset(){this.$reset()}}});export{M as u};
