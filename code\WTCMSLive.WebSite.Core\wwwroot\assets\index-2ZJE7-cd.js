import{r as _,w as Oe,j as Me,af as st,c as q,o as R,i as k,s as H,b as A,d as E,f as ne,g as N,F as ye,t as ge,cd as nt,q as Ne,ce as ot,u as ut,y as rt,e as qe,x as B,bL as dt,p as ct,v as pt,m,aP as _e}from"./index-D9CxWmlM.js";import{_ as vt,u as bt,c as mt,W as se}from"./table-C_s53ALS.js";import{F as Dt,O as te}from"./index-DyCH3qIX.js";import{_ as Ue}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as We}from"./index-BGEB0Rhf.js";import{W as ft}from"./index-R80zldKD.js";import{c as ae,S as ht,f as Ie,a as It}from"./tools-DZBuE28U.js";import{u as yt}from"./measurementDefinition-CBAS_A3c.js";import{u as gt}from"./devTree-BD8aiOqS.js";import{u as Lt}from"./configModbus--Xx2TLry.js";import{D as wt,a as Tt}from"./index-pF9HjW4G.js";import{M as kt}from"./index-DhZUqjZm.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";/* empty css                                                              */const At={class:"tableBox"},Rt={key:0},xt={key:1},Ft={class:"addRow"},Ct={key:0,class:"footer"},Vt={class:Ne(["footer-btn"])},qt={key:0,class:"btnitem"},_t={key:1,class:"btnitem"},Et={__name:"TableBoxComponent",props:{tableColumns:Array,tableDatas:Array,recordKey:String,tableTitle:String,tableOperate:{type:Array,default:()=>[]},noCopyUpKeys:{type:Array,default:()=>[]},addBtnDisabled:Boolean,size:String,noPagination:Boolean,noForm:Boolean,noCopyUp:Boolean,modelValue:{type:Object,default:()=>({})}},emits:["addRow","deleteRow","editRow","submit","hangeTableFormChange","update:modelValue"],setup(n,{expose:de,emit:oe}){const i=n,x=_({...i.modelValue}),W=oe,V=_(i.tableDatas&&i.tableDatas.length?i.tableDatas:[{key:Date.now()}]);Oe(()=>i.tableDatas,o=>{V.value=o&&o.length?o:[{key:Date.now()}],!o||!o.length?x.value={}:o.forEach((d,b)=>{i.tableColumns.forEach(g=>{if(!g.noEdit){const v=`${g.dataIndex}[${b}]`;x.value[v]=d[g.dataIndex]}})})},{immediate:!0});const ce=()=>{const o=i.noCopyUp?{key:Date.now()}:{...V.value[V.value.length-1],key:Date.now()},d=V.value.length;i.tableColumns.forEach(b=>{if(!b.noEdit){const g=`${b.dataIndex}[${d}]`;x.value[g]=o[b.dataIndex]}}),W("update:modelValue",{...i.modelValue,...x.value}),V.value.push(o)},j=(o,d,b)=>{const g=`${o}[${d}]`;x.value[g]=b,V.value[d][o]=b,W("update:modelValue",{...i.modelValue,...x.value,updateModelValue:{value:b,dataIndex:o,index:d}})},y=(o,d,b)=>{W("hangeTableFormChange",{value:o,dataIndex:b,index:d})&&W("hangeTableFormChange",{value:o,dataIndex:b,index:d})},C=o=>{W("deleteRow",o);let d=V.value.filter(b=>b.key!==o.key);d.forEach((b,g)=>{Object.keys(b).forEach(v=>{const l=`${v}[${g}]`;x.value[l]=b[v]})}),W("update:modelValue",{...x.value}),V.value=d},w=o=>{const d={};i.tableColumns.forEach(l=>{if(l.canEdit){const L=`${l.dataIndex}[${o-1}]`;d[l.dataIndex]=x.value[L]}});const g={...V.value[o]};Object.keys(d).forEach(l=>{if(!i.noCopyUpKeys.includes(l)&&l!=="key"){g[l]=d[l];const L=`${l}[${o}]`;x.value[L]=d[l]}});const v=[...V.value];v[o]=g,V.value=[...v],W("update:modelValue",{...i.modelValue,...x.value})},F=Me(()=>{var d;const o=i.tableColumns.filter(b=>!b.columnHidden);return(d=i.tableOperate)!=null&&d.length&&o.push({title:"操作",key:"action",dataIndex:"action"}),o});return de({getFieldsValue:()=>st(x.value),setFieldValues:o=>{x.value={...o}},setFieldValue:(o,d)=>{x.value[o]=d}}),(o,d)=>{const b=We,g=vt;return R(),q(ye,null,[k("div",At,[A(g,{bordered:"",columns:F.value,"data-source":V.value,pagination:!n.noPagination&&V.value.length>10,size:n.size||"middle"},{headerCell:E(({column:v})=>[N(ge(v.title),1)]),bodyCell:E(({column:v,record:l,text:L,index:J})=>{var ue,c;return[v.key==="action"?(R(),q("div",Rt,[(ue=n.tableOperate)!=null&&ue.includes("copyUp")&&J>0?(R(),ne(b,{key:0,onClick:P=>w(J)},{default:E(()=>d[1]||(d[1]=[N("同上",-1)])),_:2,__:[1]},1032,["onClick"])):H("",!0),(c=n.tableOperate)!=null&&c.includes("delete")?(R(),ne(b,{key:1,onClick:P=>C(l)},{default:E(()=>d[2]||(d[2]=[N("删除",-1)])),_:2,__:[2]},1032,["onClick"])):H("",!0)])):v.noEdit?(R(),q(ye,{key:2},[N(ge(L),1)],64)):(R(),q("div",xt,[A(Dt,{class:"formItem",notshowLabels:!0,itemProps:{...v,formItemWidth:v.columnWidth,dataIndex:v.dataIndex+"["+J+"]"},modelValue:x.value[v.dataIndex+"["+J+"]"],"onUpdate:modelValue":P=>j(v.dataIndex,J,P),onOnchangeSelect:P=>v.hasChangeEvent?y(P,J,v.dataIndex):null},null,8,["itemProps","modelValue","onUpdate:modelValue","onOnchangeSelect"])]))]}),_:1},8,["columns","data-source","pagination","size"]),k("div",Ft,[A(b,{onClick:d[0]||(d[0]=v=>ce())},{default:E(()=>d[3]||(d[3]=[N("添加一行",-1)])),_:1,__:[3]})])]),n.noForm?H("",!0):(R(),q("div",Ct,[nt(o.$slots,"footer",{},void 0),k("div",Vt,[i.noCancle?H("",!0):(R(),q("div",qt,[A(b,{onClick:o.cancelForm},{default:E(()=>d[4]||(d[4]=[N("取消",-1)])),_:1,__:[4]},8,["onClick"])])),i.noSubmit?H("",!0):(R(),q("div",_t,[A(b,{type:"primary","html-type":"submit"},{default:E(()=>d[5]||(d[5]=[N("确定",-1)])),_:1,__:[5]})]))])]))],64)}}},Ee=Ue(Et,[["__scopeId","data-v-c7b94e20"]]),D=320,Q=n=>{let de=[{align:"center",formItemWidth:D,title:"测量定义名称",dataIndex:"measDefinitionName",isrequired:!0},{align:"center",title:"测量定义状态",formItemWidth:D,dataIndex:"isAvailable",inputType:"radio",isrequired:!0,selectOptions:[{value:"1",label:"启用"},{value:"0",label:"禁用"}]},{align:"center",title:"采集单元",dataIndex:"dauID",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,hasChangeEvent:!0,formItemWidth:D},{align:"center",title:"采集间隔",dataIndex:"daqInterval",formItemWidth:D,validateRules:ae({title:"采集间隔",type:"number",required:!0})},{align:"center",title:"采集模式",formItemWidth:D,dataIndex:"modelType",inputType:"select",selectOptions:[{label:"主动",value:0},{label:"被动",value:1}]},{align:"center",title:"Modbus设备",formItemWidth:D,dataIndex:"modbusDeviceIDs",inputType:"checkbox",selectOptions:[],disabled:n&&n.edit},{align:"center",title:"采集间隔单位",dataIndex:"daqIntervalUnit",inputType:"select",formItemWidth:D,selectOptions:[],isrequired:!0}],oe=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:D},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:D},{align:"center",title:"信号类型",dataIndex:"signalType",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:D,headerOperations:{filters:[],filterOptions:n&&n.waveTypeList&&n.waveTypeList.length>0?n.waveTypeList:[]},...n&&!n.isForm&&n.waveTypeList?{customRender:({text:y,record:C,index:w,column:F})=>{const o=n.waveTypeList.find(d=>d.value==C.signalType);return o?o.label:y}}:{}},{align:"center",title:"信号带宽(Hz)",dataIndex:"upperLimitFreqency",isrequired:!0,inputType:"selectinput",selectOptions:[],formItemWidth:D},{align:"center",title:"采样长度(s)",dataIndex:"waveDefParamID",formItemWidth:D,validateRules:ae({title:"采样长度",type:"number",required:!0})}],i=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:D},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:D},{align:"center",title:"包络带宽(Hz)",dataIndex:"envBandWidth",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:D},{align:"center",title:"包络滤波器(Hz)",dataIndex:"envFiterFreq",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:D},{align:"center",title:"采样长度(s)",dataIndex:"sampleLength",formItemWidth:D,customRender:({text:y,record:C,index:w,column:F})=>C.waveDefParamID?C.waveDefParamID:"",validateRules:ae({title:"采样长度",type:"number",required:!0})}],x=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:D},{title:n&&n.isForm?"测量定义名称":"振动测量位置",dataIndex:n&&n.isForm?"measDefinitionName":"measLocationID",align:"center",disabled:!0,isrequired:!0,formItemWidth:D},{align:"center",title:"信号类型",dataIndex:"signalType",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,formItemWidth:D,headerOperations:{filters:[],filterOptions:n&&n.waveTypeList&&n.waveTypeList.length>0?n.waveTypeList:[]},...n&&!n.isForm&&n.waveTypeList?{customRender:({text:y,record:C,index:w,column:F})=>{const o=n.waveTypeList.find(d=>d.value==C.signalType);return o?o.label:y}}:{}},{align:"center",title:"信号带宽(Hz)",isrequired:!0,dataIndex:"upperLimitFreqency",inputType:"select",selectOptions:[],formItemWidth:D},{align:"center",title:"采样长度(s)",formItemWidth:D,dataIndex:"waveDefParamID",validateRules:ae({title:"采样长度",type:"number",required:!0})}],W=[{align:"center",title:"工况类型",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],disabled:n&&n.edit,canEdit:!0,hasChangeEvent:!0,labelInValue:!0,customRender:({text:y,record:C,index:w,column:F})=>C.measLocName?C.measLocName:""},{title:"上限频率(Hz)",dataIndex:"parmaChannelNumber",align:"center",inputType:"select",selectOptions:[{label:"1",value:"1"},{label:"50",value:"50"},{label:"100",value:"100"},{label:"1K",value:"1000"}],canEdit:!0},{align:"center",title:"采样长度(s)",dataIndex:"param_Type_Name",canEdit:!0,validateRules:ae({title:"采样长度",type:"number",required:!0})}],V=[{align:"center",title:"转速测量位置",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,labelInValue:!0,disabled:n&&n.edit,canEdit:!0,customRender:({text:y,record:C,index:w,column:F})=>C.measLocName?C.measLocName:""},{align:"center",title:"波形线数",dataIndex:"lineCounts",canEdit:!0}],ce=[{align:"center",title:"波形定义名称",dataIndex:"waveDefinitionName",isrequired:!0,formItemWidth:D},{title:"Modbus设备ID",dataIndex:"modbusDeviceID",align:"center",formItemWidth:D,inputType:"select",selectOptions:[],disabled:n&&n.edit,isrequired:!0,hasChangeEvent:!0,...n&&n.isForm?{}:{customRender:({text:y,record:C})=>C.modbusUnitID||""}},{title:"Modbus测量位置",dataIndex:"measLocationID",align:"center",formItemWidth:D,inputType:"select",selectOptions:[],isrequired:!0,disabled:n&&n.edit,...n&&n.isForm?{}:{customRender:({text:y,record:C})=>C.measLocationName||""}},{align:"center",title:"信号类型",dataIndex:"singleType",isrequired:!0,inputType:"select",selectOptions:[],formItemWidth:D,...n&&n.isForm?{}:{customRender:({text:y,record:C})=>C.singleTypeName||""}},{align:"center",title:"采样频率(Hz)",dataIndex:"sampleRate",isrequired:!0,formItemWidth:D},{align:"center",title:"采样长度(s)",dataIndex:"sampleLength",isrequired:!0,formItemWidth:D}],j=[{align:"center",title:"触发规则名称",dataIndex:"ruleName",isrequired:!0,formItemWidth:D},{title:"测量定义名称",dataIndex:"measdName",align:"center",formItemWidth:D,isrequired:!0,disabled:!0},{align:"center",title:"触发规则",dataIndex:"triggerRule",formItemWidth:D,hidden:!!(n&&n.isForm)},{align:"center",title:"被触发测量定义",dataIndex:"triggerMeasdedName",formItemWidth:D,hidden:!!(n&&n.isForm)},{align:"center",title:"触发采集类型",dataIndex:"triggerRuleType",formItemWidth:D,inputType:"select",hasChangeEvent:!0,headerOperations:{filters:[{text:"工况",value:"工况"},{text:"时间",value:"时间"}]},selectOptions:[{label:"工况",value:"工况"},{label:"时间",value:"时间"}]}];return[de,oe,i,x,W,V,ce,j]},Ot=()=>[{align:"center",title:"下限频率",dataIndex:"underLimitValue",canEdit:!0,columnWidth:230,validateRules:ae({title:"下限频率",type:"number",required:!0})},{title:"上限频率",dataIndex:"upperLimitValue",align:"center",canEdit:!0,columnWidth:230,validateRules:ae({title:"上限频率",type:"number",required:!0})}],Mt=()=>[{align:"center",title:"特征值类型",dataIndex:"type",canEdit:!0,isrequired:!0,inputType:"select",selectOptions:[],columnWidth:200},{title:"逻辑关系",dataIndex:"relationship",align:"center",canEdit:!0,inputType:"select",isrequired:!0,columnWidth:100,selectOptions:[{label:">",value:">"},{label:"<",value:"<>"},{label:">=",value:">="},{label:"<=",value:"<="},{label:"=",value:"="},{label:"!=",value:"!="}]},{align:"center",title:"数值",dataIndex:"value",editable:!0,isrequired:!0,canEdit:!0,columnWidth:200}],Ae=()=>[{align:"center",title:"选择采集单元",dataIndex:"collectionUnit",inputType:"select",isrequired:!0,selectOptions:[],hasChangeEvent:!0,labelInValue:!0,formItemWidth:D},{align:"center",hasLabelPosition:!0,dataIndex:"measLocIds",inputType:"checkboxGroup",defaultCheckAll:!0,labelInValue:!0,formItemWidth:"600",selectOptions:[],cancheckall:!0,defaultCheckAll:!0,isdisplay:!1},{title:"",hasLabelPosition:!0,dataIndex:"generalEVList",align:"center",inputType:"checkboxGroup",selectOptions:[],formItemWidth:"600",defaultCheckAll:!0},{align:"center",title:"",dataIndex:"isAll",inputType:"checkbox",selectOptions:[{value:"1",label:"将通带特征值配置和频带特征值配置应用到其他波形定义"}]},{title:"特征值配置",dataIndex:"evLists",align:"center",inputType:"checkbox",width:"100%",selectOptions:[{value:"41",label:"均值"},{value:"38",label:"最大值"},{value:"39",label:"最小值"}],cancheckall:!0},{title:"",dataIndex:"generalEVList",hasLabelPosition:!0,align:"center",inputType:"checkboxGroup",selectOptions:[],formItemWidth:"600",defaultCheckAll:!0},{title:"时间间隔(分钟)",dataIndex:"timeInterval",align:"center",formItemWidth:D,validateRules:ae({title:"时间间隔",type:"number"})}],Nt={class:"btnGroups"},Ut={class:"clearfix"},Wt=["onClick"],Pt={class:"baseInfo"},St={class:"border"},$t={class:"tableItems"},Gt={class:"tableItems"},Bt={class:"tableItems"},Kt={class:"tableItems"},zt={class:"tableItems"},Ht={class:"tableItems"},jt={class:"tableItems"},Jt={key:1,class:"nodata"},Qt={key:0},Xt={key:0,class:"modalContent"},Yt={class:"otherFromContent"},Zt={class:"getMeasLocName"},ea={key:0,class:"modalPart"},ta={class:"modalPart"},aa={class:"modalPart"},ia={key:1,class:"otherFromContent"},la={class:"getMeasLocName"},sa={key:0,class:"checkboxGroup1"},na={key:2,class:"otherFromContent"},oa={key:0},ua={key:3,class:"otherFromContent"},ra={class:"modalPart"},da={class:"modalPart"},ca={__name:"index",setup(n){const de=gt(),oe=ut(),i=yt(),{measdList:x,waveTypeList:W}=ot(i),V=Lt(),ce=bt(),j=_(!1),y=_(""),C=_(!1),w=_(""),F=_({}),o=_(),d=_(),b=_({}),g=_([]),v=_(Ae()),l=_({}),L=_(""),J=_([]),ue=_({}),c=_(oe.params.id),P=_(),Le=_(!0),s=rt({table1Data:[],table2Data:[],table3Data:[],table4Data:[],table5Data:[],table6Data:[],table7Data:[],modal7TableColumns:Mt(),batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{},bathApplyResponse3:{},bathApplyResponse4:{},bathApplyResponse5:{},bathApplyResponse6:{},bathApplyResponse7:{}}),Pe=async e=>{P.value&&await ce.fetchDevTreedDevicelist({windParkID:P.value,useTobath:!0})},Se=Me(()=>L.value==="batchAdd"?"1200px":w.value==="0"||w.value==="6"?"600px":"700px"),we=async e=>{if(j.value=!0,await i.fetchGetMeasdList({turbineID:c.value}),j.value=!1,x.value&&x.value.length){if(e&&e=="edit"&&JSON.stringify(l.value)!=="{}"){let t=x.value.find(a=>a.measDefinitionID==l.value.measDefinitionID);l.value=t}else l.value=x.value[0];if(Te(l.value),e&&e=="edit")return;await ke(),be(),me(),De(),pe(),ve(),fe(),he()}else l.value={},Te({})},Te=e=>{ue.value=e,J.value=[{label:"测量定义名称",value:e.measDefinitionName},{label:"测量定义状态",value:e.isAvailable?"开启":"禁用"},{label:"采集单元",value:e.dauName},{label:"采集模式",value:e.mdf_Ex?e.mdf_Ex.modelType==1?"被动":"主动":""}]},$e=()=>{let e=de.findAncestorsWithNodes(c.value);e&&e.length&&e.length>1&&(P.value=e[e.length-2].id)};Oe(()=>oe.params.id,async e=>{e&&(i.reset(),c.value=e,$e(),await Pe(),we())},{immediate:!0});const X=async(e,t)=>{let a=[...v.value],f=[...g.value];if(e){switch(w.value){case"1":case"2":case"3":let u=[];e.dataIndex=="collectionUnit"&&(e.value!=="无"?(w.value=="3"?(await He(e.value.value),u=i.unusedVoltageCurrentMeasLocList):w.value=="1"?(await ze(e.value.value),u=i.vibMeasLocList):w.value=="2"&&(await je(e.value.value),u=i.parameUnusedVibMeasLocList),a[1].selectOptions=u,a[1].isdisplay=!!(u&&u.length)):(a[1].selectOptions=[],a[1].isdisplay=!1));break;case"4":e.dataIndex&&e.dataIndex=="measLocationID"&&(f[3].tableList[e.index].selectOptions=[{label:e.value.label,value:"1"}]);break;case"5":e.dataIndex&&e.dataIndex=="measLocationID"&&(f[2].tableList[e.index].selectOptions=[{label:e.value.label,value:"1"}]);break;case"6":e.dataIndex&&e.dataIndex=="modbusDeviceID"&&(await V.fetchGetModbusChannelList({turbineID:c.value,modbusDeviceID:e.value}),f[2].selectOptions=V.modbusLocaOptions);break;case"7":e.dataIndex&&e.dataIndex=="triggerRuleType"&&(Le.value=e.value=="工况");return}g.value=[...f],v.value=[...a]}},Ge=async()=>{(!i.dauList||!i.dauList.length)&&await i.fetchGetDauList({turbineID:c.value})},Be=async()=>{(!i.initUpperLimitFreqList||!i.initUpperLimitFreqList.length)&&await i.fetchGetInitUpperLimitFreqList()},Ke=async e=>{(!i.measdDaqIntervalUnitType||!i.measdDaqIntervalUnitType.length)&&await i.fetchGetMeasdDaqIntervalUnitType()},ze=async e=>{await i.fetchGetVibMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},He=async e=>{await i.fetchGetUnusedVoltageCurrentMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},je=async e=>{await i.fetchGetParameUnusedVibMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID,DAUID:e})},Je=async()=>{(!i.eigenValueTypeList||!i.eigenValueTypeList.length)&&await i.fetchGetEigenValueTypeList()},Qe=async()=>await i.fetchGetWorkConOptionList({turbineID:c.value,MeasDefId:l.value.measDefinitionID}),Xe=async()=>await i.fetchGetWorkCondSpdMeasdOptions({WindTurbineID:c.value,MeasDefinitionID:l.value.measDefinitionID}),Ye=async()=>{(!i.envelopeFilterList||!i.envelopeFilterList.length)&&await i.fetchGetShowEnvelopeFilterList()},Ze=async()=>{(!i.upperLimitFreqList||!i.upperLimitFreqList.length)&&await i.fetchGetUpperLimitFreqList()},ke=async()=>{(!i.waveTypeList||!i.waveTypeList.length)&&await i.fetchGetWaveTypeList()},Re=()=>{var a;const e=(a=o.value)==null?void 0:a.getFieldsValue();let t="";w.value==1?t=(e.measDefinitionName||"")+(e.upperLimitFreqency||"")+"Hz"+(e.waveDefParamID||"")+" 秒":w.value==2?t="高频包络"+(e.envBandWidth||"")+"Hz"+(e.envFiterFreq||"")+"Hz"+(e.sampleLength||"")+" 秒":w.value==3&&(t=(e.measDefinitionName||"")+(e.upperLimitFreqency||"")+"Hz"+(e.waveDefParamID||"")+" 秒"),o.value.setFieldValue("waveDefinitionName",t),t&&t!==""&&o.value.clearValidate("waveDefinitionName")},xe=()=>{C.value=!0},Y=e=>{const{title:t,operateType:a,tableKey:f}=e;let u=Q({isForm:!0,validateWaveLineCounts:Ce})[f];L.value=a,w.value=f;let r=Ae(),I={tableDatas:[]};switch(f){case"0":y.value="添加测量定义",I={isAvailable:"1",daqIntervalUnit:0};break;case"1":y.value="添加时域波形定义",I={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};break;case"3":y.value="添加电流电压波形定义",I={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};break;case"2":y.value="添加高频包络波形定义",I={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};break;case"4":case"5":u.push({title:"工况监视特征值",dataIndex:"evAvg",inputType:"checkbox",selectOptions:[],tableList:[{selectOptions:[]}]}),f=="4"?y.value="添加工况波形定义":y.value="添加转速波形定义";break;case"6":y.value="添加Modbus设备波形定义";break;case"7":y.value="添加触发采集配置",I={measdName:l.value.measDefinitionName,triggerRuleType:"工况"};break}F.value={...I},g.value=u,v.value=[...r],Fe(f),xe()},et=async e=>{switch(w.value){case"0":let t={...e,measDefinitionID:L.value=="edit"?F.value.measDefinitionID:"",windTurbineID:c.value,windParkID:P.value,isAvailable:e.isAvailable&&e.isAvailable=="1",dauid:e.DauID&&e.DauID=="无"?"":e.DauID,modbusDeviceIDs:e.modbusDeviceIDs&&e.modbusDeviceIDs.length?e.modbusDeviceIDs.join(","):""},a={};L.value=="edit"?a=await i.fetchEditMeasDefinition(t):a=await i.fetchAddMeasDefinition(t),a&&a.code===1?(we(L.value),$(),m.success("提交成功")):m.error("提交失败:"+a.msg);break;case"1":case"3":let f=Ie(e),u="";f&&f.length&&f.forEach((re,Ve)=>{Ve>0&&(u+=","),u+="FBE#"+re.underLimitValue+"#"+re.upperLimitValue});let r={...e,waveDefinitionID:L.value=="edit"?F.value.waveDefinitionID:"",measDefinitionID:l.value.measDefinitionID,windTurbineID:c.value,windParkID:P.value,measLocIds:e.measLocIds&&e.measLocIds.length?e.measLocIds.join(","):"",messLocNames:"",dauid:e.collectionUnit&&e.collectionUnit.value?e.collectionUnit.value:"",isAll:!!(e.isAll&&e.isAll=="1"),FBEList:u,generalEVList:e.generalEVList&&e.generalEVList.length?e.generalEVList.join(","):"",timeWdfSampleLength:e.waveDefParamID,timeWdfUpFreq:e.upperLimitFreqency,isAddType:L.value=="edit"?"0":"1"},I={};w.value=="3"?I=await i.fetchMakeWaveDefinitionVoltageCurrent({sourceData:r,targetTurbineIds:s.batchApplyData}):I=await i.fetchMakeWaveDefinition({sourceData:r,targetTurbineIds:s.batchApplyData}),I&&I.code===1?(w.value=="3"?(De(),s.bathApplyResponse3=I.batchResults||{}):(be(),s.bathApplyResponse1=I.batchResults||{}),m.success("提交成功"),$()):m.error("提交失败:"+I.msg);break;case"2":let T={...e,waveDefinitionID:L.value=="edit"?F.value.waveDefinitionID:"",measDefinitionID:l.value.measDefinitionID,windTurbineID:c.value,windParkID:P.value,measLocIds:e.measLocIds&&e.measLocIds.length?e.measLocIds.join(","):"",messLocNames:"",dauid:e.collectionUnit&&e.collectionUnit.value?e.collectionUnit.value:"",isAll:!!(e.isAll&&e.isAll=="1"),timeWdfSampleLength:e.waveDefParamID,isAddType:L.value=="edit"?"0":"1",FBEList:"",GeneralEVList:""};const p=await i.fetchMakeParamEnvDefinition({sourceData:T,targetTurbineIds:s.batchApplyData});p&&p.code===1?(me(),s.bathApplyResponse2=p.batchResults||{},$(),m.success("提交成功")):m.error("提交失败:"+p.msg);break;case"4":let h={dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(e.evAvg&&e.evAvg.length),measLocId:e.measLocationID&&e.measLocationID.length>0?e.measLocationID[0].value:"",upperLimitFreqency:e.parmaChannelNumber,sampleLength:e.param_Type_Name},U=await i.fetchBatchEditWorkCondMeasd({sourceData:[h],targetTurbineIds:s.batchApplyData});U&&U.code===1?(pe(),s.bathApplyResponse4=U.batchResults||{},m.success("提交成功"),$()):m.error("提交失败:"+U.msg);break;case"5":let ie={...e,upperLimitFreqency:e.lineCounts?e.lineCounts*1:0,dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(e.evAvg&&e.evAvg.length),measLocId:F.value.measLocId},S=await i.fetchBatchEditWorkCondSpdMeasd({sourceData:[ie],targetTurbineIds:s.batchApplyData});S&&S.code===1?(ve(),s.bathApplyResponse5=S.batchResults||{},m.success("提交成功"),$()):m.error("提交失败:"+S.msg);break;case"6":let O={...e,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:F.value.waveDefinitionID||"",evLists:e.evLists?e.evLists:[]},M={};L.value=="add"?M=await i.fetchMeasdAddModbusWave({sourceData:[O],targetTurbineIds:s.batchApplyData}):M=await i.fetchEditModbusWave({sourceData:O,targetTurbineIds:s.batchApplyData}),M&&M.code===1?(fe(),s.bathApplyResponse6=M.batchResults||{},m.success("提交成功"),$()):m.error("提交失败:"+M.msg);break;case"7":let K=Ie(e),le=[];K&&K.length&&K.forEach((re,Ve)=>{le.push(re.type+","+re.relationship+","+re.value)});let G={isAddType:L.value=="edit"?"0":"1",turbineID:c.value,triggerRuleName:e.ruleName,triggerMeasDefName:l.value.measDefinitionName,conditionMonitoringLocIds:e.generalEVList&&e.generalEVList.length?e.generalEVList.join("#"):"",triggerData:le,dauid:l.value.dauID||"",triggertime:e.timeInterval||""},z=await i.fetchAddMeasdTirggerAcq({sourceData:G,targetTurbineIds:s.batchApplyData});z&&z.code===1?(he(),s.bathApplyResponse7=z.batchResults||{},$(),m.success("提交成功")):m.error("提交失败:"+z.msg);break}},tt=async e=>{switch(w.value){case"4":let t=Ie(e);if(t&&t.length){let f=t.map((r,I)=>({dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(r.evAvg&&r.evAvg.length),measLocId:r.measLocationID,upperLimitFreqency:r.parmaChannelNumber,sampleLength:r.param_Type_Name})),u=await i.fetchBatchAddWorkCondMeasd({sourceData:f,targetTurbineIds:s.batchApplyData});u&&u.code===1?(pe(),s.bathApplyResponse4=u.batchResults||{},m.success("提交成功"),$()):m.error("提交失败:"+u.msg)}break;case"5":let a=Ie(e);if(a&&a.length){let f=a.map((r,I)=>({dauID:l.value.dauID,windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,evAvg:!!(r.evAvg&&r.evAvg.length),measLocId:r.measLocationID,upperLimitFreqency:r.lineCounts,sampleLength:r.gearRatio})),u=await i.fetchBatchAddWorkCondSpdMeasd({sourceData:f,targetTurbineIds:s.batchApplyData});u&&u.code===1?(ve(),s.bathApplyResponse5=u.batchResults||{},m.success("提交成功"),$()):m.error("提交失败:"+u.msg)}break}},Z=e=>{const{tableKey:t,rowData:a}=e;w.value=t,L.value="edit";let f=Ae();v.value=f;let u=Q({isForm:!0,edit:!0,validateWaveLineCounts:Ce})[t],r={measDefinitionName:l.value.measDefinitionName?l.value.measDefinitionName:""};switch(t){case"0":y.value="编辑测量定义",a.DauID&&(u[3].isdisplay=!1,u[3].validateRules[0].required=!1);let I=[];a.modbusUnits&&a.modbusUnits.length&&a.modbusUnits.map(O=>{I.push(O.modbusDeviceID)}),r={...r,...a,...a.mdf_Ex,isAvailable:a.isAvailable?"1":"0",dauID:a.dauID?a.dauID:"无",modbusDeviceIDs:I};break;case"1":case"3":let T=[],p=[],h={},U=0;a.vibEigenValueConf&&a.vibEigenValueConf.length&&a.vibEigenValueConf.forEach((O,M)=>{O.evType==0?T.push(O.type):O.evType==1&&(p.push({key:M,underLimitValue:O.underLimitValue,upperLimitValue:O.upperLimitValue}),h[`underLimitValue[${U}]`]=O.underLimitValue,h[`upperLimitValue[${U}]`]=O.upperLimitValue,U++)}),r={...r,...a,generalEVList:T,tableDatas:p,...h},t=="1"?y.value="编辑时域波形定义":y.value="编辑电流电压波形定义";break;case"2":y.value="编辑高频包络波形定义",r={...r,...a,sampleLength:a.waveDefParamID};break;case"4":case"5":u.push({title:"工况监视特征值",dataIndex:"evAvg",inputType:"checkbox",selectOptions:[{label:a.measLocName,value:"1"}]}),r={...r,...a,measLocId:a.measLocationID,evAvg:a.evAvg?["1"]:[],measLocationID:[{label:a.measLocName,value:a.measLocationID}],upperLimitFreqency:a.parmaChannelNumber},t=="4"?y.value="编辑工况波形定义":y.value="编辑转速波形定义";break;case"6":y.value="编辑Modbus设备波形定义",r={...r,...a,evLists:a.eigenValues},g.value=[...u],a.modbusDeviceID&&a.modbusDeviceID!==""&&X({dataIndex:"modbusDeviceID",value:a.modbusDeviceID});break;case"7":y.value="编辑触发采集配置";let ie=[],S="";a.triggerRuleType&&(Le.value=a.triggerRuleType=="工况",a.triggerRuleList&&a.triggerRuleList.length&&(a.triggerRuleType=="工况"?a.triggerRuleList.forEach((O,M)=>{let K=O.split(",");ie.push({key:M,type:K[0],relationship:K[1],value:K[2]})}):S=a.triggerRuleList[0])),r={...r,...a,generalEVList:a.triggerMeasdId,timeInterval:S,tableDatas:ie};break}t!=="6"&&(g.value=[...u]),Fe(t),F.value={...r},xe()},ee=async e=>{const{tableKey:t,selectedkeys:a,rowData:f,record:u}=e;if((!a||!a.length)&&t!=="0"){m.error("请选择要删除的行");return}switch(t){case"0":const r=await i.fetchDeleteMeasDef({windTurbineID:c.value,measDefId:f.measDefinitionID});r&&r.code===1?(we(),m.success("删除成功")):m.error("删除失败:"+r.msg);break;case"1":const I=await i.fetchDeleteWaveChannel({sourceData:{windTurbineID:c.value,measDefId:l.value.measDefinitionID,waveId:a.join(",")},targetTurbineIds:s.batchApplyData});I&&I.code===1?(be(),s.bathApplyResponse1=I.batchResults||{},m.success("删除成功")):m.error("删除失败:"+I.msg);break;case"2":const T=await i.fetchDeleteParamEnvChannel({sourceData:{windTurbineID:c.value,measDefId:l.value.measDefinitionID,waveId:a.join(",")},targetTurbineIds:s.batchApplyData});T&&T.code===1?(me(),s.bathApplyResponse2=T.batchResults||{},m.success("删除成功")):m.error("删除失败:"+T.msg);break;case"3":const p=await i.fetchDeleteWaveChannelVoltageCurrenl({sourceData:{windTurbineID:c.value,measDefId:l.value.measDefinitionID,waveId:a.join(",")},targetTurbineIds:s.batchApplyData});p&&p.code===1?(De(),s.bathApplyResponse3=p.batchResults||{},m.success("删除成功")):m.error("删除失败:"+p.msg);break;case"4":let h=[];a.forEach((G,z)=>{h.push({windTurbineID:c.value,dauID:l.value.dauID,measDefinitionID:l.value.measDefinitionID,measLocId:G})});let U=await i.fetchBatchDeleteWorkCondMeasd({sourceData:h,targetTurbineIds:s.batchApplyData});U&&U.code===1?(pe(),s.bathApplyResponse4=U.batchResults||{},m.success("删除成功")):m.error("删除失败:"+U.msg);break;case"5":let ie=[];a.forEach((G,z)=>{ie.push({windTurbineID:c.value,dauID:l.value.dauID,measDefinitionID:l.value.measDefinitionID,measLocId:G})});let S=await i.fetchBatchDeleteWorkCondSpdMeasd({sourceData:ie,targetTurbineIds:s.batchApplyData});S&&S.code===1?(ve(),s.bathApplyResponse5=S.batchResults||{},m.success("删除成功")):m.error("删除失败:"+S.msg);break;case"6":let O=[];if(u)O.push({windTurbineID:c.value,measDefinitionID:u.measDefinitionID,waveDefinitionID:u.waveDefinitionID,measLocationID:u.measLocationID});else for(let G=0;G<a.length;G++){let z=a[G].split("&&");O.push({windTurbineID:c.value,measDefinitionID:l.value.measDefinitionID,waveDefinitionID:z[0],measLocationID:z[1]})}let M=await i.fetchBatchDeleteModbusWave({sourceData:O,targetTurbineIds:s.batchApplyData});M&&M.code===1?(fe(),s.bathApplyResponse6=M.batchResults||{},m.success("删除成功")):m.error("删除失败:"+M.msg);break;case"7":let K=[];a.forEach((G,z)=>{K.push({turbineID:c.value,dauID:l.value.dauID,triggerRuleName:G,triggerMeasDefName:l.value.measDefinitionName})});let le=await i.fetchDeleteTriggerGatherDispose({sourceData:K,targetTurbineIds:s.batchApplyData});le&&le.code===1?(he(),s.bathApplyResponse7=le.batchResults||{},m.success("删除成功")):m.error("删除失败:"+le.msg);break}},at=async()=>{(!V.modbusDeviceOptions||!V.modbusDeviceOptions.length)&&await V.fetchGetModbusDeviceList({turbineID:c.value})},Fe=async e=>{const t=g.value;if(!t||!t.length)return;const a=v.value;await Ge();const f=[{label:"无",value:"无"},...i.dauList];switch(e){case"0":await at(),await Ke(),t[5].selectOptions=[...V.modbusDeviceOptions],t[2].selectOptions=f,t[6].selectOptions=[...i.measdDaqIntervalUnitType],g.value=[...t];break;case"1":case"3":await ke(),await Be(),await Je(),t[2].selectOptions=i.waveTypeList,t[3].selectOptions=i.initUpperLimitFreqList,a[0].selectOptions=f,a[2].selectOptions=i.eigenValueTypeList,g.value=[...t],v.value=[...a];break;case"2":await Ye(),await Ze(),t[2].selectOptions=i.upperLimitFreqList,t[3].selectOptions=i.envelopeFilterList,g.value=[...t],a[0].selectOptions=f,v.value=[...a];break;case"4":case"5":if(L.value=="batchAdd"){let u=[];e=="4"?u=await Qe():u=await Xe(),t[0].selectOptions=u,g.value=[...t]}break;case"6":if(l.value.modbusUnits&&l.value.modbusUnits.length){let u=It(l.value.modbusUnits,{label:"modbusUnitID",value:"modbusDeviceID"});t[1].selectOptions=u}await ke(),t[3].selectOptions=i.waveTypeList;break;case"7":if(l.value.superviseEvName&&l.value.superviseEvName!==""){let u=l.value.superviseEvName.split(","),r=[];u.forEach((I,T)=>{r.push({label:I,value:I})}),s.modal7TableColumns[0].selectOptions=r}if(l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0){let u=[];i.measdList.forEach((r,I)=>{r.mdf_Ex&&r.mdf_Ex.modelType==1&&u.push({label:r.measDefinitionName,value:r.measDefinitionID})}),a[5].selectOptions=u}}},Ce=async(e,t)=>{var u,r;const a=L.value=="batchAdd"?(u=d.value)==null?void 0:u.getTableFieldsValue():(r=o.value)==null?void 0:r.getFieldsValue();if(!a)return;let f=!0;if(L.value=="batchAdd"){let T=e.field.match(/\d+/)[0];f=a[`gearRatio[${T}]`]&&a[`gearRatio[${T}]`]%t==0}else f=a.gearRatio&&a.gearRatio%t==0;return f?Promise.resolve(f):Promise.reject(new Error("编码器线数必须是波形线数的整数倍！"))},$=()=>{C.value=!1,F.value={},w.value="",y.value="",L.value="",g.value=[],v.value=[],b.value={}},it=e=>{if(e.measDefinitionID!=l.value.measDefinitionID){if(j.value=!0,l.value=e,Te(e),!x.value||!x.value.length){m.warning("无法点击！");return}be(),me(),De(),pe(),ve(),fe(),he(),j.value=!1}},be=async()=>{s.table1Data=await i.fetchGetTimeWaveDefList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},me=async()=>{s.table2Data=await i.fetchGetTimeParamEnvDefList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},De=async()=>{s.table3Data=await i.fetchGetVoltageCurrentWaveDefList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},pe=async()=>{s.table4Data=await i.fetchGetWorkConListForMeasLocList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},ve=async()=>{s.table5Data=await i.fetchGetWorkCondSpdMeasdList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},fe=async()=>{s.table6Data=await i.fetchGetModbusWaveList({turbineID:c.value,measDefinitionID:l.value.measDefinitionID})},he=async()=>{s.table7Data=await i.fetchGetMeasdTriggerList({turbineID:c.value,MeasDefinitionID:l.value.measDefinitionID})},lt=async e=>{e.type&&e.type=="close"?(s.batchApplyData=[],s.batchApplyKey="",s[`bathApplyResponse${e.key}`]={}):(s.batchApplyData=e.turbines,s.batchApplyKey=e.key)};return _e("deviceId",c),_e("bathApplySubmit",lt),(e,t)=>{const a=We,f=Tt,u=wt,r=kt,I=ht;return R(),ne(I,{spinning:j.value,size:"large"},{default:E(()=>[k("h1",null,[t[18]||(t[18]=N(" 测量定义 ",-1)),A(a,{class:"addbtnOfPageTitle",type:"primary",onClick:t[0]||(t[0]=T=>Y({tableKey:"0",operateType:"add",title:"测量定义"}))},{default:E(()=>t[17]||(t[17]=[N("添加",-1)])),_:1,__:[17]})]),k("div",Nt,[k("ul",Ut,[(R(!0),q(ye,null,qe(B(x),T=>(R(),q("li",{key:T.measDefinitionID,class:Ne({active:l.value.measDefinitionID===T.measDefinitionID}),onClick:p=>it(T)},ge(T.measDefinitionName),11,Wt))),128))])]),k("div",Pt,[A(mt,{tableTitle:"测量定义信息",defaultCollapse:!0,batchApply:!1},dt({content:E(()=>[k("div",St,[A(u,{column:5,size:"small"},{default:E(()=>[(R(!0),q(ye,null,qe(J.value,T=>(R(),ne(f,{label:T.label},{default:E(()=>[N(ge(T.value),1)]),_:2},1032,["label"]))),256))]),_:1})])]),_:2},[l.value&&l.value.measDefinitionID?{name:"rightButtons",fn:E(()=>[A(a,{type:"primary",onClick:t[1]||(t[1]=T=>Z({tableKey:"0",rowData:ue.value,title:"测量定义"}))},{default:E(()=>t[19]||(t[19]=[N("编辑",-1)])),_:1,__:[19]}),A(a,{onClick:t[2]||(t[2]=T=>ee({tableKey:"0",rowData:ue.value,title:"测量定义"}))},{default:E(()=>t[20]||(t[20]=[N("删除",-1)])),_:1,__:[20]})]),key:"0"}:void 0]),1024)]),l.value&&l.value.measDefinitionID?(R(),q("div",{class:"blockBorder",key:c.value},[k("div",$t,[A(se,{"table-key":"1","table-title":"时域波形定义列表","table-columns":B(Q)({waveTypeList:B(W)})[1],borderLight:s.batchApplyKey=="1",bathApplyResponse:s.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID",onAddRow:Y,"table-datas":s.table1Data,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),k("div",Gt,[A(se,{"table-key":"2","table-title":"高频包络波形定义列表","table-columns":B(Q)()[2],borderLight:s.batchApplyKey=="2",bathApplyResponse:s.bathApplyResponse2,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID","table-datas":s.table2Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),k("div",Bt,[A(se,{"table-key":"3","table-title":"电流电压波形定义列表","table-columns":B(Q)({waveTypeList:B(W)})[3],borderLight:s.batchApplyKey=="3",bathApplyResponse:s.bathApplyResponse3,"table-operate":["edit","delete","add","batchDelete"],"record-key":"waveDefinitionID","table-datas":s.table3Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),k("div",Kt,[A(se,{"table-key":"4","table-title":"工况波形定义列表","table-columns":B(Q)()[4],borderLight:s.batchApplyKey=="4",bathApplyResponse:s.bathApplyResponse4,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measLocationID","table-datas":s.table4Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),k("div",zt,[A(se,{"table-key":"5","table-title":"转速波形定义列表","table-columns":B(Q)()[5],borderLight:s.batchApplyKey=="5",bathApplyResponse:s.bathApplyResponse5,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measLocationID","table-datas":s.table5Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])]),k("div",Ht,[A(se,{"table-key":"6","table-title":"Modbus设备波形定义列表","table-columns":B(Q)()[6],borderLight:s.batchApplyKey=="6",bathApplyResponse:s.bathApplyResponse6,"table-operate":["edit","delete","add","batchDelete"],recordKey:T=>`${T.waveDefinitionID}&&${T.measLocationID}`,"table-datas":s.table6Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])]),k("div",jt,[A(se,{"table-key":"7","table-title":"触发采集配置列表","table-columns":B(Q)()[7],borderLight:s.batchApplyKey=="7",bathApplyResponse:s.bathApplyResponse7,"table-operate":["edit","delete","add","batchDelete"],"record-key":"ruleName","table-datas":s.table7Data,onAddRow:Y,onDeleteRow:ee,onEditRow:Z,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"])])])):(R(),q("div",Jt," 请先添加测量定义!")),A(r,{maskClosable:!1,destroyOnClose:!0,width:Se.value,open:C.value,title:y.value,footer:"",onCancel:$},{default:E(()=>[L.value==="add"||L.value==="edit"?(R(),q("div",Qt,[A(te,{ref_key:"formModalRef",ref:o,model:b.value,"title-col":g.value,initFormData:F.value,actions:["submit","change"],onChange:X,onSubmit:et},{otherInfo:E(({formModel:T})=>[w.value=="1"||w.value=="3"?(R(),q("div",Xt,[k("div",Yt,[k("div",Zt,[A(a,{onClick:t[3]||(t[3]=p=>Re())},{default:E(()=>t[21]||(t[21]=[N("自动生成",-1)])),_:1,__:[21]})]),L.value==="add"?(R(),q("div",ea,[t[22]||(t[22]=k("p",null,"振动测量位置",-1)),A(te,{modelValue:b.value,"onUpdate:modelValue":[t[4]||(t[4]=p=>b.value=p),t[5]||(t[5]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["collectionUnit","measLocIds"])})],onlyFormList:!0,onChange:X,"title-col":[v.value[0],v.value[1]],initFormData:F.value},null,8,["modelValue","title-col","initFormData"])])):H("",!0),k("div",ta,[t[23]||(t[23]=k("p",null,"通带特征值配置",-1)),A(te,{modelValue:b.value,"onUpdate:modelValue":[t[6]||(t[6]=p=>b.value=p),t[7]||(t[7]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["generalEVList"])})],onlyFormList:!0,"title-col":[{...v.value[2]}],initFormData:F.value},null,8,["modelValue","title-col","initFormData"])]),k("div",aa,[t[24]||(t[24]=k("p",null,"频带特征值配置",-1)),A(Ee,{modelValue:b.value,"onUpdate:modelValue":[t[8]||(t[8]=p=>b.value=p),t[9]||(t[9]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["underLimitValue","upperLimitValue"])})],size:"default","table-columns":B(Ot)(),"table-operate":["delete"],"table-datas":F.value.tableDatas,noForm:!0,noCopyUp:!0,"noCopyUp-keys":["underLimitValue","upperLimitValue"],onHangeTableFormChange:X},null,8,["modelValue","table-columns","table-datas"]),A(te,{onlyFormList:!0,titleCol:[{...v.value[3]}],initFormData:F.value},null,8,["titleCol","initFormData"])])])])):w.value=="2"?(R(),q("div",ia,[k("div",la,[A(a,{onClick:t[10]||(t[10]=p=>Re())},{default:E(()=>t[25]||(t[25]=[N("自动生成",-1)])),_:1,__:[25]})]),L.value=="add"?(R(),q("div",sa,[t[26]||(t[26]=k("p",null,"振动测量位置",-1)),A(te,{onlyFormList:!0,onChange:X,"title-col":[v.value[0],v.value[1]],initFormData:F.value,"onUpdate:modelValue":t[11]||(t[11]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["measLocIds","collectionUnit"])})},null,8,["title-col","initFormData"])])):H("",!0)])):w.value=="6"?(R(),q("div",na,[L.value=="add"||L.value=="edit"?(R(),q("div",oa,[A(te,{onlyFormList:!0,onChange:X,"title-col":[v.value[4]],"onUpdate:modelValue":t[12]||(t[12]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["evLists"])}),initFormData:F.value},null,8,["title-col","initFormData"])])):H("",!0)])):w.value=="7"?(R(),q("div",ua,[k("div",ra,[t[27]||(t[27]=k("p",null,"触发规则",-1)),Le.value?(R(),ne(Ee,{key:0,modelValue:b.value,"onUpdate:modelValue":[t[13]||(t[13]=p=>b.value=p),t[14]||(t[14]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["relationship","type","value"])})],size:"default","table-columns":s.modal7TableColumns,"table-operate":["delete"],"table-datas":F.value.tableDatas,noForm:!0,noCopyUp:!0,"noCopyUp-keys":["type","relationship","value"]},null,8,["modelValue","table-columns","table-datas"])):(R(),ne(te,{key:1,onlyFormList:!0,"title-col":[v.value[6]],"onUpdate:modelValue":t[15]||(t[15]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["timeInterval"])}),initFormData:F.value},null,8,["title-col","initFormData"]))]),ct(k("div",da,[t[28]||(t[28]=k("p",null,"被触发测量定义",-1)),A(te,{onlyFormList:!0,onChange:X,"title-col":[v.value[5]],initFormData:F.value,"onUpdate:modelValue":t[16]||(t[16]=p=>{var h;return(h=o.value)==null?void 0:h.updateSlotFormData(p,["generalEVList"])})},null,8,["title-col","initFormData"])],512),[[pt,!!(l.value&&l.value.mdf_Ex&&l.value.mdf_Ex.modelType==0)]])])):H("",!0)]),_:1},8,["model","title-col","initFormData"])])):L.value==="batchAdd"?(R(),ne(ft,{key:1,ref_key:"tableModalRef",ref:d,size:"default","table-columns":g.value,"table-operate":["copyUp","delete"],"table-datas":[],onHangeTableFormChange:X,"noCopyUp-keys":["parkNumber"],onSubmit:tt,onCancel:$},null,8,["table-columns"])):H("",!0)]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}},Ea=Ue(ca,[["__scopeId","data-v-767b0614"]]);export{Ea as default};
