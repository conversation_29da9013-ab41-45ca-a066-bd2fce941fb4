﻿using System;
using System.Collections.Generic;

namespace WindCMS.EF.WTParameter
{
    /// <summary>
    /// 存储与机舱相关的参数
    /// </summary>
    public partial class WtNacelleParameter
    {
        public WtNacelleParameter()
        {
            WtParametersGearboxes = new HashSet<WtParametersGearbox>();
        }

        /// <summary>
        /// 机舱参数的唯一标识符
        /// </summary>
        public int NacelleId { get; set; }
        /// <summary>
        /// 对应的机组型号ID（外键）
        /// </summary>
        public string ModelId { get; set; } = null!;
        /// <summary>
        /// 额定功率（单位：kw）
        /// </summary>
        public decimal RatedPower { get; set; }
        /// <summary>
        /// 发电机额定转速（单位：RPM）
        /// </summary>
        public int GeneratorRatedSpeed { get; set; }
        /// <summary>
        /// 发电机并网转速（单位：RPM）
        /// </summary>
        public int GeneratorGridSpeed { get; set; }
        /// <summary>
        /// 齿轮箱生产厂家
        /// </summary>
        public string? GearboxManufacturer { get; set; }
        /// <summary>
        /// 齿轮箱结构类型
        /// </summary>
        public string? GearboxStructureType { get; set; }
        /// <summary>
        /// 润滑油生产厂家
        /// </summary>
        public string? LubricantManufacturer { get; set; }
        /// <summary>
        /// 润滑油型号
        /// </summary>
        public string? LubricantModel { get; set; }
        /// <summary>
        /// 传动比
        /// </summary>
        public decimal? GearRatio { get; set; }
        /// <summary>
        /// 发电机类型（如永磁同步发电机）
        /// </summary>
        public string? GeneratorType { get; set; }
        /// <summary>
        /// 发电机额定电压（单位：V）
        /// </summary>
        public decimal? GeneratorRatedVoltage { get; set; }
        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        /// <summary>
        /// 主轴承映射ID
        /// </summary>
        public string? MainBearingId { get; set; }
        /// <summary>
        /// 发电机轴承驱动端1制造商
        /// </summary>
        public string? GeneratorBearingId { get; set; }
        /// <summary>
        /// 发电机制造商
        /// </summary>
        public string? GeneratorManufacturer { get; set; }
        /// <summary>
        /// 发电机型号
        /// </summary>
        public string? GeneratorModel { get; set; }
        /// <summary>
        /// 发电机磁极对数
        /// </summary>
        public int? GeneratorPolePairs { get; set; }
        /// <summary>
        /// 发电机转子条数
        /// </summary>
        public int? GeneratorRotorBarCount { get; set; }
        /// <summary>
        /// 发电机定子槽数
        /// </summary>
        public int? GeneratorStatorSlotCount { get; set; }
        /// <summary>
        /// 齿轮箱型号
        /// </summary>
        public string? GearboxModel { get; set; }

        // 新增油液属性
        /// <summary>
        /// 油液的40℃理论运动粘度(cst)
        /// </summary>
        public decimal? LubricantViscosity40C { get; set; }

        /// <summary>
        /// 油液的100℃理论运动粘度(cst)
        /// </summary>
        public decimal? LubricantViscosity100C { get; set; }

        /// <summary>
        /// 油液的25℃理论密度(kg/m²)
        /// </summary>
        public decimal? LubricantDensity25C { get; set; }

        /// <summary>
        /// 齿轮箱传动比
        /// </summary>
        public decimal? GearboxGearRatio { get; set; }

        public virtual ICollection<WtParametersGearbox> WtParametersGearboxes { get; set; }

        public List<WtParametersBearing> MainBearingList { get; set; }
        public List<WtParametersBearing> GeneratorBearingList { get; set; }
    }
}
