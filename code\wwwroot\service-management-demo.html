<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务管理系统演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a9e;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #218838;
        }
        .service-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .service-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .service-status {
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
        }
        .status-running { background: #28a745; }
        .status-stopped { background: #6c757d; }
        .status-error { background: #dc3545; }
        .status-starting { background: #ffc107; color: #000; }
        .status-stopping { background: #fd7e14; }
        .service-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .log-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 2px;
            word-wrap: break-word;
        }
        .log-info { color: #0f0; }
        .log-warn { color: #ff0; }
        .log-error { color: #f00; }
        .log-debug { color: #0ff; }
        .response-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .flex-row {
            display: flex;
            gap: 20px;
        }
        .flex-col {
            flex: 1;
        }
        .connection-status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>服务管理系统演示</h1>
        
        <!-- 连接状态 -->
        <div id="connectionStatus" class="connection-status disconnected">
            SignalR连接状态: 未连接
        </div>

        <!-- 服务配置管理 -->
        <div class="section">
            <h2>服务配置管理</h2>
            <div class="flex-row">
                <div class="flex-col">
                    <div class="form-group">
                        <label>服务名称:</label>
                        <input type="text" id="serviceName" placeholder="例: 数据采集服务">
                    </div>
                    <div class="form-group">
                        <label>服务路径:</label>
                        <input type="text" id="servicePath" placeholder="例: /opt/datacollector/datacollector">
                    </div>
                    <div class="form-group">
                        <label>工作目录:</label>
                        <input type="text" id="workingDirectory" placeholder="例: /opt/datacollector">
                    </div>
                    <div class="form-group">
                        <label>启动参数:</label>
                        <input type="text" id="arguments" placeholder="例: --config=config.json">
                    </div>
                </div>
                <div class="flex-col">
                    <div class="form-group">
                        <label>服务描述:</label>
                        <textarea id="description" rows="3" placeholder="服务功能描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isEnabled" checked> 启用服务
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="autoStart"> 自动启动
                        </label>
                    </div>
                </div>
            </div>
            <button onclick="saveServiceConfig()">保存配置</button>
            <button onclick="loadServiceConfigs()">刷新列表</button>
            <div id="saveResponse" class="response-area" style="display:none;"></div>
        </div>

        <!-- 服务列表和状态 -->
        <div class="section">
            <h2>服务列表和状态</h2>
            <button onclick="loadServiceStatuses()">刷新状态</button>
            <div id="serviceList"></div>
        </div>

        <!-- 日志查询 -->
        <div class="section">
            <h2>日志查询</h2>
            <div class="flex-row">
                <div class="flex-col">
                    <div class="form-group">
                        <label>服务ID:</label>
                        <select id="logServiceId">
                            <option value="">所有服务</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>日志类型:</label>
                        <select id="logType">
                            <option value="1">调试日志</option>
                            <option value="2">错误日志</option>
                        </select>
                    </div>
                </div>
                <div class="flex-col">
                    <div class="form-group">
                        <label>开始时间:</label>
                        <input type="datetime-local" id="startTime">
                    </div>
                    <div class="form-group">
                        <label>结束时间:</label>
                        <input type="datetime-local" id="endTime">
                    </div>
                </div>
                <div class="flex-col">
                    <div class="form-group">
                        <label>关键词:</label>
                        <input type="text" id="keyword" placeholder="搜索关键词">
                    </div>
                    <div class="form-group">
                        <label>页大小:</label>
                        <input type="number" id="pageSize" value="50" min="10" max="500">
                    </div>
                </div>
            </div>
            <button onclick="queryLogs()">查询日志</button>
            <button onclick="startRealtimeLog()" class="success">开始实时日志</button>
            <button onclick="stopRealtimeLog()" class="danger">停止实时日志</button>
            <div id="logResponse" class="response-area" style="display:none;"></div>
        </div>

        <!-- 实时日志显示 -->
        <div class="section">
            <h2>实时日志</h2>
            <button onclick="clearLogs()">清空日志</button>
            <div id="realtimeLog" class="log-container"></div>
        </div>
    </div>

    <!-- SignalR JavaScript Client -->
    <script src="https://unpkg.com/@microsoft/signalr@latest/dist/browser/signalr.js"></script>
    
    <script>
        let connection = null;
        let services = [];

        // 初始化SignalR连接
        async function initSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl("/Hubs/ServerPerformanceHub")
                .build();

            connection.on("ReceiveServiceLog", function (logItem) {
                addLogEntry(logItem);
            });

            connection.onclose(function () {
                updateConnectionStatus(false);
            });

            try {
                await connection.start();
                updateConnectionStatus(true);
                console.log("SignalR连接成功");
            } catch (err) {
                console.error("SignalR连接失败:", err);
                updateConnectionStatus(false);
            }
        }

        // 更新连接状态显示
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            if (connected) {
                statusEl.textContent = 'SignalR连接状态: 已连接';
                statusEl.className = 'connection-status connected';
            } else {
                statusEl.textContent = 'SignalR连接状态: 未连接';
                statusEl.className = 'connection-status disconnected';
            }
        }

        // 保存服务配置
        async function saveServiceConfig() {
            const config = {
                serviceName: document.getElementById('serviceName').value,
                servicePath: document.getElementById('servicePath').value,
                workingDirectory: document.getElementById('workingDirectory').value,
                arguments: document.getElementById('arguments').value,
                description: document.getElementById('description').value,
                isEnabled: document.getElementById('isEnabled').checked,
                autoStart: document.getElementById('autoStart').checked
            };

            if (!config.serviceName || !config.servicePath) {
                alert('服务名称和服务路径不能为空');
                return;
            }

            try {
                const response = await fetch('/api/ServerConf/SaveServiceConfig', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                showResponse('saveResponse', result);
                
                if (result.code === 1) {
                    clearForm();
                    loadServiceConfigs();
                }
            } catch (error) {
                showResponse('saveResponse', { error: error.message });
            }
        }

        // 加载服务配置列表
        async function loadServiceConfigs() {
            try {
                const response = await fetch('/api/ServerConf/GetServiceConfigs');
                services = await response.json();
                updateServiceSelect();
            } catch (error) {
                console.error('加载服务配置失败:', error);
            }
        }

        // 加载服务状态
        async function loadServiceStatuses() {
            try {
                const response = await fetch('/api/ServerConf/GetServiceStatuses');
                const statuses = await response.json();
                displayServiceList(statuses);
            } catch (error) {
                console.error('加载服务状态失败:', error);
            }
        }

        // 显示服务列表
        function displayServiceList(statuses) {
            const container = document.getElementById('serviceList');
            container.innerHTML = '';

            statuses.forEach(service => {
                const serviceEl = document.createElement('div');
                serviceEl.className = 'service-item';
                
                const statusClass = getStatusClass(service.status);
                const statusText = getStatusText(service.status);
                
                serviceEl.innerHTML = `
                    <div class="service-header">
                        <div class="service-name">${service.serviceName}</div>
                        <div class="service-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="service-info">
                        ID: ${service.serviceId} | 
                        ${service.processId ? `PID: ${service.processId} | ` : ''}
                        ${service.memoryUsage ? `内存: ${service.memoryUsage}MB | ` : ''}
                        ${service.runningDuration ? `运行时长: ${Math.floor(service.runningDuration / 60)}分钟` : ''}
                    </div>
                    <div>
                        <button onclick="startService('${service.serviceId}')" class="success">启动</button>
                        <button onclick="stopService('${service.serviceId}')" class="danger">停止</button>
                        <button onclick="restartService('${service.serviceId}')">重启</button>
                        <button onclick="deleteService('${service.serviceId}')" class="danger">删除</button>
                    </div>
                `;
                
                container.appendChild(serviceEl);
            });
        }

        // 服务控制操作
        async function startService(serviceId) {
            await serviceOperation('StartService', serviceId);
        }

        async function stopService(serviceId) {
            await serviceOperation('StopService', serviceId);
        }

        async function restartService(serviceId) {
            await serviceOperation('RestartService', serviceId);
        }

        async function deleteService(serviceId) {
            if (!confirm('确定要删除这个服务配置吗？')) return;
            await serviceOperation('DeleteServiceConfig', serviceId);
        }

        async function serviceOperation(operation, serviceId) {
            try {
                const response = await fetch(`/api/ServerConf/${operation}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ serviceId })
                });
                
                const result = await response.json();
                alert(result.msg);
                
                if (result.code === 1) {
                    loadServiceStatuses();
                    if (operation === 'DeleteServiceConfig') {
                        loadServiceConfigs();
                    }
                }
            } catch (error) {
                alert('操作失败: ' + error.message);
            }
        }

        // 查询日志
        async function queryLogs() {
            const query = {
                serviceId: document.getElementById('logServiceId').value || null,
                logType: parseInt(document.getElementById('logType').value),
                startTime: document.getElementById('startTime').value || null,
                endTime: document.getElementById('endTime').value || null,
                keyword: document.getElementById('keyword').value || null,
                pageNumber: 1,
                pageSize: parseInt(document.getElementById('pageSize').value)
            };

            try {
                const response = await fetch('/api/ServerConf/GetServiceLogs', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(query)
                });
                
                const result = await response.json();
                showResponse('logResponse', result);
            } catch (error) {
                showResponse('logResponse', { error: error.message });
            }
        }

        // 开始实时日志
        async function startRealtimeLog() {
            const serviceId = document.getElementById('logServiceId').value;
            const logType = parseInt(document.getElementById('logType').value);

            if (!serviceId) {
                alert('请选择要监控的服务');
                return;
            }

            try {
                const response = await fetch('/api/ServerConf/StartRealtimeLog', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serviceId,
                        logType,
                        startPush: true
                    })
                });
                
                const result = await response.json();
                alert(result.msg);
            } catch (error) {
                alert('启动实时日志失败: ' + error.message);
            }
        }

        // 停止实时日志
        async function stopRealtimeLog() {
            const serviceId = document.getElementById('logServiceId').value;
            const logType = parseInt(document.getElementById('logType').value);

            if (!serviceId) {
                alert('请选择要停止监控的服务');
                return;
            }

            try {
                const response = await fetch('/api/ServerConf/StopRealtimeLog', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        serviceId,
                        logType
                    })
                });
                
                const result = await response.json();
                alert(result.msg);
            } catch (error) {
                alert('停止实时日志失败: ' + error.message);
            }
        }

        // 添加日志条目
        function addLogEntry(logItem) {
            const logContainer = document.getElementById('realtimeLog');
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + getLogClass(logItem.level);
            logEntry.textContent = `[${logItem.timestamp}] [${logItem.serviceName}] ${logItem.content}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // 限制日志条目数量
            while (logContainer.children.length > 1000) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('realtimeLog').innerHTML = '';
        }

        // 辅助函数
        function getStatusClass(status) {
            const classes = {
                0: 'status-error',    // Unknown
                1: 'status-stopped',  // Stopped
                2: 'status-starting', // Starting
                3: 'status-running',  // Running
                4: 'status-stopping', // Stopping
                5: 'status-error'     // Error
            };
            return classes[status] || 'status-error';
        }

        function getStatusText(status) {
            const texts = {
                0: '未知',
                1: '已停止',
                2: '正在启动',
                3: '正在运行',
                4: '正在停止',
                5: '错误'
            };
            return texts[status] || '未知';
        }

        function getLogClass(level) {
            const classes = {
                'INFO': 'log-info',
                'WARN': 'log-warn',
                'ERROR': 'log-error',
                'DEBUG': 'log-debug'
            };
            return classes[level] || 'log-info';
        }

        function showResponse(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = JSON.stringify(data, null, 2);
        }

        function clearForm() {
            document.getElementById('serviceName').value = '';
            document.getElementById('servicePath').value = '';
            document.getElementById('workingDirectory').value = '';
            document.getElementById('arguments').value = '';
            document.getElementById('description').value = '';
            document.getElementById('isEnabled').checked = true;
            document.getElementById('autoStart').checked = false;
        }

        function updateServiceSelect() {
            const select = document.getElementById('logServiceId');
            select.innerHTML = '<option value="">所有服务</option>';
            
            services.forEach(service => {
                const option = document.createElement('option');
                option.value = service.serviceId;
                option.textContent = service.serviceName;
                select.appendChild(option);
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            initSignalR();
            loadServiceConfigs();
            loadServiceStatuses();
            
            // 设置默认时间范围（最近24小时）
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            
            document.getElementById('startTime').value = yesterday.toISOString().slice(0, 16);
            document.getElementById('endTime').value = now.toISOString().slice(0, 16);
        });
    </script>
</body>
</html>
