﻿namespace WTCMSLive.WebSite.Core.Models
{
    public class MeasDefinitionDTO
    {
        public string MeasDefinitionID { get; set; }
        public string MeasDefinitionName { get; set; }
        public string WindTurbineID { get; set; }
        public string WindParkID { get; set; }
        public bool IsAvailable { get; set; }
        public string DauID { get; set; }
        public int DaqInterval { get; set; }
        public int ModelType { get; set; }

        public string? ModbusDeviceIDs { get; set; }

        public int DaqIntervalUnit { get; set; }
    }
}
