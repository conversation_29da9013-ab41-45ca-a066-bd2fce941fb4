﻿using AppFramework.IDUtility;
using System.ComponentModel;
using System.Reflection;
using System.Text.RegularExpressions;

namespace WTCMSLive.WebSite
{
    public class CommonUtility
    {  /// <summary>  
       /// 判断字符串是否包含中文字符  
       /// </summary>  
       /// <param name="input">待检测的字符串</param>  
       /// <returns>如果字符串包含中文字符，则返回true；否则返回false</returns>  
        public static bool ContainsChinese(string input)
        {
            // 使用正则表达式匹配中文字符  
            // [\u4e00-\u9fa5] 范围涵盖了绝大多数中文字符  
            // \u4e00 是“一”的Unicode编码，\u9fa5 是“龥”的Unicode编码  
            // 这个范围涵盖了绝大多数常用汉字，包括扩展A区的汉字  
            string pattern = @"[\u4e00-\u9fa5]";

            // 使用Regex.IsMatch方法检查字符串中是否有匹配正则表达式的字符  
            return Regex.IsMatch(input, pattern);
        }


        public static string GetVibMeasLocID(string turbineID, string compotent, string section, string origentation)
        {
            string text = turbineID;
            Dictionary<string, string> dictionary = new Dictionary<string, string>();
            dictionary = CodeProvide.GetComponentDic();
            text = ((!dictionary.ContainsKey(compotent)) ? (text + TinyPinyin.PinyinHelper.GetPinyinInitials(compotent)) : (text + dictionary[compotent]));
            dictionary = CodeProvide.GetSectionDic(compotent);
            text = ((!dictionary.ContainsKey(section)) ? (text + TinyPinyin.PinyinHelper.GetPinyinInitials(section)) : (text + dictionary[section]));
            dictionary = CodeProvide.GetOrientationDic();
            if (dictionary.ContainsKey(origentation))
            {
                return text + dictionary[origentation];
            }

            return text + TinyPinyin.PinyinHelper.GetPinyinInitials(origentation);
        }

        public static long GetCurrentTimestampSecondsUtc()
        {
            // 获取当前的UTC时间  
            var now = DateTimeOffset.UtcNow;
            // Unix纪元时间（1970年1月1日UTC）  
            var epoch = new DateTimeOffset(1970, 1, 1, 0, 0, 0, TimeSpan.Zero);
            // 计算时间差并转换为秒  
            return (long)(now - epoch).TotalSeconds;
        }


        public static string GetDecscription(Enum en)
        {
            Type type = en.GetType();
            FieldInfo field = en.GetType().GetField(en.ToString());
            DescriptionAttribute[] array = (DescriptionAttribute[])field.GetCustomAttributes(typeof(DescriptionAttribute), inherit: false);
            if (array != null && array.Length != 0)
            {
                return array[0].Description;
            }

            return "未定义";
        }

    }
}
