﻿using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 数据库交互层 DAU, 系统组态版本
    /// </summary>
    public static class DauManagement
    {
        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// 添加 DAU
        /// </summary>
        /// <param name="_DAU"></param>
        public static void AddDAU(WindDAU _DAU)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUnits.Add(_DAU);
                ctx.SaveChanges();
                ctx.DAUVibChannels.AddRange(_DAU.DAUChannelList);
                if (_DAU.RotSpeedChannelList.Count != 0)
                {
                    ctx.DAURotSpdChannels.AddRange(_DAU.RotSpeedChannelList);
                }
                ctx.DAUProcessChannels.AddRange(_DAU.ProcessChannelList);
                ctx.DauChannelVoltageCurrents.AddRange(_DAU.VoltageCurrentList);
                ctx.SaveChanges();
            }
        }
 
        /// <summary>
        /// 修改 DAU
        /// </summary>
        /// <param name="_DAU"></param>
        /// <returns></returns>
        public static bool EditDAUInfo(WindDAU _DAU)
        {
            int count=0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                _DAU.MeasDefVersion += 1;
                ctx.DAUnits.Attach(_DAU);
                ctx.Entry(_DAU).State = EntityState.Modified;
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        /// <summary>
        /// 配置DAUIP
        /// </summary>
        /// <param name="_DAUID"></param>
        /// <param name="_ip"></param>
        /// <returns></returns>
        public static bool ConfigDAUIP(string _DAUID, string _ip)
        {
            // 根据DAU ID获取DAU实体
            int count = 0;
            WindDAU DAU = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                DAU = ctx.DAUnits.Find(_DAUID);
                DAU.IP = _ip;
                ctx.Entry(DAU).State = EntityState.Modified;
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        public static List<WindDAU> GetDAUListByWindTurbineID(string turbineID)
        {
            List<WindDAU> daus = new List<WindDAU>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                daus = ctx.DAUnits.Where(item => item.WindTurbineID == turbineID).ToList();
            }
            return daus;
        }

        /// <summary>
        /// 修改DAU状态
        /// </summary>
        /// <param name="_DAUID"></param>
        /// <param name="_status"></param>
        public static bool UpdateDAUStatus(string _DAUID, bool _status)
        {
            int count = 0;
            WindDAU DAU = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                DAU = ctx.DAUnits.Find(_DAUID);
                DAU.IsAvailable = _status;
                DAU.MeasDefVersion += 1;
                ctx.Entry(DAU).State = EntityState.Modified;
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// 删除 DAU
        /// </summary>
        /// <param name="_DAUID"></param>
        public static bool DeleteDAU(string _DAUID)
        {
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUnits.Remove(ctx.DAUnits.Find(_DAUID));
                count = ctx.SaveChanges();
            }
            return count > 0;
        }

        //-----------------------------------------------------------------------------------------------------------------------
        /// <summary>
        /// 获取DAU实体
        /// </summary>
        /// <param name="_DAUID"></param>
        /// <returns></returns>
        public static WindDAU GetDAUById(string windTurbineID)
        {
            WindDAU dau = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dau = ctx.DAUnits.FirstOrDefault(obj=>obj.WindTurbineID == windTurbineID &&( obj.DauID == "1"|| obj.DauID == "2"|| obj.DauID == "3"));
                if (dau == null)
                    return dau;  
                dau.RotSpeedChannelList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == windTurbineID).ToList();
                dau.DAUChannelList = ctx.DAUVibChannels.Where(item => item.WindTurbineID == windTurbineID).ToList();
                dau.ProcessChannelList = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == windTurbineID).ToList();
                dau.VoltageCurrentList = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == windTurbineID).ToList();
            }
            return dau;
        }


        public static List<WindDAU> GetDAUListByTurbineID(string windTurbineID)
        {
            List<WindDAU> dauList = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauList = ctx.DAUnits.Where(obj => obj.WindTurbineID == windTurbineID).ToList();
                foreach(var dau in dauList)
                {
                    dau.RotSpeedChannelList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == windTurbineID).ToList();
                    dau.DAUChannelList = ctx.DAUVibChannels.Where(item => item.WindTurbineID == windTurbineID).ToList();
                    dau.ProcessChannelList = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == windTurbineID).ToList();
                    dau.VoltageCurrentList = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == windTurbineID).ToList();
                }
            }
            return dauList;
        }
        public static WindDAU GetDAUById(string windTurbineID, string measDefID)
        {
            WindDAU dau = null;
            // 查询到对应的DAUID
            string _DAUID = "1";
            using (CMSFramework.EF.MeasDef.MDFContext meas = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
              var entity =  meas.MeasDefinitions_Exs.FirstOrDefault(obj => obj.WindTurbineID == windTurbineID && obj.MeasDefinitionID == measDefID);
                if (entity != null)
                {
                    _DAUID = entity.DauID;
                }
            }
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dau = ctx.DAUnits.FirstOrDefault(obj => obj.WindTurbineID == windTurbineID && obj.DauID == _DAUID);
                if (dau == null)
                    return dau;
                dau.RotSpeedChannelList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == windTurbineID && item.DauID == _DAUID).ToList();
                dau.DAUChannelList = ctx.DAUVibChannels.Where(item => item.WindTurbineID == windTurbineID && item.DauID == _DAUID).ToList();
                dau.ProcessChannelList = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == windTurbineID && item.DauID == _DAUID).ToList();
            }
            return dau;
        }
        /// <summary>
        /// 根据机组ID和DauID获取DAUname
        /// </summary>
        /// <param name="_windParkID"></param>
        /// <param name="_windParkID"></param>
        /// <returns></returns>
        public static WindDAU GetDAUNameById(string windTurbineID,string _DAUID)
        {
            WindDAU dau = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dau = ctx.DAUnits.FirstOrDefault(obj => obj.WindTurbineID == windTurbineID && obj.DauID == _DAUID);
                if (dau == null)
                    return dau;

            }
            return dau;
        }

        /// <summary>
        /// 根据风电场ID获取DAU列表
        /// </summary>
        /// <param name="_windParkID"></param>
        /// <returns></returns>
        public static List<WindDAU> GetDAUListByWindParkID(string _windParkID)
        {
            List<WindDAU> daunitList = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                daunitList = ctx.DAUnits.Where(item => item.WindParkID == _windParkID).OrderBy(item=>item.DAUName).ToList();
            }
            return daunitList;
        }

        /// <summary>
        /// 获取DAU振动通道列表
        /// </summary>
        /// <param name="_dauId"></param>
        /// <returns></returns>
        public static List<DAUChannelV2> GetDAUVibChannelList(string _dauId)
        {
            List<DAUChannelV2> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DAUVibChannels.Where(item => item.WindTurbineID == _dauId).ToList();
            }
            return dauChannel;
        }

        public static List<DAUChannelV2> GetDAUVibChannelListByParkID(string _parkID)
        {
            List<DAUChannelV2> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DAUVibChannels.Where(item => item.WindTurbineID.Contains(_parkID)).ToList();
            }
            return dauChannel;
        }

        /// <summary>
        /// 获取dau电流电压通道
        /// </summary>
        /// <param name="_dauId"></param>
        /// <returns></returns>
        public static List<DAUChannel_VoltageCurrent> GetDAUVoltageCurrentChannelList(string _dauId)
        {
            List<DAUChannel_VoltageCurrent> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DauChannelVoltageCurrents.Where(item => item.WindTurbineID == _dauId).ToList();
            }
            return dauChannel;
        }

        public static List<DAUChannelV2> GetDAUVibChannelList(string turbineID, string dauID)
        {
            List<DAUChannelV2> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DAUVibChannels.Where(item => item.WindTurbineID == turbineID && item.DauID == dauID).ToList();
            }
            return dauChannel;
        }

        /// <summary>
        /// 获取DAU过程量（电压电流）通道列表
        /// </summary>
        /// <param name="_DAUId"></param>
        /// <returns></returns>
        public static List<DAUChannel_Process> GetDAUChannelProcessListByDAUId(string _dauId)
        {
            List<DAUChannel_Process> dauChannel = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                dauChannel = ctx.DAUProcessChannels.Where(item => item.WindTurbineID == _dauId).ToList();
            }
            return dauChannel;
        }

        /// <summary>
        /// 采集单元是否正在被使用
        /// </summary>
        /// <param name="_DAUID"></param>
        /// <returns></returns>
        public static bool IsUsedDAU(string turbineID)
        {
            return GetDAUById(turbineID) != null;
        }
        
        /// <summary>
        /// 更新测量定义版本
        /// </summary>
        /// <param name="_dauID"></param> 
        /// <returns></returns>
        public static bool UpdateMeasDefVersion(string turbineID)
        {
            int count = 0;

            // Modify by zhanggw 测量定义变化时，升级所有的测量定义版本。
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                WindDAU DAU = ctx.DAUnits.FirstOrDefault(obj => obj.WindTurbineID == turbineID);
                List<WindDAU> daus = ctx.DAUnits.Where(obj => obj.WindTurbineID == turbineID).ToList();
                if (daus.Count == 0) {
                    return true;
                }
                foreach (var item in daus)
                {
                    item.MeasDefVersion += 1;
                }
                ctx.Entry(DAU).State = EntityState.Modified;
                count = ctx.SaveChanges();
            }
            return count > 0;
        }
        public static bool UpdateMeasDefVersion(string turbineID,string dauid)
        {
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                WindDAU DAU = ctx.DAUnits.FirstOrDefault(obj=>obj.WindTurbineID == turbineID && obj.DauID==dauid);
                if (DAU != null)
                {
                    DAU.MeasDefVersion += 1;
                    ctx.Entry(DAU).State = EntityState.Modified;
                    count = ctx.SaveChanges();
                }
            }
            return count > 0;
        }

        /// <summary>
        /// 删除振动通道（dauId 和 通道编号）
        /// </summary>
        /// <param name="_DAIID"></param>
        /// <param name="_channelNum"></param>
        public static void DeleteVoltageCurrentChannelByDAUIDAndChannelNum(string _turbineID, string _DAUID, int _channelNum)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                //ctx.DAUVibChannels.Remove(ctx.DAUVibChannels.Find(_turbineID, _DAUID, _channelNum));
                var channel = ctx.DauChannelVoltageCurrents.FirstOrDefault( item =>item.WindTurbineID == _turbineID && item.DauID == _DAUID && item.ChannelNumber==  _channelNum);
                if(channel!= null)
                {
                    ctx.DauChannelVoltageCurrents.Remove(channel);
                }
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                //ctx.AlarmStatusRTSensors.RemoveRange(ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _DAUID && item.ChannelNumber == _channelNum));
                ctx.AlarmStatusRTSensors.RemoveRange(ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _turbineID && item.DauID == _DAUID && item.ChannelNumber == _channelNum));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 删除电流电压通道
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <param name="_DAUID"></param>
        /// <param name="_channelNum"></param>
        public static void DeleteChannelByDAUIDAndChannelNum(string _turbineID, string _DAUID, int _channelNum)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                //ctx.DAUVibChannels.Remove(ctx.DAUVibChannels.Find(_turbineID, _DAUID, _channelNum));
                var channel = ctx.DAUVibChannels.FirstOrDefault(item => item.WindTurbineID == _turbineID && item.DauID == _DAUID && item.ChannelNumber == _channelNum);
                if (channel != null)
                {
                    ctx.DAUVibChannels.Remove(channel);
                }
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                //ctx.AlarmStatusRTSensors.RemoveRange(ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _DAUID && item.ChannelNumber == _channelNum));
                ctx.AlarmStatusRTSensors.RemoveRange(ctx.AlarmStatusRTSensors.Where(item => item.WindTurbineID == _turbineID && item.DauID == _DAUID && item.ChannelNumber == _channelNum));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 删除电压电流通道
        /// </summary>
        /// <param name="_dauId"></param>
        /// <param name="_channelNum"></param>
        public static void DeleteProcessChan( string turbineID,string _DAUID, int _channelNum)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUProcessChannels.Remove(ctx.DAUProcessChannels.Find(turbineID,_DAUID, _channelNum));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 删除转速通道
        /// </summary>
        /// <param name="_chan"></param>
        public static void DeleteDAURotSpeedChannel(DAUChannel_RotSpeed _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAURotSpdChannels.Attach(_chan);
                ctx.Entry(_chan).State = EntityState.Deleted;
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                var data = ctx.AlarmStatusRTRSSensors.Find(_chan.WindTurbineID,_chan.DauID, _chan.ChannelNumber);
                if (data != null)
                {
                    ctx.AlarmStatusRTRSSensors.Remove(data);
                    ctx.SaveChanges();
                }
            }
        }

        /// <summary>
        /// 添加转速通道
        /// </summary>
        /// <param name="_chan"></param>
        public static void AddDAURotSpeedChannel(DAUChannel_RotSpeed _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAURotSpdChannels.Add(_chan);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 通过DAU id 获取转速通道实体
        /// </summary>
        /// <param name="_dauId"></param>
        /// <returns></returns>
        public static List<DAUChannel_RotSpeed> GetRotSpeedChannelListByDauId(string _dauId)
        {
            List<DAUChannel_RotSpeed> rotSpdList = new List<DAUChannel_RotSpeed>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                rotSpdList = ctx.DAURotSpdChannels.Where(item => item.WindTurbineID == _dauId).ToList();
            }
            return rotSpdList;
        }
    
        /// <summary>
        /// 根据机组id取得DAU运行日志
        /// </summary>
        /// <param name="_turbineId">机组ID</param>
        /// <param name="count">显示取得数目</param>
        /// <returns></returns>
        public static List<DAURunLog> GetDAULog(string _turbineId, int count)
        {
            List<DAURunLog> logList = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                logList = ctx.DAURunLogs.Where(item => item.WindTurbineID == _turbineId).ToList();
                if (logList.Count > count)
                {
                    logList.RemoveAt(count - 1);
                }
            }
            return logList;
        }

        /// <summary>
        /// 根据查询时间取得DAU运行日志
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        public static List<DAURunLog> GetDAULogByTime(string _turbineId, DateTime beginTime, DateTime endTime)
        {
            List<DAURunLog> logList = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                logList = ctx.DAURunLogs.Where(item => item.WindTurbineID == _turbineId && item.EventTime >= beginTime && item.EventTime <= endTime).ToList();
            }
            return logList;
        }
        /// <summary>
        /// 根据dauID取得DAU运行日志
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="dauID"></param>
        /// <returns></returns>
        public static List<DAURunLog> GetDAULogByTime(string _turbineId, string dauID, DateTime beginTime, DateTime endTime)
        {
            List<DAURunLog> logList = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                logList = ctx.DAURunLogs.Where(item => item.WindTurbineID == _turbineId && item.DauId== dauID && item.EventTime >= beginTime && item.EventTime <= endTime).ToList();
            }
            return logList;
        }


        /// <summary>
        /// 添加DAU运行日志
        /// </summary>
        /// <param name="log"></param>
        public static void AddDAULog(DAURunLog log)
        {
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                ctx.DAURunLogs.Add(log);
                ctx.SaveChanges();
            }
        }


        /// <summary>
        /// 增加振动通道
        /// </summary>
        /// <param name="_chan"></param>
        public static void AddDAUVibChannel(DAUChannelV2 _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUVibChannels.Add(_chan);
                ctx.SaveChanges();
            }
        }

        public static void AddDAUVibChannel(List<DAUChannelV2> _chans)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUVibChannels.AddRange(_chans);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 过程量通道添加
        /// </summary>
        /// <param name="_chan"></param>
        public static void AddDAUVoltageCurrentChannel(DAUChannel_VoltageCurrent _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DauChannelVoltageCurrents.Add(_chan);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 修改振动通道
        /// </summary>
        /// <param name="_chan"></param>
        public static void EditDAUVibChannel(DAUChannelV2 _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUVibChannels.Attach(_chan);
                ctx.Entry(_chan).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 过程量通道修改
        /// </summary>
        /// <param name="_chan"></param>
        public static void EditDAUVoltageCurrentChannel(DAUChannel_VoltageCurrent _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DauChannelVoltageCurrents.Attach(_chan);
                ctx.Entry(_chan).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 增加工况通道
        /// </summary>
        /// <param name="_chan"></param>
        public static void addDAUProcessChannel(DAUChannel_Process _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUProcessChannels.Add(_chan);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 修改工况通道
        /// </summary>
        /// <param name="_chan"></param>
        public static void editDAUProcessChannel(DAUChannel_Process _chan)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.DAUProcessChannels.Attach(_chan);
                ctx.Entry(_chan).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 获取DAU试试状态
        /// </summary>
        /// <param name="_turid"></param>
        /// <returns></returns>
        public static RTAlarmStatus_DAU GetDAUAlarmStatus(string _turid)
        {
            RTAlarmStatus_DAU daustate = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                daustate = ctx.AlarmStatusRTDAUs.Find(_turid);
            }
            return daustate;
        }

        #region 转速传感器直流分量

        public static List<RTAlarmStatus_RSChannel> GetAlarmStatusRTRSSensorList(string _turid)
        {
            List<RTAlarmStatus_RSChannel> ListAlarmRsChannel = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                ListAlarmRsChannel = ctx.AlarmStatusRTRSSensors.Where(item => item.WindTurbineID == _turid).ToList();
            }
            return ListAlarmRsChannel;
        }

        public static RTAlarmStatus_RSChannel GetAlarmStatusRTRSSensor(string _turid,int channelNum)
        {
            RTAlarmStatus_RSChannel AlarmRsChannel = null;
            using (CMSFramework.EF.DauMonContext ctx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                AlarmRsChannel = ctx.AlarmStatusRTRSSensors.Find(_turid, channelNum);
            }
            return AlarmRsChannel;
        }

        #endregion




        #region 超声波配置

        public static void AddUltrasonic(UltrasonicChannelConfig ultrasonic)
        {
            if(ultrasonic != null)
            {
                try
                {
                    using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                    {
                        ctx.UltrasonicChannelConfigs.Add(ultrasonic);
                        ctx.SaveChanges();
                    }
                }catch(Exception ex)
                {

                }
            }
        }


        public static void AddUltrasonic(List<UltrasonicChannelConfig> ultrasonics)
        {
            if (ultrasonics != null && ultrasonics.Count>0)
            {
                try
                {
                    using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                    {
                        ctx.UltrasonicChannelConfigs.AddRange(ultrasonics);
                        ctx.SaveChanges();
                    }
                }
                catch (Exception ex)
                {

                }
            }
        }


        public static void EditUltrasonic(UltrasonicChannelConfig ultrasonic)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.UltrasonicChannelConfigs.Attach(ultrasonic);
                
                ctx.Entry(ultrasonic).State = EntityState.Modified;
                ctx.SaveChanges();
            }
        }


        public static List<UltrasonicChannelConfig> GetUltrasonicChannelConfigByTurId(string _windTurId,string dauid)
        {
            List<UltrasonicChannelConfig> list = new List<UltrasonicChannelConfig>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                list = ctx.UltrasonicChannelConfigs.Where(item => item.WindTurbineID == _windTurId && item.DauID == dauid).ToList();
            }
            return list;
        }

        public static List<UltrasonicChannelConfig> GetUltrasonicChannelConfigByTurId(string _windTurId)
        {
            List<UltrasonicChannelConfig> list = new List<UltrasonicChannelConfig>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                list = ctx.UltrasonicChannelConfigs.Where(item => item.WindTurbineID == _windTurId).ToList();
            }
            return list;
        }

        public static List<UltrasonicChannelConfig> GetUltrasonicChannelConfigByParkID(string _parkID)
        {
            List<UltrasonicChannelConfig> list = new List<UltrasonicChannelConfig>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                list = ctx.UltrasonicChannelConfigs.Where(item => item.WindTurbineID.Contains(_parkID)).ToList();
            }
            return list;
        }

        public static void DeleteUltrasonicChannel(string turbineID,string dauID,int channelNum)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    var ultra = ctx.UltrasonicChannelConfigs.FirstOrDefault(item => item.WindTurbineID == turbineID && item.DauID == dauID && item.ChannelNumber == channelNum);
                    if(ultra != null)
                    {
                        ctx.UltrasonicChannelConfigs.Remove(ultra);
                        ctx.SaveChanges();
                    }
                }
            }catch(Exception ex) { }
        }

    
        public static void DeleteUltrasonicChannel(string turbineID, string dauID)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    List<UltrasonicChannelConfig> deleteUltrasonicList = new List<UltrasonicChannelConfig>();
                    deleteUltrasonicList = ctx.UltrasonicChannelConfigs.Where(item => item.WindTurbineID == turbineID && item.DauID == dauID).ToList();
                    //var ultra = ctx.UltrasonicChannelConfigs.FirstOrDefault(item => item.WindTurbineID == turbineID && item.DauID == dauID);

                    if (deleteUltrasonicList.Any())
                    {
                        ctx.UltrasonicChannelConfigs.RemoveRange(deleteUltrasonicList);
                        ctx.SaveChanges();
                    }

                    //if (ultra != null)
                    //{
                    //    ctx.UltrasonicChannelConfigs.RemoveRange(deleteUltrasonicList);
                    //    ctx.SaveChanges();
                    //}
                }
            }
            catch (Exception ex) {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteDAU]删除UltrasonicChannelConfigs失败", ex);
            }
        }
        public static void DeleteUltrasonicChannel(string turbineID)
        {
            try
            {
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    var ultra = ctx.UltrasonicChannelConfigs.FirstOrDefault(item => item.WindTurbineID == turbineID);
                    if (ultra != null)
                    {
                        ctx.UltrasonicChannelConfigs.Remove(ultra);
                        ctx.SaveChanges();
                    }
                }
            }
            catch (Exception ex) { }
        }

        /// <summary>
        /// 批量删除振动通道（原子性操作）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="dauID">采集单元ID</param>
        /// <param name="channelNumbers">通道编号列表</param>
        public static void BatchDeleteVibChannels(string turbineID, string dauID, List<int> channelNumbers)
        {
            if (channelNumbers == null || channelNumbers.Count == 0)
                return;

            // 使用事务确保原子性
            using (CMSFramework.EF.DauContext dauCtx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            using (CMSFramework.EF.DauMonContext monCtx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                using (var dauTran = dauCtx.Database.BeginTransaction())
                using (var monTran = monCtx.Database.BeginTransaction())
                {
                    try
                    {
                        // 删除振动通道
                        var channels = dauCtx.DAUVibChannels.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (channels.Any())
                        {
                            dauCtx.DAUVibChannels.RemoveRange(channels);
                        }

                        // 删除超声波配置
                        var ultrasonics = dauCtx.UltrasonicChannelConfigs.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (ultrasonics.Any())
                        {
                            dauCtx.UltrasonicChannelConfigs.RemoveRange(ultrasonics);
                        }

                        // 删除实时报警传感器状态
                        var rtSensors = monCtx.AlarmStatusRTSensors.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (rtSensors.Any())
                        {
                            monCtx.AlarmStatusRTSensors.RemoveRange(rtSensors);
                        }

                        // 提交所有更改
                        dauCtx.SaveChanges();
                        monCtx.SaveChanges();

                        dauTran.Commit();
                        monTran.Commit();
                    }
                    catch
                    {
                        dauTran.Rollback();
                        monTran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量删除电流电压通道（原子性操作）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="dauID">采集单元ID</param>
        /// <param name="channelNumbers">通道编号列表</param>
        public static void BatchDeleteVoltageCurrentChannels(string turbineID, string dauID, List<int> channelNumbers)
        {
            if (channelNumbers == null || channelNumbers.Count == 0)
                return;

            // 使用事务确保原子性
            using (CMSFramework.EF.DauContext dauCtx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            using (CMSFramework.EF.DauMonContext monCtx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                using (var dauTran = dauCtx.Database.BeginTransaction())
                using (var monTran = monCtx.Database.BeginTransaction())
                {
                    try
                    {
                        // 删除电流电压通道
                        var channels = dauCtx.DauChannelVoltageCurrents.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (channels.Any())
                        {
                            dauCtx.DauChannelVoltageCurrents.RemoveRange(channels);
                        }

                        // 删除实时报警传感器状态
                        var rtSensors = monCtx.AlarmStatusRTSensors.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (rtSensors.Any())
                        {
                            monCtx.AlarmStatusRTSensors.RemoveRange(rtSensors);
                        }

                        // 提交所有更改
                        dauCtx.SaveChanges();
                        monCtx.SaveChanges();

                        dauTran.Commit();
                        monTran.Commit();
                    }
                    catch
                    {
                        dauTran.Rollback();
                        monTran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量添加振动通道（原子性操作）
        /// </summary>
        /// <param name="channels">振动通道列表</param>
        /// <param name="ultrasonics">超声波通道配置列表</param>
        public static void BatchAddVibChannels(List<DAUChannelV2> channels, List<UltrasonicChannelConfig> ultrasonics = null)
        {
            if (channels == null || channels.Count == 0)
                return;

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        // 添加振动通道
                        ctx.DAUVibChannels.AddRange(channels);

                        // 添加超声波配置（如果有）
                        if (ultrasonics != null && ultrasonics.Count > 0)
                        {
                            ctx.UltrasonicChannelConfigs.AddRange(ultrasonics);
                        }

                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量添加电流电压通道（原子性操作）
        /// </summary>
        /// <param name="channels">通道列表</param>
        public static void BatchAddVoltageCurrentChannels(List<DAUChannel_VoltageCurrent> channels)
        {
            if (channels == null || channels.Count == 0)
                return;

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        ctx.DauChannelVoltageCurrents.AddRange(channels);
                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量删除工况通道（原子性操作）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="dauID">采集单元ID</param>
        /// <param name="channelNumbers">通道编号列表</param>
        public static void BatchDeleteWorkConditionChannels(string turbineID, string dauID, List<int> channelNumbers)
        {
            if (channelNumbers == null || channelNumbers.Count == 0)
                return;

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        // 删除工况通道
                        var channels = ctx.DAUProcessChannels.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (channels.Any())
                        {
                            ctx.DAUProcessChannels.RemoveRange(channels);
                        }

                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量添加工况通道（原子性操作）
        /// </summary>
        /// <param name="channels">工况通道列表</param>
        public static void BatchAddWorkConditionChannels(List<DAUChannel_Process> channels)
        {
            if (channels == null || channels.Count == 0)
                return;

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        ctx.DAUProcessChannels.AddRange(channels);
                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量删除转速通道（原子性操作）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="dauID">采集单元ID</param>
        /// <param name="channelNumbers">通道编号列表</param>
        public static void BatchDeleteRotSpeedChannels(string turbineID, string dauID, List<int> channelNumbers)
        {
            if (channelNumbers == null || channelNumbers.Count == 0)
                return;

            // 使用事务确保原子性
            using (CMSFramework.EF.DauContext dauCtx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            using (CMSFramework.EF.DauMonContext monCtx = new CMSFramework.EF.DauMonContext(ConfigInfo.DBConnName))
            {
                using (var dauTran = dauCtx.Database.BeginTransaction())
                using (var monTran = monCtx.Database.BeginTransaction())
                {
                    try
                    {
                        // 删除转速通道
                        var channels = dauCtx.DAURotSpdChannels.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (channels.Any())
                        {
                            dauCtx.DAURotSpdChannels.RemoveRange(channels);
                        }

                        // 删除实时报警转速传感器状态
                        var rtSensors = monCtx.AlarmStatusRTRSSensors.Where(item =>
                            item.WindTurbineID == turbineID &&
                            item.DauID == dauID &&
                            channelNumbers.Contains(item.ChannelNumber)).ToList();

                        if (rtSensors.Any())
                        {
                            monCtx.AlarmStatusRTRSSensors.RemoveRange(rtSensors);
                        }

                        // 提交所有更改
                        dauCtx.SaveChanges();
                        monCtx.SaveChanges();

                        dauTran.Commit();
                        monTran.Commit();
                    }
                    catch
                    {
                        dauTran.Rollback();
                        monTran.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 批量添加转速通道（原子性操作）
        /// </summary>
        /// <param name="channels">转速通道列表</param>
        public static void BatchAddRotSpeedChannels(List<DAUChannel_RotSpeed> channels)
        {
            if (channels == null || channels.Count == 0)
                return;

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        ctx.DAURotSpdChannels.AddRange(channels);
                        ctx.SaveChanges();
                        tran.Commit();
                    }
                    catch
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }


        public static List<UltrasonicBoltParam> GetUltrasonicBoltParamByParkId(string _windParkID)
        {
            List<UltrasonicBoltParam> list = new List<UltrasonicBoltParam>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                list = ctx.UltrasonicBoltParams.Where(item => item.WindParkID == _windParkID).ToList();
            }
            return list;
        }

        public static ModbusUnit GetModbusDevicebyModbusID(string turbineID, string modbusID)
        {
            ModbusUnit modbusUnits = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                // 获取Modbus设备列表
                modbusUnits = ctx.ModbusUnits.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ModbusUnitID == modbusID);
            }
            return modbusUnits;
        }

        public static ModbusUnit GetModbusDevicebyModbusDeviceID(string turbineID, int devid)
        {
            ModbusUnit modbusUnits = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                // 获取Modbus设备列表
                modbusUnits = ctx.ModbusUnits.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ModbusDeviceID == devid);
            }
            return modbusUnits;
        }

        #endregion

    }
}
