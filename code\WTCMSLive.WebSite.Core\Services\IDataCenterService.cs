namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 数据中心服务接口
    /// </summary>
    public interface IDataCenterService
    {
        /// <summary>
        /// 获取集团公司代码数据
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒），null使用默认配置</param>
        /// <returns>字典，key为集团名称，value为代码</returns>
        Task<Dictionary<string, string>> GetGroupCompanyCodeAsync(int? timeoutSeconds = null);

        /// <summary>
        /// 通用GET请求方法
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <param name="parameters">查询参数</param>
        /// <param name="timeoutSeconds">超时时间（秒），null使用默认配置</param>
        /// <returns>JSON字符串</returns>
        Task<string> GetAsync(string endpoint, Dictionary<string, string>? parameters = null, int? timeoutSeconds = null);

        /// <summary>
        /// 通用POST请求方法
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <param name="jsonData">JSON数据</param>
        /// <param name="timeoutSeconds">超时时间（秒），null使用默认配置</param>
        /// <returns>JSON字符串</returns>
        Task<string> PostAsync(string endpoint, string? jsonData = null, int? timeoutSeconds = null);
    }
}
