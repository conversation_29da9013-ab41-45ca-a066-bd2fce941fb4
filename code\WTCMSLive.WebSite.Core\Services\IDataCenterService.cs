namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 数据中心服务接口
    /// </summary>
    public interface IDataCenterService
    {
        /// <summary>
        /// 获取集团公司代码数据
        /// </summary>
        /// <returns>JSON字符串</returns>
        Task<string> GetGroupCompanyCodeAsync();

        /// <summary>
        /// 通用GET请求方法
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <param name="parameters">查询参数</param>
        /// <returns>JSON字符串</returns>
        Task<string> GetAsync(string endpoint, Dictionary<string, string>? parameters = null);

        /// <summary>
        /// 通用POST请求方法
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <param name="jsonData">JSON数据</param>
        /// <returns>JSON字符串</returns>
        Task<string> PostAsync(string endpoint, string? jsonData = null);
    }
}
