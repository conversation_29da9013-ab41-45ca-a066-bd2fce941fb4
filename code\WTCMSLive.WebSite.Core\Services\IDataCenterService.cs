using WTCMSLive.WebSite.Core.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 数据中心服务接口
    /// </summary>
    public interface IDataCenterService
    {
        /// <summary>
        /// 获取集团公司代码列表
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>集团公司代码列表</returns>
        Task<ApiResponse<List<GroupCompanyCodeDTO>>> GetGroupCompanyCodesAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据代码获取集团公司信息
        /// </summary>
        /// <param name="code">公司代码</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>集团公司信息</returns>
        Task<ApiResponse<GroupCompanyCodeDTO>> GetGroupCompanyByCodeAsync(string code, CancellationToken cancellationToken = default);

        /// <summary>
        /// 通用GET请求方法
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="parameters">查询参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>API响应</returns>
        Task<ApiResponse<T>> GetAsync<T>(string endpoint, Dictionary<string, string>? parameters = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 通用POST请求方法
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>API响应</returns>
        Task<ApiResponse<T>> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查数据中心连接状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接状态</returns>
        Task<bool> CheckConnectionAsync(CancellationToken cancellationToken = default);
    }
}
