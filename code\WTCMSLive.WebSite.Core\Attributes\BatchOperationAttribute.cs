using Microsoft.AspNetCore.Mvc.Filters;
using WTCMSLive.WebSite.Core.Services;

namespace WTCMSLive.WebSite.Core.Attributes
{
    /// <summary>
    /// 批量操作特性，用于标记需要支持批量操作的Controller方法
    /// </summary>
    [AttributeUsage(AttributeTargets.Method)]
    public class BatchOperationAttribute : ActionFilterAttribute
    {
        /// <summary>
        /// 批量操作方法名称（在同一个Controller中）
        /// </summary>
        public string BatchMethodName { get; set; }

        /// <summary>
        /// 目标机组ID列表参数名称（默认为"targetTurbineIds"）
        /// </summary>
        public string TargetIdsParameter { get; set; } = "targetTurbineIds";

        /// <summary>
        /// 是否启用批量操作（默认为true）
        /// </summary>
        public bool EnableBatchOperation { get; set; } = true;

        /// <summary>
        /// 最大并发数（默认为5）
        /// </summary>
        public int MaxConcurrency { get; set; } = 20;

        /// <summary>
        /// 是否忽略批量操作中的错误（默认为true，继续执行其他操作）
        /// </summary>
        public bool IgnoreErrors { get; set; } = true;

        public BatchOperationAttribute(string batchMethodName)
        {
            BatchMethodName = batchMethodName;
        }

        public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            if (!EnableBatchOperation)
            {
                await next();
                return;
            }

            // 执行主操作
            var result = await next();

            // 只有在主操作成功时才执行批量操作
            if (result.Result is Microsoft.AspNetCore.Mvc.OkObjectResult okResult)
            {
                var batchService = result.HttpContext.RequestServices
                    .GetRequiredService<IBatchOperationService>();

                await batchService.ExecuteBatchAsync(result, this);
            }
        }
    }
}
