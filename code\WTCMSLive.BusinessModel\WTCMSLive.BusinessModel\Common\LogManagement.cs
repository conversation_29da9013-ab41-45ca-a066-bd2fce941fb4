﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessModel;
using CMSFramework.BusinessEntity;

namespace WTCMSLive.BusinessModel
{
    public static class LogManagement
    {
        /// <summary>
        /// 根据用户输入条件 获取系统运行日志列表
        /// </summary>
        /// <param name="_entity"></param>
        public static List<SystemRunningLog> GetSystemRunningLogList(LogQueryCondition _entity)
        {
            if (_entity == null)
            {
                return null;
            }
            List<SystemRunningLog> logList = new List<SystemRunningLog>();
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                logList = ctx.SysRunningLogs.Where(item => item.EventTime >= _entity.StartTime&&item.EventTime<=_entity.EndTime
                                                    &&item.LogType== _entity.LogDB.ToString()).ToList();
            }
            return logList;
        }

        /// <summary>
        /// 设备树操作 日志记录
        /// </summary>
        /// <param name="_log"></param>
        public static void DevTreelogWrite(LogEntity _log)
        {
           /* _log.LogDB = (Int32)EnumLogType.DevTreeManagementLog;


            sysService.AddSysRunningLog(ConvertEntityBusinessToDACommon.ConvertSysRunningLog(_log));*/
        }

        /// <summary>
        /// 采集单元操作 日志记录
        /// </summary>
        /// <param name="_log"></param>
        public static void DAUlogWrite(LogEntity _log)
        {
           /* _log.LogDB = (Int32)EnumLogType.DAUManagementLog
                ;
            sysService.AddSysRunningLog(ConvertEntityBusinessToDACommon.ConvertSysRunningLog(_log));*/
        }
        /*
                    SystemRunningLog log = new SystemRunningLog();
            log.EventTime = DateTime.Now;
            log.GUID = Guid.NewGuid().ToString();
            log.LogContent = _log.OperationDescription;
            log.Operator = _log.UserName;
            log.LogType = _log.LogDB.ToString();
        */
        /// <summary>
        /// 用户操作 日志记录
        /// </summary>
        /// <param name="_log"></param>
        public static void UserlogWrite(LogEntity _log)
        {
            try
            {
                _log.LogDB = (Int32)EnumLogType.UserManagementLog;
                SystemRunningLog log = new SystemRunningLog();
                log.EventTime = DateTime.Now;
                log.GUID = Guid.NewGuid().ToString("N");
                log.LogTitle = AppFramework.Utility.EnumHelper.GetDescription(EnumLogType.UserManagementLog);
                log.LogContent = _log.OperationDescription;
                log.Operator = _log.UserName;
                log.LogType = _log.LogDB.ToString();
                using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
                {
                    //ctx.SysRunningLogs.Attach(log);
                    ctx.SysRunningLogs.Add(log);
                    //ctx.Entry(log).State = System.Data.Entity.EntityState.Added;
                    ctx.SaveChanges();
                }
            }
            catch (Exception ex)
            { 
                //如果记录日志出异常，不记录
            }
        }

        /// <summary>
        /// 系统运行日志 写入
        /// </summary>
        /// <param name="_log"></param>
        public static void SystemRunningLogWrite(LogEntity _log)
        {
           /* _log.LogDB = (Int32)EnumLogType.SystemRunningLog;

            sysService.AddSysRunningLog(ConvertEntityBusinessToDACommon.ConvertSysRunningLog(_log));*/
        }

        /// <summary>
        /// 删除系统运行日志列表
        /// </summary>
        /// <param name="_queryCondition"></param>
        public static void DeleteSystemRunningLogList(LogQueryCondition _queryCondition)
        {
          //  DeleteLogList(_queryCondition);
        }

        /// <summary>
        /// 删除日志列表
        /// </summary>
        /// <param name="_queryCondition"></param>
        public static void DeleteLogList(LogQueryCondition _queryCondition)
        {/*
            switch (_queryCondition.LogDB)
            {
                case (int)EnumLogType.UserManagementLog:
                    _queryCondition.LogDB = (int)EnumLogType.UserManagementLog;
                    break;
                case (int)EnumLogType.DAUManagementLog:
                    _queryCondition.LogDB = (int)EnumLogType.DAUManagementLog;
                    break;
                case (int)EnumLogType.DevTreeManagementLog:
                    _queryCondition.LogDB = (int)EnumLogType.DevTreeManagementLog;
                    break;
                case (int)EnumLogType.AlarmEventLog:
                    _queryCondition.LogDB = (int)EnumLogType.AlarmEventLog;
                    break;
                case (int)EnumLogType.SystemRunningLog:
                    _queryCondition.LogDB = (int)EnumLogType.SystemRunningLog;
                    break;
            }

            sysService.DelSysRunningLog(ConvertEntityBusinessToDACommon.ConvertSysLogQueryCondition(_queryCondition));*/
        }

        #region 机组运行日志
        /// <summary>
        /// 取得机组运行日志
        /// </summary>
        /// <param name="_turID">机组ID</param>
        /// <returns></returns>
        public static List<WindTurbineRunLog> GetTurbineRunLogByTurID(string _turID)
        {
            List<WindTurbineRunLog> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.WindTurbineRunLogList.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return list;
        }

        /// <summary>
        /// 取得机组运行日志
        /// </summary>
        /// <param name="_turID">机组ID</param>
        /// <returns></returns>
        public static List<WindTurbineRunLog> GetTurbineRunLogByTurID(string _turID ,DateTime _beginTime,DateTime _endTime)
        {
            List<WindTurbineRunLog> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.WindTurbineRunLogList.Where(item => item.WindTurbineID == _turID && item.EventTime >= _beginTime && item.EventTime <= _endTime).ToList();
            }
            return list;
        }

        /// <summary>
        /// 取得机组最新运行日志
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static WindTurbineRunLog GetTurbineOneRunLogByTurID(string _turID)
        {
            WindTurbineRunLog log = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                log = ctx.WindTurbineRunLogList.Where(item => item.WindTurbineID == _turID).OrderByDescending(item=>item.EventTime).FirstOrDefault();
            }
            return log;
        }
        /// <summary>
        /// 新增机组运行日志
        /// </summary>
        /// <param name="_turRunLog"></param>
        public static void AddTurbineRunLog(WindTurbineRunLog _turRunLog)
        {
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                ctx.WindTurbineRunLogList.Add(_turRunLog);
                ctx.SaveChanges();
            }
        }
        #endregion 
    }
}
