<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能监控测试页面</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .metric-card { padding: 10px; background: #f5f5f5; border-radius: 5px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #007bff; }
        .metric-label { font-size: 14px; color: #666; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        input, select { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>性能监控系统测试</h1>
        
        <!-- 实时性能数据显示 -->
        <div class="section">
            <h2>实时性能数据 (SignalR)</h2>
            <div id="connection-status">连接状态: 未连接</div>
            <button onclick="connectSignalR()">连接SignalR</button>
            <button onclick="disconnectSignalR()">断开连接</button>
            
            <div class="metrics" id="realtime-metrics">
                <div class="metric-card">
                    <div class="metric-value" id="cpu-value">--</div>
                    <div class="metric-label">CPU使用率 (%)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="memory-value">--</div>
                    <div class="metric-label">内存使用量 (MB)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="disk-read-value">--</div>
                    <div class="metric-label">磁盘读取 (KiB/s)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="disk-write-value">--</div>
                    <div class="metric-label">磁盘写入 (KiB/s)</div>
                </div>
            </div>
        </div>

        <!-- 历史数据查询 -->
        <div class="section">
            <h2>历史数据查询和下载</h2>
            <div>
                <label>开始时间: <input type="datetime-local" id="start-time"></label>
                <label>结束时间: <input type="datetime-local" id="end-time"></label>
                <label>采样间隔(秒): <input type="number" id="sample-interval" value="60" min="1"></label>
                <button onclick="queryHistoryData()">查询历史数据</button>
                <button onclick="downloadHistoryData()">下载历史数据</button>
                <button onclick="downloadAllData()" style="background: #28a745;">下载所有数据(CSV)</button>
            </div>
            <div id="history-result" class="log"></div>
        </div>

        <!-- 存储统计信息 -->
        <div class="section">
            <h2>存储统计信息</h2>
            <button onclick="getStorageStatistics()">获取存储统计</button>
            <button onclick="cleanupExpiredData()">清理过期数据</button>
            <div id="storage-stats" class="log"></div>
        </div>

        <!-- 日志显示 -->
        <div class="section">
            <h2>操作日志</h2>
            <button onclick="clearLog()">清空日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let connection = null;
        const apiBase = '/api/ServerPerformance';

        // 初始化时间输入框
        function initTimeInputs() {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            
            document.getElementById('start-time').value = oneHourAgo.toISOString().slice(0, 16);
            document.getElementById('end-time').value = now.toISOString().slice(0, 16);
        }

        // 日志记录
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // SignalR连接
        async function connectSignalR() {
            try {
                connection = new signalR.HubConnectionBuilder()
                    .withUrl("/Hubs/ServerPerformanceHub")
                    .build();

                connection.on("ReceiveMetrics", function (data) {
                    updateRealtimeMetrics(data);
                });

                await connection.start();
                document.getElementById('connection-status').textContent = '连接状态: 已连接';
                log('SignalR连接成功');
            } catch (err) {
                log('SignalR连接失败: ' + err);
                document.getElementById('connection-status').textContent = '连接状态: 连接失败';
            }
        }

        async function disconnectSignalR() {
            if (connection) {
                await connection.stop();
                connection = null;
                document.getElementById('connection-status').textContent = '连接状态: 已断开';
                log('SignalR连接已断开');
            }
        }

        // 更新实时指标显示
        function updateRealtimeMetrics(data) {
            document.getElementById('cpu-value').textContent = data.cpu.toFixed(2);
            document.getElementById('memory-value').textContent = data.memory.toFixed(2);
            document.getElementById('disk-read-value').textContent = data.diskRead.toFixed(2);
            document.getElementById('disk-write-value').textContent = data.diskWrite.toFixed(2);
        }

        // 查询历史数据
        async function queryHistoryData() {
            try {
                const startTime = document.getElementById('start-time').value;
                const endTime = document.getElementById('end-time').value;
                const sampleInterval = document.getElementById('sample-interval').value;

                if (!startTime || !endTime) {
                    alert('请选择开始时间和结束时间');
                    return;
                }

                const requestData = {
                    startTime: new Date(startTime).toISOString(),
                    endTime: new Date(endTime).toISOString(),
                    sampleInterval: parseInt(sampleInterval)
                };

                const response = await fetch(`${apiBase}/query-history`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                document.getElementById('history-result').innerHTML = JSON.stringify(result, null, 2);
                log(`查询历史数据成功，返回 ${result.data ? result.data.length : 0} 条记录`);
            } catch (err) {
                log('查询历史数据失败: ' + err);
            }
        }

        // 下载历史数据
        async function downloadHistoryData() {
            try {
                const startTime = document.getElementById('start-time').value;
                const endTime = document.getElementById('end-time').value;
                const sampleInterval = document.getElementById('sample-interval').value;

                if (!startTime || !endTime) {
                    alert('请选择开始时间和结束时间');
                    return;
                }

                const requestData = {
                    startTime: new Date(startTime).toISOString(),
                    endTime: new Date(endTime).toISOString(),
                    sampleInterval: parseInt(sampleInterval)
                };

                const response = await fetch(`${apiBase}/download-history`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `metrics_${startTime.replace(/[:\-T]/g, '')}_${endTime.replace(/[:\-T]/g, '')}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    log('历史数据下载成功');
                } else {
                    const errorText = await response.text();
                    log('下载失败: ' + errorText);
                }
            } catch (err) {
                log('下载历史数据失败: ' + err);
            }
        }

        // 下载所有数据
        async function downloadAllData() {
            try {
                const response = await fetch(`${apiBase}/download-all`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `all_metrics_${new Date().toISOString().slice(0, 19).replace(/[:\-T]/g, '')}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    log('所有数据下载成功');
                } else {
                    const errorText = await response.text();
                    log('下载失败: ' + errorText);
                }
            } catch (err) {
                log('下载所有数据失败: ' + err);
            }
        }

        // 获取存储统计信息
        async function getStorageStatistics() {
            try {
                const response = await fetch(`${apiBase}/storage-statistics`);
                const result = await response.json();
                document.getElementById('storage-stats').innerHTML = JSON.stringify(result, null, 2);
                log('获取存储统计信息成功');
            } catch (err) {
                log('获取存储统计信息失败: ' + err);
            }
        }

        // 清理过期数据
        async function cleanupExpiredData() {
            try {
                const response = await fetch(`${apiBase}/cleanup-expired`, {
                    method: 'POST'
                });
                const result = await response.json();
                document.getElementById('storage-stats').innerHTML = JSON.stringify(result, null, 2);
                log('清理过期数据操作完成');
            } catch (err) {
                log('清理过期数据失败: ' + err);
            }
        }

        // 页面加载时初始化
        window.onload = function() {
            initTimeInputs();
            log('页面加载完成，可以开始测试');
        };

        // 页面卸载时断开连接
        window.onbeforeunload = function() {
            if (connection) {
                connection.stop();
            }
        };
    </script>
</body>
</html>
