﻿using System.Runtime.InteropServices;

namespace WTCMSLive.WebSite.Models
{
    public class WaveEnvelope
    {
        unsafe private struct emxArray_real_T
        {
            public double* data;
            public int* size;
            public int allocatedSize;
            public int numDimensions;
            public byte canFreeData;
        };
        unsafe private struct forCsharp
        {
            public emxArray_real_T* data;
        };


        [DllImport("EnvelopeSpectrum.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        private static extern void EnvelopeSpectrum(ref emxArray_real_T x, double fs, ref forCsharp amp, ref forCsharp ff);


        [DllImport("EvlpAnlysBdKts.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        private static extern void EvlpAnlysBdKts(ref emxArray_real_T x, double nlevel, ref forCsharp xf);

        /// <summary>
        /// 获取包络波形
        /// </summary>
        /// <param name="waveData"></param>
        /// <param name="fs"></param>
        /// <returns></returns>
        public static Dictionary<string, double[]> GetEnvelopeWave(double[] waveData, double fs)
        {
            Dictionary<string, double[]> dic = new Dictionary<string, double[]>();
            unsafe
            {
                try
                {
                    forCsharp ampOut = new forCsharp();
                    forCsharp ffOut = new forCsharp();
                    //forCsharp temp = new forCsharp();

                    //emxArray_real_T xf = new emxArray_real_T();
                    emxArray_real_T rawdata = new emxArray_real_T();
                    emxArray_real_T _ampoutdata = new emxArray_real_T();
                    emxArray_real_T _fftoutdata = new emxArray_real_T();
                    int size = waveData.Length;
                    double* temp_data_ff = stackalloc double[size];
                    int* temp_size_ff = stackalloc int[1];
                    //int nlevel = 7;
                    rawdata.data = temp_data_ff;
                    rawdata.size = temp_size_ff;
                    *rawdata.size = waveData.Length;
                    rawdata.numDimensions = 1;
                    rawdata.allocatedSize = waveData.Length;
                    rawdata.canFreeData = 1;
                    // 波形数据转换
                    for (int i = 0; i < waveData.Length; i++)
                    {
                        rawdata.data[i] = waveData[i];
                    }
                    // EvlpAnlysBdKts(ref rawdata, nlevel, ref temp);
                    // xf = *temp.data;
                    EnvelopeSpectrum(ref rawdata, fs, ref ampOut, ref ffOut);
                    // 数据转换
                    _ampoutdata = *ampOut.data;
                    _fftoutdata = *ffOut.data;
                    double[] xdata = new double[_ampoutdata.allocatedSize]; // ampOut
                    double[] ydata = new double[_fftoutdata.allocatedSize]; // ffOut

                    for (int i = 0; i < _ampoutdata.allocatedSize; i++)
                    {
                        ydata[i] = _ampoutdata.data[i];
                        xdata[i] = _fftoutdata.data[i];
                    }
                    dic.Add("x", xdata);
                    dic.Add("y", ydata);
                    return dic;
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage(string.Format("获取包络出错。"), ex);
                    return dic;
                }
            }
        }


        /// <summary>
        /// 解包络
        /// </summary>
        /// <param name="_waveDataWithoutDC"></param>
        /// <param name="_sampleRate"></param>
        /// <param name="_evpband"></param>
        public static double[] WALG_TDA_Env_gpk(double[] _waveDataWithoutDC, double _sampleRate, double _evpband)
        {
            int u32DataPoints = (int)(_sampleRate / (2.56 * _evpband));  /*每隔多少个数据点数取一个最大值*/
            int SourcDataLength = _waveDataWithoutDC.Length;

            int OutputLength = (int)(SourcDataLength / u32DataPoints); /*包络完之后输出的数据长度*/
            double[] pfltOutput = new double[OutputLength];

            // 幅值取绝对值
            for (int i = 0; i < _waveDataWithoutDC.Length; i++)
            {
                _waveDataWithoutDC[i] = Math.Abs(_waveDataWithoutDC[i]);
            }

            for (int u32Index = 0; u32Index < OutputLength; u32Index++)
            {
                double[] temp = new double[u32DataPoints];
                Array.Copy(_waveDataWithoutDC, u32Index * u32DataPoints, temp, 0, u32DataPoints);

                double fltMax = temp.Max();
                pfltOutput[u32Index] = fltMax;
            }

            return pfltOutput;
        }
    }
}