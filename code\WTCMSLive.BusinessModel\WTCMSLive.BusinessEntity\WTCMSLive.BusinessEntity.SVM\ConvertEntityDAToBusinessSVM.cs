﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.BusinessEntity.SVM;
using WTCMSLive.Entity.Enum;
using WTCMSLive.Entity.Models;
using WTCMSLive.IDALService;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: Maxy
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从DA层实体转换为业务层实体
    /// </summary>
    public class ConvertEntityDAToBusinessSVM
    {
        static ISVMMeasDefService svmMdfSvc = AppFramework.ServiceBus.ServiceLocator.GetService<ISVMMeasDefService>();
        static IDeviceService devSvc = AppFramework.ServiceBus.ServiceLocator.GetService<IDeviceService>();
        /// <summary>
        /// 晃度仪实体转换
        /// </summary>
        /// <param name="_svm"></param>
        /// <returns></returns>
        public static SVMUnit ConvertSVM(SVMonitor _svm)
        {
            SVMUnit _svmUnit = new SVMUnit();
            _svmUnit.AssocWindTurbineID = _svm.WindTurbineID.ToString();
            DevWindTurbine myWindTurbine = devSvc.GetDevWindTurbineByTurID(_svmUnit.AssocWindTurbineID);
            if (myWindTurbine != null)
            {
                _svmUnit.AssocWindTurbineName = myWindTurbine.WindTurbineName;
            }
            _svmUnit.SVMID = _svm.WindTurbineID.ToString();
            _svmUnit.SVMName = _svm.SVMName;
            _svmUnit.SVMCode = _svm.SVMCode;
            //wangy 2015年11月26日 09:53:06去掉采集方式显示字段
            //_svmUnit.SVMAcqType = (EnumSVMAcquisitionType)_svm.AcquisitionType;
            _svmUnit.ModbusAddress = _svm.ModbusAddress.ToString();
            _svmUnit.SVMSoftwareVersion = _svm.SVMSoftwareVersion;
            _svmUnit.ComponentID = _svm.ComponentID;
            return _svmUnit;
        }
        /// <summary>
        /// 寄存器实体转换
        /// </summary>
        /// <param name="_register"></param>
        /// <returns></returns>
        public static BusinessEntity.SVM.SVMRegister ConvertRegister(Entity.Models.SVMRegister _register)
        {
            BusinessEntity.SVM.SVMRegister svmRegister = new BusinessEntity.SVM.SVMRegister();
            svmRegister.AssocWindTurbineID = _register.WindTurbineID.ToString();
            svmRegister.SVMRegisterAdr = _register.RegisterAdress.ToString();
            svmRegister.SVMMeasLocId = _register.MeasLocationID.ToString();
            svmRegister.ComponentID = _register.ComponentID.ToString();
            SVMMeasLocation RegisterLoc = svmMdfSvc.GetSVMMeaslocByMeasLocID(_register.MeasLocationID);
            if(RegisterLoc!=null)
                svmRegister.SVMMeasLocName = RegisterLoc.MeasurementLocationName;
            svmRegister.Coeff = (double)_register.ConvertCoefficient;
            svmRegister.RegisterType = (int)_register.RegisterType;
            svmRegister.RegisterStorrageType = (int)_register.RegisterStorrageType;
            svmRegister.ByteArrayType = (int)_register.ByteArrayType;
            return svmRegister;
        }

        public static WaveDef_SVM ConvertWaveDefinition(Entity.Models.SVMWaveDefinition _waveDef)
        {
            WaveDef_SVM mySVM = new WaveDef_SVM();
            mySVM.WindTurbineID = _waveDef.WindTurbineID.ToString();
            mySVM.MeasDefinitionID = _waveDef.MeasDefinitionID.ToString();
            mySVM.MeasLocationID = _waveDef.MeasLocationID.ToString();
            mySVM.MeasLocationName = _waveDef.WaveDefinitionName;
            mySVM.WaveDefinitionID = _waveDef.WaveDefinitionID.ToString();
            mySVM.WaveDefinitionName = _waveDef.WaveDefinitionName;
            mySVM.SampleLength = _waveDef.SampleLength;
            mySVM.ParamType = (EnumSVMLocOriention)_waveDef.ParamType;
            mySVM.SampleRate = float.Parse(_waveDef.SampleRate.ToString("0.00"));
            return mySVM;
        }

        /// <summary>
        ///  波形定义(角度)实体转换
        /// </summary>
        /// <param name="_waveDef"></param>
        /// <returns></returns>
        public static SVMWaveDefinition_Angle ConvertWaveDefinitionANG(Entity.Models.SVMWaveDefinition _waveDef)
        {
            SVMWaveDefinition_Angle waveDefAng = new SVMWaveDefinition_Angle();
            waveDefAng.WindTurbineID = _waveDef.WindTurbineID.ToString();
            waveDefAng.WaveDefinitionID = _waveDef.WaveDefinitionID.ToString();
            waveDefAng.MeasLocationID = _waveDef.MeasLocationID.ToString();
            waveDefAng.MeasDefinitionID = _waveDef.MeasDefinitionID.ToString();
            waveDefAng.ParamType = (EnumSVMLocOriention)_waveDef.ParamType;
            waveDefAng.WaveDefinitionName = _waveDef.WaveDefinitionName;
            // add by Zjs 2014-10-10 15:02 
            waveDefAng.MeasLocationName = _waveDef.SVMMeasLocation.MeasurementLocationName;
            return waveDefAng;
        }

        /// <summary>
        /// 波形定义(加速度)实体转换
        /// </summary>
        /// <param name="_waveDef"></param>
        /// <returns></returns>
        public static SVMWaveDefinition_ACC ConvertWaveDefinitionACC(Entity.Models.SVMWaveDefinition _waveDef)
        {
            SVMWaveDefinition_ACC waveDefAcc = new SVMWaveDefinition_ACC();
            waveDefAcc.WindTurbineID = _waveDef.WindTurbineID.ToString();
            waveDefAcc.WaveDefinitionID = _waveDef.WaveDefinitionID.ToString();
            waveDefAcc.MeasLocationID = _waveDef.MeasLocationID.ToString();
            waveDefAcc.MeasDefinitionID = _waveDef.MeasDefinitionID.ToString();
            waveDefAcc.ParamType = (EnumSVMLocOriention)_waveDef.ParamType;
            waveDefAcc.WaveDefinitionName = _waveDef.WaveDefinitionName;
            // add by Zjs 2014-10-10 15:02 
            waveDefAcc.MeasLocationName = _waveDef.SVMMeasLocation.MeasurementLocationName;
            return waveDefAcc;
        }
        public static List<BusinessEntity.SVM.SVMEigenValueData> ConvertEigenValueList(List<Entity.Models.SVMEVDataRT> _evList)
        {
            if (_evList == null)
            { return null; }
            List<BusinessEntity.SVM.SVMEigenValueData> SVMEVList = new List<BusinessEntity.SVM.SVMEigenValueData>();
            for (int i = 0; i < _evList.Count; i++)
            {
                SVMEVList.Add(ConvertEigenValueDataRT(_evList[i]));
            }
            return SVMEVList;
        }

        /// <summary>
        /// 特征值实时数据转换
        /// </summary>
        /// <param name="_ev"></param>
        /// <returns></returns>
        public static BusinessEntity.SVM.SVMEigenValueData ConvertEigenValueDataRT(Entity.Models.SVMEVDataRT _ev)
        {
            BusinessEntity.SVM.SVMEigenValueData svmEVRT = new SVMEigenValueData();
            svmEVRT.WindTurbineID = _ev.WindTurbineID.ToString();
            svmEVRT.MeasDefinitionID = _ev.MeasDefinitionID.ToString();
            svmEVRT.WaveDefinitionID = _ev.WaveDefinitionID.ToString();
            svmEVRT.EigenValueID = _ev.EigenValueID.ToString();
            svmEVRT.MeasLocationID = _ev.MeasLocationID.ToString();
            svmEVRT.EigenValueType = (EnumSVMEigenValueType)_ev.EigenValueType;
            svmEVRT.SVMRegister = _ev.SVMRegister.ToString();
            svmEVRT.LevelCode = _ev.WkConLevelCode.ToString();
            svmEVRT.AcquisitionTime = _ev.AcquisitionTime;
            try
            {
                svmEVRT.AlarmDegree = (int)_ev.AlarmDegree;
                svmEVRT.EigenValue = (float)_ev.EigenValue;
            }
            catch {
                
            }
            //svmEVRT.Data_Qual_Type = (EnumDataQualityType)_ev.DataQualType;
            return svmEVRT;
        }
        /// <summary>
        /// 测量位置实体转换
        /// </summary>
        /// <param name="_measLoc"></param>
        /// <returns></returns>
        public static MeasLoc_SVM ConvertMeasLocation(Entity.Models.SVMMeasLocation _measLoc)
        {
            MeasLoc_SVM svmMeasLoc = new MeasLoc_SVM();
            svmMeasLoc.WindTurbineID = _measLoc.WindTurbineID.ToString();
            svmMeasLoc.ComponentID = _measLoc.ComponentID.ToString();
            svmMeasLoc.MeasLocationID = _measLoc.MeasLocationID.ToString();
            svmMeasLoc.WindTurbineID = _measLoc.WindTurbineID.ToString();
            svmMeasLoc.MeasurementLocationName = _measLoc.MeasurementLocationName;
            svmMeasLoc.ParamType = (EnumSVMParamType)_measLoc.ParamType;
            //为俯仰角或横滚角时
            if (_measLoc.ParamType == 0 || _measLoc.ParamType == 1)
            {
                svmMeasLoc.DataType = EnumSVMLocDataType.Angle;
            }
            else {
                svmMeasLoc.DataType = EnumSVMLocDataType.Acceleration;
            }
            svmMeasLoc.OrderSeq = (int)_measLoc.OrderSeq;
            svmMeasLoc.Orientation = (EnumSVMLocOriention)_measLoc.ParamType;
            return svmMeasLoc;
        }
    }
}
