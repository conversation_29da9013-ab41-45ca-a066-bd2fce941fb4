﻿using System;
using System.Collections.Generic;
using System.Linq;
using AppFramework.Utility;
using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel.TIM;
using Microsoft.EntityFrameworkCore;
using CMSFramework.DevTreeEntities;
using CMSFramework.MeasDefEntities;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 数据库交互层 SVM, 系统组态版本
    /// </summary>
    public static class SVMManagement
    {
        /// <summary>
        /// 根据机组ID查询晃动仪数据
        /// </summary>
        ///<param name="_turId"></param>
        public static List<WaveDef_SVM> GetWaveDef_sData(string _turId) {
            List<WaveDef_SVM> ml_sList = null;
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ml_sList = ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == _turId).ToList();

            }

            if (ml_sList != null && ml_sList.Count > 0)
            {
                using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    ml_sList.ForEach(item =>
                    {
                        item.EigenValueConf = ctx.TimeDomainEvConfs.Where(t => t.WindTurbineID == _turId && t.MeasLocationID == item.MeasLocationID && t.MeasDefinitionID == item.MeasDefinitionID && t.WaveDefinitionID == item.WaveDefinitionID).ToList();
                    });
                }
            }
            return ml_sList;
        }




        /// <summary>
        /// 根据风场ID查询测量位置名称
        /// </summary>
        /// <param name="_turId"></param>
        /// 

      


    public static string[] GetMeasurementLocName(string _turId, string[] existML_SData, string section, string ModbusID)
    {
            List<string> existMLSlist = existML_SData.ToList();
            List<string> _existMLSlist = existML_SData.ToList();
          
    
            List<MeasLoc_SVM> mlsList = SVMManagement.GetMeasLoc_SVMListByTurID(_turId);

            for (int n = 0; n < existML_SData.Length; n++)
            {
                MeasLoc_SVM dbUpSvm = mlsList.Find(i => i.SectionName == "塔基" && i.MeasLocName == existMLSlist[n]);
                if (dbUpSvm != null && section=="FDA")
                {
                    _existMLSlist.Remove(existMLSlist[n]);
                }
                MeasLoc_SVM dbDownSvm = mlsList.Find(k => k.SectionName == "塔顶" && k.MeasLocName == existMLSlist[n]);
                if (dbDownSvm != null && section == "TOP")
                {
                    _existMLSlist.Remove(existMLSlist[n]);
                }

            }

/*                for (int n = 0; n < existMLSlist.Count; n++)
                {
                   foreach (MeasLoc_SVM mls in mlsList)
                   {
                    //查询数据库中是否存在塔基的温度
                    MeasLoc_SVM dbUpSvm = mlsList.Find(i => i.SectionName == "塔基" && i.MeasLocName == existMLSlist[n] );
                    if (dbUpSvm !=null && dbUpSvm.SectionName==mls.SectionName) {
                        _existMLSlist.Remove(existMLSlist[n]);
                        continue;
                    }
                    //查询数据库中是否存在塔顶的温度
                    MeasLoc_SVM dbDownSvm = mlsList.Find(i => i.SectionName == "塔顶" && i.MeasLocName == existMLSlist[n] );
                    if (dbDownSvm != null && dbDownSvm.SectionName == mls.SectionName) {
                        _existMLSlist.Remove(existMLSlist[n]);
                        continue;
                    }

                   }
                }*/
            return _existMLSlist.ToArray();

   }

/// <summary>
/// 根据风场ID获取SVM列表
/// </summary>
/// <param name="_windParkID"></param>
public static List<SVMUnit> GetSVMUnitListByWindParkID(string windTurbineID)
        {
            List<SVMUnit> list = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                //list = ctx.SVMUnits.Where(item => item.AssocWindTurbineID.Contains(windTurbineID)).ToList();
                list = ctx.SVMUnits.ToList();
                list = list.Where(t => t.AssocWindTurbineID.Contains(windTurbineID)).ToList();
            }
            return list;
        }

        /// <summary>
        /// 根据机组ID获取SVM列表
        /// </summary>
        /// <param name="_windParkID"></param>
        public static List<SVMUnit> GetSVMUnitListByWindTurbineID(string windTurbineID)
        {
            List<SVMUnit> list = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                //list = ctx.SVMUnits.Where(item => item.AssocWindTurbineID.Contains(windTurbineID)).ToList();
                list = ctx.SVMUnits.ToList();
                list = list.Where(t => t.AssocWindTurbineID.Contains(windTurbineID)).ToList();
            }
            return list;
        }


        /// <summary>
        /// 根据SVMID 获取晃度仪实体
        /// </summary>
        /// <param name="_svmId"></param>
        public static SVMUnit GetSVMById(string _svmId)
        {
            SVMUnit svm = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                svm = ctx.SVMUnits.FirstOrDefault(item => item.AssocWindTurbineID == _svmId);
            }
            return svm;
        }


        public static SVMUnit GetSVMById(string turbine,string svmid)
        {
            SVMUnit svm = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                svm = ctx.SVMUnits.FirstOrDefault(item => item.AssocWindTurbineID == turbine && item.SVMID == svmid);
            }
            return svm;
        }

        /// <summary>
        /// 通过ID删除晃度仪
        /// </summary>
        /// <param name="_svmId"></param>
        public static void DeleteSVM(string _svmId, string ComponentID)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.SVMUnits.RemoveRange(ctx.SVMUnits.Where(item => item.ComponentID == ComponentID));
                ctx.SVMRegisters.RemoveRange(ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == _svmId));
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocSVMs.RemoveRange(ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _svmId));
                ctx.SaveChanges();
            }
        }
        public static void DeleteSVMByID(string turbineID, string svmID)
        {
            string MeasLocIddata = string.Empty;

            //根据turbineID, svmID查询svmregister表
            List<string> svmList = new List<string>();
            List<SVMRegister> registers = SVMRegisterSelect(turbineID, svmID);
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
          
                foreach (SVMRegister sVMRegister in registers)
                {
                    svmList.Add(sVMRegister.SVMMeasLocId);
                } 
            }
            //如果WaveDef_SVM表中没有测量定义ID择在测量位置表中删除测量定义对应的哪一行数据，否则反之。
            using (CMSFramework.EF.MeasDef.MDFContext _ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    foreach (var svmidData in svmList)
                    {
                    List<WaveDef_SVM> deflist = _ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == turbineID && item.MeasLocationID == svmidData).ToList();
                    if (deflist.Count==0) {
                        using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                        {
                            ctx.DevMeasLocSVMs.RemoveRange(ctx.DevMeasLocSVMs.Where(item => item.MeasLocationID == svmidData));
                           
                            ctx.SaveChanges();
                        }

                        
                    }

                    }


                }

            // 删除报警定义，报警定义未和设备绑定，通过测量位置删除，测量位置具有唯一性，
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                foreach (var svmidData in svmList)
                {
                    var alarm = ctx.AlarmDefLists.Where(t => t.WindTurbineID == turbineID && t.MeasLocationID == svmidData).ToList();
                    if (alarm != null && alarm.Count >0)
                    {
                        foreach(var al in alarm)
                        {
                            ctx.AlarmDefThresholdGroupLists.RemoveRange(ctx.AlarmDefThresholdGroupLists.Where(item => item.ThresholdGroup == al.ThresholdGroup));
                        }
                        ctx.AlarmDefLists.RemoveRange(alarm);
                    }

                }
                ctx.SaveChanges();
            }

            /*    // 删除测量位置
                using (CMSFramework.EF.DevContext _ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    foreach (String location in svmList) {
                        _ctx.DevMeasLocSVMs.RemoveRange(_ctx.DevMeasLocSVMs.Where(item => item.MeasLocationID == location));
                        _ctx.SaveChanges();
                    } 
                }*/
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {

                ctx.SVMUnits.RemoveRange(ctx.SVMUnits.Where(item => item.AssocWindTurbineID == turbineID && item.SVMID == svmID));
                IEnumerable<SVMRegister> registerData = ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == turbineID && item.SVMID == svmID);
                if (registers.Count() != 0)
                {
                    MeasLocIddata = registerData.First().SVMMeasLocId;
                }
                ctx.SVMRegisters.RemoveRange(ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == turbineID && item.SVMID == svmID));
                // 查询对应绑定的测点ID
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 添加晃度仪
        /// </summary>
        /// <param name="svmUnit"></param>
        public static void AddSVM(SVMUnit svmUnit)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.SVMUnits.Add(svmUnit);
                ctx.SaveChanges();
            }
        }
        
        /// <summary>
        /// 修改晃度仪 
        /// </summary>
        /// <param name="_svmUnit"></param>
        public static bool EditSVMInfo(SVMUnit _svmUnit)
        {
            int count = 0;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.SVMUnits.Attach(_svmUnit);
                ctx.Entry(_svmUnit).State = EntityState.Modified;
                count = ctx.SaveChanges();
            }
            return count > 0;
        }
        /// <summary>
        /// 添加晃度寄存器
        /// </summary>
        /// <param name="RegisterList"></param>
        public static void AddSVMRegister(List<SVMRegister> RegisterList)
        {
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                ctx.SVMRegisters.AddRange(RegisterList);
                ctx.SaveChanges();
            }
        }
        
        /// <summary>
        /// 根据SVMID查找寄存器列表
        /// </summary>
        /// <param name="_svmID"></param>
        public static List<SVMRegister> GetSVMRegisterListBySVMId(string _svmID)
        {
            List<SVMRegister> list = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                list = ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == _svmID).ToList();
            }
            return list;
        }

        ///<summary>
        ///根据机组ID和SVMID查找寄存器列表
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_svmID"></param>
        public static List<SVMRegister> GetSVMRegisterlistByWTidAndSvmID(string _turID, string _svmID) 
        {
            List<SVMRegister> svmList = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                svmList = ctx.SVMRegisters.Where(m => m.AssocWindTurbineID == _turID && m.SVMID == _svmID).ToList(); 
            }
            return svmList;
        }


        /// <summary>
        /// 根据晃度仪Id获取晃度仪测量位置列表
        /// </summary>
        /// <param name="_SVMID"></param>
        /// <returns></returns>
        public static List<MeasLoc_SVM> GetMeasLoc_SVMListByTurID(string _turID)
        {
            List<MeasLoc_SVM> list = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return list;
        }

        /// <summary>
        /// 获取modbus测量位置列表
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static List<MeasLoc_Modbus> GetMeasLoc_ModbusListByTurID(string _turID)
        {
            List<MeasLoc_Modbus> list = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.MeasLoc_Modbus.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return list;
        }



        public static List<MeasLoc_SVM> GetMeasLoc_SVMListByTurID(string _turID,string compID,string sectionID)
        {
            List<MeasLoc_SVM> list = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _turID && item.ComponentID == compID && item.SectionName == sectionID).ToList();
            }
            return list;
        }
        /// <summary>
        /// 根据机组ID获取没有绑定寄存器的测量位置列表
        /// </summary>
        /// <param name="_turId"></param>
        public static List<MeasLoc_SVM> GetNotUsedMeasLoc_SVMList(string _turId)
        {
            List<MeasLoc_SVM> list = null;
            List<SVMRegister> RegisterList = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _turId).ToList();
            }
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                RegisterList = ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == _turId).ToList();
            }
            if (RegisterList == null || RegisterList.Count == 0)
                return list;
            RegisterList.ForEach(item => {
                var MeasLocSVM = list.Find(svm => svm.MeasLocationID == item.SVMMeasLocId);
                if (MeasLocSVM != null)
                {
                    list.Remove(MeasLocSVM);
                }
            });
            return list;
        }

        /// <summary>
        /// 根据晃度测量位置ID获取晃度仪测量位置对象
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_SVMMeasLocId"></param>
        /// <returns></returns>
        public static MeasLoc_SVM GetMeasloc_SVMByMeasLocID(string _SVMMeasLocId)
        {
            MeasLoc_SVM measLocSVM = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                measLocSVM = ctx.DevMeasLocSVMs.Find(_SVMMeasLocId);
            }
            return measLocSVM;
        }

        /// <summary>
        /// 根据测量位置ID查找寄存器
        /// </summary>
        /// <param name="_turID"></param>
        /// <param name="_measLocId"></param>
        /// <returns></returns>
        public static SVMRegister GetSVMRegisterByMeasLocId(string _measLocId)
        {
            SVMRegister reg = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                reg = ctx.SVMRegisters.Where(item => item.SVMMeasLocId == _measLocId).FirstOrDefault();
            }
            return reg;
        }

        public static List<SVMRegister> GetSVMRegisterByturbineID(string turbineID)
        {
            List<SVMRegister> reg = new List<SVMRegister>();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                reg = ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == turbineID).ToList();
            }
            return reg;
        }

        /// <summary>
        /// 获取寄存器地址
        /// </summary>
        /// <param name="loc"></param>
        /// <returns></returns>
        public static string GetRegisterAdr(MeasLoc_SVM loc)
        {
            switch (loc.ParamType)
            {
                case EnumSVMParamType.Horizontal:
                case EnumSVMParamType.XAcc:
                    return "0";
                case EnumSVMParamType.Vertical:
                case EnumSVMParamType.YAcc:
                    return "1";
                case EnumSVMParamType.Axisl:
                case EnumSVMParamType.ZAcc:
                    return "2";
                case EnumSVMParamType.Roll:
                case EnumSVMParamType.XInclination:
                    return "3";
                case EnumSVMParamType.Pitch:
                case EnumSVMParamType.YInclination:
                    return "4";
                case EnumSVMParamType.Temperature:
                    return "6";
                default:
                    return "0";
            }
        }



        public static EnumSVMParamType GetParamType(string measloc)
        {
            switch (measloc)
            {
                case "俯仰角":
                    return EnumSVMParamType.Pitch;
                case "横滚角":
                    return EnumSVMParamType.Roll;
                case "垂直加速度":
                    return EnumSVMParamType.Vertical;
                case "水平加速度":
                    return EnumSVMParamType.Horizontal;
                case "轴向加速度":
                    return EnumSVMParamType.Axisl;
                case "X轴倾角":
                    return EnumSVMParamType.XInclination;
                case "Y轴倾角":
                    return EnumSVMParamType.YInclination;
                case "X轴加速度":
                    return EnumSVMParamType.XAcc;
                case "Y轴加速度":
                    return EnumSVMParamType.YAcc;
                case "Z轴加速度":
                    return EnumSVMParamType.ZAcc;
                case "温度":
                    return EnumSVMParamType.Temperature;
                default:
                    return EnumSVMParamType.Temperature;
            }
        }

        public static void AddSVMMeasLoc(string _turId, List<MeasLoc_SVM> measloc_svmList)
        {
            //List<WindTurbineComponent> comList = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
      
            {
                //ctx.DevMeasLocSVMs.Find(ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _turId));
                //ctx.SaveChanges();
               // comList = ctx.DevTurComponents.Where(item => item.WindTurbineID == _turId).ToList();

                ctx.DevMeasLocSVMs.AddRange(measloc_svmList);
                ctx.SaveChanges();

            }
        }

        /// <summary>
        /// 自动生成机组晃度仪测量位置
        /// </summary> 
        /// <param name="_turId"></param>
        public static void AutoAddSVMMeasLoc(string _turId,string ComponentName)
        {
            //先删除机组下原有的晃度仪测量位置 
            List<WindTurbineComponent> comList = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocSVMs.RemoveRange(ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _turId));
                ctx.SaveChanges();
                comList = ctx.DevTurComponents.Where(item => item.WindTurbineID == _turId).ToList();
                WindTurbineComponent module = comList.Find(item => item.ComponentName == ComponentName);
                List<MeasLoc_SVM> listLoc = new List<MeasLoc_SVM>();
                foreach (EnumSVMParamType enuloc in Enum.GetValues(typeof(EnumSVMParamType)))
                {
                    MeasLoc_SVM loc = new MeasLoc_SVM();
                    loc.WindTurbineID = _turId;
                    loc.ComponentID = module.ComponentID;
                    loc.ParamType = enuloc;
                    loc.MeasLocName = EnumHelper.GetDescription(enuloc);
                    loc.MeasLocationID = AppFramework.IDUtility.IDProvide.GetSVMLocID("", module.ComponentID,"", loc.MeasLocName);
                    //晃动测量位置的排序字段从100开始，wangy 2015年9月18日 13:37:05
                    loc.OrderSeq = GetSvmOrderSeq(loc.MeasLocName) + 100;
                    listLoc.Add(loc);
                }
                ctx.DevMeasLocSVMs.AddRange(listLoc);
                ctx.SaveChanges();
            }
        }

        public static void AutoAddSVMMeasLoc(string _turId, string ComponentID,string SectionName)
        {
            //先删除机组下原有的晃度仪测量位置 
            List<WindTurbineComponent> comList = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevMeasLocSVMs.RemoveRange(ctx.DevMeasLocSVMs.Where(item => item.WindTurbineID == _turId && item.ComponentID == ComponentID && item.SectionName == SectionName));
                ctx.SaveChanges();
                comList = ctx.DevTurComponents.Where(item => item.WindTurbineID == _turId).ToList();
                //WindTurbineComponent module = comList.Find(item => item.ComponentName == ComponentName);
                List<MeasLoc_SVM> listLoc = new List<MeasLoc_SVM>();
                foreach (EnumSVMParamType enuloc in Enum.GetValues(typeof(EnumSVMParamType)))
                {
                    MeasLoc_SVM loc = new MeasLoc_SVM();
                    loc.WindTurbineID = _turId;
                    loc.ComponentID = ComponentID;
                    loc.SectionName = SectionName;
                    loc.ParamType = enuloc;
                    loc.MeasLocName = EnumHelper.GetDescription(enuloc);
                    loc.MeasLocationID = AppFramework.IDUtility.IDProvide.GetSVMLocID("", ComponentID, SectionName, loc.MeasLocName);
                    //晃动测量位置的排序字段从100开始，wangy 2015年9月18日 13:37:05
                    loc.OrderSeq = GetSvmOrderSeq(loc.MeasLocName) + 100;
                    listLoc.Add(loc);
                }
                ctx.DevMeasLocSVMs.AddRange(listLoc);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 晃度仪排序的时候用
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private static int GetSvmOrderSeq(string name)
        {
            switch (name)
            { 
                case "水平加速度":
                    return 1;
                case "轴向加速度":
                    return 2;
                case "垂直加速度":
                    return 3;
                case "横滚角":
                    return 4;
                case "俯仰角":
                    return 5;
                case "SVM温度":
                    return 6;
                default:
                    break;
            }
            return 0;
        }

        #region 晃度波形定义
        /// <summary>
        /// 添加修改晃度波形定义
        /// </summary>
        /// <param name="waveSVM"></param>
        public static void AddWaveDefSVM(List<WaveDef_SVM> waveSVMList)
        {
            int LastNumber = 0;
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence SequenceEntity = ctx.SysIDSequence.Find(EnumSequenceType.WaveDefinitionID);
                LastNumber = SequenceEntity.LastNumber;
                SequenceEntity.LastNumber += waveSVMList.Count;
                ctx.SysIDSequence.Attach(SequenceEntity);
                ctx.Entry(SequenceEntity).State = EntityState.Modified;
                ctx.SaveChanges();
            }
      
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<MeasDef_Ev_Vib> evlist = new List<MeasDef_Ev_Vib>();
                waveSVMList.ForEach(svmWave => {
                    LastNumber++;
                
                    svmWave.WaveDefinitionID = LastNumber.ToString();

                    if(svmWave.EigenValueConf != null && svmWave.EigenValueConf.Count > 0)
                    {
                        svmWave.EigenValueConf.ForEach(k =>
                        {
                            k.WaveDefinitionID = svmWave.WaveDefinitionID;
                        });
                        evlist.AddRange(svmWave.EigenValueConf);
                    }
                });
                ctx.SVMWaveDefinitions.AddRange(waveSVMList);
                ctx.TimeDomainEvConfs.AddRange(evlist);
                ctx.SaveChanges();
            }
        }


        public static void AddWaveDefModbus(List<WaveDef_Modbus> waveSVMList)
        {
            int LastNumber = 0;
            using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            {
                Sequence SequenceEntity = ctx.SysIDSequence.Find(EnumSequenceType.WaveDefinitionID);
                LastNumber = SequenceEntity.LastNumber;
                SequenceEntity.LastNumber += waveSVMList.Count;
                ctx.SysIDSequence.Attach(SequenceEntity);
                ctx.Entry(SequenceEntity).State = EntityState.Modified;
                ctx.SaveChanges();
            }

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<MeasDef_Ev_Vib> evlist = new List<MeasDef_Ev_Vib>();
                waveSVMList.ForEach(svmWave => {
                    LastNumber++;

                    svmWave.WaveDefinitionID = LastNumber.ToString();

                    if (svmWave.EigenValueConf != null && svmWave.EigenValueConf.Count > 0)
                    {
                        svmWave.EigenValueConf.ForEach(k =>
                        {
                            k.WaveDefinitionID = svmWave.WaveDefinitionID;
                        });
                        evlist.AddRange(svmWave.EigenValueConf);
                    }
                });
                ctx.WDFModbusDefs.AddRange(waveSVMList);
                ctx.TimeDomainEvConfs.AddRange(evlist);
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 复制晃度波形定义
        /// </summary>
        /// <param name="waveSVM"></param>
        public static void AddSVMWaveDef(List<WaveDef_SVM> wSVMList)
        {
            //int LastNumber = 0;
            //using (CMSFramework.EF.SysContext ctx = new CMSFramework.EF.SysContext(ConfigInfo.DBConnName))
            //{
            //    Sequence SequenceEntity = ctx.SysIDSequence.Find(EnumSequenceType.WaveDefinitionID);
            //    LastNumber = SequenceEntity.LastNumber;
            //    SequenceEntity.LastNumber += wSVMList.Count;
            //    ctx.SysIDSequence.Attach(SequenceEntity);
            //    ctx.Entry(SequenceEntity).State = EntityState.Modified;
            //    ctx.SaveChanges();
            //}
            using (CMSFramework.EF.MeasDef.MDFContext ctxx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                ctxx.SVMWaveDefinitions.AddRange(wSVMList);
                ctxx.SaveChanges();
            }
        }

        public static void UpdateDefin(string windturbineid, float SampleRate, double SampleLength,string ModbusIdData)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {

                var listData = ctx.ModbusDefs.Where(item => item.WindTurbineID == windturbineid && item.ModbusUnitID == ModbusIdData).ToList();
                listData.ForEach(item =>
                {
                    item.SampleTime = (float)SampleLength;
                    item.SampleFrequency = SampleRate;
                    ctx.ModbusDefs.Attach(item);
                    ctx.Entry(item).State = EntityState.Modified;
                    ctx.SaveChanges();
                });
     
                ModbusDef modata= listData.Find(i => i.ModbusUnitID== ModbusIdData);
                List<SVMRegister> svmDataList = SVMManagement.GetSVMRegisterlistByWTidAndSvmID(windturbineid, ModbusIdData);

                List<string> svmStrList = new List<string>();
                List<WaveDef_SVM> wavedefList = new List<WaveDef_SVM>();
                foreach (SVMRegister svmData in svmDataList)
                {
                    svmStrList.Add(svmData.SVMMeasLocId);
                }

                var list = ctx.SVMWaveDefinitions.Where(obj => obj.WindTurbineID == windturbineid && obj.MeasDefinitionID == modata.MeasDefinitionID).ToList();
                list.ForEach(item =>
                {
                    for (int i = 0; i < svmStrList.Count; i++)
                    {
                        if (item.MeasLocationID == svmStrList[i])
                        {
                            item.SampleRate = SampleRate;
                            item.SampleLength = (float)SampleLength;
                            ctx.SVMWaveDefinitions.Attach(item);
                            ctx.Entry(item).State = EntityState.Modified;
                            ctx.SaveChanges();
                        }
                    }
                });
                TIMManagement.UpdateMeasDefVersion(windturbineid);
            }
        }
        /// <summary>
        ///晃度仪修改，只能修改频率和采样长度
        /// </summary>
        /// <param name="myList"></param>
        /// <param name="SampleRate"></param>
        /// <param name="SampleLength"></param>
        public static void UpdateDefin(List<WaveDef_SVM> myList, float SampleRate, short SampleLength)
        {
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                myList.ForEach(item =>
                {
                    item.SampleRate = SampleRate;
                    item.SampleLength = SampleLength;
                    ctx.SVMWaveDefinitions.Attach(item);
                    ctx.Entry(item).State = EntityState.Modified;
                    ctx.SaveChanges();
                });
            }
        }


        public static void DeleteSVMdefin(string turbineID, string measID, string svmID)

        {
            string mlsID = string.Empty;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                List<string> svmList = new List<string>();   
                List<SVMRegister> registers = SVMRegisterSelect(turbineID,svmID);
                foreach ( SVMRegister sVMRegister in  registers) {
                    svmList.Add(sVMRegister.SVMMeasLocId);
                }
                ctx.SaveChanges();
                //获取采样时间和采样频率
                ModbusDef modbusmdfData = WTCMSLive.BusinessModel.ModbusManage.GetModbusBymodbusID(turbineID, svmID);
     
        


                using (CMSFramework.EF.MeasDef.MDFContext _ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                {
                    foreach (String svmidData in svmList) { 
                      List<WaveDef_SVM> deflist = _ctx.SVMWaveDefinitions.Where(item => item.WindTurbineID == turbineID && item.SampleLength == modbusmdfData.SampleTime && item.MeasLocationID == svmidData && item.SampleRate == modbusmdfData.SampleFrequency).ToList();
                      _ctx.SVMWaveDefinitions.RemoveRange(deflist);
                      _ctx.SaveChanges();
                     }
                }
            }
        }
        /// <summary>
        /// 判断该晃动测量位置是否添加寄存器
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="measLocId"></param>
        /// <returns></returns>
        public static bool SVMIsRegister(string windTurbineId, string measLocId)
        {
            SVMRegister register = null;
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                register = ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == windTurbineId && item.SVMMeasLocId == measLocId).FirstOrDefault();
            }
            return register != null;
        }
        /// <summary>
        /// 根据测量位置和
        /// </summary>
        /// <param name="windTurbineId"></param>
        /// <param name="SVMID"></param>
        /// <returns></returns>
        public static List<SVMRegister> SVMRegisterSelect(string windTurbineId, string svmid)
        {
            List<SVMRegister> svmList = null;
             using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                svmList = ctx.SVMRegisters.Where(item => item.AssocWindTurbineID == windTurbineId && item.SVMID == svmid).ToList();
            }
            return svmList;
        }



        #endregion

        #region  获取晃度仪状态信息
        /// <summary>
        /// 获取风场下的晃度测量位置实时状态
        /// </summary>
        /// <param name="windParkId"></param>
        /// <returns></returns>
        public static List<AlarmStatus_MeasLocSVM> GetAllSVMeasLocStateList(string windParkId)
        {
            List<AlarmStatus_MeasLocSVM> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                //list = ctx.AlarmStatus_MeasLocSVMs.Where(item => item.WindTurbineID.Contains(windParkId)).ToList();
                list = ctx.AlarmStatus_MeasLocSVMs.ToList();
                list = list.Where(t => t.WindTurbineID.Contains(windParkId)).ToList();
            }
            return list;
        }
        /// <summary>
        /// 获取机组下的晃度测量位置实时状态
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <returns></returns>
        public static List<AlarmStatus_MeasLocSVM> GetSVMeasLocStateListByWindTurbine(string WindTurbineID)
        {
            List<AlarmStatus_MeasLocSVM> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmStatus_MeasLocSVMs.Where(item => item.WindTurbineID == WindTurbineID).ToList();
            }
            return list;
        }
        #endregion


    }
}
