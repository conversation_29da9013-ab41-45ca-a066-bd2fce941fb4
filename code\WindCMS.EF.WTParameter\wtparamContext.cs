﻿using System;
using System.Collections.Generic;
using System.Xml.Linq;
using CMSFramework.EF.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace WindCMS.EF.WTParameter
{
    public partial class wtparamContext : DbContextBase
    {
        public wtparamContext(string _name) : base(_name)
        {
        }


        public virtual DbSet<WtBladeParameter> WtBladeParameters { get; set; } = null!;
        public virtual DbSet<WtNacelleParameter> WtNacelleParameters { get; set; } = null!;
        public virtual DbSet<WtParametersBearing> WtParametersBearings { get; set; } = null!;
        public virtual DbSet<WtParametersFlange> WtParametersFlanges { get; set; } = null!;
        public virtual DbSet<WtParametersGearbox> WtParametersGearboxes { get; set; } = null!;
        public virtual DbSet<WtTowerParameter> WtTowerParameters { get; set; } = null!;

        public virtual DbSet<WtGearboxModel> WtGearboxModels { get; set; }



        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.UseCollation("utf8_general_ci")
                .HasCharSet("utf8");

            modelBuilder.Entity<WtBladeParameter>(entity =>
            {
                entity.HasKey(e => e.RotorId)
                    .HasName("PRIMARY");

                entity.ToTable("wt_blade_parameters");

                entity.HasComment("存储与叶轮相关的全部参数");

                entity.HasIndex(e => e.ModelId, "wtm");

                entity.Property(e => e.RotorId)
                    .HasColumnType("int(11)")
                    .HasColumnName("rotor_id")
                    .HasComment("叶轮的唯一标识符");

                entity.Property(e => e.BladeCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("blade_count")
                    .HasComment("叶片数量");

                entity.Property(e => e.BladeEdgeFrequency1)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_edge_frequency_1")
                    .HasComment("叶片摆振1阶固有频率（单位：Hz）");

                entity.Property(e => e.BladeEdgeFrequency2)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_edge_frequency_2")
                    .HasComment("叶片摆振2阶固有频率（单位：Hz）");

                entity.Property(e => e.BladeEdgeFrequency3)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_edge_frequency_3")
                    .HasComment("叶片摆振3阶固有频率（单位：Hz）");

                entity.Property(e => e.BladeFlapFrequency1)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_flap_frequency_1")
                    .HasComment("叶片挥舞1阶固有频率（单位：Hz）");

                entity.Property(e => e.BladeFlapFrequency2)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_flap_frequency_2")
                    .HasComment("叶片挥舞2阶固有频率（单位：Hz）");

                entity.Property(e => e.BladeFlapFrequency3)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_flap_frequency_3")
                    .HasComment("叶片挥舞3阶固有频率（单位：Hz）");

                entity.Property(e => e.BladeLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_length")
                    .HasComment("叶片长度（单位：m）");

                entity.Property(e => e.BladeManufacturer)
                    .HasMaxLength(255)
                    .HasColumnName("blade_manufacturer")
                    .HasComment("叶片生产厂家");

                entity.Property(e => e.BladeMass)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_mass")
                    .HasComment("叶片质量（单位：吨）");

                entity.Property(e => e.BladeMaterial)
                    .HasMaxLength(255)
                    .HasColumnName("blade_material")
                    .HasComment("叶片材料");

                entity.Property(e => e.BladeModel)
                    .HasMaxLength(255)
                    .HasColumnName("blade_model")
                    .HasComment("叶片型号");

                entity.Property(e => e.BladeVibrationDataRate)
                    .HasPrecision(10, 2)
                    .HasColumnName("blade_vibration_data_rate")
                    .HasComment("叶片振动数据采样率（单位：Hz）");

                entity.Property(e => e.BladeVibrationSensorCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("blade_vibration_sensor_count")
                    .HasComment("叶片振动传感器数量");

                entity.Property(e => e.BladeVibrationSensorType)
                    .HasMaxLength(255)
                    .HasColumnName("blade_vibration_sensor_type")
                    .HasComment("叶片振动传感器类型（如：加速度计、应变计等）");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.GearCircleGearLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("gear_circle_gear_length")
                    .HasComment("叶片齿圈齿轮长度（单位：mm）");

                entity.Property(e => e.GearCirclePitch)
                    .HasPrecision(10, 2)
                    .HasColumnName("gear_circle_pitch")
                    .HasComment("叶片齿圈模数（单位：mm）");

                entity.Property(e => e.GearCircleProfileShiftFactor)
                    .HasPrecision(10, 2)
                    .HasColumnName("gear_circle_profile_shift_factor")
                    .HasComment("叶片齿圈变位系数");

                entity.Property(e => e.GearCircleToothNumber)
                    .HasColumnType("int(11)")
                    .HasColumnName("gear_circle_tooth_number")
                    .HasComment("叶片齿圈齿数");

                entity.Property(e => e.GearCircleToothTipDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("gear_circle_tooth_tip_diameter")
                    .HasComment("叶片齿圈齿顶圆直径（单位：mm）");

                entity.Property(e => e.ModelId)
                    .HasMaxLength(50)
                    .HasColumnName("model_id")
                    .HasComment("对应的机组型号ID（外键）");

                entity.Property(e => e.PitchBearingInnerDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_inner_diameter")
                    .HasComment("变桨轴承内圈直径（单位：mm）");

                entity.Property(e => e.PitchBearingInnerHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_inner_height")
                    .HasComment("变桨轴承内圈高度（单位：mm）");

                entity.Property(e => e.PitchBearingInnerRollwayDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_inner_rollway_diameter")
                    .HasComment("变桨轴承内圈滚道直径（单位：mm）");

                entity.Property(e => e.PitchBearingManufacturer1)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_manufacturer_1")
                    .HasComment("变桨轴承1厂家");

                entity.Property(e => e.PitchBearingManufacturer2)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_manufacturer_2")
                    .HasComment("变桨轴承2厂家");

                entity.Property(e => e.PitchBearingManufacturer3)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_manufacturer_3")
                    .HasComment("变桨轴承3厂家");

                entity.Property(e => e.PitchBearingModel1)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_model_1")
                    .HasComment("变桨轴承1型号");

                entity.Property(e => e.PitchBearingModel2)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_model_2")
                    .HasComment("变桨轴承2型号");

                entity.Property(e => e.PitchBearingModel3)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_model_3")
                    .HasComment("变桨轴承3型号");

                entity.Property(e => e.PitchBearingOuterDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_outer_diameter")
                    .HasComment("变桨轴承外圈直径（单位：mm）");

                entity.Property(e => e.PitchBearingOuterHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_outer_height")
                    .HasComment("变桨轴承外圈高度（单位：mm）");

                entity.Property(e => e.PitchBearingOuterRollwayDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_outer_rollway_diameter")
                    .HasComment("变桨轴承外圈滚道直径（单位：mm）");

                entity.Property(e => e.PitchBearingRollCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("pitch_bearing_roll_count")
                    .HasComment("变桨轴承滚子个数");

                entity.Property(e => e.PitchBearingRollDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_roll_diameter")
                    .HasComment("变桨轴承滚子直径（单位：mm）");

                entity.Property(e => e.PitchBearingRollNumber)
                    .HasColumnType("int(11)")
                    .HasColumnName("pitch_bearing_roll_number")
                    .HasComment("变桨轴承滚道数量");

                entity.Property(e => e.PitchBearingRollType)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_roll_type")
                    .HasComment("变桨轴承滚子类型");

                entity.Property(e => e.PitchBearingRollBackDiameter)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_roll_back_diameter")
                    .HasComment("变桨轴承滚子类型");

                entity.Property(e => e.PitchBearingVibrationDataRate)
                    .HasPrecision(10, 2)
                    .HasColumnName("pitch_bearing_vibration_data_rate")
                    .HasComment("变桨轴承振动数据采样率（单位：Hz）");

                entity.Property(e => e.PitchBearingVibrationSensorCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("pitch_bearing_vibration_sensor_count")
                    .HasComment("变桨轴承振动传感器数量");

                entity.Property(e => e.PitchBearingVibrationSensorType)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_bearing_vibration_sensor_type")
                    .HasComment("变桨轴承振动传感器类型（如：加速度计、应变计等）");

                entity.Property(e => e.PitchMethod)
                    .HasMaxLength(255)
                    .HasColumnName("pitch_method")
                    .HasComment("变桨方式（如：液压变桨、电动变桨）");

                entity.Property(e => e.RootBoltDataRate)
                    .HasPrecision(10, 2)
                    .HasColumnName("root_bolt_data_rate")
                    .HasComment("叶根螺栓数据采样率（单位：Hz）");

                entity.Property(e => e.RootBoltMonitoringSystem)
                    .HasMaxLength(255)
                    .HasColumnName("root_bolt_monitoring_system")
                    .HasComment("叶根螺栓监测系统类型（如：有/无）");

                entity.Property(e => e.RootBoltSensorCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("root_bolt_sensor_count")
                    .HasComment("叶根螺栓传感器数量");

                entity.Property(e => e.RootBoltSensorType)
                    .HasMaxLength(255)
                    .HasColumnName("root_bolt_sensor_type")
                    .HasComment("叶根螺栓传感器类型（如：应变计、压力传感器等）");

                entity.Property(e => e.SweptArea)
                    .HasPrecision(10, 2)
                    .HasColumnName("swept_area")
                    .HasComment("扫风面积（单位：m²）");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .ValueGeneratedOnAddOrUpdate()
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");
            });

            modelBuilder.Entity<WtNacelleParameter>(entity =>
            {
                entity.HasKey(e => e.NacelleId)
                    .HasName("PRIMARY");

                entity.ToTable("wt_nacelle_parameters");

                entity.HasComment("存储与机舱相关的参数");

                entity.HasIndex(e => e.ModelId, "nwtm");

                entity.Property(e => e.NacelleId)
                    .HasColumnType("int(11)")
                    .HasColumnName("nacelle_id")
                    .HasComment("机舱参数的唯一标识符");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.GearRatio)
                    .HasPrecision(10, 2)
                    .HasColumnName("gear_ratio")
                    .HasComment("传动比");

                entity.Property(e => e.GearboxGearRatio)
                    .HasPrecision(10, 2)
                    .HasColumnName("gearbox_gear_ratio")
                    .HasComment("齿轮箱传动比");

                entity.Property(e => e.GearboxManufacturer)
                    .HasMaxLength(255)
                    .HasColumnName("gearbox_manufacturer")
                    .HasComment("齿轮箱生产厂家");

                entity.Property(e => e.GearboxModel)
                    .HasMaxLength(255)
                    .HasColumnName("gearbox_model")
                    .HasComment("齿轮箱型号");

                entity.Property(e => e.GearboxStructureType)
                    .HasMaxLength(255)
                    .HasColumnName("gearbox_structure_type")
                    .HasComment("齿轮箱结构类型");

                entity.Property(e => e.GeneratorBearingId)
                    .HasMaxLength(255)
                    .HasColumnName("generator_bearing_id")
                    .HasComment("发电机轴承驱动端1制造商");

                entity.Property(e => e.GeneratorGridSpeed)
                    .HasColumnType("int(11)")
                    .HasColumnName("generator_grid_speed")
                    .HasComment("发电机并网转速（单位：RPM）");

                entity.Property(e => e.GeneratorManufacturer)
                    .HasMaxLength(255)
                    .HasColumnName("generator_manufacturer")
                    .HasComment("发电机制造商");

                entity.Property(e => e.GeneratorModel)
                    .HasMaxLength(255)
                    .HasColumnName("generator_model")
                    .HasComment("发电机型号");

                entity.Property(e => e.GeneratorPolePairs)
                    .HasColumnType("int(11)")
                    .HasColumnName("generator_pole_pairs")
                    .HasComment("发电机磁极对数");

                entity.Property(e => e.GeneratorRatedSpeed)
                    .HasColumnType("int(11)")
                    .HasColumnName("generator_rated_speed")
                    .HasComment("发电机额定转速（单位：RPM）");

                entity.Property(e => e.GeneratorRatedVoltage)
                    .HasPrecision(10, 2)
                    .HasColumnName("generator_rated_voltage")
                    .HasComment("发电机额定电压（单位：V）");

                entity.Property(e => e.GeneratorRotorBarCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("generator_rotor_bar_count")
                    .HasComment("发电机转子条数");

                entity.Property(e => e.GeneratorStatorSlotCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("generator_stator_slot_count")
                    .HasComment("发电机定子槽数");

                entity.Property(e => e.GeneratorType)
                    .HasMaxLength(255)
                    .HasColumnName("generator_type")
                    .HasComment("发电机类型（如永磁同步发电机）");

                entity.Property(e => e.LubricantManufacturer)
                    .HasMaxLength(255)
                    .HasColumnName("lubricant_manufacturer")
                    .HasComment("润滑油生产厂家");

                entity.Property(e => e.LubricantModel)
                    .HasMaxLength(255)
                    .HasColumnName("lubricant_model")
                    .HasComment("润滑油型号");

                entity.Property(e => e.MainBearingId)
                    .HasMaxLength(255)
                    .HasColumnName("main_bearing_id")
                    .HasComment("主轴承映射ID");

                entity.Property(e => e.ModelId)
                    .HasMaxLength(50)
                    .HasColumnName("model_id")
                    .HasComment("对应的机组型号ID（外键）");

                // 新增油液属性的配置
                entity.Property(e => e.LubricantViscosity40C)
                    .HasPrecision(10, 2)
                    .HasColumnName("lubricant_viscosity_40c")
                    .HasComment("油液的40℃理论运动粘度(cst)");

                entity.Property(e => e.LubricantViscosity100C)
                    .HasPrecision(10, 2)
                    .HasColumnName("lubricant_viscosity_100c")
                    .HasComment("油液的100℃理论运动粘度(cst)");

                entity.Property(e => e.LubricantDensity25C)
                    .HasPrecision(10, 2)
                    .HasColumnName("lubricant_density_25c")
                    .HasComment("油液的25℃理论密度(kg/m²)");


                entity.Property(e => e.RatedPower)
                    .HasPrecision(10, 2)
                    .HasColumnName("rated_power")
                    .HasComment("额定功率（单位：kw）");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .ValueGeneratedOnAddOrUpdate()
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");

                entity.Ignore(t => t.MainBearingList);
                entity.Ignore(t => t.GeneratorBearingList);
            });

            modelBuilder.Entity<WtParametersBearing>(entity =>
            {
                entity.HasKey(e => e.BearingId)
                    .HasName("PRIMARY");

                entity.ToTable("wt_parameters_bearing");

                entity.HasComment("存储齿轮箱轴承的详细参数");

                entity.HasIndex(e => e.StageId, "stage_id_idx");

                entity.Property(e => e.BearingId)
                    .HasColumnType("int(11)")
                    .HasColumnName("bearing_id")
                    .HasComment("轴承参数的唯一标识符");

                entity.Property(e => e.BearingContactAngle)
                    .HasPrecision(5, 2)
                    .HasColumnName("bearing_contact_angle")
                    .HasComment("轴承接触角（单位：度）");

                entity.Property(e => e.BearingManufacturer)
                    .HasMaxLength(255)
                    .HasColumnName("bearing_manufacturer")
                    .HasComment("轴承制造商");

                entity.Property(e => e.BearingModel)
                    .HasMaxLength(255)
                    .HasColumnName("bearing_model")
                    .HasComment("轴承型号");

                entity.Property(e => e.BearingName)
                    .HasMaxLength(255)
                    .HasColumnName("bearing_name")
                    .HasComment("轴承种类");

                entity.Property(e => e.BearingPitchDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("bearing_pitch_diameter")
                    .HasComment("轴承节圆直径（单位：mm）");

                entity.Property(e => e.BearingRollCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("bearing_roll_count")
                    .HasComment("轴承滚动体个数");

                entity.Property(e => e.BearingRollDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("bearing_roll_diameter")
                    .HasComment("轴承滚动体直径（单位：mm）");

                entity.Property(e => e.BearingType)
                    .HasMaxLength(255)
                    .HasColumnName("bearing_type")
                    .HasComment("轴承类型");

                entity.Property(e => e.CompType)
                    .HasMaxLength(255)
                    .HasColumnName("comp_type")
                    .HasComment("部件类型");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.StageId)
                    .HasMaxLength(50)
                    .HasColumnName("stage_id")
                    .HasComment("齿轮箱级别参数的外键");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .ValueGeneratedOnAddOrUpdate()
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");
            });

            modelBuilder.Entity<WtParametersFlange>(entity =>
            {
                entity.HasKey(e => e.FlangeId)
                    .HasName("PRIMARY");

                entity.ToTable("wt_parameters_flange");

                entity.HasComment("存储塔筒的法兰参数，每条记录对应一层法兰");

                entity.HasIndex(e => e.CompId, "tpgs");

                entity.Property(e => e.FlangeId)
                    .HasColumnType("int(11)")
                    .HasColumnName("flange_id")
                    .HasComment("法兰的唯一标识符");

                entity.Property(e => e.BoltCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("bolt_count")
                    .HasComment("法兰螺栓数量");

                entity.Property(e => e.BoltDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("bolt_diameter")
                    .HasComment("法兰螺栓直径（单位：mm）");

                entity.Property(e => e.BoltLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("bolt_length")
                    .HasComment("法兰螺栓长度（单位：mm）");

                entity.Property(e => e.BoltManufacturer)
                    .HasMaxLength(255)
                    .HasColumnName("bolt_manufacturer")
                    .HasComment("法兰螺栓厂家");

                entity.Property(e => e.BoltModel)
                    .HasMaxLength(255)
                    .HasColumnName("bolt_model")
                    .HasComment("法兰螺栓型号");

                entity.Property(e => e.BoltPreTensionForce)
                    .HasPrecision(10, 2)
                    .HasColumnName("bolt_pre_tension_force")
                    .HasComment("法兰螺栓预紧力标定系数");

                entity.Property(e => e.ClampingLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("clamping_length")
                    .HasComment("夹持长度（单位： mm）");

                entity.Property(e => e.CompId)
                    .HasColumnType("int(11)")
                    .HasColumnName("comp_id")
                    .HasComment("对应的部件ID（外键）");

                entity.Property(e => e.CompType)
                    .HasMaxLength(255)
                    .HasColumnName("comp_type")
                    .HasComment("部件类型");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.FlangeLevel)
                    .HasColumnType("int(11)")
                    .HasColumnName("flange_level")
                    .HasComment("法兰层数（第几层法兰）");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .ValueGeneratedOnAddOrUpdate()
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");

                entity.HasOne(d => d.Comp)
                    .WithMany(p => p.WtParametersFlanges)
                    .HasForeignKey(d => d.CompId)
                    .HasConstraintName("wpt3");

                entity.HasOne(d => d.CompNavigation)
                    .WithMany(p => p.WtParametersFlanges)
                    .HasForeignKey(d => d.CompId)
                    .HasConstraintName("wpt2");
            });

            modelBuilder.Entity<WtParametersGearbox>(entity =>
            {
                entity.HasKey(e => e.StageId)
                    .HasName("PRIMARY");

                entity.ToTable("wt_parameters_gearbox");

                entity.HasComment("存储与齿轮箱每一级相关的详细参数");

                entity.HasIndex(e => e.NacelleId, "nnpg");

                entity.Property(e => e.StageId)
                    .HasColumnType("int(11)")
                    .HasColumnName("stage_id")
                    .HasComment("齿轮箱级别参数的唯一标识符");

                entity.Property(e => e.BearingId)
                    .HasMaxLength(50)
                    .HasColumnName("bearing_id")
                    .HasComment("轴承ID");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.LargeTeethCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("large_teeth_count")
                    .HasComment("大齿轮齿数（仅平行级适用）");

                entity.Property(e => e.NacelleId)
                    .HasColumnType("int(11)")
                    .HasColumnName("nacelle_id")
                    .HasComment("齿轮箱参数表的外键");

                entity.Property(e => e.PlanetCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("planet_count")
                    .HasComment("行星轮个数（仅行星级适用）");

                entity.Property(e => e.PlanetTeethCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("planet_teeth_count")
                    .HasComment("行星轮齿数（仅行星级适用）");

                entity.Property(e => e.RingTeethCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("ring_teeth_count")
                    .HasComment("内齿圈齿数（仅行星级适用）");

                entity.Property(e => e.SmallTeethCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("small_teeth_count")
                    .HasComment("小齿轮齿数（仅平行级适用）");

                entity.Property(e => e.StageNumber)
                    .HasColumnType("tinyint(4)")
                    .HasColumnName("stage_number")
                    .HasComment("级别编号（1、2、3）");

                entity.Property(e => e.StageType)
                    .HasMaxLength(10)
                    .HasColumnName("stage_type")
                    .HasComment("级别类型（行星级或平行级）");

                entity.Property(e => e.SunTeethCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("sun_teeth_count")
                    .HasComment("太阳轮齿数（仅行星级适用）");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .ValueGeneratedOnAddOrUpdate()
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");

                entity.HasOne(d => d.Nacelle)
                    .WithMany(p => p.WtParametersGearboxes)
                    .HasForeignKey(d => d.NacelleId)
                    .HasConstraintName("wt_parameters_gearbox_ibfk_1");

                entity.Ignore(t => t.BearingList);
            });

            modelBuilder.Entity<WtTowerParameter>(entity =>
            {
                entity.HasKey(e => e.TowerId)
                    .HasName("PRIMARY");

                entity.ToTable("wt_tower_parameters");

                entity.HasComment("存储与塔筒相关的参数，包括法兰和索力监测系统");

                entity.HasIndex(e => e.ModelId, "twtm");

                entity.Property(e => e.TowerId)
                    .HasColumnType("int(11)")
                    .HasColumnName("tower_id")
                    .HasComment("塔筒参数的唯一标识符");

                entity.Property(e => e.BaseDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("base_diameter")
                    .HasComment("塔基直径（单位：mm）");

                entity.Property(e => e.CableClusterCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("cable_cluster_count")
                    .HasComment("拉索簇数");

                entity.Property(e => e.CableLength)
                    .HasPrecision(10, 2)
                    .HasColumnName("cable_length")
                    .HasComment("拉索总长度（单位：m）");

                entity.Property(e => e.CablePerCluster)
                    .HasColumnType("int(11)")
                    .HasColumnName("cable_per_cluster")
                    .HasComment("每簇拉索根数");

                entity.Property(e => e.ConcreteTowerHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("concrete_tower_height")
                    .HasComment("混凝土塔筒高度（单位：mm）");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.DeflectionCoefficient)
                    .HasPrecision(10, 2)
                    .HasColumnName("deflection_coefficient")
                    .HasComment("挠度系数");

                entity.Property(e => e.FlangeCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("flange_count")
                    .HasComment("法兰层数");

                entity.Property(e => e.FlangeDetails)
                    .HasColumnType("text")
                    .HasColumnName("flange_details")
                    .HasComment("存储多层法兰的详细参数（嵌套结构）");

                entity.Property(e => e.GridFrequency)
                    .HasPrecision(10, 2)
                    .HasColumnName("grid_frequency")
                    .HasComment("叶轮并网转频（单位：Hz）");

                entity.Property(e => e.HasSwitch)
                    .HasColumnName("has_switch")
                    .HasComment("是否有换向器或摩擦片（1表示有，0表示无）");

                entity.Property(e => e.MaxGridFrequency)
                    .HasPrecision(10, 2)
                    .HasColumnName("max_grid_frequency")
                    .HasComment("叶轮最大转频（单位：Hz）");

                entity.Property(e => e.ModelId)
                    .HasMaxLength(50)
                    .HasColumnName("model_id")
                    .HasComment("对应的机组型号ID（外键）");

                entity.Property(e => e.SecondOrderNaturalFrequency)
                    .HasPrecision(10, 2)
                    .HasColumnName("second_order_natural_frequency")
                    .HasComment("塔筒理论二阶固有频率（单位：Hz）");

                entity.Property(e => e.SteelTowerHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("steel_tower_height")
                    .HasComment("钢制塔筒高度（单位：mm）");

                entity.Property(e => e.SwitchHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("switch_height")
                    .HasComment("换向器或摩擦片高度（单位：m）");

                entity.Property(e => e.TowerHeight)
                    .HasPrecision(10, 2)
                    .HasColumnName("tower_height")
                    .HasComment("塔筒总高度（单位：mm）");

                entity.Property(e => e.TowerNaturalFrequency)
                    .HasPrecision(10, 2)
                    .HasColumnName("tower_natural_frequency")
                    .HasComment("塔筒理论一阶固有频率（单位：Hz）");

                entity.Property(e => e.TowerNaturalFrequencyLower)
                    .HasPrecision(10, 2)
                    .HasColumnName("tower_natural_frequency_lower")
                    .HasComment("塔筒理论一阶固有频率下限（单位：Hz）");

                entity.Property(e => e.TowerNaturalFrequencyUpper)
                    .HasPrecision(10, 2)
                    .HasColumnName("tower_natural_frequency_upper")
                    .HasComment("塔筒理论一阶固有频率上限（单位：Hz）");

                entity.Property(e => e.TowerSectionCount)
                    .HasColumnType("int(11)")
                    .HasColumnName("tower_section_count")
                    .HasComment("塔筒节数");

                entity.Property(e => e.TowerType)
                    .HasMaxLength(50)
                    .HasColumnName("tower_type")
                    .HasComment("塔筒类型");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .ValueGeneratedOnAddOrUpdate()
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");

                entity.Property(e => e.WireDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("wire_diameter")
                    .HasComment("每条钢丝直径（单位：mm）");

                entity.Property(e => e.TopDiameter)
                    .HasPrecision(10, 2)
                    .HasColumnName("top_diameter")
                    .HasComment("塔顶直径（单位：mm）");

                entity.Property(e => e.WirePerCable)
                    .HasColumnType("int(11)")
                    .HasColumnName("wire_per_cable")
                    .HasComment("每根拉索的钢丝条数");
            });

            modelBuilder.Entity<WtGearboxModel>(entity =>
            {
                entity.HasKey(e => e.Id)
                .HasName("PRIMARY");

                entity.ToTable("wt_gearbox_model");

                entity.HasComment("存储齿轮箱厂家、型号及传动比信息");

                entity.Property(e => e.Id)
                    .HasColumnType("int(11)")
                    .HasColumnName("id")
                    .HasComment("齿轮箱信息的唯一标识符");

                entity.Property(e => e.Manufacturer)
                    .IsRequired()
                    .HasMaxLength(50)
                    .HasColumnName("manufacturer")
                    .HasComment("齿轮箱厂家");

                entity.Property(e => e.Model)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("model")
                    .HasComment("齿轮箱型号");

                entity.Property(e => e.GearRatio)
                    .HasColumnType("decimal(10,3)")
                    .HasColumnName("gear_ratio")
                    .HasComment("传动比");

                entity.Property(e => e.CreatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("created_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录创建时间");

                entity.Property(e => e.UpdatedAt)
                    .HasColumnType("timestamp")
                    .HasColumnName("updated_at")
                    .HasDefaultValueSql("CURRENT_TIMESTAMP")
                    .HasComment("记录更新时间");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
