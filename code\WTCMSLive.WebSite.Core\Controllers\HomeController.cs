﻿using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MySqlX.XDevAPI;
using System.Diagnostics;
using System.Web;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Models;
using System.Security.Claims;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Swashbuckle.AspNetCore.Annotations;
using WTCMSLive.WebSite.Core.Helpers;

namespace WTCMSLive.WebSite.Core.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class HomeController : ControllerBase
    {
        private readonly IConfiguration _configuration;

        public HomeController(IConfiguration configuration)
        {
            _configuration = configuration;
        }


        #region 系统日志

        /// <summary>
        /// 系统日志类型
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetSystemLogType")]
        public IActionResult GetSystemLogType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumLogType value in Enum.GetValues(typeof(EnumLogType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }


        /// <summary>
        /// 获取系统日志信息
        /// </summary>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <param name="logType"></param>
        /// <returns></returns>
        /// 
        [HttpGet("systemLog")]
        public IActionResult ShowSystemLog(string beginTime, string endTime, int logType)
        {
            try
            {
                LogQueryCondition _logQC = new LogQueryCondition();
                _logQC.StartTime = Convert.ToDateTime(beginTime);
                _logQC.EndTime = Convert.ToDateTime(endTime);
                _logQC.LogDB = logType;
                List<SystemRunningLog> logList = LogManagement.GetSystemRunningLogList(_logQC);

                return Ok(logList);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[ShowSystemLog]系统日志", ex);
            }
            return Ok(new List<SystemRunningLog>());
        }

       
        #endregion 系统日志
 
      
        /// <summary>
        /// 登录接口，jwt token
        /// </summary>
        /// <param name="account"></param>
        /// <param name="password"></param>
        /// <param name="remember"></param>
        /// <returns></returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromForm] string account, [FromForm] string password)
        {
            try
            {
                if(string.IsNullOrEmpty(account) || string.IsNullOrEmpty(password))
                {
                    return Ok(ApiResponse<LoginResponse>.Error("请输出正确的账号密码！"));
                }


                User user = UserManagement.Login(account, password);
                if (user == null)
                {
                    return Ok(ApiResponse<LoginResponse>.Error("登录失败，账号或密码错误！"));
                }

                // 获取用户的角色和模块信息
                var userRole = RoleModuleHelper.GetUserRoleAndModules(user.UserID);

                var jwtSettings = _configuration.GetSection("JwtSettings").Get<JwtSettings>();
                var key = Encoding.ASCII.GetBytes(jwtSettings.SecretKey);
                var tokenHandler = new JwtSecurityTokenHandler();
                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Subject = new ClaimsIdentity(new[]
                    {
                        new Claim(ClaimTypes.Name, account),
                        new Claim(ClaimTypes.NameIdentifier, user.UserID.ToString()),
                        new Claim("role", userRole.RoleName) // 使用实际的角色名称
                    }),
                    Expires = DateTime.UtcNow.AddMinutes(jwtSettings.ExpirationInMinutes),
                    SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                    Issuer = jwtSettings.Issuer,
                    Audience = jwtSettings.Audience
                };

                var token = tokenHandler.CreateToken(tokenDescriptor);
                var tokenString = tokenHandler.WriteToken(token);

                var loginResponse = new LoginResponse
                {
                    Token = tokenString,
                    UserName = user.UserName,
                    UserId = user.UserID.ToString(),
                    Role = userRole.RoleName, // 使用实际的角色名称
                    Expiration = DateTime.UtcNow.AddMinutes(jwtSettings.ExpirationInMinutes),
                    UserRole = userRole, // 添加完整的角色信息
                    
                };

                return Ok(ApiResponse<LoginResponse>.Success(loginResponse, "登录成功！"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[Login]登录失败", ex);
                return Ok(ApiResponse<LoginResponse>.Error("登录错误，请检查系统日志！"));
            }
        }

        /// <summary>
        /// 获取版本号
        /// </summary>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        [HttpGet("version")]
        public string GetVersion()
        {
            return System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
        }

        /// <summary>
        /// 设备树获取
        /// </summary>
        /// <returns></returns>
        [HttpGet("tree")]
        public ActionResult GetTree()
        {
            TreeManager treeManager = new TreeManager();
            List<TreeModel> treeList = new List<TreeModel>();
            try
            {
                treeList = treeManager.GetTreeModel();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetTree]取得左侧树菜单失败", ex);
            }
            return Ok(treeList);
        }
        
    }
}
