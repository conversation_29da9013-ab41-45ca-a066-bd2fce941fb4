﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WTCMSLive.BusinessModel
{
    public class ConstDefine
    {
        public const string AccessDeniedError = "可能是由于登录超时，或者没有操作该功能的权限！";
        public const string ALARM_DEFTYPE_ENV = "ENVALARMDEFINITION";
        public const string ALARM_DEFTYPE_ORDER = "ORDERALARMDEFINITION";
        public const string ALARM_DEFTYPE_ORDERENV = "ORDERENVALARMDEFINITION";
        public const string ALARM_DEFTYPE_TIME = "TIMEALARMDEFINITION";
        public const int AlarmEventLog = 4;
        public const string ALRMENVENT_LOGTYPE_DIAGNOSIS = "诊断报告";
        public const string ALRMENVENT_LOGTYPE_DISCRIPTION = "报警备注";
        public const string ALRMENVENT_LOGTYPE_MAINTENANCE = "维修报告";
        public const string ALRMENVENT_STATE_DIAGNOSIS = "诊断中";
        public const string ALRMENVENT_STATE_FINISHED = "已完成";
        public const string ALRMENVENT_STATE_IGNORE = "已忽略";
        public const string ALRMENVENT_STATE_MAINTENANCE = "维修中";
        public const string ALRMENVENT_STATE_UNHANDLED = "未处理";
        public const string ApplicationError = "网页发生错误！";
        public const int DAUManagementLog = 2;
        public const int DevTreeManagementLog = 3;
        public const string FIELDBUSTYPE_DAU = "DAU";
        public const string FREQBAND_OVERALL = "通带";
        public const string MEASDEF_DEFTYPE_ENV = "MEASDEFINITION_ENVLOPE";
        public const string MEASDEF_DEFTYPE_ORDER = "MEASDEFINITION_ORDER";
        public const string MEASDEF_DEFTYPE_ORDERENV = "MEASDEFINITION_ORDERENV";
        public const string MEASDEF_DEFTYPE_TIME = "MEASDEFINITION_TIME";
        public const string MODBUSONTCP = "ModbusOnTcp";
        public const string NotFoundError = "您访问的信息未找到，可能已被其他用户删除！";
        public const int PageSize = 30;
        public const int SVMManagementLog = 6;
        public const int SystemRunningLog = 5;
        public const string TRENDCHART_NAME_ALARM = "危险";
        public const string TRENDCHART_NAME_DCDATA = "直流分量趋势";
        public const string TRENDCHART_NAME_HIS = "趋势";
        public const string TRENDCHART_NAME_MAXBIASVOLT = "最高偏置电压";
        public const string TRENDCHART_NAME_MINBIASVOLT = "最低偏置电压";
        public const string TRENDCHART_NAME_NEGATIVEALARM = "危险(负)";
        public const string TRENDCHART_NAME_NEGATIVEWARNNING = "注意(负)";
        public const string TRENDCHART_NAME_WARNNING = "注意";
        public const string USER_STATE_DISABLE = "失效";
        public const string USER_STATE_ENABLE = "正常";
        public const int UserManagementLog = 1;
    }
}
