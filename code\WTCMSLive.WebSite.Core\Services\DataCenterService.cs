using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Text;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 数据中心服务实现
    /// </summary>
    public class DataCenterService : IDataCenterService
    {
        private readonly HttpClient _httpClient;
        private readonly DataCenterOptions _options;
        private readonly ILogger<DataCenterService> _logger;

        public DataCenterService(
            HttpClient httpClient,
            IOptions<DataCenterOptions> options,
            ILogger<DataCenterService> logger)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 配置HttpClient
            _httpClient.BaseAddress = new Uri(_options.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "WindCMS-DataCenter-Client/1.0");
        }

        /// <summary>
        /// 获取集团公司代码列表
        /// </summary>
        public async Task<ApiResponse<List<GroupCompanyCodeDTO>>> GetGroupCompanyCodesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("开始获取集团公司代码列表");
                
                var response = await GetAsync<List<GroupCompanyCodeDTO>>("GroupCompanyCode", cancellationToken: cancellationToken);
                
                _logger.LogInformation("成功获取集团公司代码列表，共 {Count} 条记录", response.Data?.Count ?? 0);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取集团公司代码列表失败");
                return ApiResponse<List<GroupCompanyCodeDTO>>.Error($"获取集团公司代码列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据代码获取集团公司信息
        /// </summary>
        public async Task<ApiResponse<GroupCompanyCodeDTO>> GetGroupCompanyByCodeAsync(string code, CancellationToken cancellationToken = default)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(code))
                {
                    return ApiResponse<GroupCompanyCodeDTO>.Error("公司代码不能为空");
                }

                _logger.LogInformation("开始获取集团公司信息，代码: {Code}", code);
                
                var parameters = new Dictionary<string, string> { { "code", code } };
                var response = await GetAsync<GroupCompanyCodeDTO>("GroupCompanyCode/GetByCode", parameters, cancellationToken);
                
                _logger.LogInformation("成功获取集团公司信息，代码: {Code}", code);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取集团公司信息失败，代码: {Code}", code);
                return ApiResponse<GroupCompanyCodeDTO>.Error($"获取集团公司信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 通用GET请求方法
        /// </summary>
        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint, Dictionary<string, string>? parameters = null, CancellationToken cancellationToken = default)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                var url = BuildUrl(endpoint, parameters);
                _logger.LogDebug("发送GET请求: {Url}", url);

                var response = await _httpClient.GetAsync(url, cancellationToken);
                return await ProcessResponseAsync<T>(response);
            });
        }

        /// <summary>
        /// 通用POST请求方法
        /// </summary>
        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object? data = null, CancellationToken cancellationToken = default)
        {
            return await ExecuteWithRetryAsync(async () =>
            {
                _logger.LogDebug("发送POST请求: {Endpoint}", endpoint);

                StringContent? content = null;
                if (data != null)
                {
                    var json = JsonConvert.SerializeObject(data);
                    content = new StringContent(json, Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.PostAsync(endpoint, content, cancellationToken);
                return await ProcessResponseAsync<T>(response);
            });
        }

        /// <summary>
        /// 检查数据中心连接状态
        /// </summary>
        public async Task<bool> CheckConnectionAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("检查数据中心连接状态");
                
                var response = await _httpClient.GetAsync("health", cancellationToken);
                var isConnected = response.IsSuccessStatusCode;
                
                _logger.LogInformation("数据中心连接状态: {Status}", isConnected ? "正常" : "异常");
                return isConnected;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "检查数据中心连接状态失败");
                return false;
            }
        }

        /// <summary>
        /// 构建URL
        /// </summary>
        private static string BuildUrl(string endpoint, Dictionary<string, string>? parameters)
        {
            if (parameters == null || !parameters.Any())
                return endpoint;

            var queryString = string.Join("&", parameters.Select(p => $"{Uri.EscapeDataString(p.Key)}={Uri.EscapeDataString(p.Value)}"));
            return $"{endpoint}?{queryString}";
        }

        /// <summary>
        /// 处理HTTP响应
        /// </summary>
        private async Task<ApiResponse<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("HTTP请求失败: {StatusCode} - {Content}", response.StatusCode, content);
                return ApiResponse<T>.Error($"HTTP请求失败: {response.StatusCode}");
            }

            try
            {
                // 尝试解析为数据中心标准响应格式
                var dataCenterResponse = JsonConvert.DeserializeObject<DataCenterResponseDTO<T>>(content);
                if (dataCenterResponse != null)
                {
                    if (dataCenterResponse.IsSuccess)
                    {
                        return ApiResponse<T>.Success(dataCenterResponse.Data!, dataCenterResponse.Message);
                    }
                    else
                    {
                        return ApiResponse<T>.Error(dataCenterResponse.Message);
                    }
                }

                // 如果不是标准格式，直接解析为目标类型
                var directData = JsonConvert.DeserializeObject<T>(content);
                return ApiResponse<T>.Success(directData!, "Success");
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "解析响应数据失败: {Content}", content);
                return ApiResponse<T>.Error($"解析响应数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 带重试机制的执行方法
        /// </summary>
        private async Task<ApiResponse<T>> ExecuteWithRetryAsync<T>(Func<Task<ApiResponse<T>>> operation)
        {
            var lastException = new Exception("未知错误");
            
            for (int attempt = 1; attempt <= _options.RetryCount + 1; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    
                    if (attempt <= _options.RetryCount)
                    {
                        _logger.LogWarning(ex, "请求失败，第 {Attempt} 次重试，{Delay} 秒后重试", attempt, _options.RetryDelaySeconds);
                        await Task.Delay(TimeSpan.FromSeconds(_options.RetryDelaySeconds));
                    }
                    else
                    {
                        _logger.LogError(ex, "请求失败，已达到最大重试次数 {MaxRetries}", _options.RetryCount);
                    }
                }
            }

            return ApiResponse<T>.Error($"请求失败: {lastException.Message}");
        }
    }
}
