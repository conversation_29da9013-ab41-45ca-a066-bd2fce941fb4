import{u as ee,W as te}from"./table-CWAoXMlp.js";import{O as ae}from"./index-QAVfwe37.js";import{W as le}from"./index-LnZyOkjv.js";import{C as se,cs as re,ct as ne,cu as oe,cv as ie,cw as ue,cx as de,r as p,u as ce,y as pe,w as me,j as he,f as k,d as D,o as w,c as B,b as C,g as G,m as b,aP as O}from"./index-ClUwy-U2.js";import{a as N,S as fe,d as $,f as ve}from"./tools-BpvUNSHS.js";import{u as be}from"./devTree-BfAcuD8A.js";import{B as ye}from"./index-ByAZPsB5.js";import{M as ge}from"./index-DkT7RQa1.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-p4-l6Zij.js";import"./styleChecker-D2z1GQZd.js";import"./initDefaultProps-CbhiVpW4.js";import"./shallowequal-DorGB6RW.js";import"./index-CUxYOaAq.js";import"./index-Cg9xkC5X.js";import"./index-B9pLYs_L.js";import"./index-BUWlNxdP.js";import"./index-CgagFv0b.js";/* empty css                                                              */const De=se("alarmDefinition",{state:()=>({alarmDefineList:[],measLocations:[],eigenValueTypeList:[]}),actions:{reset(){this.$reset()},async fetchGetAlarmDefineList(d){try{const t=await de(d);return t&&t.length>0&&(this.alarmDefineList=t),t}catch(t){throw console.error("获取失败:",t),t}},async fetchMeasLocations(d){try{const t=await ue(d);let m=N(t,{label:"measLocName",value:"measLocationID"});return this.deviceOptions=m,m}catch(t){throw console.error("获取失败:",t),t}},async fetchGetEigenValueTypeList(d){try{const t=await ie(d);let m=N(t,{label:"eigenValueName",value:"eigenValueID"},{nother:!0});return this.eigenValueTypeList=m,m}catch(t){throw console.error("获取失败:",t),t}},async fetchWarnBatchAddWarnRulee(d){try{return await oe(d)}catch(t){throw console.error(t),t}},async fetchEditWarnRule(d){try{return await ne(d)}catch(t){throw console.error(t),t}},async fetchBatchDeleteWarnRule(d){try{return await re(d)}catch(t){throw console.error(t),t}}}}),we={key:2},$e={__name:"alarmDefinition",setup(d){const t=De(),m=be(),q=ee(),E=async(e,s)=>{let l=null,a="warnValue",r="alarmValue";if(u.value==="edit")l=f.value.getFieldsValue();else{l=v.value.getTableFieldsValue();let g=e.field.match(/\d+/)[0];a=`warnValue[${g}]`,r=`alarmValue[${g}]`}if(!l)return;const n=parseFloat(l[a]),o=parseFloat(l[r]);if(n!==void 0&&o!==void 0&&n>o)return Promise.reject(new Error("正向危险值不能小于正向注意值"));let y=e.field==a?r:a;return u.value==="edit"?f.value.clearValidate(y):v.value.clearValidate(y),Promise.resolve()},F=async(e,s)=>{let l=null,a="reverseWarnValue",r="reverseAlarmValue";if(u.value==="edit")l=f.value.getFieldsValue();else{l=v.value.getTableFieldsValue();let g=e.field.match(/\d+/)[0];a=`reverseWarnValue[${g}]`,r=`reverseAlarmValue[${g}]`}if(!l)return;const n=parseFloat(l[a]),o=parseFloat(l[r]);if(n!==void 0&&o!==void 0&&n<o)return Promise.reject(new Error("反向危险值不能大于反向注意值"));let y=e.field==a?r:a;return u.value==="edit"?f.value.clearValidate(y):v.value.clearValidate(y),Promise.resolve()},V=(e={isform:!1,idEdit:!1})=>{let s=320;return[{title:"测量位置",dataIndex:"measLocationID",columnWidth:150,formItemWidth:s,labelInValue:!e.idEdit,isrequired:!e.idEdit,inputType:"select",selectOptions:[],hasChangeEvent:!0,isdisplay:!e.idEdit,...e.isform?{}:{customRender:({text:l,record:a})=>a.measLocationName}},{title:"特征值",dataIndex:"eigenValueID",columnWidth:160,formItemWidth:s,tableList:[],isdisplay:!e.idEdit,inputType:"select",selectOptions:[],isrequired:!e.idEdit,...e.isform?{}:{customRender:({text:l,record:a})=>a.eigenValueName}},{title:"工况参数",dataIndex:"workConditionParams",columnWidth:120,formItemWidth:s,isrequired:!e.idEdit,isdisplay:!e.idEdit,afterContent:!0,headerOperations:{filters:[],filterDataIndex:["workConParameterName"]},inputType:"select",selectOptions:[{label:"功率(KW)",value:"功率"},{label:"转速(RPM)",value:"转速"},{label:"温度(°C)",value:"温度"}],...e.isform?{}:{customRender:({text:l,record:a})=>a.workConParameterName}},{title:"工况下限",dataIndex:"lowerLimitValue",columnWidth:80,formItemWidth:s,isdisplay:!e.idEdit,validateRules:$({title:"工况下限",type:"number",required:!e.idEdit})},{title:"工况上限",dataIndex:"upperLimitValue",columnWidth:80,formItemWidth:s,isdisplay:!e.idEdit,validateRules:$({title:"工况下限",type:"number",required:!e.idEdit})},{title:"正向注意",dataIndex:"warnValue",columnWidth:80,formItemWidth:s,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:E,trigger:"change"}]},{title:"正向危险",dataIndex:"alarmValue",columnWidth:80,formItemWidth:s,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:E,trigger:"change"}]},{title:"反向注意",dataIndex:"reverseWarnValue",columnWidth:80,formItemWidth:s,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:F,trigger:"change"}]},{title:"反向危险",dataIndex:"reverseAlarmValue",columnWidth:80,formItemWidth:s,validateRules:[{pattern:/^[0-9]+(\.[0-9]+)?$/,message:"请输入数字"},{validator:F,trigger:"change"}]}]},W=p(!1),I=p(""),u=p(""),f=p(),L=p({}),h=p([]),x=p(!1),_=ce(),c=p(_.params.id),R=p(""),i=pe({tableColumns:V(),tableData:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),v=p({}),A=async e=>{W.value=!0,i.tableData=await t.fetchGetAlarmDefineList({turbineID:c.value}),i.tableColumns=V(),W.value=!1},M=async e=>{R.value&&await q.fetchDevTreedDevicelist({windParkID:R.value,useTobath:!0})},K=()=>{let e=m.findAncestorsWithNodes(c.value);e&&e.length&&e.length>1&&(R.value=e[e.length-2].id)};me(()=>_.params.id,async e=>{e&&(t.reset(),c.value=e,K(),await M(),A())},{immediate:!0});const j=he(()=>u.value==="add"||u.value==="batchAdd"?"1200px":"600px"),P=()=>{x.value=!0},T=e=>{x.value=!1,h.value=[],L.value={},u.value="",I.value=""},z=e=>{const{tableKey:s,title:l,operateType:a}=e;u.value=a,I.value="批量添加报警定义",h.value=[...V({isform:!0})],J(),P()},U=async e=>{const{selectedkeys:s,record:l}=e;let a=[];if(!s||s.length===0)return;let r=t.alarmDefineList.filter(o=>s.includes(o.thresholdGroup));if(!r||r.length===0)return;for(let o=0;o<r.length;o++)a.push({windTurbineID:c.value,workConParameter:r[o].workConParameter,thresholdGroup:r[o].thresholdGroup,eigenValueID:r[o].eigenValueID,measLocationID:r[o].measLocationID});const n=await t.fetchBatchDeleteWarnRule({sourceData:a,targetTurbineIds:i.batchApplyData});n&&n.code===1?(A(),i.bathApplyResponse1=n.batchResults||{},b.success("删除成功")):b.error("删除失败:"+n.msg)},H=e=>{const{rowData:s,tableKey:l,title:a,operateType:r}=e;u.value=r,I.value="编辑报警定义",h.value=[...V({isform:!0,idEdit:!0})],L.value={...s},P()},S=async(e,s,l)=>{let a=h.value,r=e.value.value;if(e&&e.dataIndex&&e.dataIndex=="measLocationID"&&u.value=="batchAdd"){let n=await t.fetchGetEigenValueTypeList({turbineID:c.value,measLocId:r});if(u.value=="batchAdd"){if(e.index>=a[1].tableList.length)for(let o=a[1].tableList.length;o<=e.index;o++)a[1].tableList.push({});a[1].tableList[e.index].selectOptions=n,n&&n.length&&v.value.setTableFieldValue({formDataIndex:`eigenValueID[${e.index}]`,tableDataIndex:"eigenValueID",index:e.index,value:n&&n.length?n[0].value:""})}else a[1].selectOptions=n,f.value.setFieldValue("eigenValueID",n&&n.length?n[0].value:"")}},J=async e=>{let s=h.value,l=await t.fetchMeasLocations({turbineID:c.value});s[0].selectOptions=l;let a="";if(a=l&&l.length?l[0].value:"",l&&l.length){let r=await t.fetchGetEigenValueTypeList({turbineID:c.value,measLocId:a});s[1].selectOptions=r}},Q=async e=>{let s={...e,thresholdGroup:L.value.thresholdGroup,WindTurbineID:c.value,applyToAll:!1},l=await t.fetchEditWarnRule({sourceData:s,targetTurbineIds:i.batchApplyData});l&&l.code===1?(A(),i.bathApplyResponse1=l.batchResults||{},b.success("提交成功"),T()):b.error("提交失败:"+l.msg)},X=async e=>{let s={measLocationID:{option:["measLocType","measLocType"],value:"measLocationID"}},l=ve(e,{applyToAll:!1,windParkID:R.value,windTurbineID:c.value},s);if(l&&l.length){let a=await t.fetchWarnBatchAddWarnRulee({sourceData:l,targetTurbineIds:i.batchApplyData});a&&a.code===1?(A(),i.bathApplyResponse1=a.batchResults||{},b.success("提交成功"),T()):b.error("提交失败:"+a.msg)}},Y=async e=>{},Z=async e=>{e.type&&e.type=="close"?(i.batchApplyData=[],i.batchApplyKey="",i[`bathApplyResponse${e.key}`]={}):(i.batchApplyData=e.turbines,i.batchApplyKey=e.key)};return O("deviceId",c),O("bathApplySubmit",Z),(e,s)=>{const l=ye,a=ge,r=fe;return w(),k(r,{spinning:W.value,size:"large"},{default:D(()=>[(w(),B("div",{key:c.value},[C(te,{ref:"table",size:"default","table-key":"1","table-title":"报警设置列表",borderLight:i.batchApplyKey=="1",bathApplyResponse:i.bathApplyResponse1,"table-columns":i.tableColumns,"table-operate":["delete","add","edit","batchDelete","batchAdd"],"record-key":"thresholdGroup","table-datas":i.tableData,onAddRow:z,onDeleteRow:U,onEditRow:H},{rightButtons:D(({selectedRowKeys:n})=>[C(l,{type:"primary",onClick:s[0]||(s[0]=o=>Y())},{default:D(()=>s[1]||(s[1]=[G(" 初始化报警定义 ",-1)])),_:1,__:[1]})]),default:D(()=>[s[2]||(s[2]=G(" > ",-1))]),_:1,__:[2]},8,["borderLight","bathApplyResponse","table-columns","table-datas"]),C(a,{maskClosable:!1,width:j.value,open:x.value,title:I.value,footer:"",onCancel:T},{default:D(()=>[u.value==="add"||u.value==="edit"?(w(),k(ae,{key:0,titleCol:h.value,ref_key:"formRef",ref:f,initFormData:L.value,onChange:S,onSubmit:Q},null,8,["titleCol","initFormData"])):u.value==="batchAdd"?(w(),k(le,{key:1,ref_key:"tableFormRef",ref:v,size:"default","table-key":"0","table-columns":h.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":["measLocationID","eigenValueID"],onSubmit:X,onHangeTableFormChange:S,onCancel:T},null,8,["table-columns"])):(w(),B("div",we))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{$e as default};
