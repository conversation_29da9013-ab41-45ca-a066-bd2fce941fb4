import{r as d,w as v,f as p,x as w,d as b,u as C,o as e,i as s,c as r,s as L,g as n,t as a,F as N,e as B}from"./index-D9CxWmlM.js";import{u as _}from"./statusMonitor-CqkPK8Q-.js";import{N as T}from"./noPermission-BBS3T4wn.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as x}from"./tools-DZBuE28U.js";import"./initDefaultProps-C2vKchlZ.js";const k={class:"border padding totalInfo"},P={class:"totalNumber"},D={class:"clearfix totalList"},V={class:"totalNumber pullLeft"},F={class:"pullLeft clearfix"},M={class:"parkNumber pullLeft redBg"},c={class:"parkNumber pullLeft yellowBg"},z={class:"parkNumber pullLeft greenBg"},E={class:"parkNumber pullLeft grayBg"},G={key:0,class:"clearfix parkList"},J={class:"title"},O={class:"number"},R={__name:"status",setup(j){const f=_(),l=d({});let i=(window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[]).includes("21");const g=C(),o=d(!1),S=async()=>{o.value=!0,l.value=await f.fetchGetParkStatusCount(),o.value=!1};return v(()=>g.params.id,()=>{i&&S()},{immediate:!0}),(A,t)=>{const m=x;return w(i)?(e(),p(m,{key:0,spinning:o.value,size:"large"},{default:b(()=>[s("div",null,[s("div",k,[s("p",P,[t[0]||(t[0]=n("厂站总数 ",-1)),s("b",null,a(l.value.totalParkCount||""),1)]),s("div",D,[s("p",V,[t[1]||(t[1]=n(" 设备总数 ",-1)),s("b",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.totalCount:""),1)]),s("ul",F,[s("li",M,[t[2]||(t[2]=n("危险 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.dangerCount:""),1)]),s("li",c,[t[3]||(t[3]=n("注意 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.warningCount:""),1)]),s("li",z,[t[4]||(t[4]=n("正常 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.normalCount:""),1)]),s("li",E,[t[5]||(t[5]=n("其他 ",-1)),s("span",null,a(l.value.totalTurbineStatus?l.value.totalTurbineStatus.otherCount:""),1)])])])]),l.value.parkDetailList&&l.value.parkDetailList.length?(e(),r("ul",G,[(e(!0),r(N,null,B(l.value.parkDetailList,u=>(e(),r("li",{class:"border padding pullLeft card",key:u.windParkID},[s("p",J,a(u.windParkName),1),s("p",O,[s("b",null,a(u.turbineStatus?u.turbineStatus.totalCount:0),1),t[6]||(t[6]=n(" 台",-1))]),s("ul",null,[s("li",null,[t[7]||(t[7]=s("span",{class:"redBg"},"危险",-1)),t[8]||(t[8]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.dangerCount:0),1)]),s("li",null,[t[9]||(t[9]=s("span",{class:"yellowBg"},"注意",-1)),t[10]||(t[10]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.warningCount:0),1)]),s("li",null,[t[11]||(t[11]=s("span",{class:"greenBg"},"正常",-1)),t[12]||(t[12]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.normalCount:0),1)]),s("li",null,[t[13]||(t[13]=s("span",{class:"grayBg"},"其他",-1)),t[14]||(t[14]=n()),s("b",null,a(u.turbineStatus?u.turbineStatus.otherCount:0),1)])])]))),128))])):L("",!0)])]),_:1},8,["spinning"])):(e(),p(T,{key:1},{default:b(()=>t[15]||(t[15]=[n(" 无权限 ",-1)])),_:1,__:[15]}))}}},Y=y(R,[["__scopeId","data-v-d3538d7c"]]);export{Y as default};
