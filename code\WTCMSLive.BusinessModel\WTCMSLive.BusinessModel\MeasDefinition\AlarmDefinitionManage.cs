﻿using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 报警设置
    /// </summary>
    public static class AlarmDefinitionManage
    {
        /// <summary>
        /// 获取机组报警定义
        /// </summary>
        /// <param name="_WindTurbineID"></param>
        /// <returns></returns>
        public static List<AlarmDefinition> GetAlarmDefListByTurID(string _WindTurbineID)
        {
            List<AlarmDefinition> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmDefLists.Where(item => item.WindTurbineID == _WindTurbineID).ToList();
                List<AlarmDefThreshold> alarmDefHold = ctx.AlarmDefThresholdGroupLists.Where(item => item.WindTurbineID == _WindTurbineID).ToList();
                list.ForEach(item => {
                    item.AlarmDefThresholdGroup = alarmDefHold.FindAll(c => c.ThresholdGroup == item.ThresholdGroup);
                });
            }
            return list;
        }

        /// <summary>
        /// 获取工况的下限
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="MeasLocType"></param>
        /// <param name="_MeasLocId"></param>
        /// <param name="_EigenValueId"></param>
        /// <param name="_WorkCon"></param>
        /// <returns></returns>
        public static string GetMDFAlarmDefThresholdGroupMaxList(string _EigenValueId, short _WorkCon)
        {
            AlarmDefinition alarmDef = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmDef = ctx.AlarmDefLists.Where(item =>item.EigenValueID == _EigenValueId && item.WorkConParameter == _WorkCon).OrderByDescending(item=>item.UpperLimitValue).FirstOrDefault();
            }
            if (alarmDef != null)
            {
                return Math.Round(alarmDef.UpperLimitValue, 0, MidpointRounding.AwayFromZero).ToString();
            }
            return "0";
        }
        /// <summary>
        /// 获取报警定义实体
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="_MeasLocId"></param>
        /// <param name="_EigenValueId"></param>
        /// <param name="_WorkCon"></param>
        /// <param name="MeasLocType"></param>
        /// <returns></returns>
        public static List<AlarmDefinition> GetAlarmDefinition(string _EigenValueId, short _WorkCon)
        {
            List<AlarmDefinition> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmDefLists.Where(item => item.EigenValueID == _EigenValueId && item.WorkConParameter == _WorkCon).ToList();
            }
            return list;
        }
        /// <summary>
        /// 获取机组下的报警定义
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="MeasLocType"></param>
        /// <returns></returns>
        public static List<AlarmDefinition> GetMDFAlarmDefListByTurbineId(string _turbineId, EnumMeasLocType MeasLocType)
        {
            List<AlarmDefinition> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmDefLists.Where(item => item.WindTurbineID == _turbineId && item.MeasLocType == MeasLocType).ToList();
                List<AlarmDefThreshold> alarmDefHold = ctx.AlarmDefThresholdGroupLists.Where(item => item.WindTurbineID == _turbineId).ToList();
                list.ForEach(item =>
                {
                    item.AlarmDefThresholdGroup = alarmDefHold.FindAll(c => c.ThresholdGroup == item.ThresholdGroup);
                });
            }
            return list;
        }

        /// <summary>
        /// 获取机组报警定义
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="Groupid"></param>
        /// <returns></returns>
        public static AlarmDefinition GetMDFAlarmDefByTurbineId(string _turbineId, string Groupid)
        {
            AlarmDefinition alarm = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarm = ctx.AlarmDefLists.FirstOrDefault(item => item.WindTurbineID == _turbineId && item.ThresholdGroup == Groupid);
                if (alarm != null)
                {
                    alarm.AlarmDefThresholdGroup = ctx.AlarmDefThresholdGroupLists.Where(item => item.WindTurbineID == _turbineId && item.ThresholdGroup == alarm.ThresholdGroup).ToList();
                }
            }
            return alarm;
        }
        public static AlarmDefinition GetMDFAlarmDefByTurbineId(string _turbineId, string measloc,string evid, short wcparm, EnumMeasLocType measType)
        {
            AlarmDefinition alarm = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarm = ctx.AlarmDefLists.FirstOrDefault(item => item.WindTurbineID == _turbineId && item.MeasLocationID == measloc && item.EigenValueID == evid && item.MeasLocType == measType && item.WorkConParameter == wcparm);
                
            }
            return alarm;
        }

        /// <summary>
        /// 添加报警定义
        /// </summary>
        /// <param name="myDefin"></param>
        public static void AddAlarmDefinition(List<AlarmDefinition> myDefin)
        {
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            { 
                List<AlarmDefThreshold> thresholdList = new List<AlarmDefThreshold>();
                myDefin.ForEach(item=>{
                    thresholdList.AddRange(item.AlarmDefThresholdGroup);
                });
                ctx.AlarmDefLists.AddRange(myDefin);
                ctx.AlarmDefThresholdGroupLists.AddRange(thresholdList);
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 获取阈值组信息
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="ThresholdGroup"></param>
        /// <returns></returns>
        public static AlarmDefinition GetMDFAlarmDefThresholdGroupListbyGroupId(string _turbineId, string ThresholdGroup)
        {
            AlarmDefinition alarmDef = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmDef = ctx.AlarmDefLists.Where(item => item.WindTurbineID == _turbineId && item.ThresholdGroup == ThresholdGroup).FirstOrDefault();
                List<AlarmDefThreshold> alarmDefHold = ctx.AlarmDefThresholdGroupLists.Where(item => item.ThresholdGroup == ThresholdGroup).ToList();
                alarmDef.AlarmDefThresholdGroup = alarmDefHold;
            }
            return alarmDef;
        }
        /// <summary>
        /// 更新阈值组信息
        /// </summary>
        /// <param name="myDefin"></param>
        public static void updateMDFAlarmDefThresholdGroup(List<AlarmDefThreshold> myDefin)
        {
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                myDefin.ForEach(item => {
                    ctx.AlarmDefThresholdGroupLists.Attach(item);
                    ctx.Entry(item).State = EntityState.Modified;
                });
                ctx.SaveChanges();
            }
        }

        public static void updateMDFAlarmDefThresholdGroup(string turbineID,string groupID,List<AlarmDefThreshold> myDefin)
        {
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {

                var alarmList = ctx.AlarmDefThresholdGroupLists.Where(t=>t.WindTurbineID == turbineID && t.ThresholdGroup == groupID);
                ctx.AlarmDefThresholdGroupLists.RemoveRange(alarmList);
                ctx.AlarmDefThresholdGroupLists.AddRange(myDefin);
                //myDefin.ForEach(item => {
                //    ctx.AlarmDefThresholdGroupLists.Attach(item);
                //    ctx.Entry(item).State = EntityState.Modified;
                //});
                ctx.SaveChanges();
            }
        }
        /// <summary>
        /// 删除报警定义
        /// </summary>
        /// <param name="myDefin"></param>
        public static void DeleteAlarmDefinition(string EigenValueID,short WorkConParameter,string ThresholdGroup)
        {
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                ctx.AlarmDefLists.RemoveRange(ctx.AlarmDefLists.Where(item => item.ThresholdGroup == ThresholdGroup));
                ctx.AlarmDefThresholdGroupLists.RemoveRange(ctx.AlarmDefThresholdGroupLists.Where(item => item.ThresholdGroup == ThresholdGroup));
                ctx.SaveChanges();
            }
        }

        /// <summary>
        /// 根据振动测量位置ID获取报警组信息
        /// </summary>
        /// <param name="_turbineId"></param>
        /// <param name="_MeasLocId"></param>
        /// <returns></returns>
        public static List<AlarmDefinition> GetAlarmDefListByTruidAndLocID(string _turbineId, string _MeasLocId)
        {
            List<AlarmDefinition> list = null;
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                list = ctx.AlarmDefLists.Where(item => item.MeasLocationID == _MeasLocId).ToList();
                List<AlarmDefThreshold> alarmDefHold = ctx.AlarmDefThresholdGroupLists.Where(item => item.WindTurbineID == _turbineId).ToList();
                list.ForEach(item =>
                {
                    item.AlarmDefThresholdGroup = alarmDefHold.FindAll(c => c.ThresholdGroup == item.ThresholdGroup);
                });
            }
            return list;
        }
        /// <summary>
        /// 清除机组下所有的报警定义信息
        /// </summary>
        /// <param name="_turbineId"></param>
        public static void DeleteAllAlarmDefinition(string _turbineId)
        {
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                ctx.AlarmDefLists.RemoveRange(ctx.AlarmDefLists.Where(item => item.WindTurbineID == _turbineId));
                ctx.AlarmDefThresholdGroupLists.RemoveRange(ctx.AlarmDefThresholdGroupLists.Where(item => item.WindTurbineID == _turbineId));
                ctx.SaveChanges();
            }
        }
    }
}
