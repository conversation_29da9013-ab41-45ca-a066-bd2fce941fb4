import{C as c,aY as a,aZ as n,a_ as i,a$ as h,b0 as l,b1 as p,b2 as d,b3 as u,b4 as L,b5 as y,b6 as w,b7 as M,b8 as b,b9 as f,ba as m,bb as D,bc as g,bd as k,be as C,bf as S,bg as v,bh as G,bi as W,bj as V,bk as P,bl as O,bm as A}from"./index-D9CxWmlM.js";import{a as t,d as s}from"./tools-DZBuE28U.js";const I=c("configDevice",{state:()=>({deviceInfo:{},vibMeaslocationList:[],processMeaslocationList:[],componentList:[],sectionList:[],orientatioList:[],workCondMeasLocDicOptions:[],workCondMeasLocsList:[],enumWorkConDataSourceOptions:[],rotSpdMeasLocList:[],modbusMeasLocList:[],sVMParamTypeList:[],modbusMeasLocoptions:[]}),actions:{reset(){this.$reset()},async fetchDeviceInfo(o={}){try{const e=await A(o);return this.deviceInfo=e,e}catch(e){throw console.error("获取设备失败:",e),e}},async fetcheditOneDevice(o={}){try{return await O(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchGetVibMeaslocation(o={}){try{const e=await P(o);return e&&e.length>0?(this.getVibMeaslocation=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetProcessMeaslocation(o={}){try{const e=await V(o);return e&&e.length>0?(this.processMeaslocationList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetOrientationList(o={}){try{const e=await W(o);let r=s(e,"key");return this.orientatioList=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchGetComponentList(o={}){try{const e=await G(o);if(e&&e.length>0){let r=t(e,{label:"componentName",value:"componentID"},{nother:!0});return this.componentList=r,r}return[]}catch(e){throw console.error("获取失败:",e),e}},async fetchGetSectionList(o={}){try{const e=await v(o);let r=s(e,"key");return this.sectionList=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchGetWorkCondMeasLocDic(o={}){try{const e=await S(o);let r=t(e);return this.workCondMeasLocDicOptions=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchGetEnumWorkConDataSource(o={}){try{const e=await C(o);let r=t(e,{label:"value",value:"key",text:"value"},{nother:!0});return this.enumWorkConDataSourceOptions=r,r}catch(e){throw console.error("获取失败:",e),e}},async fetchAddVibMeasLocs(o={}){try{return await k(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditVibMeasLoc(o={}){try{return await g(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteVibMeasLocs(o={}){try{return await D(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchAddProcessMeasLocs(o={}){try{return await m(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditProcessMeasLoc(o={}){try{return await f(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteProcessMeasLocs(o={}){try{return await b(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetWorkCondMeasLocs(o={}){try{const e=await M(o);return e&&e.length>0?(this.workCondMeasLocsList=e,e):[]}catch(e){throw console.error("获取失败:",e),e}},async fetchAddWorkingConditionMeaslocs(o={}){try{return await w(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditWorkingConditionMeas(o={}){try{return await y(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteWorkingConditionMeasBatch(o={}){try{return await L(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetRotSpdMeasLocList(o={}){try{const e=await u(o);return this.rotSpdMeasLocList=e,e}catch(e){throw console.error("删除失败:",e),e}},async fetchAddRotSpdMeaslocs(o={}){try{return await d(o)}catch(e){throw console.error("批量添加失败:",e),e}},async fetchEditRotSpdLoc(o={}){try{return await p(o)}catch(e){throw console.error("编辑失败:",e),e}},async fetchDeleteRotSpdLoc(o={}){try{return await l(o)}catch(e){throw console.error("删除失败:",e),e}},async fetchGetModbusMeasLocList(o){try{const e=await h(o);this.modbusMeasLocList=e;let r=t(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.modbusMeasLocoptions=r,e}catch(e){throw console.error("获取失败:",e),e}},async fetchAddModbusMeasloc(o){try{return await i(o)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteMeasLoc(o){try{return await n(o)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetSVMParamType(o){try{const e=await a(o);let r=t(e,{label:"value",value:"value"},{nother:!0});return this.sVMParamType=r,r}catch(e){throw console.error("操作失败:",e),e}}}});export{I as u};
