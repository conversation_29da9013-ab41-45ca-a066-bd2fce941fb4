import{c as ot,W as N}from"./table-C_s53ALS.js";import{O as B}from"./index-DyCH3qIX.js";import{z as l,r as a,u as it,y as rt,w as st,f as b,d as c,o as r,i as s,b as D,c as _,F as O,e as H,q as ut,t as W,g as ct,A as g}from"./index-D9CxWmlM.js";import{L as mt}from"./index-BcbB5WJk.js";import{u as dt}from"./collectionUnitConfig-tdEU9a2d.js";import{u as Dt}from"./devTree-BD8aiOqS.js";import{u as pt}from"./collectionUnitMonitor-DMECnQY9.js";import{S as ft,b as S}from"./tools-DZBuE28U.js";import{_ as ht}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as vt,a as bt}from"./index-pF9HjW4G.js";import{M as _t}from"./index-DhZUqjZm.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";const gt={class:"btnGroups"},It={class:"clearfix"},Yt=["onClick"],Mt={class:"border"},Ct=["onClick"],yt={class:"formHeader"},kt={class:"chartBox"},xt={key:1,class:"nodata"},St={__name:"status",setup(Tt){const p=dt(),z=Dt(),I=pt();let u=[l().subtract(3,"month"),l()];const T=a(""),Y=a(!1),M=a(!1),A=it(),n=a({}),L=a(),m=a(A.params.id),U=a([]),G=a({}),v=a(u),C=a(u),V=a({timeRange:u}),f=a({time:u}),d=a({}),o=rt({dauList:[],tableData1:[],tableData2:[],chartInformation:{title:"",legendArr:[]}}),P=()=>{let t=z.findAncestorsWithNodes(m.value);t&&t.length&&t.length>1&&(L.value=t[t.length-2].id)},y=t=>{G.value=t,U.value=[{label:"采集单元名称",value:t.dauName},{label:"IP地址",value:t.ip},{label:"采集间隔(分钟)",value:t.dataAcquisitionInterval},{label:"状态",value:t.isAvailable?"开启":"禁用"},{label:"端口",value:t.port}]},$=async t=>{Y.value=!0,o.dauList=await p.fetchGetDAUList({WindTurbineID:m.value,WindParkId:L.value}),Y.value=!1,p.dAUList&&p.dAUList.length?(n.value=p.dAUList[0],y(n.value),w(),k()):(n.value={},y({}))},w=async()=>{const t=await I.fetchGetDauStatusInfoByTurbineIDAndDauID({turbineID:m.value,DAUID:n.value.dauID});o.tableData1=t.channelStatusList||[]},k=async()=>{const t=await I.fetchGetDSearchDAULog({turbineID:m.value,dauID:n.value.dauID,beginTime:C.value[0],endTime:C.value[1]});o.tableData2=t||[]};st(()=>A.params.id,t=>{t&&(p.reset(),m.value=t,P(),$())},{immediate:!0});const q=[{title:"选择时间",dataIndex:"timeRange",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],E=[{title:"通道",dataIndex:"channelNumber"},{title:"测量位置",dataIndex:"measLocationName"},{title:"偏置电压(V)",dataIndex:"dcDataValue"},{title:"状态",dataIndex:"alarmState",customRender:({record:t})=>t.alarmState?g("span",{style:{color:S(t.alarmState).color}},S(t.alarmState).text):""},{title:"更新时间",dataIndex:"statusUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({record:t})=>t.statusUpdateTime?g("span",{},l(t.statusUpdateTime).format("YYYY-MM-DD HH:mm:ss")):""},{title:"操作",dataIndex:"otherColumn",columnHidden:!0,width:160}],j=[{title:"采集单元状态",dataIndex:"alarmStateDescription",headerOperations:{filters:[]},width:160,customRender:({text:t,record:e})=>e.alarmState?g("span",{style:{color:S(e.alarmState).color}},t):""},{title:"日志",dataIndex:"logTitle",customRender:({text:t,record:e})=>e.logTitle?g("p",{style:{textAlign:"left"}},t):""},{title:"更新时间",dataIndex:"eventTimeFormatted",headerOperations:{sorter:!0,date:!0},width:160}],J=t=>{!t.timeRange||!t.timeRange.length||(C.value=[l(t.timeRange[0]).format("YYYY-MM-DD"),l(t.timeRange[1]).format("YYYY-MM-DD")],k())},K=t=>{t.dauID!=n.value.dauID&&(n.value=t,y(t),V.value={timeRange:u},w(),k())},Q=t=>{T.value=`${t.measLocationName}`,X(),f.value={...f.value,...t},F()},F=async()=>{const t=f.value;let e=await I.fetchGetChannelDCTrendChart({turbineID:m.value,beginTime:l(v.value[0]).format("YYYY-MM-DD"),endTime:l(v.value[1]).format("YYYY-MM-DD"),channelID:t.channelNumber,DAUID:n.value.dauID});if(e&&e.timeValueData&&e.timeValueData.length){let h=[];e.waringValueData&&e.waringValueData.length&&(h.push(e.waringValueData[0]),e.errorValueData&&e.errorValueData.length&&h.push(e.errorValueData[0]));const x={time:e.timeValueData,lineData:[{line:e.eigenValueData}],markLine:h};d.value=x,o.chartInformation={title:`${e.titleName}`,legendArr:[e.eigenValue||"偏置电压"]}}else d.value={}},X=()=>{M.value=!0},Z=t=>{M.value=!1,f.value={time:u},v.value=u},tt=t=>{v.value=[l(t.time[0]).format("YYYY-MM-DD"),l(t.time[1]).format("YYYY-MM-DD")],F()},et=[{title:"选择时间",dataIndex:"time",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],at={showToolbox:!0};return(t,e)=>{const h=bt,x=vt,nt=_t,lt=ft;return r(),b(lt,{spinning:Y.value,size:"large"},{default:c(()=>[s("div",null,[s("div",gt,[s("ul",It,[(r(!0),_(O,null,H(o.dauList,i=>(r(),_("li",{key:i.dauID,class:ut({active:n.value.dauID===i.dauID}),onClick:R=>K(i)},W(i.dauName),11,Yt))),128))])]),D(ot,{tableTitle:"采集单元信息",defaultCollapse:!0,batchApply:!1},{content:c(()=>[s("div",Mt,[D(x,{column:5,size:"small"},{default:c(()=>[(r(!0),_(O,null,H(U.value,i=>(r(),b(h,{label:i.label},{default:c(()=>[ct(W(i.value),1)]),_:2},1032,["label"]))),256))]),_:1})])]),_:1}),s("div",null,[D(N,{ref:"table",size:"default","table-key":"0","table-title":"传感器状态列表","table-columns":E,noPagination:!0,"record-key":"measLocationID","table-datas":o.tableData1,noBatchApply:!0},{otherColumn:c(({column:i,record:R,text:At})=>[s("span",{onClick:Lt=>Q(R),class:"editBtn"},"查看偏置电压趋势图",8,Ct)]),_:1},8,["table-datas"]),D(N,{ref:"table",size:"default","table-key":"1","table-title":"采集单元监测日志","table-columns":j,"record-key":"ModbusUnitID","table-datas":o.tableData2,noBatchApply:!0},{contentHeader:c(()=>[s("div",yt,[(r(),b(B,{titleCol:q,initFormData:V.value,key:n.value.dauID,onSubmit:J,formlayout:"inline"},null,8,["initFormData"]))])]),_:1},8,["table-datas"])]),D(nt,{maskClosable:!1,destroyOnClose:!0,width:"800px",open:M.value,title:T.value,footer:"",onCancel:Z},{default:c(()=>[D(B,{titleCol:et,ref:"formRef",initFormData:f.value,formlayout:"inline",onSubmit:tt},null,8,["initFormData"]),s("div",kt,[d.value&&d.value.time&&d.value.time.length?(r(),b(mt,{key:0,boxId:"chart2",chartOptions:at,informations:o.chartInformation,chartData:d.value},null,8,["informations","chartData"])):(r(),_("div",xt,"暂无数据"))])]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},Zt=ht(St,[["__scopeId","data-v-c66a5c84"]]);export{Zt as default};
