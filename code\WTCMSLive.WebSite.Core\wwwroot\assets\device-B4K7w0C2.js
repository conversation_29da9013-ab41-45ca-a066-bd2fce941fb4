import{W as R}from"./table-C_s53ALS.js";import{O as S}from"./index-DyCH3qIX.js";import{u as F,g as b}from"./configRoot-CCVOK_kf.js";import{u as B}from"./devTree-BD8aiOqS.js";import{r as o,h as O,f as _,d as C,o as v,i as I,b as x,c as E,z as Y,m as s}from"./index-D9CxWmlM.js";import{S as z}from"./tools-DZBuE28U.js";import{M as N}from"./index-DhZUqjZm.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";const A={key:1},H="YYYY-MM-DD HH:mm:ss",de={__name:"device",setup(V){const n=F(),h=B(),y=e=>[{title:"场站编号",dataIndex:"windParkCode",columnWidth:80,headerOperations:{sorter:!0}},{title:"场站名称",dataIndex:"windParkName",columnWidth:100},{title:"联系人",dataIndex:"contactMan",columnWidth:70},{title:"联系人电话",dataIndex:"contactTel",columnWidth:110},{title:"区域",dataIndex:"area",columnWidth:100,headerOperations:{filters:[]}},{title:"地址",dataIndex:"address",columnWidth:150},{title:"经纬度",dataIndex:"location",columnWidth:80},{title:"邮编",dataIndex:"postCode",columnWidth:100},{title:"概况",dataIndex:"description",columnWidth:100}],r=o(""),l=o(""),d=o({}),w=o([]),i=o([]),c=o(!1),u=o(!1),k=o(y()),m=async()=>{u.value=!0,w.value=await n.fetchParkList(),u.value=!1,k.value=y()};O(()=>{m()});const D=()=>{c.value=!0},p=e=>{c.value=!1,d.value={},i.value=[],r.value="",l.value=""},T=async e=>{const{title:t,operateType:a}=e;d.value={country:"中国",area:"河北"},l.value=a,r.value="增加厂站",i.value=[...b()],await g(),D()},W=async(e={})=>{const{selectedkeys:t}=e;if(t.length<1){s.error("请选择要删除的行");return}const a=await n.fetchDeletetPark({windParkID:t[0]});a&&a.code===1?(m(),p(),s.success("提交成功"),h.getDevTreeDatas()):s.error("提交失败:"+a.msg)},L=async e=>{const{rowData:t,tableKey:a,operateType:f,title:j}=e;l.value=f;let M=t.operationalDate?Y(t.operationalDate,H):"";d.value={...t,operationalDate:M},r.value="编辑厂站",i.value=[...b({isEdit:!0})],await g(),D()},g=async()=>{(!n.groupCompanyList||n.groupCompanyList.length<1)&&await n.fetchGroupCompanyList();const e=n.groupCompanyList;if(e&&e.length>0){let t=[...i.value];t[1].selectOptions=e,i.value=[...t]}},P=async e=>{const t=await n.fetchEditWindparkInformation({...e,description:e.description||"",windParkID:e.windParkID||""});t&&t.code===1?(m(),s.success("提交成功"),l.value=="add"&&h.getDevTreeDatas(),p()):s.error("提交失败:"+t.msg)};return(e,t)=>{const a=N,f=z;return v(),_(f,{spinning:u.value,size:"large"},{default:C(()=>[I("div",null,[I("div",null,[x(R,{ref:"table",size:"default","table-key":"0","table-title":"厂站列表","table-columns":k.value,noBatchApply:!0,"record-key":"windParkID","table-operate":["edit","delete","add"],"table-datas":w.value,onAddRow:T,onDeleteRow:W,onEditRow:L},null,8,["table-columns","table-datas"])]),x(a,{maskClosable:!1,width:"600px",open:c.value,title:r.value,footer:"",onCancel:p},{default:C(()=>[l.value==="add"||l.value==="edit"?(v(),_(S,{key:0,titleCol:i.value,initFormData:d.value,onSubmit:P},null,8,["titleCol","initFormData"])):(v(),E("div",A))]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}};export{de as default};
