using CMSFramework.BusinessEntity;
using WTCMSLive.WebSite.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// 批量操作结果
    /// </summary>
    /// <typeparam name="T">主操作结果类型</typeparam>
    public class BatchOperationResult<T>
    {
        /// <summary>
        /// 主操作结果
        /// </summary>
        public T MainResult { get; set; }

        /// <summary>
        /// 批量操作结果列表
        /// </summary>
        public List<BatchItemResult> BatchResults { get; set; } = new List<BatchItemResult>();

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasErrors => BatchResults.Any(r => !r.Success);

        /// <summary>
        /// 成功的操作数量
        /// </summary>
        public int SuccessCount => BatchResults.Count(r => r.Success);

        /// <summary>
        /// 失败的操作数量
        /// </summary>
        public int FailureCount => BatchResults.Count(r => !r.Success);

        /// <summary>
        /// 总操作数量
        /// </summary>
        public int TotalCount => BatchResults.Count;
    }

    /// <summary>
    /// 批量操作单项结果
    /// </summary>
    public class BatchItemResult
    {
        /// <summary>
        /// 目标机组ID
        /// </summary>
        public string TurbineId { get; set; }

        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息（如果失败）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 操作结果数据
        /// </summary>
        public object Result { get; set; }

        /// <summary>
        /// 操作执行时间
        /// </summary>
        public DateTime ExecutedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 操作耗时（毫秒）
        /// </summary>
        public long ElapsedMilliseconds { get; set; }
    }

    /// <summary>
    /// 批量操作请求基类
    /// </summary>
    public abstract class BatchOperationRequestBase<T>
    {
        /// <summary>
        /// 目标机组ID列表
        /// </summary>
        public List<string> TargetTurbineIds { get; set; } = new List<string>();

        /// <summary>
        /// 源数据
        /// </summary>
        public T SourceData { get; set; }
    }

    /// <summary>
    /// 批量添加振动测量位置请求
    /// </summary>
    public class BatchAddVibMeasLocsRequest : BatchOperationRequestBase<List<MeaslocDTO>>
    {
    }

    /// <summary>
    /// 批量编辑振动测量位置请求
    /// </summary>
    public class BatchEditVibMeasLocRequest : BatchOperationRequestBase<MeasLoc_Vib>
    {
    }

    /// <summary>
    /// 批量删除振动测量位置请求
    /// </summary>
    public class BatchDeleteVibMeasLocsRequest : BatchOperationRequestBase<List<string>>
    {
    }

    /// <summary>
    /// 批量添加电流电压测量位置请求
    /// </summary>
    public class BatchAddProcessMeasLocsRequest : BatchOperationRequestBase<List<MeaslocDTO>>
    {
    }

    /// <summary>
    /// 批量删除电流电压测量位置请求
    /// </summary>
    public class BatchDeleteProcessMeasLocsRequest : BatchOperationRequestBase<List<string>>
    {
    }

    /// <summary>
    /// 批量添加工况测量位置请求
    /// </summary>
    public class BatchAddWorkingConditionMeasLocsRequest : BatchOperationRequestBase<List<WorkingConditionMeasDTO>>
    {
    }

    /// <summary>
    /// 批量删除工况测量位置请求
    /// </summary>
    public class BatchDeleteWorkingConditionMeasLocsRequest : BatchOperationRequestBase<List<MeaslocDelDTO>>
    {
    }

    /// <summary>
    /// 批量编辑转速测量位置请求
    /// </summary>
    public class BatchEditRotSpdLocRequest : BatchOperationRequestBase<MeasLoc_RotSpd>
    {
    }

    /// <summary>
    /// 批量添加转速测量位置请求
    /// </summary>
    public class BatchAddRotSpdLocRequest : BatchOperationRequestBase<List<MeasLoc_RotSpd>>
    {
    }

    /// <summary>
    /// 批量删除转速测量位置请求
    /// </summary>
    public class BatchDeleteRotSpdLocRequest : BatchOperationRequestBase<List<MeaslocDelDTO>>
    {
    }

    /// <summary>
    /// 批量添加Modbus测量位置请求
    /// </summary>
    public class BatchAddModbusMeasLocRequest : BatchOperationRequestBase<List<ModbusMeasLocDTO>>
    {
    }

    /// <summary>
    /// 批量删除测量位置请求
    /// </summary>
    public class BatchDeleteMeasLocRequest : BatchOperationRequestBase<List<string>>
    {
    }

    /// <summary>
    /// 批量添加Modbus设备请求
    /// </summary>
    public class BatchAddModbusDeviceRequest : BatchOperationRequestBase<ModbusDeviceDTO>
    {
    }

    /// <summary>
    /// 批量编辑Modbus设备请求
    /// </summary>
    public class BatchEditModbusDeviceRequest : BatchOperationRequestBase<EditModbusDeviceDTO>
    {
    }

    /// <summary>
    /// 批量删除Modbus设备请求
    /// </summary>
    public class BatchDeleteModbusDeviceRequest : BatchOperationRequestBase<List<ModbusDeviceIdentifier>>
    {
    }

    /// <summary>
    /// 批量添加Modbus通道请求
    /// </summary>
    public class BatchAddModbusChannelRequest : BatchOperationRequestBase<List<ModbusChannelDTO>>
    {
    }

    /// <summary>
    /// 批量删除Modbus通道请求
    /// </summary>
    public class BatchDeleteModbusChannelRequest : BatchOperationRequestBase<List<ModbusChannelIdentifier>>
    {
    }

    /// <summary>
    /// 批量编辑Modbus通道请求
    /// </summary>
    public class BatchEditModbusChannelRequest : BatchOperationRequestBase<EditModbusChannelDTO>
    {
    }

    /// <summary>
    /// 批量添加MCS寄存器请求
    /// </summary>
    public class BatchAddMCSRegisterRequest : BatchOperationRequestBase<List<MCSChannelValueParam>>
    {
    }

    /// <summary>
    /// 批量编辑MCS寄存器请求
    /// </summary>
    public class BatchEditMCSRegisterRequest : BatchOperationRequestBase<List<MCSChannelValueParam>>
    {
    }

    /// <summary>
    /// 批量删除MCS寄存器请求
    /// </summary>
    public class BatchDeleteMCSRegisterRequest : BatchOperationRequestBase<List<MCSRegisterDeleteDTO>>
    {
    }

    /// <summary>
    /// 批量删除数据寄存器参数DTO
    /// </summary>
    public class MCSRegisterDeleteDTO
    {
        public string WindTurbineID { get; set; }
        public string MeasLocProcessID { get; set; }
    }

    // DAU通道批量操作请求类

    /// <summary>
    /// 批量添加振动通道请求
    /// </summary>
    public class BatchAddVibChannelsRequest : BatchOperationRequestBase<BatchAddVibChannelDTO>
    {
    }

    /// <summary>
    /// 批量删除振动通道请求
    /// </summary>
    public class BatchDeleteVibChannelsRequest : BatchOperationRequestBase<BatchDeleteVibChannelDTO>
    {
    }

    /// <summary>
    /// 编辑振动通道请求
    /// </summary>
    public class EditVibChannelRequest : BatchOperationRequestBase<EditVibChannelDTO>
    {
    }

    /// <summary>
    /// 批量添加电流电压通道请求
    /// </summary>
    public class BatchAddProcessChannelsRequest : BatchOperationRequestBase<BatchAddProcessChannelDTO>
    {
    }

    /// <summary>
    /// 批量删除电流电压通道请求
    /// </summary>
    public class BatchDeleteProcessChannelsRequest : BatchOperationRequestBase<BatchDeleteProcessChannelDTO>
    {
    }

    /// <summary>
    /// 编辑电流电压通道请求
    /// </summary>
    public class EditProcessChannelRequest : BatchOperationRequestBase<EditProcessChannelDTO>
    {
    }

    /// <summary>
    /// 批量添加工况通道请求
    /// </summary>
    public class BatchAddWorkConditionChannelsRequest : BatchOperationRequestBase<BatchAddWorkConditionChannelDTO>
    {
    }

    /// <summary>
    /// 批量删除工况通道请求
    /// </summary>
    public class BatchDeleteWorkConditionChannelsRequest : BatchOperationRequestBase<BatchDeleteWorkConditionChannelDTO>
    {
    }

    /// <summary>
    /// 编辑工况通道请求
    /// </summary>
    public class EditWorkConditionChannelRequest : BatchOperationRequestBase<EditWorkConditionChannelDTO>
    {
    }

    /// <summary>
    /// 批量添加转速通道请求
    /// </summary>
    public class BatchAddRotSpeedChannelsRequest : BatchOperationRequestBase<BatchAddRotSpeedChannelDTO>
    {
    }

    /// <summary>
    /// 批量删除转速通道请求
    /// </summary>
    public class BatchDeleteRotSpeedChannelsRequest : BatchOperationRequestBase<BatchDeleteRotSpeedChannelDTO>
    {
    }

    /// <summary>
    /// 编辑转速通道请求
    /// </summary>
    public class EditRotSpeedChannelRequest : BatchOperationRequestBase<EditRotSpeedChannelDTO>
    {
    }

    #region MeasdController批量操作DTO

    /// <summary>
    /// 批量创建时域波形定义请求
    /// </summary>
    public class BatchMakeWaveDefinitionRequest : BatchOperationRequestBase<WaveDefinitionDTO>
    {
    }

    /// <summary>
    /// 批量创建电流电压波形定义请求
    /// </summary>
    public class BatchMakeWaveDefinitionVoltageCurrentRequest : BatchOperationRequestBase<WaveDefinitionDTO>
    {
    }

    /// <summary>
    /// 批量删除时域波形通道请求
    /// </summary>
    public class BatchDeleteWaveChannelRequest : BatchOperationRequestBase<WaveChannelDeleteDTO>
    {
    }

    /// <summary>
    /// 批量删除电流电压波形通道请求
    /// </summary>
    public class BatchDeleteWaveChannelVoltageCurrentRequest : BatchOperationRequestBase<WaveChannelDeleteDTO>
    {
    }

    /// <summary>
    /// 批量创建高频包络波形定义请求
    /// </summary>
    public class BatchMakeParamEnvDefinitionRequest : BatchOperationRequestBase<WaveDefinitionDTO>
    {
    }

    /// <summary>
    /// 批量删除高频包络通道请求
    /// </summary>
    public class BatchDeleteParamEnvChannelRequest : BatchOperationRequestBase<ParamEnvChannelDeleteDTO>
    {
    }

    /// <summary>
    /// 波形通道删除DTO
    /// </summary>
    public class WaveChannelDeleteDTO
    {
        public string WindTurbineID { get; set; }
        public string MeasDefId { get; set; }
        public string WaveId { get; set; }
    }

    /// <summary>
    /// 高频包络通道删除DTO
    /// </summary>
    public class ParamEnvChannelDeleteDTO
    {
        public string WindTurbineID { get; set; }
        public string WaveId { get; set; }
        public string MeasDefId { get; set; }
    }

    #endregion

    #region 工况测量定义批量操作DTO

    /// <summary>
    /// 批量添加工况测量定义请求
    /// </summary>
    public class BatchAddWorkCondMeasdRequest : BatchOperationRequestBase<List<WorkCondMeasdWaveDTO>>
    {
    }

    /// <summary>
    /// 批量编辑工况测量定义请求
    /// </summary>
    public class BatchEditWorkCondMeasdRequest : BatchOperationRequestBase<List<WorkCondMeasdWaveDTO>>
    {
    }

    /// <summary>
    /// 批量删除工况测量定义请求
    /// </summary>
    public class BatchDeleteWorkCondMeasdRequest : BatchOperationRequestBase<List<WorkCondMeasdDeleteDTO>>
    {
    }

    /// <summary>
    /// 工况测量定义删除DTO
    /// </summary>
    public class WorkCondMeasdDeleteDTO
    {
        public string WindTurbineID { get; set; }
        public string? DauID { get; set; }
        public string MeasDefinitionID { get; set; }
        public string measLocId { get; set; }
    }

    #endregion

    #region 转速波形定义批量操作DTO

    /// <summary>
    /// 批量添加转速波形定义请求
    /// </summary>
    public class BatchAddWorkCondSpdMeasdRequest : BatchOperationRequestBase<List<WorkCondMeasdWaveDTO>>
    {
    }

    /// <summary>
    /// 批量编辑转速波形定义请求
    /// </summary>
    public class BatchEditWorkCondSpdMeasdRequest : BatchOperationRequestBase<List<WorkCondMeasdWaveDTO>>
    {
    }

    /// <summary>
    /// 批量删除转速波形定义请求
    /// </summary>
    public class BatchDeleteWorkCondSpdMeasdRequest : BatchOperationRequestBase<List<WorkCondMeasdDeleteDTO>>
    {
    }

    #endregion

    #region Modbus波形定义批量操作DTO

    /// <summary>
    /// 批量添加Modbus波形定义请求
    /// </summary>
    public class BatchAddModbusWaveRequest : BatchOperationRequestBase<List<WaveModbusDTO>>
    {
    }

    /// <summary>
    /// 批量编辑Modbus波形定义请求
    /// </summary>
    public class BatchEditModbusWaveRequest : BatchOperationRequestBase<EditModbusWaveDTO>
    {
    }

    /// <summary>
    /// 批量删除Modbus波形定义请求
    /// </summary>
    public class BatchDeleteModbusWaveRequest : BatchOperationRequestBase<List<ModbusWaveIdentifier>>
    {
    }

    #endregion

    #region 触发采集批量操作DTO

    /// <summary>
    /// 批量添加触发采集请求
    /// </summary>
    public class BatchAddTriggerAcqRequest : BatchOperationRequestBase<TriggerAcquisitionTOD>
    {
    }

    /// <summary>
    /// 批量删除触发采集请求
    /// </summary>
    public class BatchDeleteTriggerGatherDisposeRequest : BatchOperationRequestBase<List<TriggerAcquisitionTOD>>
    {
    }

    #endregion

    #region 报警规则批量操作DTO

    /// <summary>
    /// 批量添加报警规则请求
    /// </summary>
    public class BatchAddWarnRuleRequest : BatchOperationRequestBase<List<AddWarnRuleDTO>>
    {
    }

    /// <summary>
    /// 批量编辑报警规则请求
    /// </summary>
    public class BatchEditWarnRuleRequest : BatchOperationRequestBase<EditWarnRuleDTO>
    {
    }

    /// <summary>
    /// 批量删除报警规则请求
    /// </summary>
    public class BatchDeleteWarnRuleRequest : BatchOperationRequestBase<List<DeleteWarnRuleItemDTO>>
    {
    }

    #endregion

    #region 测量方案批量操作DTO

    /// <summary>
    /// 批量编辑测量方案请求
    /// </summary>
    public class BatchEditMeasSolutionRequest : BatchOperationRequestBase<EditMeasSolutionDTO>
    {
    }

    /// <summary>
    /// 批量添加测量方案请求
    /// </summary>
    public class BatchAddMeasSolutionsRequest : BatchOperationRequestBase<List<MeasSolutionAddItem>>
    {
    }

    /// <summary>
    /// 批量删除测量方案请求
    /// </summary>
    public class BatchDeleteMeasSolutionsRequest : BatchOperationRequestBase<List<MeasSolutionDelDTO>>
    {
    }

    #endregion
}
