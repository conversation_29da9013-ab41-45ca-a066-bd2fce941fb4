using System;

namespace WTCMSLive.WebSite.Core.Models
{
    public class ApiResponse<T>
    {
        public string Msg { get; set; }
        public int Code { get; set; }
        public T Data { get; set; }

        public static ApiResponse<T> Success(T data, string msg = "Success")
        {
            return new ApiResponse<T>
            {
                Code = 1,
                Msg = msg,
                Data = data
            };
        }

        public static ApiResponse<T> Error(string msg, int code = 0)
        {
            return new ApiResponse<T>
            {
                Code = code,
                Msg = msg,
                Data = default
            };
        }

        public static ApiResponse<T> Error(string msg, T data, int code = 0)
        {
            return new ApiResponse<T>
            {
                Code = code,
                Msg = msg,
                Data = data
            };
        }

    }
}