using Newtonsoft.Json;

namespace WTCMSLive.WebSite.Core.Services
{
    /// <summary>
    /// 数据中心服务使用示例
    /// </summary>
    public class DataCenterServiceExample
    {
        private readonly IDataCenterService _dataCenterService;
        private readonly ILogger<DataCenterServiceExample> _logger;

        public DataCenterServiceExample(
            IDataCenterService dataCenterService,
            ILogger<DataCenterServiceExample> logger)
        {
            _dataCenterService = dataCenterService;
            _logger = logger;
        }

        /// <summary>
        /// 示例1：获取集团公司代码数据（使用默认超时）
        /// </summary>
        public async Task<string> GetGroupCompanyCodeExample()
        {
            try
            {
                _logger.LogInformation("开始获取集团公司代码数据（使用默认超时）");

                var jsonResult = await _dataCenterService.GetGroupCompanyCodeAsync();

                _logger.LogInformation("成功获取集团公司代码数据");
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取集团公司代码数据时发生异常");
                return $"{{\"error\": \"获取数据失败: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 示例1-2：获取集团公司代码数据（自定义超时）
        /// </summary>
        public async Task<string> GetGroupCompanyCodeWithTimeoutExample()
        {
            try
            {
                _logger.LogInformation("开始获取集团公司代码数据（自定义超时60秒）");

                // 使用60秒超时
                var jsonResult = await _dataCenterService.GetGroupCompanyCodeAsync(60);

                _logger.LogInformation("成功获取集团公司代码数据");
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取集团公司代码数据时发生异常");
                return $"{{\"error\": \"获取数据失败: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 示例2：解析JSON数据为对象
        /// </summary>
        public async Task<List<GroupCompanyInfo>?> GetGroupCompanyListExample()
        {
            try
            {
                var jsonResult = await _dataCenterService.GetGroupCompanyCodeAsync();
                
                // 解析JSON为对象列表
                var companies = JsonConvert.DeserializeObject<List<GroupCompanyInfo>>(jsonResult);
                
                _logger.LogInformation("成功解析集团公司数据，共 {Count} 条记录", companies?.Count ?? 0);
                return companies;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解析集团公司数据时发生异常");
                return null;
            }
        }

        /// <summary>
        /// 示例3：使用通用GET方法调用其他API（默认超时）
        /// </summary>
        public async Task<string> CallCustomApiExample(string endpoint, Dictionary<string, string>? parameters = null)
        {
            try
            {
                _logger.LogInformation("开始调用自定义API: {Endpoint}", endpoint);

                var jsonResult = await _dataCenterService.GetAsync(endpoint, parameters);

                _logger.LogInformation("成功调用自定义API: {Endpoint}", endpoint);
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用自定义API时发生异常: {Endpoint}", endpoint);
                return $"{{\"error\": \"调用API失败: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 示例3-2：使用通用GET方法调用其他API（自定义超时）
        /// </summary>
        public async Task<string> CallCustomApiWithTimeoutExample(string endpoint, Dictionary<string, string>? parameters = null, int timeoutSeconds = 15)
        {
            try
            {
                _logger.LogInformation("开始调用自定义API: {Endpoint}, 超时时间: {TimeoutSeconds}秒", endpoint, timeoutSeconds);

                var jsonResult = await _dataCenterService.GetAsync(endpoint, parameters, timeoutSeconds);

                _logger.LogInformation("成功调用自定义API: {Endpoint}", endpoint);
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用自定义API时发生异常: {Endpoint}", endpoint);
                return $"{{\"error\": \"调用API失败: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 示例4：使用POST方法发送数据（默认超时）
        /// </summary>
        public async Task<string> PostDataExample(string endpoint, object data)
        {
            try
            {
                _logger.LogInformation("开始发送POST数据到: {Endpoint}", endpoint);

                var jsonData = JsonConvert.SerializeObject(data);
                var jsonResult = await _dataCenterService.PostAsync(endpoint, jsonData);

                _logger.LogInformation("成功发送POST数据到: {Endpoint}", endpoint);
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送POST数据时发生异常: {Endpoint}", endpoint);
                return $"{{\"error\": \"发送数据失败: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 示例4-2：使用POST方法发送数据（自定义超时）
        /// </summary>
        public async Task<string> PostDataWithTimeoutExample(string endpoint, object data, int timeoutSeconds = 45)
        {
            try
            {
                _logger.LogInformation("开始发送POST数据到: {Endpoint}, 超时时间: {TimeoutSeconds}秒", endpoint, timeoutSeconds);

                var jsonData = JsonConvert.SerializeObject(data);
                var jsonResult = await _dataCenterService.PostAsync(endpoint, jsonData, timeoutSeconds);

                _logger.LogInformation("成功发送POST数据到: {Endpoint}", endpoint);
                return jsonResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送POST数据时发生异常: {Endpoint}", endpoint);
                return $"{{\"error\": \"发送数据失败: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// 示例5：在其他业务方法中使用
        /// </summary>
        public async Task<string> BusinessMethodExample()
        {
            try
            {
                // 获取集团公司数据
                var companiesJson = await _dataCenterService.GetGroupCompanyCodeAsync();
                
                // 可以直接返回JSON给前端
                return companiesJson;
                
                // 或者解析后进行业务处理
                // var companies = JsonConvert.DeserializeObject<List<GroupCompanyInfo>>(companiesJson);
                // // 进行业务逻辑处理...
                // return JsonConvert.SerializeObject(processedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "业务方法执行时发生异常");
                return $"{{\"error\": \"业务处理失败: {ex.Message}\"}}";
            }
        }
    }

    /// <summary>
    /// 集团公司信息模型（用于JSON反序列化）
    /// </summary>
    public class GroupCompanyInfo
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ShortName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public int Status { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public string? Remark { get; set; }
    }
}
