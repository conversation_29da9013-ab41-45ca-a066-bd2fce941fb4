﻿using Microsoft.AspNetCore.SignalR;
using RealtimePerf.Hubs;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;

namespace WTCMSLive.WebSite.Core.Service
{
    public class MetricsCollectorService:BackgroundService
    {
        private readonly IHubContext<ServerPerformanceHub> _hub;
        private readonly IMetricsStorageService _storageService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<MetricsCollectorService> _logger;
        private readonly TimeSpan _interval = TimeSpan.FromSeconds(1);
        private readonly Timer _cleanupTimer;

        public MetricsCollectorService(
            IHubContext<ServerPerformanceHub> hub,
            IMetricsStorageService storageService,
            IConfiguration configuration,
            ILogger<MetricsCollectorService> logger)
        {
            _hub = hub;
            _storageService = storageService;
            _configuration = configuration;
            _logger = logger;

            // 设置每天凌晨2点清理过期数据
            var cleanupInterval = TimeSpan.FromHours(24);
            _cleanupTimer = new Timer(async _ => await CleanupExpiredDataAsync(), null, GetTimeUntilNextCleanup(), cleanupInterval);
        }

        private TimeSpan GetTimeUntilNextCleanup()
        {
            var now = DateTime.Now;
            var nextCleanup = now.Date.AddDays(1).AddHours(2); // 明天凌晨2点
            return nextCleanup - now;
        }

        private async Task CleanupExpiredDataAsync()
        {
            try
            {
                var cleanedCount = await _storageService.CleanupExpiredDataAsync();
                _logger.LogInformation("[CleanupExpiredDataAsync]清理了 {Count} 个过期数据文件", cleanedCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CleanupExpiredDataAsync]清理过期数据失败");
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stop)
        {
            _logger.LogInformation("[MetricsCollectorService]性能数据采集服务已启动");

            while (!stop.IsCancellationRequested)
            {
                // 检查配置开关是否启用
                var isEnabled = _configuration.GetValue<bool>("MetricsCollection:Enable", true);

                if (isEnabled)
                {
                    try
                    {
                        var (read, write) = MetricsManager.GetDiskIoRate();
                        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

                        var snapshot = new MetricsSnapshot
                        {
                            Timestamp = timestamp,
                            Cpu = MetricsManager.GetCpuUsage(),
                            Memory = MetricsManager.GetMemoryMB(),
                            DiskRead = read,
                            DiskWrite = write
                        };

                        // 实时发送到SignalR客户端
                        await _hub.Clients.All.SendAsync("ReceiveMetrics", snapshot, stop);

                        // 存储到本地文件
                        var metricsData = new MetricsDataDTO
                        {
                            Timestamp = timestamp,
                            Cpu = snapshot.Cpu,
                            Memory = snapshot.Memory,
                            DiskRead = snapshot.DiskRead,
                            DiskWrite = snapshot.DiskWrite
                        };

                        await _storageService.StoreMetricsDataAsync(metricsData);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "[MetricsCollectorService]采集和发送性能数据失败");
                    }
                }

                await Task.Delay(_interval, stop);
            }
        }

        public override void Dispose()
        {
            _cleanupTimer?.Dispose();
            base.Dispose();
        }

    }
}
