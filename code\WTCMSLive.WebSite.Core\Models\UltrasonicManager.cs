﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class UltrasonicManager
    {
        
        public static List<UltrasonicChannelConfigModel> GetUltrasonicChannelConf(string turbineID,string dauID)
        {
            // 获取超声波配置
            List<UltrasonicChannelConfig> ullist = DauManagement.GetUltrasonicChannelConfigByTurId(turbineID, dauID);
            // 获取通道
            List<DAUChannelV2> dauChannellist = DauManagement.GetDAUVibChannelList(turbineID, dauID);
            // 获取测量位置
            List<MeasLoc_Vib> measLocList_Vib = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);

            List<UltrasonicChannelConfigModel> res = new List<UltrasonicChannelConfigModel>();

            ullist.ForEach(item =>
            {
                DAUChannelV2 channel = dauChannellist.FirstOrDefault(p => p.ChannelNumber == item.ChannelNumber);
                if (channel != null)
                {
                    MeasLoc_Vib measloc = measLocList_Vib.FirstOrDefault(p => p.MeasLocationID == channel.MeasLocVibID);

                    res.Add(new UltrasonicChannelConfigModel()
                    {
                        ChannelNumber = item.ChannelNumber,
                        DauID = item.DauID,
                        DispatcherChannelID = item.DispatcherChannelID,
                        DispatcherID = item.DispatcherID,
                        MeasLocVibID = channel.MeasLocVibID,
                        PreloadCalCoeffs = item.PreloadCalCoeffs,
                        MeasLocVibName = measloc.MeasLocName,
                        PreloadLowerLimmit = item.PreloadLowerLimmit,
                        PreloadUpperLimmit = item.PreloadUpperLimmit,
                        StandardFilePath = item.StandardFilePath,
                        TempCalibCoeff = item.TempCalibCoeff,
                        WindTurbineID = item.WindTurbineID,
                        SectionName = measloc.SectionName,
                        BoltModel = item.BoltModel,
                    });
                }
            });

            return res;
        }

    }
}
