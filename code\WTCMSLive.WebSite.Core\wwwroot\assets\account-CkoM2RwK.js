import{C as s,dm as t,dn as o,dp as c,dq as n,dr as a,ds as u}from"./index-ClUwy-U2.js";const i=s("account",{state:()=>({userlist:[]}),actions:{reset(){this.$reset()},async fetchGetuserlist(r){try{const e=await u(r);return this.userlist=e,e}catch(e){throw console.error("获取失败:",e),e}},async fetchDeleteUser(r){try{return await a(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchEdituser(r){try{return await n(r)}catch(e){throw console.error(e),e}},async fetchAdduser(r){try{return await c(r)}catch(e){throw console.error(e),e}},async fetchResetUser(r){try{return await o(r)}catch(e){throw console.error(e),e}},async fetchChangePasswordr(r){try{return await t(r)}catch(e){throw console.error(e),e}}}});export{i as u};
