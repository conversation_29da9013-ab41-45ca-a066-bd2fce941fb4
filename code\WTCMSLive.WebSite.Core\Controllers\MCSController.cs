﻿using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.Models;
using Microsoft.AspNetCore.Authorization;
using WTCMSLive.WebSite.Core.Models.DTOs;
using WTCMSLive.WebSite.Core.Services;
using WTCMSLive.WebSite.Core.Attributes;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class MCSController : ControllerBase
    {
        private readonly ILogger<MCSController> _logger;

        public MCSController(ILogger<MCSController> logger)
        {
            _logger = logger;
        }
        // 王岩 2015年6月16日 15:58:29
        // 主控配置相关方法

        #region 风场级别

        /// <summary>
        /// 获取主控列表
        /// </summary>
        /// <param name="WindParkID"></param>
        /// <returns></returns>
        [HttpGet("GetMCSInfoList")]
        public IActionResult GetMCSInfoList(string WindParkID,string? TurbineID)
        {
            List<MCSDTO> datalist = new List<MCSDTO>();
            List<WindTurbine> mcsManagerlist = DevTreeManagement.GetTurbinesListByWindParkId(WindParkID);
            List<MCS> mcsList = DAUMCS.GetMCSByWindParkId(WindParkID);
            if (!string.IsNullOrEmpty(TurbineID))
            {
                //mcsList = mcsList.Where(t => t.WindTurbineID == TurbineID).ToList();
                mcsManagerlist = mcsManagerlist.Where(t => t.WindTurbineID == TurbineID).ToList();
            }
            for (int i = 0; i < mcsManagerlist.Count; i++)
            {
                WindTurbine mcsManager = mcsManagerlist[i];
                MCS mcs = mcsList.Find(item => item.WindTurbineID == mcsManager.WindTurbineID);
                if (mcs == null) continue;

                datalist.Add(new MCSDTO() { 
                    MCSIP = mcs.MCSIP,
                    MCSPort = mcs.MCSPort,
                    WindParkName = mcsManager.WindTurbineName,
                    WindTurbineID = mcsManager.WindTurbineID,
                    WindTurbineName = mcsManager.WindTurbineName
                });

            }
            return Ok(datalist);
        }


        /// <summary>
        /// 批量编辑MCS信息
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost("BatchEditMCS")]
        public IActionResult BatchEditMCS([FromBody] List<MCSDTO> list)
        {
            if (list == null || list.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName);
            using var tran = ctx.Database.BeginTransaction();
            try
            {
                foreach (var dto in list)
                {
                    var mcs = ctx.MCSystems.Find(dto.WindTurbineID);
                    if (mcs == null)
                        return Ok(ApiResponse<string>.Error($"WindTurbineID={dto.WindTurbineID} 不存在，无法编辑"));
                    mcs.MCSIP = dto.MCSIP;
                    mcs.MCSPort = dto.MCSPort;
                    ctx.Entry(mcs).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                }
                ctx.SaveChanges();
                tran.Commit();
                return Ok(ApiResponse<string>.Success("批量编辑成功"));
            }
            catch (Exception ex)
            {
                tran.Rollback();
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchEditMCS]批量编辑机组主控失败", ex);
                return Ok(ApiResponse<string>.Error("批量编辑失败: " + ex.Message));
            }
        }

        /// <summary>
        /// 批量添加MCS信息（全部成功或全部失败）
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost("BatchAddMCS")]
        public IActionResult BatchAddMCS([FromBody] List<MCSDTO> list)
        {
            if (list == null || list.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var existedIds = new List<string>();
                        foreach (var dto in list)
                        {
                            var exist = ctx.MCSystems.Find(dto.WindTurbineID);
                            if (exist != null)
                            {
                                existedIds.Add(dto.WindTurbineID);
                            }
                        }
                        if (existedIds.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error($"以下WindTurbineID已存在，批量添加失败: {string.Join(", ", existedIds)}"));
                        }
                        foreach (var dto in list)
                        {
                            var mcs = new MCS
                            {
                                WindTurbineID = dto.WindTurbineID,
                                MCSIP = dto.MCSIP,
                                MCSPort = dto.MCSPort,
                                FieldBusType = "0"
                            };
                            ctx.MCSystems.Add(mcs);
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量添加成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddMCS]批量添加机组主控失败", ex);
                        return Ok(ApiResponse<string>.Error("批量添加失败: " + ex.Message));
                    }
                }
            }
        }

        /// <summary>
        /// 批量删除MCS信息（全部成功或全部失败）
        /// </summary>
        /// <param name="windTurbineIds"></param>
        /// <returns></returns>
        [HttpPost("BatchDeleteMCS")]
        public IActionResult BatchDeleteMCS([FromBody] List<string> windTurbineIds)
        {
            if (windTurbineIds == null || windTurbineIds.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var notFoundIds = new List<string>();
                        foreach (var id in windTurbineIds)
                        {
                            var mcs = ctx.MCSystems.Find(id);
                            if (mcs == null)
                            {
                                notFoundIds.Add(id);
                            }
                            else
                            {
                                ctx.MCSystems.Remove(mcs);
                            }
                        }
                        if (notFoundIds.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error($"以下WindTurbineID不存在，批量删除失败: {string.Join(", ", notFoundIds)}"));
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量删除成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteMCS]批量删除机组主控失败", ex);
                        return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
                    }
                }
            }
        }

        #endregion

        #region 风机级别

        #region 下拉列表数据

        /// <summary>
        /// 获取工况测量位置列表（数据寄存器）
        /// </summary>
        /// <param name="trubineId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetWorkFromMcs")]
        public IActionResult GetWorkFromMcs(string trubineId)
        {
            List<MeasLoc_Process> listWorkFromMCS = DevTreeManagement.GetNotUsedMCSChanLocProcessList(trubineId);
            var list = from m in listWorkFromMCS
                       where m.FieldBusType == EnumWorkConDataSource.ModbusOnTcp
                       select m;
            listWorkFromMCS = list.ToList();
            //排序工况测量位置列表
            List<MeasLoc_Process> processList = new List<MeasLoc_Process>();
            List<KeyValuePair<int, string>> parmeTypelist = EnumWorkCondParamTypeHelper.GetParamTypeList();
            foreach (var process in parmeTypelist)
            {
                EnumWorkCondition_ParamType processType = (EnumWorkCondition_ParamType)process.Key;
                MeasLoc_Process measLocProcess = listWorkFromMCS.Find(item => item.Param_Type_Code == processType);
                if(measLocProcess !=null)
                {
                    processList.Add(measLocProcess);
                }
            }
            processList.RemoveAll(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_YAWState);
            return Ok(processList);
        }

        /// <summary>
        /// 获取工况测量位置列表（状态寄存器）
        /// </summary>
        /// <param name="trubineId"></param>
        /// <returns></returns>
        public string GetWorkFromStateMcs(string trubineId)
        {
            List<MeasLoc_Process> listWorkFromMCS = DevTreeManagement.GetNotUsedMCSChanLocProcessList(trubineId);
            List<MCSChannelValueParam> McsChannelValueList = DAUMCS.GetMCSChannelValueListByTurID(trubineId);
            List<KeyValuePair<int, string>> parmeTypelist = EnumWorkCondParamTypeHelper.GetParamTypeList();
            foreach (MCSChannelValueParam data in McsChannelValueList)
            {  // 如果在数据寄存器中已添加，就删除对应的工况测量信息
                EnumWorkCondition_ParamType ParmaType = (EnumWorkCondition_ParamType)parmeTypelist.Find(item => item.Value == data.ParamMeaning).Key;
                listWorkFromMCS.RemoveAll(item => item.Param_Type_Code == ParmaType);
            }
            var list = from m in listWorkFromMCS
                       where m.FieldBusType == EnumWorkConDataSource.ModbusOnTcp
                       select m;
            listWorkFromMCS = list.ToList();
            //排序工况测量位置列表
            List<MeasLoc_Process> processList = new List<MeasLoc_Process>();
            foreach (var processName in parmeTypelist)
            {
                EnumWorkCondition_ParamType ParmaType = (EnumWorkCondition_ParamType)processName.Key;
                MeasLoc_Process measLocProcess = listWorkFromMCS.Find(item => item.Param_Type_Code == ParmaType);
                if (measLocProcess != null)
                {
                    processList.Add(measLocProcess);
                }
            }
            processList.RemoveAll(item => item.Param_Type_Code != EnumWorkCondition_ParamType.WCPT_YAWState);
            return processList.ToJson();
        }

        /// <summary>
        /// 寄存器类型
        /// </summary>
        /// <param name="trubineId"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetStateRegisterType")]
        public IActionResult GetStateRegisterType()
        {
            List<KeyValuePair<string, EnumMCSChannelType>> _RegisterTypeList = CommonRefData.GetMCSChannelTypeList();
            return Ok(_RegisterTypeList);
        }
        /// <summary>
        /// 数据存储方式
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("GetRegisterStorage")]
        public IActionResult GetRegisterStorage()
        {

            List<KeyValuePair<string, EnumMCSChannelStorageType>> _StorageTypeList = CommonRefData.GetMCSChannelStorageTypeList();
            return Ok(_StorageTypeList);
        }
        /// <summary>
        /// 对齐方式
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("GetByteArrayType")]
        public IActionResult GetByteArrayType()
        {
            List<KeyValuePair<string, EnumMCSChannelByteArrayType>> _ByteArrayTypeList = CommonRefData.GetMCSChannelByteArrayTypeList();
            return Ok(_ByteArrayTypeList);
        }
        /// <summary>
        /// 判断模式下拉列表
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("GetJudgeType")]
        public IActionResult GetJudgeType()
        {
            List<KeyValuePair<string, EnumMCSChannelJudgeType>> _JudgeTypeList = CommonRefData.GetMCSChannelJudgeTypeList();
            return Ok(_JudgeTypeList);
        }

        #endregion

        #region 数据寄存器

        /// <summary>
        /// 获取数据寄存器列表
        /// </summary>
        /// <param name="windTurbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetMCSData")]
        public IActionResult GetMCSDataInfoTable(string windTurbineID)
        {
            WindTurbine windTurbineMCS = DevTreeManagement.GetAllWindTurbine(windTurbineID);
            List<MCSChannelValueParam> McsChannelValueList = DAUMCS.GetMCSChannelValueListByTurID(windTurbineID);

            // 兼容之前数据
            McsChannelValueList.ForEach(i =>
            {
                if (i.ParamMeaning == "发电机转速")
                    i.ParamMeaning = "发电机转速(MCS)";
            });

            List<MCSChannelValueParam> McsChannelValueOrderList = new List<MCSChannelValueParam>();
            //排序工况测量位置列表
            List<MeasLoc_Process> processList = new List<MeasLoc_Process>();
            foreach (string processName in DevTree.GetMeasLocProcessOrderSeqList())
            {

                MCSChannelValueParam measLocProcess = McsChannelValueList.Find(item => item.ParamMeaning == processName);
                if (measLocProcess != null)
                {
                    McsChannelValueOrderList.Add(measLocProcess);
                }
            }
            return Ok(McsChannelValueOrderList);
        }
        /// <summary>
        /// 批量新增数据寄存器（全部成功或全部失败）
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost("BatchAddMCSRegister")]
        [BatchOperation(nameof(BatchAddMCSRegisterBatch))]
        public IActionResult BatchAddMCSRegister([FromBody] BatchAddMCSRegisterRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var errorMsgs = new List<string>();
                        foreach (var dto in request.SourceData)
                        {
                            // 判断寄存器地址是否重复
                            if (DAUMCS.IsExistMCSChannel(dto.WindTurbineID, dto.ChannelNumber))
                            {
                                errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的寄存器地址 {dto.ChannelNumber} 已存在");
                                continue;
                            }
                            // 判断工况类型的寄存器是否已存在
                            var existValueChannel = GetMCSChannelValue(dto.WindTurbineID, dto.MeasLocProcessID);
                            if (existValueChannel != null)
                            {
                                errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的工况类型 {dto.MeasLocProcessID} 已存在，地址为 {existValueChannel.ChannelNumber}");
                                continue;
                            }
                        }
                        if (errorMsgs.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error(string.Join("；", errorMsgs)));
                        }
                        foreach (var dto in request.SourceData)
                        {
                            ctx.MCSRegisters.Add(dto);
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量新增成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddMCSRegister]批量新增数据寄存器失败", ex);
                        return Ok(ApiResponse<string>.Error("批量新增失败: " + ex.Message));
                    }
                }
            }
        }

        /// <summary>
        /// 批量编辑数据寄存器（全部成功或全部失败）
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [HttpPost("BatchEditMCSRegister")]
        [BatchOperation(nameof(BatchEditMCSRegisterBatch))]
        public IActionResult BatchEditMCSRegister([FromBody] BatchEditMCSRegisterRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var errorMsgs = new List<string>();
                        foreach (var dto in request.SourceData)
                        {
                            var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(
                                x => x.WindTurbineID == dto.WindTurbineID && x.MeasLocProcessID == dto.MeasLocProcessID);
                            if (exist == null)
                            {
                                errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的工况类型 {dto.MeasLocProcessID} 不存在，无法编辑");
                                continue;
                            }
                            // 判断寄存器地址是否被其他工况类型占用
                            var addressConflict = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(
                                x => x.WindTurbineID == dto.WindTurbineID && x.ChannelNumber == dto.ChannelNumber && x.MeasLocProcessID != dto.MeasLocProcessID);
                            if (addressConflict != null)
                            {
                                errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的寄存器地址 {dto.ChannelNumber} 已被其他工况类型占用");
                                continue;
                            }
                        }
                        if (errorMsgs.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error(string.Join("；", errorMsgs)));
                        }
                        foreach (var dto in request.SourceData)
                        {
                            var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().First(
                                x => x.WindTurbineID == dto.WindTurbineID && x.MeasLocProcessID == dto.MeasLocProcessID);
                            exist.ChannelNumber = dto.ChannelNumber;
                            exist.RegisterType = dto.RegisterType;
                            exist.RegisterStorageType = dto.RegisterStorageType;
                            exist.ByteArrayType = dto.ByteArrayType;
                            exist.Coeff = dto.Coeff;
                            exist.ByteSwap = dto.ByteSwap;
                            ctx.Entry(exist).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量编辑成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchEditMCSRegister]批量编辑数据寄存器失败", ex);
                        return Ok(ApiResponse<string>.Error("批量编辑失败: " + ex.Message));
                    }
                }
            }
        }

        /// <summary>
        /// 批量删除数据寄存器（全部成功或全部失败）
        /// </summary>
        /// <param name="deleteList"></param>
        /// <returns></returns>
        [HttpPost("BatchDeleteMCSRegister")]
        [BatchOperation(nameof(BatchDeleteMCSRegisterBatch))]
        public IActionResult BatchDeleteMCSRegister([FromBody] BatchDeleteMCSRegisterRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                using (var tran = ctx.Database.BeginTransaction())
                {
                    try
                    {
                        var errorMsgs = new List<string>();
                        foreach (var item in request.SourceData)
                        {
                            var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(
                                x => x.WindTurbineID == item.WindTurbineID && x.MeasLocProcessID == item.MeasLocProcessID);
                            if (exist == null)
                            {
                                errorMsgs.Add($"WindTurbineID={item.WindTurbineID} 的工况类型 {item.MeasLocProcessID} 不存在，无法删除");
                                continue;
                            }
                            // 检查是否被测量定义使用
                            if (MeasDefinitionManagement.GetWorkCondMeasLocUsedByMeasLocId(item.WindTurbineID, item.MeasLocProcessID))
                            {
                                errorMsgs.Add($"WindTurbineID={item.WindTurbineID} 的工况类型 {item.MeasLocProcessID} 正在被测量定义使用，无法删除");
                                continue;
                            }
                        }
                        if (errorMsgs.Count > 0)
                        {
                            tran.Rollback();
                            return Ok(ApiResponse<string>.Error(string.Join("；", errorMsgs)));
                        }
                        foreach (var item in request.SourceData)
                        {
                            var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().First(
                                x => x.WindTurbineID == item.WindTurbineID && x.MeasLocProcessID == item.MeasLocProcessID);
                            ctx.MCSRegisters.Remove(exist);
                        }
                        ctx.SaveChanges();
                        tran.Commit();
                        return Ok(ApiResponse<string>.Success("批量删除成功"));
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteMCSRegister]批量删除数据寄存器失败", ex);
                        return Ok(ApiResponse<string>.Error("批量删除失败: " + ex.Message));
                    }
                }
            }
        }



        /// <summary>
        /// 批量添加MCS寄存器 - 批量操作实现
        /// </summary>
        /// <param name="request">批量操作请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddMCSRegisterBatch(string targetTurbineId,BatchAddMCSRegisterRequest request)
        {
            var result = new List<string>();

            try
            {
                // 为目标机组创建新的MCS寄存器列表
                var newMCSRegisters = new List<MCSChannelValueParam>();

                foreach (var mcsRegister in request.SourceData)
                {
                    // 创建新的MCS寄存器，替换机组ID
                    var newMCSRegister = new MCSChannelValueParam
                    {
                        WindTurbineID = targetTurbineId,
                        ChannelNumber = mcsRegister.ChannelNumber,
                        MeasLocProcessID = mcsRegister.MeasLocProcessID?.Replace(mcsRegister.WindTurbineID, targetTurbineId),
                        RegisterType = mcsRegister.RegisterType,
                        RegisterStorageType = mcsRegister.RegisterStorageType,
                        ByteArrayType = mcsRegister.ByteArrayType,
                        Coeff = mcsRegister.Coeff,
                        ByteSwap = mcsRegister.ByteSwap,
                        ParamMeaning = mcsRegister.ParamMeaning,
                        DataType = mcsRegister.DataType
                    };

                    if (string.IsNullOrEmpty(newMCSRegister.WindTurbineID) ||
                        string.IsNullOrEmpty(newMCSRegister.MeasLocProcessID))
                    {
                        result.Add($"机组 {targetTurbineId} MCS寄存器参数不完整");
                        continue;
                    }

                    var measloc = DevTreeManagement.GetWorkCondMeasLocation(newMCSRegister.MeasLocProcessID);
                    if(measloc == null)
                    {
                        result.Add($"机组 {targetTurbineId} 没有工况测量位置：{newMCSRegister.MeasLocProcessID}");
                        continue;
                    }

                    newMCSRegisters.Add(newMCSRegister);
                }

                if (newMCSRegisters.Count == 0)
                {
                    result.Add($"机组 {targetTurbineId} 没有有效的MCS寄存器需要添加");

                }

                // 批量添加到目标机组
                using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
                {
                    using (var tran = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            var errorMsgs = new List<string>();
                            foreach (var dto in newMCSRegisters)
                            {
                                // 判断寄存器地址是否重复
                                if (DAUMCS.IsExistMCSChannel(dto.WindTurbineID, dto.ChannelNumber))
                                {
                                    errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的寄存器地址 {dto.ChannelNumber} 已存在");
                                    continue;
                                }
                                // 判断工况类型的寄存器是否已存在
                                var existValueChannel = GetMCSChannelValue(dto.WindTurbineID, dto.MeasLocProcessID);
                                if (existValueChannel != null)
                                {
                                    errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的工况类型 {dto.MeasLocProcessID} 已存在，地址为 {existValueChannel.ChannelNumber}");
                                    continue;
                                }
                            }

                            if (errorMsgs.Count > 0)
                            {
                                tran.Rollback();
                                result.Add($"机组 {targetTurbineId} 添加失败: {string.Join("；", errorMsgs)}");
                           
                            }

                            foreach (var dto in newMCSRegisters)
                            {
                                ctx.MCSRegisters.Add(dto);
                            }
                            ctx.SaveChanges();
                            tran.Commit();
                            result.Add($"机组 {targetTurbineId} MCS寄存器批量添加成功，共添加 {newMCSRegisters.Count} 个");
                        }
                        catch (Exception ex)
                        {
                            tran.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddMCSRegister]机组 {targetTurbineId} 批量添加MCS寄存器失败", ex);
                            result.Add($"机组 {targetTurbineId} 添加失败: {ex.Message}");
                        }
                    }
                }
             
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchAddMCSRegister]批量添加MCS寄存器失败", ex);
            }

            return result;
        }

        /// <summary>
        /// 批量编辑MCS寄存器 - 批量操作实现
        /// </summary>
        /// <param name="request">批量操作请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditMCSRegisterBatch(string targetTurbineId,BatchEditMCSRegisterRequest request)
        {
            var result = new List<string>();

            try
            {
                // 缓存原始记录
                var originalRecords = new List<MCSChannelValueParam>();
                foreach (var mcsRegister in request.SourceData)
                {
                    var record = GetMCSChannelValue(mcsRegister.WindTurbineID, mcsRegister.MeasLocProcessID);
                    if (record != null) originalRecords.Add(record);
                }
                HttpContext.Items["OriginalMCSRegisterRecords"] = originalRecords;

                try
                {
                    // 为目标机组创建编辑的MCS寄存器列表
                    var editMCSRegisters = new List<MCSChannelValueParam>();

                    foreach (var mcsRegister in request.SourceData)
                    {
                        // 在目标机组中查找对应的MCS寄存器
                        var targetMeasLocProcessID = mcsRegister.MeasLocProcessID?.Replace(mcsRegister.WindTurbineID, targetTurbineId);
                        var targetMCSRegister = GetMCSChannelValue(targetTurbineId, targetMeasLocProcessID);

                        if (targetMCSRegister != null)
                        {
                            // 更新寄存器属性
                            targetMCSRegister.ChannelNumber = mcsRegister.ChannelNumber;
                            targetMCSRegister.RegisterType = mcsRegister.RegisterType;
                            targetMCSRegister.RegisterStorageType = mcsRegister.RegisterStorageType;
                            targetMCSRegister.ByteArrayType = mcsRegister.ByteArrayType;
                            targetMCSRegister.Coeff = mcsRegister.Coeff;
                            targetMCSRegister.ByteSwap = mcsRegister.ByteSwap;

                            editMCSRegisters.Add(targetMCSRegister);
                        }
                    }

                    if (editMCSRegisters.Count == 0)
                    {
                        result.Add($"机组 {targetTurbineId} 没有找到对应的MCS寄存器需要编辑");
                    }

                    // 批量编辑目标机组的寄存器
                    using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
                    {
                        using (var tran = ctx.Database.BeginTransaction())
                        {
                            try
                            {
                                var errorMsgs = new List<string>();
                                foreach (var dto in editMCSRegisters)
                                {
                                    var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(
                                        x => x.WindTurbineID == dto.WindTurbineID && x.MeasLocProcessID == dto.MeasLocProcessID);
                                    if (exist == null)
                                    {
                                        errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的工况类型 {dto.MeasLocProcessID} 不存在，无法编辑");
                                        continue;
                                    }
                                    // 判断寄存器地址是否被其他工况类型占用
                                    var addressConflict = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(
                                        x => x.WindTurbineID == dto.WindTurbineID && x.ChannelNumber == dto.ChannelNumber && x.MeasLocProcessID != dto.MeasLocProcessID);
                                    if (addressConflict != null)
                                    {
                                        errorMsgs.Add($"WindTurbineID={dto.WindTurbineID} 的寄存器地址 {dto.ChannelNumber} 已被其他工况类型占用");
                                        continue;
                                    }
                                }

                                if (errorMsgs.Count > 0)
                                {
                                    tran.Rollback();
                                    result.Add($"机组 {targetTurbineId} 编辑失败: {string.Join("；", errorMsgs)}");
                                }

                                foreach (var dto in editMCSRegisters)
                                {
                                    var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().First(
                                        x => x.WindTurbineID == dto.WindTurbineID && x.MeasLocProcessID == dto.MeasLocProcessID);
                                    exist.ChannelNumber = dto.ChannelNumber;
                                    exist.RegisterType = dto.RegisterType;
                                    exist.RegisterStorageType = dto.RegisterStorageType;
                                    exist.ByteArrayType = dto.ByteArrayType;
                                    exist.Coeff = dto.Coeff;
                                    exist.ByteSwap = dto.ByteSwap;
                                    ctx.Entry(exist).State = Microsoft.EntityFrameworkCore.EntityState.Modified;
                                }
                                ctx.SaveChanges();
                                tran.Commit();
                                result.Add($"机组 {targetTurbineId} MCS寄存器批量编辑成功，共编辑 {editMCSRegisters.Count} 个");
                            }
                            catch (Exception ex)
                            {
                                tran.Rollback();
                                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditMCSRegister]机组 {targetTurbineId} 批量编辑MCS寄存器失败", ex);
                                result.Add($"机组 {targetTurbineId} 编辑失败: {ex.Message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditMCSRegister]处理机组 {targetTurbineId} 时发生错误", ex);
                    result.Add($"机组 {targetTurbineId} 处理失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchEditMCSRegister]批量编辑MCS寄存器失败", ex);
            }
            return result;
        }

        /// <summary>
        /// 批量删除MCS寄存器 - 批量操作实现
        /// </summary>
        /// <param name="request">批量操作请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteMCSRegisterBatch(string targetTurbineId, BatchDeleteMCSRegisterRequest request)
        {
            var result = new List<string>();

            try
            {
                // 缓存原始记录
                var originalRecords = new List<MCSChannelValueParam>();
                foreach (var mcsRegister in request.SourceData)
                {
                    var record = GetMCSChannelValue(mcsRegister.WindTurbineID, mcsRegister.MeasLocProcessID);
                    if (record != null) originalRecords.Add(record);
                }
                HttpContext.Items["OriginalMCSRegisterRecords"] = originalRecords;

                try
                {
                    // 为目标机组创建删除的MCS寄存器列表
                    var deleteMCSRegisters = new List<MCSRegisterDeleteDTO>();

                    foreach (var mcsRegister in request.SourceData)
                    {
                        // 在目标机组中查找对应的MCS寄存器
                        var targetMeasLocProcessID = mcsRegister.MeasLocProcessID?.Replace(mcsRegister.WindTurbineID, targetTurbineId);
                        var targetMCSRegister = GetMCSChannelValue(targetTurbineId, targetMeasLocProcessID);

                        if (targetMCSRegister != null)
                        {
                            deleteMCSRegisters.Add(new MCSRegisterDeleteDTO
                            {
                                WindTurbineID = targetTurbineId,
                                MeasLocProcessID = targetMeasLocProcessID
                            });
                        }
                    }

                    if (deleteMCSRegisters.Count == 0)
                    {
                        result.Add($"机组 {targetTurbineId} 没有找到对应的MCS寄存器需要删除");
                    }

                    // 批量删除目标机组的寄存器
                    using (var ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
                    {
                        using (var tran = ctx.Database.BeginTransaction())
                        {
                            try
                            {
                                var errorMsgs = new List<string>();
                                foreach (var item in deleteMCSRegisters)
                                {
                                    var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(
                                        x => x.WindTurbineID == item.WindTurbineID && x.MeasLocProcessID == item.MeasLocProcessID);
                                    if (exist == null)
                                    {
                                        errorMsgs.Add($"WindTurbineID={item.WindTurbineID} 的工况类型 {item.MeasLocProcessID} 不存在，无法删除");
                                        continue;
                                    }
                                    // 检查是否被测量定义使用
                                    if (MeasDefinitionManagement.GetWorkCondMeasLocUsedByMeasLocId(item.WindTurbineID, item.MeasLocProcessID))
                                    {
                                        errorMsgs.Add($"WindTurbineID={item.WindTurbineID} 的工况类型 {item.MeasLocProcessID} 正在被测量定义使用，无法删除");
                                        continue;
                                    }
                                }

                                if (errorMsgs.Count > 0)
                                {
                                    tran.Rollback();
                                    //result.Add($"机组 {targetTurbineId} 删除失败: {string.Join("；", errorMsgs)}");
                                }

                                foreach (var item in deleteMCSRegisters)
                                {
                                    var exist = ctx.MCSRegisters.OfType<MCSChannelValueParam>().First(
                                        x => x.WindTurbineID == item.WindTurbineID && x.MeasLocProcessID == item.MeasLocProcessID);
                                    ctx.MCSRegisters.Remove(exist);
                                }
                                ctx.SaveChanges();
                                tran.Commit();
                                result.Add($"机组 {targetTurbineId} MCS寄存器批量删除成功，共删除 {deleteMCSRegisters.Count} 个");
                            }
                            catch (Exception ex)
                            {
                                tran.Rollback();
                                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMCSRegister]机组 {targetTurbineId} 批量删除MCS寄存器失败", ex);
                                result.Add($"机组 {targetTurbineId} 删除失败: {ex.Message}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMCSRegister]处理机组 {targetTurbineId} 时发生错误", ex);
                    result.Add($"机组 {targetTurbineId} 处理失败: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteMCSRegister]批量删除MCS寄存器失败", ex);
            }
            return result;
        }

        /// <summary>
        /// 编辑主控制数据寄存器
        /// </summary>
        /// <param name="mcsId"></param>
        /// <param name="ChannelNumber"></param>
        /// <param name="MeasLocProcessName"></param>
        /// <param name="RegisterType"></param>
        /// <param name="RegisterStorageType"></param>
        /// <param name="ByteArrayType"></param>
        /// <param name="Coeff"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public string EditMcsChannel(string mcsId, string ChannelNumber, string MeasLocProcessId, string MeasLocProcessName, string RegisterType, string RegisterStorageType, string ByteArrayType, string Coeff, string isAddType)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                if (string.IsNullOrEmpty(MeasLocProcessId))
                {
                    Message = string.Format(Message, 0, "参数异常，请刷新后重试！");
                    return "{" + Message + "}";
                }
                string actionName = "添加";
                MCSChannelValueParam mcschannel = new MCSChannelValueParam();
                if (isAddType == "1")
                {
                    mcschannel.WindTurbineID = mcsId;
                    // 参数含义获取工况参数的名称
                    string loc_ProId = MeasLocProcessId;
                    mcschannel.ParamMeaning = GetMCSChannelParamMeaning(loc_ProId);
                }
                else
                {
                    mcschannel = GetExistValueChannel(mcsId, MeasLocProcessId);
                    actionName = "修改";
                }
                mcschannel.ChannelNumber = ChannelNumber;
                int type = int.Parse(RegisterType);
                mcschannel.RegisterType = (EnumMCSChannelType)(type);
                mcschannel.MeasLocProcessID = MeasLocProcessId;

                // add by Yangming @ 2012-08-16
                int registerStorageType = int.Parse(RegisterStorageType);
                mcschannel.RegisterStorageType = (EnumMCSChannelStorageType)(registerStorageType);
                mcschannel.Coeff = float.Parse(Coeff);

                mcschannel.ByteArrayType = (EnumMCSChannelByteArrayType)int.Parse(ByteArrayType);
                if (isAddType == "1")
                {
                    // 判断寄存器地址是否重复
                    if (DAUMCS.IsExistMCSChannel(mcsId, ChannelNumber))
                    {
                        Message = string.Format(Message, 0, "该寄存器地址已存在！");
                        return "{" + Message + "}";
                    }
                    MCSChannelValueParam existValueChannel = GetExistValueChannel(mcsId, MeasLocProcessId);
                    if (existValueChannel != null)
                    {
                        Message = string.Format(Message, 0, "此工况类型的寄存器已存在，地址为" + existValueChannel.ChannelNumber + "！");
                        return "{" + Message + "}";
                    }
                    DAUMCS.AddMCSChannelValue(mcschannel);
                    Message = string.Format(Message, 1, "");
                }
                else
                {
                    DAUMCS.EditMCSChanValue(MeasLocProcessId, mcschannel);
                    Message = string.Format(Message, 1, "");
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocProcessId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_主控制数据寄存器({0})", MeasLocProcessId, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception myex)
            {
                Message = string.Format(Message, 0, myex.Message);
            }
            return "{" + Message + "}";
        }

        //-----------------------------------------------------------------------------------------------------------------------
        // Author: ym
        // Create: 2012-08-21
        /// <summary>
        /// 获取寄存器参数含义
        /// </summary>
        /// <returns></returns>
        private string GetMCSChannelParamMeaning(string _loc_ProId)
        {
            MeasLoc_Process selMeasLoc_Pro = DevTreeManagement.GetWorkCondMeasLocation(_loc_ProId);
            //工况信息已枚举的描述信息为准
            //@wangy  2016年9月26日
            string paramMeaning = AppFramework.Utility.EnumHelper.GetDescription(selMeasLoc_Pro.Param_Type_Code);
            return paramMeaning;
        }

        // Author: ym
        // Create: 2012-08-21
        /// <summary>
        /// 查找选定的工况测量位置绑定的数据寄存器
        /// </summary>
        /// <returns></returns>
        private MCSChannelValueParam GetExistValueChannel(string mcsId, string loc_ProId)
        {
            //string paramMeaning = GetMCSChannelParamMeaning(loc_ProId);
            MCSChannelValueParam channelValue = GetMCSChannelValue(mcsId, loc_ProId);
            return channelValue;
        }


        /// <summary>
        /// 获取数据寄存器对象
        /// </summary>
        /// <param name="_mcsId"></param>
        /// <param name="measloc"></param>
        /// <returns></returns>
        public static MCSChannelValueParam GetMCSChannelValue(string _mcsId, string measloc)
        {
            MCSChannelValueParam ValueParam = null;
            using (CMSFramework.EF.MCSContext ctx = new CMSFramework.EF.MCSContext(ConfigInfo.DBConnName))
            {
                ValueParam = ctx.MCSRegisters.OfType<MCSChannelValueParam>().FirstOrDefault(item => item.WindTurbineID == _mcsId && item.MeasLocProcessID == measloc);
            }
            return ValueParam;
        }

        /// <summary>
        /// 删除数据寄存器信息
        /// </summary>
        /// <param name="mcsId"></param>
        /// <param name="MeasLocProcessId"></param>
        /// <returns></returns>
        public string DeleteMCSChannel(string turbindId, string mcsId, string MeasLocProcessId)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                //查看该寄存器是否被测量定义使用
                if (MeasDefinitionManagement.GetWorkCondMeasLocUsedByMeasLocId(turbindId, MeasLocProcessId))
                {
                    return "{" + string.Format(Message, 0,"该寄存器信息正在被测量定义使用，请检查后再删除！") + "}";
                }
                DAUMCS.DeleteMCSChan(mcsId, GetMCSChannelParamMeaning(MeasLocProcessId));

                Message = string.Format(Message, 1, "");
            }
            catch (Exception myex)
            {
                Message = string.Format(Message, 0, myex.Message);
            }
            return "{" + Message + "}";
        }
        #endregion

        #region 状态寄存器
        /// <summary>
        /// 编辑主控制状态寄存器
        /// </summary>
        /// <param name="mcsId"></param>
        /// <param name="ChannelNumber"></param>
        /// <param name="MeasLocProcessId"></param>
        /// <param name="MeasLocProcessName"></param>
        /// <param name="RegisterType"></param>
        /// <param name="RegisterStorageType"></param>
        /// <param name="ByteArrayType"></param>
        /// <param name="Coeff"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public string EditMcsStateChannel(string mcsId, string ChannelNumber, string MeasLocProcessId, string MeasLocProcessName, string RegisterType, string JudgeType, string StateRegisterParmaValues, string isAddType)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                string actionName = "添加";
                MCSChannelStateParam mcschannel = new MCSChannelStateParam();
                if (isAddType == "1")
                {
                    mcschannel.WindTurbineID = mcsId;
                    mcschannel.ParamMeaning = GetMCSChannelParamMeaning(MeasLocProcessId);
                }
                else
                {
                    mcschannel = GetExistStateChannel(mcsId, MeasLocProcessId);
                }
                mcschannel.ChannelNumber = ChannelNumber;
                if (!string.IsNullOrEmpty(RegisterType))
                {
                    int type = int.Parse(RegisterType);
                    mcschannel.RegisterType = (EnumMCSChannelType)(type);
                }
                mcschannel.DataType = EnumMCSChannelDataType.Status;
                mcschannel.MeasLocProcessID = MeasLocProcessId;
                mcschannel.RegisterStorageType = EnumMCSChannelStorageType.DataInteger; // 默认16位单值整数
                mcschannel.ByteArrayType = EnumMCSChannelByteArrayType.Big_Endian; // 默认大端
                if (!string.IsNullOrEmpty(JudgeType))
                {
                    EnumMCSChannelJudgeType judgeType = (EnumMCSChannelJudgeType)int.Parse(JudgeType);
                    mcschannel.JudgeType = judgeType;
                }
                if (!string.IsNullOrEmpty(StateRegisterParmaValues))
                {
                    List<short> stateValues = GetStateValuesToList(StateRegisterParmaValues);
                    mcschannel.StateValues = stateValues;
                }

                if (isAddType == "1")
                {
                    // 判断寄存器地址是否重复
                    if (CheckExistChannelNumber(ChannelNumber, mcsId))
                    {
                        Message = string.Format(Message, 0, "该寄存器地址已存在！");
                        return "{" + Message + "}";
                    }
                    // 判断寄存器是否已经存在
                    MCSChannelStateParam channelState = GetExistStateChannel(mcsId, MeasLocProcessId);
                    if (channelState != null)
                    {
                        Message = string.Format(Message, 0, "此工况类型的寄存器已存在，地址为" + channelState.ChannelNumber + "！");
                        return "{" + Message + "}";
                    }
                    DAUMCS.AddMCSChannelState(mcschannel);
                    Message = string.Format(Message, 1, "");
                }
                else
                {
                    DAUMCS.EditMCSChanState(MeasLocProcessId, mcschannel);
                    Message = string.Format(Message, 1, "");
                }

                #region ---添加日志---
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = MeasLocProcessId;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_主控制状态寄存器({0})", MeasLocProcessId, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception myex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("编辑状态寄存器", myex);
            }
            return "{" + Message + "}";
        }

        // Author: ym
        // Create: 2012-08-22
        /// <summary>
        /// 检查寄存器地址是否存在
        /// </summary>
        /// <returns></returns>
        private bool CheckExistChannelNumber(string _channelNum, string mcsId)
        {
            bool result = DAUMCS.IsExistMCSChannel(mcsId, _channelNum);
            return result;
        }

        // Author: ym
        // Create: 2012-08-21
        /// <summary>
        /// 查找选定的工况测量位置绑定的状态寄存器
        /// </summary>
        /// <returns></returns>
        private MCSChannelStateParam GetExistStateChannel(string mcsId, string loc_ProId)
        {
            string paramMeaning = GetMCSChannelParamMeaning(loc_ProId);
            return DAUMCS.GetMCSChannelState(mcsId, paramMeaning);
            //MCSChannelStateParam channelState = Presenter.GetMCSChannelStateById(mcsId, paramMeaning);
            //return channelState;
        }

        // Author: ym
        // Create: 2012-08-16
        /// <summary>
        /// 转换字符串为List<ushort>
        /// </summary>
        /// <param name="_inputValues"></param>
        /// <returns></returns>
        private List<short> GetStateValuesToList(string _inputValues)
        {
            string[] intNum = _inputValues.Split(',');
            List<short> stateValueList = new List<short>();
            for (int i = 0; i < intNum.Length; i++)
            {
                if (intNum[i] != "")
                {
                    short value = short.Parse(intNum[i]);
                    stateValueList.Add(value);
                }
            }
            return stateValueList;
        }
        #endregion

        #endregion
    }
}
