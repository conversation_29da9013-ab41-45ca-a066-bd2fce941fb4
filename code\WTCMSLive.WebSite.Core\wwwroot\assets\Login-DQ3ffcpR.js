import{dg as v,y as h,r as y,c as w,i as l,b as o,d as t,a as b,u as B,o as x,g as F,t as S,m as i,dk as k}from"./index-D9CxWmlM.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{F as N,_ as R}from"./index-CgGbkMmC.js";import{I as T,p as V}from"./index-DMbgFMzZ.js";import{B as $}from"./index-BGEB0Rhf.js";import"./styleChecker-z-opWSaj.js";import"./initDefaultProps-C2vKchlZ.js";const z="/logo.svg",L={class:"login-container"},U={__name:"Login",setup(A){const p=v(),m=b();B();const s=h({username:"",password:""}),n=y(!1),c=async()=>{try{n.value=!0,await p.fetchlogin(s),i.success("登录成功"),k(),await new Promise(e=>setTimeout(e,0)),m.push("/")}catch(e){console.error("登录失败:",e),i.error(e.message||"登录失败，请稍后重试")}finally{n.value=!1}};return(e,a)=>{const d=T,r=R,_=V,g=$,f=N;return x(),w("div",L,[a[2]||(a[2]=l("div",{class:"logoBox"},[l("div",{class:"header"},[l("img",{src:z,class:"logo",alt:"logo"}),l("span",{class:"title"},"配置网站")])],-1)),o(f,{class:"user-layout-login",model:s,onFinish:c},{default:t(()=>[o(r,null,{default:t(()=>[o(d,{size:"large",type:"text",placeholder:e.$t("user.login.username.placeholder"),value:s.username,"onUpdate:value":a[0]||(a[0]=u=>s.username=u)},null,8,["placeholder","value"])]),_:1}),o(r,null,{default:t(()=>[o(_,{size:"large",placeholder:e.$t("user.login.password.placeholder"),value:s.password,"onUpdate:value":a[1]||(a[1]=u=>s.password=u)},null,8,["placeholder","value"])]),_:1}),o(r,{style:{"margin-top":"24px"}},{default:t(()=>[o(g,{type:"primary",htmlType:"submit",class:"login-button",loading:n.value,disabled:n.value},{default:t(()=>[F(S(e.$t("user.login.login")),1)]),_:1},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])])}}},H=I(U,[["__scopeId","data-v-4d946730"]]);export{H as default};
