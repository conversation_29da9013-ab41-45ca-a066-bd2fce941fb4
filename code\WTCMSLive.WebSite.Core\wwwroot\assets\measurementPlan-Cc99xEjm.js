import{u as U,W as V}from"./table-C_s53ALS.js";import{O as $}from"./index-DyCH3qIX.js";import{W as H}from"./index-R80zldKD.js";import{C as J,cy as Q,cz as X,cA as Y,cB as Z,r,u as ee,y as te,j as ae,w as se,f as I,d as k,o as b,c as _,b as x,m as d,aP as C}from"./index-D9CxWmlM.js";import{S as oe,c as L,f as ne}from"./tools-DZBuE28U.js";import{u as le}from"./measurementDefinition-CBAS_A3c.js";import{u as ie}from"./devTree-BD8aiOqS.js";import{M as re}from"./index-DhZUqjZm.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";/* empty css                                                              */const ue=J("configMeasurementPlan",{state:()=>({measSolutionList:[]}),actions:{reset(){this.$reset()},async fetchGetMeasSolutionList(u){try{const t=await Z(u);return t&&t.length>0&&(this.measSolutionList=t),t}catch(t){throw console.error("获取失败:",t),t}},async fetchBatchAddMeasSolutions(u){try{return await Y(u)}catch(t){throw console.error(t),t}},async fetchEditMeasSolution(u){try{return await X(u)}catch(t){throw console.error(t),t}},async fetchBatchDeleteMeasSolutions(u){try{return await Q(u)}catch(t){throw console.error(t),t}}}}),ce={key:2},Le={__name:"measurementPlan",setup(u){const t=ue(),m=le(),W=U(),q=ie();let h=320;const S=(e={isform:!1})=>{let a=[{label:"传动链",value:"传动链",text:"传动链"},{label:"叶片",value:"叶片",text:"叶片"},{label:"塔筒",value:"塔筒",text:"塔筒"}];return[{title:"测量方案",dataIndex:"measSolutionName",formItemWidth:h,columnWidth:200,isrequired:!0},{title:"测量方案类型",dataIndex:"measSolutionType",formItemWidth:h,columnWidth:200,isrequired:!0,inputType:"select",selectOptions:a,headerOperations:{filters:a}},{title:"测量定义",dataIndex:"measDefinitionIDList",formItemWidth:h,columnWidth:200,isrequired:!0,inputType:"select",selectOptions:[],mode:"multiple",...e&&e.isform?{}:{customRender:({record:s})=>s.measDefinitions&&s.measDefinitions.length?s.measDefinitions.map(o=>o.key).join(","):""}},{title:"波形采集间隔(分)",dataIndex:"waveInterval",formItemWidth:h,columnWidth:150,validateRules:L({type:"number",title:"波形采集间隔",required:!0})},{title:"特征值采集间隔(分)",dataIndex:"eigenInterval",formItemWidth:h,columnWidth:150,validateRules:L({type:"number",title:"特征值采集间隔",required:!0})}]},g=r(!1),w=r(!1),T=ee(),y=r(""),c=r(""),B=r(),f=r({}),A=r(""),i=r(T.params.id),n=te({tableColumns:S(),tableData:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{}}),p=r([]),P=async e=>{A.value&&await W.fetchDevTreedDevicelist({windParkID:A.value,useTobath:!0})},O=()=>{let e=q.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(A.value=e[e.length-2].id)},v=async e=>{g.value=!0,n.tableData=await t.fetchGetMeasSolutionList({WindTurbineID:i.value}),g.value=!1},E=ae(()=>c.value==="batchAdd"?"1200px":"600px");se(()=>T.params.id,async e=>{e&&(t.reset(),m.reset(),i.value=e,O(),await P(),v())},{immediate:!0});const M=()=>{w.value=!0},D=e=>{w.value=!1,p.value=[],c.value="",y.value="",f.value={}},F=e=>{const{title:a,operateType:s}=e;c.value=s,y.value="批量增加测量方案",p.value=[...S({isform:!0})],R(),M()},N=async e=>{const{selectedkeys:a}=e;if(!a||!a.length){d.warning("请选择要删除的行");return}let s=[];for(let l=0;l<a.length;l++)s.push({windTurbineID:i.value,measSolutionID:a[l]});const o=await t.fetchBatchDeleteMeasSolutions({sourceData:s,targetTurbineIds:n.batchApplyData});o&&o.code===1?(v(),n.bathApplyResponse1=o.batchResults||{},d.success("删除成功")):d.error("删除失败:"+o.msg)},z=e=>{const{rowData:a,title:s,operateType:o}=e;c.value=o,f.value={...a,measDefinitionIDList:a.measDefinitions&&a.measDefinitions.length?a.measDefinitions.map(l=>l.value):[]},y.value="编辑测量方案",p.value=[...S({isform:!0})],R(),M()},R=async e=>{let a=p.value;(!m.measdList||!m.measdList.length)&&await m.fetchGetMeasdList({turbineID:i.value});let s=m.measdList,o=[];s&&s.length&&s.map(l=>{l.mdf_Ex&&l.mdf_Ex.modelType==0&&o.push({value:l.measDefinitionID,label:l.measDefinitionName})}),a[2].selectOptions=o},G=async e=>{let a={measSolutionID:f.value.measSolutionID,...e,daqInterval:f.value.daqInterval,WindTurbineID:i.value},s=await t.fetchEditMeasSolution({sourceData:a,targetTurbineIds:n.batchApplyData});s&&s.code===1?(v(),n.bathApplyResponse1=s.batchResults||{},d.success("提交成功"),D()):d.error("提交失败:"+s.msg)},K=async e=>{let a=ne(e);if(a&&a.length){let s=a.map((l,de)=>({...l,daqInterval:0,windTurbineID:i.value})),o=await t.fetchBatchAddMeasSolutions({sourceData:s,targetTurbineIds:n.batchApplyData});o&&o.code===1?(v(),n.bathApplyResponse1=o.batchResults||{},d.success("提交成功"),D()):d.error("提交失败:"+o.msg)}},j=async e=>{e.type&&e.type=="close"?(n.batchApplyData=[],n.batchApplyKey="",n[`bathApplyResponse${e.key}`]={}):(n.batchApplyData=e.turbines,n.batchApplyKey=e.key)};return C("deviceId",i),C("bathApplySubmit",j),(e,a)=>{const s=re,o=oe;return b(),I(o,{spinning:g.value,size:"large"},{default:k(()=>[(b(),_("div",{key:i.value},[x(V,{ref:"table",size:"default","table-key":"0","table-title":"测量方案","table-columns":n.tableColumns,borderLight:n.batchApplyKey=="0",bathApplyResponse:n.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"measSolutionID","table-datas":n.tableData,onAddRow:F,onDeleteRow:N,onEditRow:z},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"]),x(s,{maskClosable:!1,width:E.value,open:w.value,title:y.value,footer:"",onCancel:D},{default:k(()=>[c.value==="add"||c.value==="edit"?(b(),I($,{key:0,titleCol:p.value,form:B.value,initFormData:f.value,onSubmit:G},null,8,["titleCol","form","initFormData"])):c.value==="batchAdd"?(b(),I(H,{key:1,ref:"table",size:"default","table-key":"0","table-columns":p.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":["measSolutionName","measDefinitionIDList"],onSubmit:K,onCancel:D},null,8,["table-columns"])):(b(),_("div",ce))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}};export{Le as default};
