import{O as w}from"./index-DyCH3qIX.js";import{u as _}from"./account-C64QhiRN.js";import{dg as g,r as d,c as v,b as I,o as h,m as l}from"./index-D9CxWmlM.js";import{_ as b}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./index-DMbgFMzZ.js";import"./shallowequal-vFdacwF3.js";import"./index-BS38043a.js";const y={class:"container"},P={__name:"password",setup(S){const m=_(),u=g();let s=window.localStorage.getItem("user"),t={};s&&(t=JSON.parse(s));const c=d({username:t.username}),a=d(null),p=async r=>{const o={...r,account:t.userId},e=await m.fetchChangePasswordr(o);e&&e.code==1?(l.success("修改密码成功！请重新登录！"),u.logout()):l.error("修改密码失败！")},f=[{title:"用户名",dataIndex:"username",formItemWidth:300,isrequired:!0,autocomplete:"username"},{title:"密码",dataIndex:"oldPassword",inputType:"password",visibilityToggle:!1,isrequired:!0,formItemWidth:300,autocomplete:"current-password"},{title:"新密码",dataIndex:"password1",inputType:"password",visibilityToggle:!1,isrequired:!0,formItemWidth:300,autocomplete:"new-password"},{title:"再次输入",dataIndex:"newPassword",inputType:"password",visibilityToggle:!1,isrequired:!0,formItemWidth:300,autocomplete:"new-password",validateRules:[{validator:async(r,o)=>{var n;const e=(n=a.value)==null?void 0:n.getFieldsValue();if(!e||!e.password1||!e.newPassword)return;let i=e.password1===e.newPassword;return i?Promise.resolve(i):Promise.reject(new Error("两次输入的密码不一致！"))},trigger:"change"}]}];return(r,o)=>(h(),v("div",y,[I(w,{ref_key:"formModalRef",ref:a,titleCol:f,initFormData:c.value,onSubmit:p,buttonsAlign:"center"},null,8,["initFormData"])]))}},E=b(P,[["__scopeId","data-v-c30b5a8b"]]);export{E as default};
