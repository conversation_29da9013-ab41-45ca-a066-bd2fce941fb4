﻿using AppFramework.IDUtility;
using CMSFramework.BusinessEntity;
using System.Web;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using WTCMSLive.BusinessModel.TIM;
using WTCMSLive.WebSite.Helpers;
using System.Data;
using Newtonsoft.Json;
using System.Text;
using OfficeOpenXml;
using Microsoft.AspNetCore.Mvc;
using Dapper;
using System.IO.Compression;
using WTCMSLive.WebSite.Core.Models;
using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CopyTurbineController : ControllerBase
    {
        /// <summary>
        /// 复制机组
        /// </summary>
        /// <param name="_windTurbineIDOld">需要复制的机组ID</param>
        /// <param name="_num">复制的个数</param>
        /// <returns></returns>
        /// 
        [HttpGet("CopyTurbine")]
        public IActionResult CopyTurbine(string _windTurbineIDOld, int _num,  string _curParkId, string turbineModel, string? _prefix, string? _suffix, int? startNum)

        {
            string Message = "state:{0},msg:'{1}'";
            string msg = "";
            try
            {
                string windParkID = "";
                string oldWindTurbineName = "";

                CopyTurbineController copyTurbineController = new CopyTurbineController();
                for (int i = 1; i <= _num; i++)
                {
                    int maxCode = GetNewWindTurbineCode(_windTurbineIDOld, _curParkId);

                    if (startNum != null && startNum > maxCode)
                    {
                        maxCode = (int)startNum - 1;
                    }

                    // 重写风场编码，使用传入的当前的风场ID，通过不同的风场复制
                    windParkID = _curParkId;
                    //机组的编号
                    /*  string windTurbineCodeNew = GetNewWindTurbineName(oldWindTurbineName, (maxCode + i));*/
                    string windTurbineIDNew = IDProvide.GetTurbineId(windParkID, (maxCode + 1).ToString());

                    //保存设备树 
                    WindTurbine windTurbine = copyTurbineController.CopyDevTree(_windTurbineIDOld, windTurbineIDNew, maxCode.ToString());
                    windTurbine.WindParkID = _curParkId;
                    if (_prefix != "" || _suffix != "")
                    {
                        windTurbine.WindTurbineName = _prefix + (maxCode + 1) + _suffix;
                    }
                    else
                    {
                        int _length = Math.Abs(maxCode).ToString().Length;

                        if (_prefix == "" && _suffix == "" && _length == 1)
                        {
                            windTurbine.WindTurbineCode = "0" + (maxCode + 1).ToString();
                        }
                        else
                        {
                            int windturbineCode = maxCode + 1;
                            windTurbine.WindTurbineName = Convert.ToString(windturbineCode);
                        }
                    }

                    WindTurbine tur = DevTreeManagement.GetWindTurbine(_windTurbineIDOld);
                    string zeroNum = "";
                    for (int n = 0; n < tur.WindTurbineCode.Length; n++)
                    {
                        zeroNum += "0";
                    }

                    windTurbine.WindTurbineCode = (maxCode + 1).ToString(zeroNum);

                    // 机型
                    if (!string.IsNullOrEmpty(turbineModel))
                    {
                        windTurbine.WindTurbineModel = turbineModel;
                    }

                    windTurbine.MeasLocSVMList = new List<MeasLoc_SVM>();
                    DevTreeManagement.AddWindTurbine_ManagerOverLoad(windTurbine);

                    //复制主控
                    MCS mcs = CopyMCS(windTurbine, _windTurbineIDOld);
                    if (mcs != null)
                    {
                        DAUMCS.AddMCS(mcs);
                        List<MCSChannelStateParam> MCSChannelStateParamList = this.CopyMCSChannelStateParam(windTurbine, _windTurbineIDOld);
                        if (MCSChannelStateParamList != null && MCSChannelStateParamList.Count > 0)
                            DAUMCS.AddMCSChannelState(MCSChannelStateParamList);
                        List<MCSChannelValueParam> MCSChannelValueParamList = this.CopyMCSChannelValueParam(windTurbine, _windTurbineIDOld);
                        if (MCSChannelValueParamList != null && MCSChannelValueParamList.Count > 0)
                            DAUMCS.AddMCSChannelValue(MCSChannelValueParamList);
                    }
                    //保存采集单元
                    //WindDAU dataSourceV = copyTurbineController.CopyDAU(windTurbineIDNew, _windTurbineIDOld, windTurbineCodeNew);
                    List<WindDAU> dataSourceV2 = copyTurbineController.CopyDAUList(windTurbineIDNew, _windTurbineIDOld, maxCode.ToString(), windParkID);
                    if (dataSourceV2.Count > 0)
                    {
                        //复制机组后，新机组的测量定义版本号、采集单元版本号都为0
                        //dataSourceV2.MeasDefVersion = dataSourceV2.DAUMeasDefVersion = 0;
                        //DauManagement.AddDAU(dataSourceV2);

                        foreach (WindDAU dau in dataSourceV2)
                        {
                            dau.MeasDefVersion = 0;
                            dau.DAUMeasDefVersion = 0;
                            DauManagement.AddDAU(dau);
                        }
                    }


                    //保存晃度
                    /*           SVMUnit svmUnit = copyTurbineController.CopySvmUnit(windTurbineIDNew, _windTurbineIDOld);
                               if (svmUnit != null)
                               {
                                   //用新的机组名称替换就模版机组名称
                                   //@wangy 2016年10月10日
                                   svmUnit.SVMName = windTurbineCodeNew;
                                   SVMManagement.AddSVM(svmUnit);
                                   SVMManagement.AddSVMRegister(svmUnit.SVMRegisterList);
                               }*/
                    //保存测量定义
                    List<MeasDefinition> meaDefinitionList = copyTurbineController.CopyMeasDefinition(windTurbineIDNew, _windTurbineIDOld, _curParkId);
                    if (meaDefinitionList != null && meaDefinitionList.Count > 0)
                    {
                        MeasDefinitionManagement.AddMeaDefition(meaDefinitionList);
                    }

                    //保存modbus设备
                    copyTurbineController.copyModbusDefinition(windTurbineIDNew, _windTurbineIDOld);
                    /*      if (modbusList != null && modbusList.Count > 0)
                    {
                    TIMManagement.AddModbusDefinition(modbusList);
                    }*/

                    //保存油液

                    //1. 添加modbusUnit表
                    /*          using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                              {
                                  List<ModbusUnit> modbusDefslist = TIMManagement.GetModbusunitList(_windTurbineIDOld);
                                  modbusDefslist.ForEach(c =>
                                  {
                                      c.WindTurbineID = windTurbineIDNew;
                                  });
                                  ctx.ModbusUnits.AddRange(modbusDefslist);
                                  ctx.SaveChanges();
                              }*/
                    //添加油液配置信息
                    //IDbConnection conn = new MySqlConnection(MySqlConnect._MysqlBaseDB);
      //              System.Data.Common.DbConnection conn = MySqlConnect.GetMysqlConnection();


      //              string sql = $"SELECT * FROM oilanalyzeconfig WHERE WindTurbineID = '{_windTurbineIDOld}' ORDER BY SettingName";
      //              var data = conn.Query<OilAnalyzeConfig>(sql).ToList();
      //              data.ForEach(item =>
      //              {
      //                  item.WindTurbineID = windTurbineIDNew;
      //              });
      //              data.ForEach(item =>
      //              {
      //                  string asql = @"INSERT INTO oilanalyzeconfig (WindTurbineID, SensorType, SettingName, SettingID, TimeInterval, Content, AlarmNum, AlarmNumRate, WarnNum, WarnNumRate, GroupID)
						//VALUES (@WindTurbineID,@SensorType,@SettingName,@SettingID,@TimeInterval,@Content,@AlarmNum,@AlarmNumRate,@WarnNum,@WarnNumRate,@GroupID)";
      //                  var res = conn.Execute(asql, item);
      //              });


                    //保存超声螺栓预紧力配置
                    List<UltrasonicChannelConfig> ultrasonicList = DauManagement.GetUltrasonicChannelConfigByTurId(_windTurbineIDOld);
                    foreach (UltrasonicChannelConfig item in ultrasonicList)
                    {
                        UltrasonicChannelConfig ulcc = new UltrasonicChannelConfig()
                        {
                            WindTurbineID = windTurbineIDNew,
                            DauID = item.DauID,
                            ChannelNumber = item.ChannelNumber,
                            DispatcherID = item.DispatcherID,
                            DispatcherChannelID = item.DispatcherChannelID,
                            PreloadLowerLimmit = item.PreloadLowerLimmit,
                            PreloadUpperLimmit = item.PreloadUpperLimmit,
                            StandardFilePath = null,
                            PreloadCalCoeffs = "0",
                            TempCalibCoeff = 0
                        };
                        DauManagement.AddUltrasonic(ulcc);
                    }
                    //保存报警
                    List<AlarmDefinition> alarmDefinitionListThr = copyTurbineController.CopyAlarmDefinition(windTurbineIDNew, _windTurbineIDOld);
                    if (alarmDefinitionListThr != null && alarmDefinitionListThr.Count > 0)
                        AlarmDefinitionManage.AddAlarmDefinition(alarmDefinitionListThr);

                }

                // 机组映射
                if (windParkID != "")
                {
                    // 读取appsetting.json配置
                    
                    //var configuration = new ConfigurationBuilder()
                    //    .SetBasePath(Directory.GetCurrentDirectory())
                    //    .AddJsonFile("appsetting.json", optional: true, reloadOnChange: true)
                    //    .Build();
                    
                    // 获取配置信息并传递给GetDevMap方法
                    //var appSettings = configuration.GetSection("AppSettings");
                    DataPushManager dpm = new DataPushManager();
                    dpm.GetDevMap(windParkID);
                }

                msg = "复制机组成功";
                Message = string.Format(Message, 1, msg);
            }
            catch (Exception ex)
            {
                msg = "复制机组失败，原因：" + ex.Message;
                Message = string.Format(Message, 0, msg);
                return Ok(ApiResponse<string>.Error(Message));
            }
            #region 添加日志
            LogEntity logEntity = new LogEntity();
            logEntity.LogDB = ConstDefine.UserManagementLog;
            logEntity.LogTime = DateTime.Now;
            logEntity.NodeID = _windTurbineIDOld;
            logEntity.UserName = Request.Cookies["WindCMSUserName"];
            logEntity.OperationDescription
                = string.Format("复制机组({0}) \r\n", _windTurbineIDOld) + msg;
            LogManagement.UserlogWrite(logEntity);
            #endregion
            return Ok(ApiResponse<string>.Success(Message));
        }

        private string GetNewWindTurbineName(string oldTurbineName, int code)
        {
            string pattern = @"\D";
            bool isNAN = System.Text.RegularExpressions.Regex.IsMatch(oldTurbineName, pattern);
            if (isNAN)
            {
                return code.ToString(GetCodeRegex(oldTurbineName.Length));
            }
            int firstIndex = 0;
            foreach (System.Text.RegularExpressions.Match match in System.Text.RegularExpressions.Regex.Matches(oldTurbineName, @"\d"))
            {
                firstIndex = match.Index;
                break;
            }
            string Mark = oldTurbineName.Substring(0, firstIndex);

            return Mark + code.ToString(GetCodeRegex(oldTurbineName.Length - Mark.Length));
        }

        private string GetCodeRegex(int codeLength)
        {
            string code = "";
            for (int i = 0; i < codeLength; i++)
            {
                code += "0";
            }
            return code;
        }

        /// <summary>
        /// 获取机组的Code值
        /// </summary>
        /// <param name="_windTurbineIDOld"></param>
        /// <returns></returns>
        public int GetNewWindTurbineCode(string _windTurbineIDOld, string windparkID)
        {
            //WindPark windPark = DevTreeManagement.GetWindParkByTurID(_windTurbineIDOld);
            WindPark windPark = DevTreeManagement.GetWindParkByParkID(windparkID);
            List<WindTurbine> windTurbineList = DevTreeManagement.GetTurbinesListByWindParkId(windPark.WindParkID);
            //WindTurbine windTurbineTem = windTurbineList.FirstOrDefault(t => t.WindTurbineID == _windTurbineIDOld);
            //找出机组最大编号，用以生成新复制的机组ID

            /*        List<WindTurbine> resultList = new List<WindTurbine>();

                    foreach (var windata in windTurbineList)
                    {
                        string codeID = windata.WindTurbineName;
                        string subStr = codeID.Substring(codeID.Length - 1, 1);
                        if (subStr == "#")
                        {
                            resultList = windTurbineList.Where(t => t.WindTurbineName.Substring(codeID.Length - 1, 1) == "#").ToList();
                        }
                        else {
                            windTurbineList = windTurbineList.Where(t => t.WindTurbineName.Substring(codeID.Length - 1, 1) != "#").ToList();
                        }


                    }*/
            /*       List<WindTurbine> resultList = new List<WindTurbine>();
                   foreach (WindTurbine item in windTurbineList)
                   {
                       string codeID = item.WindTurbineName;
                       string subStr = codeID.Substring(codeID.Length - 1, 1);
                       if (subStr == "#")
                       {
                           resultList = windTurbineList.Where(t => t.WindTurbineName.Substring(codeID.Length - 1, 1) == "#").ToList();
                       }
                   }*/
            int maxCode = 0;

            windparkID = windPark.WindParkID;
            //oldWindTurbineName = windTurbineTem.WindTurbineName;
            /*   if (prefix != "" || suffix != "") {
                   resultList.ForEach(c =>
                   {
                       maxCode = maxCode < int.Parse(c.WindTurbineCode) ? int.Parse(c.WindTurbineCode) : maxCode;
                   });

                   return maxCode;

               }*/
            windTurbineList.ForEach(c =>
            {
                maxCode = maxCode < int.Parse(c.WindTurbineCode) ? int.Parse(c.WindTurbineCode) : maxCode;
            });

            return maxCode;


        }
        #region---复制操作---

        /// <summary>
        /// 复制设备树
        /// </summary>
        /// <param name="_oldWindTurbineID">被复制的机组ID</param>
        /// <param name="_windTurbineIDNew">复制后的机组ID</param>
        /// <param name="_codeNew">复制后的编码</param>
        /// <returns></returns>
        public WindTurbine CopyDevTree(string _oldWindTurbineID, string _windTurbineIDNew, string _codeNew)
        {
            WindTurbine windTurbine = new WindTurbine();
            windTurbine = DevTreeManagement.GetAllWindTurbineOverLoad(_oldWindTurbineID);
            windTurbine.WindTurbineID = _windTurbineIDNew;
            windTurbine.WindTurbineCode = _codeNew;
            windTurbine.WindTurbineName = _codeNew;
            //晃度测量位置ID变换
            string SVMComponentID = "";
            //需要把模版的晃度仪部件获取到，否则获取的绑定会出错
            string TemplateComponentName = "";
            if (windTurbine.MeasLocSVMList != null && windTurbine.MeasLocSVMList.Count > 0)
            {
                TemplateComponentName = windTurbine.TurComponentList.Find(item => item.ComponentID == windTurbine.MeasLocSVMList.First().ComponentID).ComponentName;
            }
            var SVMComponent = windTurbine.TurComponentList.Find(item => item.ComponentName == TemplateComponentName);
            if (SVMComponent != null)
            {
                SVMComponentID = SVMComponent.ComponentID;
                windTurbine.MeasLocSVMList = this.CopyMeasLoc_SVM(windTurbine, SVMComponentID);
            }
            //工况复制
            windTurbine.ProcessMeasLocList.ForEach(item =>
            {
                item.MeasLocationID = this.GetNewMeasLocationID(_windTurbineIDNew, item.WindTurbineID, item.MeasLocationID);
                item.WindTurbineID = _windTurbineIDNew;
            });

            //转速复制
            if (windTurbine.RotSpdMeasLoc != null)
            {
                windTurbine.RotSpdMeasLoc.MeasLocationID = this.GetNewMeasLocationID(_windTurbineIDNew, windTurbine.RotSpdMeasLoc.WindTurbineID, windTurbine.RotSpdMeasLoc.MeasLocationID);
                windTurbine.RotSpdMeasLoc.WindTurbineID = _windTurbineIDNew;
            }
            //部件复制
            windTurbine.TurComponentList.ForEach(t =>
            {
                t.ComponentID = this.GetNewMeasLocationID(_windTurbineIDNew, t.WindTurbineID, t.ComponentID);
                t.WindTurbineID = _windTurbineIDNew;
            });
            //振动测量位置复制
            windTurbine.VibMeasLocList.ForEach(g =>
            {
                g.ComponentID = this.GetNewMeasLocationID(_windTurbineIDNew, g.WindTurbineID, g.ComponentID);
                g.MeasLocationID = this.GetNewMeasLocationID(_windTurbineIDNew, g.WindTurbineID, g.MeasLocationID);
                g.WindTurbineID = _windTurbineIDNew;
            });

            // 电流电压测量位置
            windTurbine.VoltageCurrentMeasLocList.ForEach(t =>
            {
                t.ComponentID = this.GetNewMeasLocationID(_windTurbineIDNew, t.WindTurbineID, t.ComponentID);
                t.MeasLocationID = this.GetNewMeasLocationID(_windTurbineIDNew, t.WindTurbineID, t.MeasLocationID);
                t.WindTurbineID = _windTurbineIDNew;
            });

            // modbus 测量位置
            windTurbine.DevMeasLocModbus.ForEach(t =>
            {
                t.ComponentID = this.GetNewMeasLocationID(_windTurbineIDNew, t.WindTurbineID, t.ComponentID);
                t.MeasLocationID = this.GetNewMeasLocationID(_windTurbineIDNew, t.WindTurbineID, t.MeasLocationID);
                t.WindTurbineID = _windTurbineIDNew;
            });
            return windTurbine;
        }

        /// <summary>
        /// 复制晃度测量位置信息
        /// </summary>
        /// <param name="_windTurbine"></param>
        /// <returns></returns>
        private List<MeasLoc_SVM> CopyMeasLoc_SVM(WindTurbine _windTurbine, string ComponentID)
        {
            _windTurbine.MeasLocSVMList.ForEach(t =>
            {
                string dataType = t.MeasLocationID.Replace(ComponentID, "");
                switch (dataType)
                {
                    case "Ax": t.ParamType = EnumSVMParamType.Axisl; break;
                    case "Ho": t.ParamType = EnumSVMParamType.Horizontal; break;
                    case "Pi": t.ParamType = EnumSVMParamType.Pitch; break;
                    case "Ro": t.ParamType = EnumSVMParamType.Roll; break;
                    case "Ve": t.ParamType = EnumSVMParamType.Vertical; break;
                    case "Temp": t.ParamType = EnumSVMParamType.Temperature; break;
                }
                t.ComponentID = this.GetNewMeasLocationID(_windTurbine.WindTurbineID, t.WindTurbineID, ComponentID);
                t.MeasLocationID = this.GetNewMeasLocationID(_windTurbine.WindTurbineID, t.WindTurbineID, t.MeasLocationID);
                t.WindTurbineID = _windTurbine.WindTurbineID;
            });
            return _windTurbine.MeasLocSVMList;
        }

        /// <summary>
        /// 复制主控
        /// </summary>
        /// <returns></returns>
        public MCS CopyMCS(WindTurbine _windTurbine, string _oldWindTurbineID)
        {
            MCS mcs = DAUMCS.GetMCSByTurbineId(_oldWindTurbineID);
            if (mcs != null)
            {
                mcs.WindTurbineID = _windTurbine.WindTurbineID;
            }
            return mcs;
        }

        /// <summary>
        /// 复制值寄存器
        /// </summary>
        /// <param name="_windTurbine"></param>
        /// <param name="_oldWindTurbineID"></param>
        /// <returns></returns>
        public List<MCSChannelValueParam> CopyMCSChannelValueParam(WindTurbine _windTurbine, string _oldWindTurbineID)
        {
            List<MCSChannelValueParam> mCSChannelValueParamList = DAUMCS.GetMCSChannelValueListByTurID(_oldWindTurbineID);
            if (mCSChannelValueParamList != null && mCSChannelValueParamList.Count > 0)
            {
                mCSChannelValueParamList.ForEach(c =>
                {
                    c.MeasLocProcessID = GetNewMeasLocationID(_windTurbine.WindTurbineID, c.WindTurbineID, c.MeasLocProcessID);
                    c.WindTurbineID = _windTurbine.WindTurbineID;
                });
            }
            return mCSChannelValueParamList;
        }

        /// <summary>
        /// 复制状态寄存器
        /// </summary>
        /// <param name="_windTurbine">新机组信息</param>
        /// <param name="_oldWindTurbineID">被复制的机组ID</param>
        /// <returns></returns>
        public List<MCSChannelStateParam> CopyMCSChannelStateParam(WindTurbine _windTurbine, string _oldWindTurbineID)
        {
            List<MCSChannelStateParam> cCSChannelStateParamList = DAUMCS.GetMCSChannelStateListByTurID(_oldWindTurbineID);
            if (cCSChannelStateParamList != null && cCSChannelStateParamList.Count > 0)
            {
                cCSChannelStateParamList.ForEach(c =>
                {
                    c.MeasLocProcessID = GetNewMeasLocationID(_windTurbine.WindTurbineID, c.WindTurbineID, c.MeasLocProcessID);
                    c.WindTurbineID = _windTurbine.WindTurbineID;
                });
            }
            return cCSChannelStateParamList;
        }

        /// <summary>
        /// 复制DAU
        /// </summary>
        /// <param name="_windTurbineID">要更新的机组ID</param>
        /// <param name="_windTurbineIDOld">需要复制的机组ID</param>
        /// <returns></returns>
        public WindDAU CopyDAU(string _windTurbineID, string _windTurbineIDOld, string _windTurbineCodeNew)
        {
            WindDAU dataSourceV2 = new WindDAU();
            //模板的DAU
            dataSourceV2 = DauManagement.GetDAUById(_windTurbineIDOld);
            if (dataSourceV2 != null)
            {
                dataSourceV2.WindTurbineID = _windTurbineID;
                dataSourceV2.IsAvailable = false;
                dataSourceV2.DAUName = _windTurbineCodeNew;
                //工况通道
                dataSourceV2.ProcessChannelList.ForEach(t =>
                {
                    t.MeasLoc_ProcessId = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLoc_ProcessId);
                    t.WindTurbineID = _windTurbineID;
                });
                //转速
                dataSourceV2.RotSpeedChannelList.ForEach(t =>
                {
                    t.MeasLocRotSpdID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocRotSpdID);
                    t.WindTurbineID = _windTurbineID;
                });
                //振动
                dataSourceV2.DAUChannelList.ForEach(t =>
                {
                    t.MeasLocVibID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocVibID);
                    t.WindTurbineID = _windTurbineID;
                });
            }
            return dataSourceV2;
        }

        // 适用于多采集单元的copy
        public List<WindDAU> CopyDAUList(string _windTurbineID, string _windTurbineIDOld, string _windTurbineCodeNew, string _parkId)
        {
            //WindDAU dataSourceV2 = new WindDAU();
            List<WindDAU> dataSourceV3 = new List<WindDAU>();
            dataSourceV3 = DAUSManageModel.GetDAUListById(_windTurbineIDOld);
            if (dataSourceV3.Count > 0)
            {
                dataSourceV3.ForEach(item =>
                {
                    item.WindTurbineID = _windTurbineID;
                    item.WindParkID = _parkId;
                    item.IsAvailable = false;
                    //item.DAUName = _windTurbineCodeNew;

                    //item.MeasDefVersion = 0;
                    //item.DAUMeasDefVersion = 0;
                    //工况通道
                    item.ProcessChannelList = item.ProcessChannelList.Where(p => p.DauID == item.DauID).ToList();
                    item.ProcessChannelList.ForEach(t =>
                    {
                        t.MeasLoc_ProcessId = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLoc_ProcessId);
                        t.WindTurbineID = _windTurbineID;
                    });
                    //转速
                    item.RotSpeedChannelList = item.RotSpeedChannelList.Where(p => p.DauID == item.DauID).ToList();
                    item.RotSpeedChannelList.ForEach(t =>
                    {
                        t.MeasLocRotSpdID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocRotSpdID);
                        t.WindTurbineID = _windTurbineID;
                    });
                    //振动
                    item.DAUChannelList = item.DAUChannelList.Where(p => p.DauID == item.DauID).ToList();
                    item.DAUChannelList.ForEach(t =>
                    {
                        t.MeasLocVibID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocVibID);
                        t.WindTurbineID = _windTurbineID;
                    });

                    // 电流电压
                    item.VoltageCurrentList = item.VoltageCurrentList.Where(k => k.DauID == item.DauID).ToList();
                    item.VoltageCurrentList.ForEach(t =>
                    {
                        t.MeasLoc_ProcessId = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLoc_ProcessId);
                        t.WindTurbineID = _windTurbineID;

                    });
                });
            }

            return dataSourceV3;
        }

        /// <summary>
        /// 复制晃度
        /// </summary>
        /// <param name="_windTurbineID"></param>
        /// <param name="_windTurbineIDOld"></param>
        /// <returns></returns>
        public SVMUnit CopySvmUnit(string _windTurbineID, string _windTurbineIDOld)
        {
            SVMUnit svmUnit = new SVMUnit();
            svmUnit = SVMManagement.GetSVMById(_windTurbineIDOld);
            //@wangy 如果晃度仪不存在，就不复制晃度仪数据
            if (svmUnit == null)
                return null;
            svmUnit.ComponentID = this.GetNewMeasLocationID(_windTurbineID, _windTurbineIDOld, svmUnit.ComponentID);
            svmUnit.SVMRegisterList = SVMManagement.GetSVMRegisterListBySVMId(_windTurbineIDOld);
            if (svmUnit.SVMRegisterList != null && svmUnit.SVMRegisterList.Count > 0)
            {
                svmUnit.SVMRegisterList.ForEach(t =>
                {
                    t.ComponentID = svmUnit.ComponentID;
                    t.SVMMeasLocId = this.GetNewMeasLocationID(_windTurbineID, t.AssocWindTurbineID, t.SVMMeasLocId);
                    t.AssocWindTurbineID = _windTurbineID;
                });

            }
            svmUnit.AssocWindTurbineID = _windTurbineID;
            return svmUnit;
        }


        /// <summary>
        /// 测量定义复制
        /// </summary>
        /// <param name="_windTurbineID"></param>
        /// <param name="_windTurbineIDOld"></param>
        /// <returns></returns>
        public List<MeasDefinition> CopyMeasDefinition(string _windTurbineID, string _windTurbineIDOld, string newParkID)
        {
            List<MeasDefinition> measDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurIdOverload(_windTurbineIDOld);
            List<MeasDefinition_Ex> measDefEX = MeasDefinitionManagement.GetMeasdefinitionEXListByTurID(_windTurbineIDOld);
            List<MeasDefinition_Ex> measDefEXresult = new List<MeasDefinition_Ex>();
            // 获取测量方案
            List<MeasSolution> measSolutionlist = MeasDefinitionManagement.GetMeasSolution(_windTurbineIDOld);
            //List<MeasSolution> measSolutionRes = new List<MeasSolution>();

            // 测量方案映射表(可以out出去，在svm等使用)
            Dictionary<string, string> measDefIDdic = new Dictionary<string, string>();


            measDefinitionList.ForEach(c =>
            {
                // 找到DAU绑定信息修改测量定义之前找到
                var measex = measDefEX.FirstOrDefault(p => p.MeasDefinitionID == c.MeasDefinitionID);

                var _measSolution = measSolutionlist.Where(t => t.MeasDefinitionID == c.MeasDefinitionID).ToList();

                List<MeasDef_Ev_Vib> vibEvRes = new List<MeasDef_Ev_Vib>();
                // 振动测量定义复制
                int measDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.MeasDefinitionID);

                if (!measDefIDdic.ContainsKey(c.MeasDefinitionID))
                {
                    measDefIDdic.Add(c.MeasDefinitionID, measDefinitionID.ToString());
                }

                if (c != null)
                {
                    c.WaveDefList.ForEach(t =>
                    {
                        // 特征值保存
                        var _waveId = t.WaveDefinitionID;
                        var _measLocId = t.MeasLocationID;
                        var _measDefId = t.MeasDefinitionID;
                        t.WaveDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID).ToString();
                        t.MeasLocationID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocationID);
                        t.MeasDefinitionID = measDefinitionID.ToString();
                        t.WindTurbineID = _windTurbineID;

                        // 振动特征值
                        if (c.VibEigenValueConf != null && c.VibEigenValueConf.Count > 0)
                        {
                            var _curEv = c.VibEigenValueConf.Where(k => k.WaveDefinitionID == _waveId && k.MeasDefinitionID == _measDefId && k.MeasLocationID == _measLocId).ToList();
                            foreach (var _evi in _curEv)
                            {
                                _evi.WaveDefinitionID = t.WaveDefinitionID;
                                _evi.MeasLocationID = t.MeasLocationID;
                                _evi.MeasDefinitionID = t.MeasDefinitionID;
                                _evi.WindTurbineID = t.WindTurbineID;
                                vibEvRes.Add(_evi);
                            }
                        }

                    });
                    // 电流电压
                    c.WaveDefVoltageCurrentList.ForEach(t =>
                    {
                        // 特征值保存
                        var _waveId = t.WaveDefinitionID;
                        var _measLocId = t.MeasLocationID;
                        var _measDefId = t.MeasDefinitionID;
                        t.WaveDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID).ToString();
                        t.MeasLocationID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocationID);
                        t.MeasDefinitionID = measDefinitionID.ToString();
                        t.WindTurbineID = _windTurbineID;

                        // 振动特征值
                        if (c.VibEigenValueConf != null && c.VibEigenValueConf.Count > 0)
                        {
                            var _curEv = c.VibEigenValueConf.Where(k => k.WaveDefinitionID == _waveId && k.MeasDefinitionID == _measDefId && k.MeasLocationID == _measLocId).ToList();
                            foreach (var _evi in _curEv)
                            {
                                _evi.WaveDefinitionID = t.WaveDefinitionID;
                                _evi.MeasLocationID = t.MeasLocationID;
                                _evi.MeasDefinitionID = t.MeasDefinitionID;
                                _evi.WindTurbineID = t.WindTurbineID;
                                vibEvRes.Add(_evi);
                            }
                        }

                    });


                    c.VibEigenValueConf = vibEvRes;
                }
                if (measex != null)
                {
                    measex.WindTurbineID = _windTurbineID;
                    measex.MeasDefinitionID = measDefinitionID.ToString();

                    measDefEXresult.Add(measex);
                }

                _measSolution.ForEach(k =>
                {
                    k.WindTurbineID = _windTurbineID;
                    k.MeasDefinitionID = measDefinitionID.ToString();
                    k.MeasSolutionID = k.MeasSolutionID;
                });

                c.SolutionList = _measSolution;
                //工况波形定义复制
                if (c.ProcessDefList != null && c.ProcessDefList.Count > 0)
                {
                    c.ProcessDefList.ForEach(t =>
                    {
                        var _measLocId = t.MeasLocationID;
                        var _measDefId = t.MeasDefinitionID;

                        t.MeasLocationID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocationID);
                        t.WindTurbineID = _windTurbineID;
                        t.MeasDefinitionID = measDefinitionID.ToString();

                        //工况特征值复制
                        if (c.ProcessDefList != null && c.ProcessDefList.Count > 0)
                        {
                            var processEv = c.ProcessSuperviseDefList.FirstOrDefault(k => k.MeasDefinitionID == _measDefId && k.MeasLocationID == _measLocId);
                            if (processEv != null)
                            {
                                processEv.MeasDefinitionID = t.MeasDefinitionID;
                                processEv.MeasLocationID = t.MeasLocationID;
                                processEv.WindTurbineID = _windTurbineID;
                            }
                        }
                    });

                }


                //转速测量定义复制
                if (c.RotSpdWaveDefList != null && c.RotSpdWaveDefList.Count > 0)
                {
                    c.RotSpdWaveDefList.ForEach(t =>
                    {
                        var _measLocId = t.MeasLoc_RotSpdID;
                        var _measDefId = t.MeasDefinitionID;

                        t.MeasLoc_RotSpdID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLoc_RotSpdID);
                        t.WindTurbineID = _windTurbineID;
                        t.MeasDefinitionID = measDefinitionID.ToString();


                        //转速特征值复制
                        if (c.RotSpdWaveDefList != null && c.RotSpdWaveDefList.Count > 0)
                        {
                            var spdEv = c.ProcessSuperviseDefList.FirstOrDefault(k => k.MeasDefinitionID == _measDefId && k.MeasLocationID == _measLocId);
                            if (spdEv != null)
                            {
                                spdEv.MeasDefinitionID = t.MeasDefinitionID;
                                spdEv.MeasLocationID = t.MeasLoc_RotSpdID;
                                spdEv.WindTurbineID = _windTurbineID;
                            }
                        }
                    });


                }


                // 触发采集复制
                if (c.TriggerRules != null && c.TriggerRules.Count > 0)
                {
                    foreach (var trigger in c.TriggerRules)
                    {
                        var newgid = Guid.NewGuid().ToString();
                        trigger.RuleID = newgid;
                        trigger.WindTurbineID = _windTurbineID;
                        trigger.MeasDefinitionID = measDefinitionID.ToString();

                        if (trigger.SupervisedVariables.Count > 0)
                        {
                            foreach (var k in trigger.SupervisedVariables)
                            {
                                k.RuleID = newgid;
                                k.WindTurbineID = _windTurbineID;
                                k.MeasLocationID = k.MeasLocationID.Replace(_windTurbineIDOld, _windTurbineID);
                            }
                        }

                        if (trigger.TriggerTime != null)
                        {
                            trigger.TriggerTime.RuleID = newgid;
                        }

                        if (trigger.ExecuteMdfs.Count > 0)
                        {
                            foreach (var k in trigger.ExecuteMdfs)
                            {
                                k.RuleID = newgid;
                                k.WindTurbineID = _windTurbineID;
                            }
                        }
                    }
                }



                /*    //晃度测量定义复制
                    if (c.SVMWaveDefinition != null && c.SVMWaveDefinition.Count>0)
                    {
                        c.SVMWaveDefinition.ForEach(t =>
                        {
                            t.WaveDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID).ToString();
                            t.MeasLocationID = this.GetNewMeasLocationID(_windTurbineID, t.WindTurbineID, t.MeasLocationID);
                            t.MeasDefinitionID = measDefinitionID.ToString();
                            t.WindTurbineID = _windTurbineID;
                        });
                    }*/

                c.MeasDefinitionID = measDefinitionID.ToString();
                c.WindTurbineID = _windTurbineID;
                c.WindParkID = newParkID;
            });
            if (measDefEXresult.Count > 0)
            {
                // 增加测量定义和机组绑定
                MeasDefinitionManagement.AddMeaDefitionEX(measDefEXresult);

            }

            // 触发采集后，被触发的测量方案id
            if (measDefinitionList != null && measDefinitionList.Count > 0)
            {
                foreach (var item in measDefinitionList)
                {
                    foreach (var k in item.TriggerRules)
                    {
                        if (k.ExecuteMdfs.Count > 0)
                        {
                            foreach (var p in k.ExecuteMdfs)
                            {
                                if (measDefIDdic.ContainsKey(p.MeasDefinitionID))
                                {
                                    p.MeasDefinitionID = measDefIDdic[p.MeasDefinitionID];
                                }
                            }
                        }
                    }
                }
            }

            return measDefinitionList;
        }


        /// <summary>
        /// 复制Modbus
        /// </summary>
        /// <param name="_windTurbineID"></param>
        /// <param name="_windTurbineIDOld"></param>
        /// <returns></returns>
        public void copyModbusDefinition(string _windTurbineID, string _windTurbineIDOld)
        {
            //ModbusUnitID数据获取
            List<String> modbusDataList = new List<String>();

            Dictionary<int, int> modbusdeviceIDmap = new Dictionary<int, int>();

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var channelList = ctx.ModbusChannelList.Where(t=>t.WindTurbineID ==  _windTurbineIDOld).ToList();
                //添加modbusUnit表
                List<ModbusUnit> modbusDefslist = TIMManagement.GetModbusunitList(_windTurbineIDOld);
                modbusDefslist.ForEach(c =>
                {
                    var olddeviceID = c.ModbusDeviceID;
                    c.WindTurbineID = _windTurbineID;
                    c.ModbusDeviceID = default(int);
                    ctx.Entry(c).State = EntityState.Added;
                    modbusDataList.Add(c.ModbusUnitID);
                    ctx.ModbusUnits.Add(c);
                    ctx.SaveChanges();

                    if (!modbusdeviceIDmap.ContainsKey(olddeviceID))
                    {
                        modbusdeviceIDmap.Add(olddeviceID, c.ModbusDeviceID);
                    }

                    // 查找通道
                    var chans = channelList.Where(it => it.ModbusDeviceID == olddeviceID).ToList();
                    foreach (var channel in chans)
                    {
                        channel.WindTurbineID = _windTurbineID;
                        channel.ModbusDeviceID = c.ModbusDeviceID;
                        channel.MeasLocationID = channel.MeasLocationID.Replace(_windTurbineIDOld, _windTurbineID);
                    }

                    ctx.ModbusChannelList.AddRange(chans);

                });
                //ctx.ModbusUnits.AddRange(modbusDefslist);
                ctx.SaveChanges();
                //添加服务表
                List<SerialServer> serialServerslist = TIMManagement.GetSerialServer(_windTurbineIDOld);
                serialServerslist.ForEach(m =>
                {
                    m.ModbusDeviceID = modbusdeviceIDmap[m.ModbusDeviceID];
                    m.WindTurbineID = _windTurbineID;
                });
                ctx.SerialServers.AddRange(serialServerslist);
                ctx.SaveChanges();
                //timcalibration表添加
                List<TimCalibration> timUnitList = TIMManagement.GetTimUnit(_windTurbineIDOld);
                if (timUnitList.Count > 0)
                {
                    timUnitList.ForEach(m =>
                    {
                        m.WindTurbineID = _windTurbineID;
                        m.CalibrationAngleX = 0;
                        m.CalibrationAngleY = 0;
                    });
                    ctx.TimCalibrations.AddRange(timUnitList);
                    ctx.SaveChanges();
                }
                //油液添加
                List<OilUnit> oilUnitList = TIMManagement.GetOilconfigByturID(_windTurbineIDOld);

                if (oilUnitList.Count > 0 && !oilUnitList.Equals(""))
                {
                    oilUnitList.ForEach(m =>
                    {
                        m.WindTurbineID = _windTurbineID;
                    });
                    ctx.OilUnits.AddRange(oilUnitList);
                    ctx.SaveChanges();
                }

            }

            //查询机组部件
            List<WindTurbineComponent> windTurbineComponentslist = DevTreeManagement.GetComListByTurbineId(_windTurbineID);

            WindTurbineComponent myComponent = windTurbineComponentslist.Find(i => i.ComponentName == "塔筒");
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                List<SVMUnit> svmuList = SVMManagement.GetSVMUnitListByWindTurbineID(_windTurbineIDOld);
                foreach (SVMUnit svm in svmuList)
                {
                    svm.AssocWindTurbineID = _windTurbineID;
                    svm.ComponentID = myComponent.ComponentID;
                }
                ctx.SVMUnits.AddRange(svmuList);
                ctx.SaveChanges();


            }
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {

                if (myComponent == null)
                {
                    myComponent = new WindTurbineComponent();
                    myComponent.WindTurbineID = _windTurbineID;
                    myComponent.ComponentName = "塔筒";
                    myComponent.ComponentID = $"{ _windTurbineID}TOW";
                    myComponent.CompManufacturer = "未知";
                    ctx.DevTurComponents.Add(myComponent);
                }


                List<SVMRegister> svmDataList = SVMManagement.GetSVMRegisterByturbineID(_windTurbineIDOld);
                //SVMManagement.AddSVMRegister(svmDataList);
                //测试开始
                List<MeasLoc_SVM> measDataList = SVMManagement.GetMeasLoc_SVMListByTurID(_windTurbineIDOld);

                List<WaveDef_SVM> waveDef_SVMsList = SVMManagement.GetWaveDef_sData(_windTurbineIDOld);

                List<MeasLoc_SVM> addMlsList = new List<MeasLoc_SVM>();

                List<string> mlsStrNameList = new List<string>();
                List<string> mlsStrIDList = new List<string>();
                List<string> mlIDList = new List<string>();
                foreach (MeasLoc_SVM m in measDataList)
                {

                    MeasLoc_SVM measLoc = new MeasLoc_SVM();
                    measLoc.ComponentID = myComponent.ComponentID;
                    string Sname = m.SectionName;
                    if (Sname == "塔基")
                    {
                        measLoc.MeasLocationID = IDProvide.GetSVMLocID(_windTurbineID, "TOW", "FDA", m.MeasLocName);
                    }
                    else if (Sname == "塔顶")
                    {
                        measLoc.MeasLocationID = IDProvide.GetSVMLocID(_windTurbineID, "TOW", "TOP", m.MeasLocName);
                    }
                    measLoc.MeasLocName = m.MeasLocName;
                    measLoc.SectionName = m.SectionName;
                    measLoc.WindTurbineID = _windTurbineID;
                    measLoc.ParamType = SVMManagement.GetParamType(m.MeasLocName); ;// SVMManagement.GetParamType(m.MeasLocName);
                    addMlsList.Add(measLoc);
                    mlsStrNameList.Add(m.MeasLocName);
                    mlsStrIDList.Add(m.MeasLocationID);
                    mlIDList.Add(measLoc.MeasLocationID);
                }
                //添加测位置
                SVMManagement.AddSVMMeasLoc(_windTurbineID, addMlsList);

                //添加晃度仪寄存器
                List<SVMRegister> RegisterList = new List<SVMRegister>();
                List<SVMRegister> newSVMRegistersDataList = SVMManagement.GetSVMRegisterByturbineID(_windTurbineIDOld);
                List<MeasLoc_SVM> newMeasDataList = SVMManagement.GetMeasLoc_SVMListByTurID(_windTurbineID);

                foreach (SVMRegister newData in newSVMRegistersDataList)
                {
                    MeasLoc_SVM svmData = newMeasDataList.Find(k => k.WindTurbineID == _windTurbineID);
                    newData.AssocWindTurbineID = _windTurbineID;
                    newData.ComponentID = svmData.ComponentID;

                    string measStr = newData.SVMMeasLocId;
                    newData.SVMMeasLocId = _windTurbineID + measStr.Substring(_windTurbineIDOld.Length, measStr.Length - _windTurbineIDOld.Length);

                }


                List<WaveDef_SVM> svmWaveDeflist = new List<WaveDef_SVM>();
                List<MeasDefinition> meaDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurId(_windTurbineID);

                List<MeasDefinition> meaDefList = MeasDefinitionManagement.GetMeasDefListByTurId(_windTurbineIDOld);

                List<WaveDef_SVM> newsvmWaveDeflist = SVMManagement.GetWaveDef_sData(_windTurbineIDOld);

                foreach (WaveDef_SVM newSWDdata in newsvmWaveDeflist)
                {
                    //获取复制后的测量定义
                    MeasDefinition measDef = meaDefList.Find(t => t.MeasDefinitionID == newSWDdata.MeasDefinitionID);
                    List<MeasDefinition> newMeaDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurId(_windTurbineID);
                    MeasDefinition _measDef = newMeaDefinitionList.Find(t => t.MeasDefinitionName == measDef.MeasDefinitionName);
                    newSWDdata.MeasDefinitionID = _measDef.MeasDefinitionID;
                    newSWDdata.WindTurbineID = _windTurbineID;
                    MeasLoc_SVM svmData = newMeasDataList.Find(k => k.WindTurbineID == _windTurbineID);
                    string mlsIDstr = newSWDdata.MeasLocationID;
                    newSWDdata.MeasLocationID = _windTurbineID + mlsIDstr.Substring(_windTurbineIDOld.Length, mlsIDstr.Length - _windTurbineIDOld.Length);

                    // 添加svm特征值
                    if (newSWDdata.EigenValueConf.Count > 0)
                    {
                        newSWDdata.EigenValueConf.ForEach(k =>
                        {
                            k.MeasDefinitionID = _measDef.MeasDefinitionID;
                            k.MeasLocationID = newSWDdata.MeasLocationID;
                            k.WindTurbineID = _windTurbineID;
                            k.WaveDefinitionID = newSWDdata.WaveDefinitionID;
                        });
                    }
                }

                /*                for (int i = 0; i < mlsStrNameList.Count; i++)
                                {
                                    MeasLoc_SVM mySVM = addMlsList.FirstOrDefault(k => k.MeasLocName == mlsStrNameList[i]);
                                    SVMRegister svm = svmDataList.Find(v => v.SVMMeasLocId == mlsStrIDList[i]);
                                    SVMRegister register = new SVMRegister();
                                    register.AssocWindTurbineID = mySVM.WindTurbineID;
                                    register.SVMRegisterAdr = SVMManagement.GetRegisterAdr(mySVM);
                                    register.ComponentID = mySVM.ComponentID;
                                    register.SVMMeasLocId = mlIDList[i];
                                    register.RegisterType = (int)mySVM.ParamType;
                                    register.SVMID = svm.SVMID;
                                    RegisterList.Add(register);
                                    WaveDef_SVM waveDef_SVM = waveDef_SVMsList.Find(w => w.MeasLocationID == mlsStrIDList[i]);
                                    //从测量定义表里面取出
                                    MeasDefinition measDef = meaDefList.Find(t => t.MeasDefinitionID == waveDef_SVM.MeasDefinitionID);
                                    MeasDefinition measDefinition = meaDefinitionList.Find(s => s.MeasDefinitionName == measDef.MeasDefinitionName);
                                    // 晃度仪波形定义
                                    svmWaveDeflist.Add(new WaveDef_SVM()
                                    {
                                        MeasDefinitionID = measDefinition.MeasDefinitionID,
                                        MeasLocationID = mlIDList[i],
                                        WindTurbineID = _windTurbineID,
                                        WaveDefinitionID = waveDef_SVM.WaveDefinitionID,
                                        SampleLength = waveDef_SVM.SampleLength,
                                        SampleRate = waveDef_SVM.SampleRate,
                                        ParamType = mySVM.ParamType,
                                        WaveDefinitionName = mlsStrNameList[i],
                                    });

                                }*/
                //添加晃度仪寄存器
                SVMManagement.AddSVMRegister(newSVMRegistersDataList);

                SVMManagement.AddWaveDefSVM(newsvmWaveDeflist);

            }
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                List<ModbusDef> modbusList = TIMManagement.GetModbusDef(_windTurbineIDOld);
                List<WaveDef_SVM> waveDefDataList = SVMManagement.GetWaveDef_sData(_windTurbineID);
                // 测量定义
                List<MeasDefinition> mdList = MeasDefinitionManagement.GetMeasDefListByTurId(_windTurbineIDOld);
                List<MeasDefinition> newMdList = MeasDefinitionManagement.GetMeasDefListByTurId(_windTurbineID);
                //转换测量定义ID

                // modbus波形定义
                var modbuswavedef = ctx.WDFModbusDefs.Where(it=>it.WindTurbineID ==  _windTurbineIDOld).ToList();
                var evconfig = ctx.TimeDomainEvConfs.Where(it => it.WindTurbineID == _windTurbineIDOld).AsNoTracking().ToList();

                WaveDef_SVM waveDefdata = waveDefDataList.Find(s => s.WindTurbineID == _windTurbineID);
                foreach (ModbusDef mbd in modbusList)
                {
                    MeasDefinition mdn = mdList.Find(n => n.MeasDefinitionID == mbd.MeasDefinitionID);
                    MeasDefinition _mdn = newMdList.Find(r => r.MeasDefinitionName == mdn.MeasDefinitionName && r.WindTurbineID == _windTurbineID);
                    ctx.ModbusDefs.Add(new ModbusDef()
                    {
                        MeasDefinitionID = _mdn.MeasDefinitionID,
                        WindTurbineID = _windTurbineID,
                        ModbusUnitID = mbd.ModbusUnitID,
                        SampleTime = mbd.SampleTime,
                        SampleFrequency = mbd.SampleFrequency,
                        ModbusDeviceID = modbusdeviceIDmap[mbd.ModbusDeviceID],
                    });
                }

                foreach(var mwave in modbuswavedef)
                {
                    MeasDefinition mdn = mdList.Find(n => n.MeasDefinitionID == mwave.MeasDefinitionID);
                    MeasDefinition _mdn = newMdList.Find(r => r.MeasDefinitionName == mdn.MeasDefinitionName && r.WindTurbineID == _windTurbineID);

                    mwave.WindTurbineID = _windTurbineID;
                    mwave.MeasDefinitionID = _mdn.MeasDefinitionID;
                    mwave.MeasLocationID = mwave.MeasLocationID.Replace(_windTurbineIDOld, _windTurbineID);
                    mwave.ModbusDeviceID = modbusdeviceIDmap[mwave.ModbusDeviceID];
                    ctx.WDFModbusDefs.Add(mwave);
                    ctx.SaveChanges();
                    var ev = evconfig.Where(it => it.WaveDefinitionID == mwave.WaveDefinitionID).ToList();
                    foreach(var evdef in ev)
                    {
                        evdef.EvId = default(int);
                        evdef.WindTurbineID = _windTurbineID;
                        evdef.MeasDefinitionID = _mdn.MeasDefinitionID;
                        evdef.MeasLocationID = evdef.MeasLocationID.Replace(_windTurbineIDOld, _windTurbineID);
                        evdef.WaveDefinitionID = mwave.WaveDefinitionID;
                    }

                    ctx.TimeDomainEvConfs.AddRange(ev);
                }
                //ctx.MeasDefinitions_Exs.Add(new MeasDefinition_Ex()
                //{
                //    DaqInterval = timeLength,
                //    DauID = dauID,
                //    MeasDefinitionID = measdID,
                //    WindTurbineID = turbineID
                //});
                ctx.SaveChanges();
            }

        }


        /// <summary>
        /// 复制报警定义
        /// </summary>
        /// <param name="_windTurbineID"></param>
        /// <param name="_windTurbineIDOld"></param>
        /// <returns></returns>
        public List<AlarmDefinition> CopyAlarmDefinition(string _windTurbineID, string _windTurbineIDOld)
        {
            //获取报警定义
            List<AlarmDefinition> alarmDefinitionList = AlarmDefinitionManage.GetAlarmDefListByTurID(_windTurbineIDOld);

            alarmDefinitionList.ForEach(c =>
            {
                string thresholdGroup = System.Guid.NewGuid().ToString("N");
                c.ThresholdGroup = thresholdGroup;
                c.AlarmDefThresholdGroup.ForEach(x =>
                    {
                        x.ThresholdGroup = thresholdGroup;
                        x.WindTurbineID = _windTurbineID;
                    });
                c.EigenValueID = c.EigenValueID.Replace(_windTurbineIDOld, _windTurbineID);
                c.MeasLocationID = c.MeasLocationID.Replace(_windTurbineIDOld, _windTurbineID);
                c.WindTurbineID = _windTurbineID;
            });
            return alarmDefinitionList;
        }

        /// <summary>
        /// 根据测量位置ID，原始机组ID，产生新的测量位置ID
        /// </summary>
        /// <param name="_windTurbineID"></param>
        /// <param name="_oldWindTurbineID"></param>
        /// <param name="_oldMeasLocationID"></param>
        /// <returns></returns>
        private string GetNewMeasLocationID(string _windTurbineID, string _oldWindTurbineID, string _oldMeasLocationID)
        {
            return _windTurbineID + _oldMeasLocationID.Replace(_oldWindTurbineID, ""); ;
        }

        #endregion


        #region 导入导出

        /// <summary>
        /// 模板机组导出
        /// </summary>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        [HttpGet("TemplateDownload")]
        public IActionResult TemplateDownload()
        {
            List<TemplateTurbineModel> res = new List<TemplateTurbineModel>();

            // 查找模板机组
            List<WindTurbine> windTurbineList = DevTreeManagement.GetTurbinesListByWindParkId("HN999");

            string fName = "模板";

            if (windTurbineList.Count > 0)
            {
                //fName = windTurbineList.FirstOrDefault().DevWindPark?.WindParkName + windTurbineList.Count;
                foreach (var tt in windTurbineList)
                {
                    TemplateTurbineModel tur = new TemplateTurbineModel();

                    // 机组设备树信息
                    var turInfo = DevTreeManagement.GetAllWindTurbineOverLoad(tt.WindTurbineID);
                    //var turInfo = DevTreeManagement.GetWindTurbine(tt.WindTurbineID);

                    tur.WindParkID = turInfo.WindParkID;
                    tur.WindTurbineID = turInfo.WindTurbineID;
                    tur.WindTurbineCode = turInfo.WindTurbineCode;
                    tur.WindTurbineModel = turInfo.WindTurbineModel;
                    tur.WindTurbineName = turInfo.WindTurbineName;
                    tur.OperationalDate = turInfo.OperationalDate;
                    tur.MinWorkingRotSpeed = turInfo.MinWorkingRotSpeed;

                    tur.DevWindPark = turInfo.DevWindPark;
                    tur.WTurbineModel = turInfo.WTurbineModel;
                    tur.VibMeasLocList = turInfo.VibMeasLocList;
                    tur.DevMeasLocRotSpds = turInfo.DevMeasLocRotSpds;
                    tur.ProcessMeasLocList = turInfo.ProcessMeasLocList;
                    tur.TurComponentList = turInfo.TurComponentList;
                    tur.MeasLocSVMList = turInfo.MeasLocSVMList;

                    tur.VoltageCurrentMeasLocList = turInfo.VoltageCurrentMeasLocList;

                    // 主控信息
                    tur.Mcs = DAUMCS.GetMCSByTurbineId(tt.WindTurbineID);
                    tur.MCSChannelStateParamList = DAUMCS.GetMCSChannelStateListByTurID(tt.WindTurbineID);
                    tur.MCSChannelValueParamList = DAUMCS.GetMCSChannelValueListByTurID(tt.WindTurbineID);

                    // 采集单元
                    tur.WindDAUList = DAUSManageModel.GetDAUListById(tt.WindTurbineID);

                    // 测量定义
                    tur.MeasDefinitionList = MeasDefinitionManagement.GetMeasDefListByTurIdOverload(tt.WindTurbineID);
                    tur.MeasDefinition_ExList = MeasDefinitionManagement.GetMeasdefinitionEXListByTurID(tt.WindTurbineID);

                    List<MeasSolution> measSolutionlist = MeasDefinitionManagement.GetMeasSolution(tt.WindTurbineID);

                    tur.MeasDefinitionList.ForEach(m =>
                    {
                        m.Mdf_Ex = tur.MeasDefinition_ExList.FirstOrDefault(k => k.MeasDefinitionID == m.MeasDefinitionID);
                        m.SolutionList = measSolutionlist.Where(k => k.MeasDefinitionID == m.MeasDefinitionID).ToList();

                        m.WaveDefList.ForEach(ww =>
                        {

                            if (ww.WaveFormType == EnumWaveFormType.WDF_Envelope)
                            {
                                if (m.WaveDefList_Envelope.FirstOrDefault(_wdfe => _wdfe.WaveDefParamID == ww.WaveDefinitionID) == null)
                                {
                                    m.WaveDefList_Envelope.Add(MeasDefinitionManagement.GetWaveDefById_Envlope(ww.WindTurbineID, ww.WaveDefinitionID));
                                }

                            }
                            else if (ww.WaveFormType == EnumWaveFormType.WDF_Time)
                            {
                                if (m.WaveDefList_Time.FirstOrDefault(_wtime => _wtime.WaveDefParamID == ww.WaveDefParamID) == null)
                                {
                                    m.WaveDefList_Time.Add(MeasDefinitionManagement.GetWaveDefById_Time(ww.WindTurbineID, ww.WaveDefinitionID));
                                }

                            }
                        });
                    });

                    // modbus设备

                    //添加modbusUnit表
                    tur.modbusDefslist = TIMManagement.GetModbusunitList(tt.WindTurbineID);
                    //服务表
                    tur.serialServerslist = TIMManagement.GetSerialServer(tt.WindTurbineID);
                    //timcalibration表添加
                    tur.timUnitList = TIMManagement.GetTimUnit(tt.WindTurbineID);

                    //油液添加
                    tur.oilUnitList = TIMManagement.GetOilconfigByturID(tt.WindTurbineID);


                    tur.modbusDefs = TIMManagement.GetModbusDef(tt.WindTurbineID);



                    // svmunit
                    tur.svmuList = SVMManagement.GetSVMUnitListByWindTurbineID(tt.WindTurbineID);
                    tur.svmRegisterList = SVMManagement.GetSVMRegisterByturbineID(tt.WindTurbineID);
                    //SVMManagement.AddSVMRegister(svmDataList);
                    //测试开始
                    tur.svmMeasDataList = SVMManagement.GetMeasLoc_SVMListByTurID(tt.WindTurbineID);

                    tur.waveDef_SVMsList = SVMManagement.GetWaveDef_sData(tt.WindTurbineID);



                    //tur.oilAnalyzeConfigs = GetOilAnalyseSettingLists(tt.WindTurbineID);

                    //保存超声螺栓预紧力配置
                    tur.ultrasonicList = DauManagement.GetUltrasonicChannelConfigByTurId(tt.WindTurbineID);

                    // 获取报警配置
                    //获取报警定义
                    tur.AlarmDefinitions = AlarmDefinitionManage.GetAlarmDefListByTurID(tt.WindTurbineID);
                    res.Add(tur);
                }

            }
            //var serializer = new JavaScriptSerializer();
            //serializer.MaxJsonLength = int.MaxValue;
            //var jsonString = serializer.Serialize(res);

            ////var jsonString = JsonConvert.SerializeObject(res);
            ////var jsonString = JsonConvert.SerializeObject(res);
            //jsonString = DbConfig.EncryptDES(jsonString);

            ////string json = JsonConvert.SerializeObject(res);
            //var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(jsonString));
            //string fileDownName = HttpUtility.UrlEncode(fName, Encoding.UTF8) + "_" + windTurbineList.Count + "_" + DateTime.Now.ToString("yyyyMMddHHmmss");
            //return File(stream.ToArray(), "text/plain", fileDownName);


            // 使用 System.Text.Json 序列化
            var jsonString = System.Text.Json.JsonSerializer.Serialize(res);

            // 加密字符串
            jsonString = DbConfig.EncryptDES(jsonString);

            // 将字符串转换为字节数组
            byte[] fileContents = Encoding.UTF8.GetBytes(jsonString);

            // 构造文件名
            string fileDownName = HttpUtility.UrlEncode(fName, Encoding.UTF8) + "_" + windTurbineList.Count + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".txt";

            // 返回文件下载
            return File(fileContents, "text/plain", fileDownName);
        }

        /// <summary>
        /// 模板机组导入
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        [HttpPost("TemplateUpload")]
        public string TemplateUpload(IFormFile file)
        {
            string Message = "state:{0},msg:'{1}'";
            string msg = "文件错误";
            if (file != null && file.Length > 0)
            {
                // 读取文件流
                using (var reader = new StreamReader(file.OpenReadStream(), Encoding.UTF8))
                {
                    string fileContent = reader.ReadToEnd();

                    // 执行其他操作，根据需求进行处理
                    fileContent = DbConfig.DecryptDES(fileContent);

                    //var serializer = new JavaScriptSerializer();
                    //serializer.MaxJsonLength = int.MaxValue;
                    try
                    {
                        //var turList = serializer.Deserialize<List<TemplateTurbineModel>>(fileContent);
                        //var turList = JsonConvert.DeserializeObject<List<TemplateTurbineModel>>(fileContent);
                        var turList = System.Text.Json.JsonSerializer.Deserialize<List<TemplateTurbineModel>>(fileContent);

                        if (turList.Count > 0)
                        {

                            // 读取到模板配置文件，开始写入

                            // 1. 清空旧模板
                            List<WindTurbine> windTurbineList = DevTreeManagement.GetTurbinesListByWindParkId("HN999");
                            if (windTurbineList.Count > 0)
                            {
                                windTurbineList.ForEach(tt =>
                                {
                                    DevTreeController dt = new DevTreeController();
                                    dt.DeleteTurbineByID(tt.WindTurbineID);
                                });

                            }

                            // 2. 写入新配置

                            foreach (var item in turList)
                            {
                                // 1) 设备树信息
                                WindTurbine newTurbine = new WindTurbine()
                                {
                                    WindParkID = item.WindParkID,
                                    WindTurbineCode = item.WindTurbineCode,
                                    WindTurbineID = item.WindTurbineID,
                                    WindTurbineName = item.WindTurbineName,
                                    WindTurbineModel = item.WindTurbineModel,
                                    OperationalDate = item.OperationalDate.ToLocalTime(),
                                    MinWorkingRotSpeed = item.MinWorkingRotSpeed,
                                    TurComponentList = item.TurComponentList,
                                    DevMeasLocRotSpds = item.DevMeasLocRotSpds,
                                    VibMeasLocList = item.VibMeasLocList,
                                    ProcessMeasLocList = item.ProcessMeasLocList,

                                    VoltageCurrentMeasLocList = item.VoltageCurrentMeasLocList,
                                };

                                if (newTurbine.VibMeasLocList.Count > 0)
                                {
                                    foreach (var t in newTurbine.VibMeasLocList)
                                    {
                                        t.DevTurComponent = null;
                                    }
                                }

                                if (newTurbine.VoltageCurrentMeasLocList.Count > 0)
                                {
                                    foreach (var t in newTurbine.VoltageCurrentMeasLocList)
                                    {
                                        t.DevTurComponent = null;
                                    }
                                }
                                DevTreeManagement.AddWindTurbine_ManagerOverLoad(newTurbine);

                                // 2) 采集单元
                                if (item.WindDAUList != null && item.WindDAUList.Count > 0)
                                {
                                    foreach (WindDAU dau in item.WindDAUList)
                                    {
                                        dau.MeasDefVersion = 0;
                                        dau.DAUMeasDefVersion = 0;
                                        DauManagement.AddDAU(dau);
                                    }
                                }

                                // 3) z主控信息
                                if (item.Mcs != null)
                                {
                                    DAUMCS.AddMCS(item.Mcs);
                                    if (item.MCSChannelStateParamList != null && item.MCSChannelStateParamList.Count > 0)
                                    {
                                        DAUMCS.AddMCSChannelState(item.MCSChannelStateParamList);
                                    }

                                    if (item.MCSChannelValueParamList != null && item.MCSChannelValueParamList.Count > 0)
                                    {
                                        DAUMCS.AddMCSChannelValue(item.MCSChannelValueParamList);
                                    }
                                }

                                // 4) 保存测量定义
                                if (item.MeasDefinitionList != null && item.MeasDefinitionList.Count > 0)
                                {
                                    // 保存波形参数

                                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                                    {

                                        //var maxTime = ctx.WDFParamTimes.AsEnumerable().Max(_dd =>int.Parse(_dd.WaveDefParamID));
                                        var maxTime = ctx.WDFParamTimes
                                                        .Select(_dd => new { Value = _dd.WaveDefParamID })
                                                        .AsEnumerable()
                                                        .Select(_dd => int.TryParse(_dd.Value, out var num) ? num : 0)
                                                        .DefaultIfEmpty()
                                                        .Max();
                                        var maxEnvlope = ctx.WDFParamEnvlopes.Select(_dd => new { Value = _dd.WaveDefParamID })
                                                        .AsEnumerable()
                                                        .Select(_dd => int.TryParse(_dd.Value, out var num) ? num : 0)
                                                        .DefaultIfEmpty()
                                                        .Max();

                                        int _maxTime = Convert.ToInt32(maxTime);
                                        int _maxEnvlope = Convert.ToInt32(maxEnvlope);

                                        Dictionary<string, string> measIDMap = new Dictionary<string, string>();
                                        item.MeasDefinitionList.ForEach(mm =>
                                        {
                                            var oldMeasDefinitionID = mm.MeasDefinitionID;
                                            // 重写测量定义id
                                            int measDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.MeasDefinitionID);
                                            measIDMap.Add(mm.MeasDefinitionID, measDefinitionID.ToString());

                                            var _ex = item.MeasDefinition_ExList.FirstOrDefault(mdfex => mdfex.MeasDefinitionID == mm.MeasDefinitionID);
                                            if (_ex != null) { _ex.MeasDefinitionID = measDefinitionID.ToString(); }

                                            //item.ProcessDefList = GetMDFWorkCondLocListByMeasDefId(item.WindTurbineID, item.MeasDefinitionID);
                                            //item.RotSpdWaveDefList = GetWaveDefListRotSpd(item.WindTurbineID, item.MeasDefinitionID);
                                            //item.SVMWaveDefinition = WaveDefinitionManagement.GetSVMWaveDefListByMdfId(item.WindTurbineID, item.MeasDefinitionID);
                                            //item.WaveDefList = GetWaveDefByTurId(item.WindTurbineID, item.MeasDefinitionID);

                                            //// 特征值
                                            //item.VibEigenValueConf = EigenValueManage.GetMdfTimeDomainEvConf(item.WindTurbineID, item.MeasDefinitionID);
                                            //item.ProcessSuperviseDefList = EigenValueManage.GetEigenValueProcess(item.WindTurbineID, item.MeasDefinitionID);

                                            //// 触发采集
                                            //item.TriggerRules = TriggerManager.GetMeasTriggerRuleDefs(item.WindTurbineID, item.MeasDefinitionID);
                                            if (mm.RotSpdWaveDefList != null && mm.RotSpdWaveDefList.Count > 0)
                                            {
                                                for (int i = 0; i < mm.RotSpdWaveDefList.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.RotSpdWaveDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                                }
                                            }

                                            if (mm.SVMWaveDefinition != null && mm.SVMWaveDefinition.Count > 0)
                                            {
                                                for (int i = 0; i < mm.SVMWaveDefinition.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.SVMWaveDefinition[i].MeasDefinitionID = measDefinitionID.ToString();
                                                }
                                            }
                                            if (mm.WaveDefList != null && mm.WaveDefList.Count > 0)
                                            {
                                                for (int i = 0; i < mm.WaveDefList.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    var oldWaveDefid = mm.WaveDefList[i].WaveDefinitionID;
                                                    var newWaveDefid = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID);
                                                    mm.WaveDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                                    mm.WaveDefList[i].WaveDefinitionID = newWaveDefid.ToString();

                                                    var _evconfigs = mm.VibEigenValueConf.Where(t => t.WaveDefinitionID == oldWaveDefid);
                                                    foreach (var e in _evconfigs)
                                                    {
                                                        e.WaveDefinitionID = newWaveDefid.ToString();
                                                    }

                                                }
                                            }

                                            if (mm.WaveDefVoltageCurrentList != null && mm.WaveDefVoltageCurrentList.Count > 0)
                                            {
                                                for (int i = 0; i < mm.WaveDefVoltageCurrentList.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    var oldWaveDefid = mm.WaveDefVoltageCurrentList[i].WaveDefinitionID;
                                                    var newWaveDefid = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID);
                                                    mm.WaveDefVoltageCurrentList[i].MeasDefinitionID = measDefinitionID.ToString();
                                                    mm.WaveDefVoltageCurrentList[i].WaveDefinitionID = newWaveDefid.ToString();

                                                    var _evconfigs = mm.VibEigenValueConf.Where(t => t.WaveDefinitionID == oldWaveDefid);
                                                    foreach (var e in _evconfigs)
                                                    {
                                                        e.WaveDefinitionID = newWaveDefid.ToString();
                                                    }

                                                }
                                            }

                                            if (mm.VibEigenValueConf != null && mm.VibEigenValueConf.Count > 0)
                                            {
                                                for (int i = 0; i < mm.VibEigenValueConf.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.VibEigenValueConf[i].MeasDefinitionID = measDefinitionID.ToString();
                                                    //mm.VibEigenValueConf[i].EvId = 0;
                                                }
                                            }
                                            if (mm.ProcessSuperviseDefList != null && mm.ProcessSuperviseDefList.Count > 0)
                                            {
                                                for (int i = 0; i < mm.ProcessSuperviseDefList.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.ProcessSuperviseDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                                }
                                            }
                                            if (mm.TriggerRules != null && mm.TriggerRules.Count > 0)
                                            {
                                                for (int i = 0; i < mm.TriggerRules.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.TriggerRules[i].MeasDefinitionID = measDefinitionID.ToString();

                                                    if (mm.TriggerRules[i].ExecuteMdfs != null && mm.TriggerRules[i].ExecuteMdfs.Count > 0)
                                                    {
                                                        var _exMdf = mm.TriggerRules[i].ExecuteMdfs.Where(tre => tre.MeasDefinitionID == mm.MeasDefinitionID).ToList();
                                                        if (_exMdf.Count > 0)
                                                        {
                                                            for (int pp = 0; pp < _exMdf.Count; pp++)
                                                            {
                                                                // 修改指定字段的值
                                                                _exMdf[pp].MeasDefinitionID = measDefinitionID.ToString();
                                                            }
                                                        }
                                                    }
                                                }
                                            }


                                            if (mm.ProcessDefList != null && mm.ProcessDefList.Count > 0)
                                            {
                                                for (int i = 0; i < mm.ProcessDefList.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.ProcessDefList[i].MeasDefinitionID = measDefinitionID.ToString();
                                                }
                                            }

                                            if (mm.SolutionList != null && mm.SolutionList.Count > 0)
                                            {
                                                for (int i = 0; i < mm.SolutionList.Count; i++)
                                                {
                                                    // 修改指定字段的值
                                                    mm.SolutionList[i].MeasDefinitionID = measDefinitionID.ToString();
                                                }

                                            }

                                            if (item.waveDef_SVMsList != null && item.waveDef_SVMsList.Count > 0)
                                            {
                                                var _svmWave = item.waveDef_SVMsList.Where(_wSvm => _wSvm.MeasDefinitionID == mm.MeasDefinitionID).ToList();
                                                if (_svmWave.Count > 0)
                                                {
                                                    for (int i = 0; i < _svmWave.Count; i++)
                                                    {
                                                        // 修改指定字段的值
                                                        _svmWave[i].MeasDefinitionID = measDefinitionID.ToString();
                                                        _svmWave[i].WaveDefinitionID = MeasDefinitionManagement.GetNewMeaID(EnumSequenceType.WaveDefinitionID).ToString();
                                                    }
                                                }
                                            }

                                            mm.MeasDefinitionID = measDefinitionID.ToString();

                                            if (mm.WaveDefList_Envelope != null && mm.WaveDefList_Envelope.Count > 0)
                                            {
                                                mm.WaveDefList_Envelope.ForEach(en =>
                                                {
                                                    var _wdflist = mm.WaveDefList.Where(wdf => wdf.WaveDefParamID == en.WaveDefParamID).ToList();
                                                    var ParameEnvlope = ctx.WDFParamEnvlopes.FirstOrDefault(wdfenv => wdfenv.EnvBandWidth == en.EnvBandWidth && wdfenv.EnvFiterFreq == en.EnvFiterFreq && wdfenv.SampleLength == en.SampleLength);
                                                    if (ParameEnvlope != null)
                                                    {

                                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = ParameEnvlope.WaveDefParamID);

                                                    }
                                                    else
                                                    {
                                                        _maxEnvlope++;
                                                        en.WaveDefParamID = _maxEnvlope.ToString();
                                                        ctx.WDFParamEnvlopes.Add(new WaveDefParam_Envlope()
                                                        {

                                                            EnvBandWidth = en.EnvBandWidth,
                                                            EnvFiterFreq = en.EnvFiterFreq,
                                                            SampleLength = en.SampleLength,
                                                            WaveDefParamID = en.WaveDefParamID,
                                                            WaveDefParamName = en.WaveDefinitionName,
                                                        });
                                                        ctx.SaveChanges();

                                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = en.WaveDefParamID);
                                                    }
                                                });
                                            }


                                            if (mm.WaveDefList_Time != null && mm.WaveDefList_Time.Count > 0)
                                            {
                                                mm.WaveDefList_Time.ForEach(en =>
                                                {
                                                    var _wdflist = mm.WaveDefList.Where(wdf => wdf.WaveDefParamID == en.WaveDefParamID).ToList();
                                                    var WDFParamTime = ctx.WDFParamTimes.FirstOrDefault(wdfenv => wdfenv.LowerLimitFreqency == en.LowerLimitFreqency && wdfenv.UpperLimitFreqency == en.UpperLimitFreqency && wdfenv.SampleLength == en.SampleLength);
                                                    if (WDFParamTime != null)
                                                    {

                                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = WDFParamTime.WaveDefParamID);

                                                    }
                                                    else
                                                    {
                                                        _maxTime++;
                                                        en.WaveDefParamID = _maxTime.ToString();
                                                        ctx.WDFParamTimes.Add(new WaveDefParam_Time()
                                                        {

                                                            LowerLimitFreqency = en.LowerLimitFreqency,
                                                            UpperLimitFreqency = en.UpperLimitFreqency,
                                                            SampleLength = en.SampleLength,
                                                            WaveDefParamID = en.WaveDefParamID,
                                                            WaveDefParamName = en.WaveDefinitionName,
                                                        });
                                                        ctx.SaveChanges();

                                                        _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = en.WaveDefParamID);
                                                    }
                                                });
                                            }

                                            //if (mm.WaveDefVoltageCurrentList != null && mm.WaveDefVoltageCurrentList.Count > 0)
                                            //{
                                            //    mm.WaveDefVoltageCurrentList.ForEach(en =>
                                            //    {
                                            //        var _wdflist = mm.WaveDefVoltageCurrentList.Where(wdf => wdf.WaveDefParamID == en.WaveDefParamID).ToList();
                                            //        var WDFParamTime = ctx.WDFParamTimes.FirstOrDefault(wdfenv => wdfenv.LowerLimitFreqency == en.LowerLimitFreqency && wdfenv.UpperLimitFreqency == en.UpperLimitFreqency && wdfenv.SampleLength == en.SampleLength);
                                            //        if (WDFParamTime != null)
                                            //        {

                                            //            _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = WDFParamTime.WaveDefParamID);

                                            //        }
                                            //        else
                                            //        {
                                            //            _maxTime++;
                                            //            en.WaveDefParamID = _maxTime.ToString();
                                            //            ctx.WDFParamTimes.Add(new WaveDefParam_Time()
                                            //            {

                                            //                //LowerLimitFreqency = en.LowerLimitFreqency,
                                            //                //UpperLimitFreqency = en.UpperLimitFreqency,
                                            //                //SampleLength = en.SampleLength,
                                            //                WaveDefParamID = en.WaveDefParamID,
                                            //                WaveDefParamName = en.WaveDefinitionName,
                                            //            });
                                            //            ctx.SaveChanges();

                                            //            _wdflist.ForEach(_wdf => _wdf.WaveDefParamID = en.WaveDefParamID);
                                            //        }
                                            //    });
                                            //}

                                            // modbusdef measid重写
                                            if (item.modbusDefs != null && item.modbusDefs.Count > 0)
                                            {
                                                var modbusDefs = item.modbusDefs.Where(_def => _def.MeasDefinitionID == oldMeasDefinitionID);
                                                foreach (var _mdf in modbusDefs)
                                                {
                                                    _mdf.MeasDefinitionID = measDefinitionID.ToString();
                                                }
                                            }

                                        });
                                        item.MeasDefinitionList.ForEach(pk =>
                                        {
                                            if (pk.TriggerRules != null && pk.TriggerRules.Count > 0)
                                            {
                                                for (int i = 0; i < pk.TriggerRules.Count; i++)
                                                {
                                                    if (pk.TriggerRules[i].ExecuteMdfs != null && pk.TriggerRules[i].ExecuteMdfs.Count > 0)
                                                    {
                                                        var _exMdf = pk.TriggerRules[i].ExecuteMdfs.ToList();
                                                        if (_exMdf.Count > 0)
                                                        {
                                                            for (int pp = 0; pp < _exMdf.Count; pp++)
                                                            {
                                                                // 修改指定字段的值
                                                                if (measIDMap.ContainsKey(_exMdf[pp].MeasDefinitionID))
                                                                {
                                                                    _exMdf[pp].MeasDefinitionID = measIDMap[_exMdf[pp].MeasDefinitionID];
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                        });

                                    }
                                    MeasDefinitionManagement.AddMeaDefition(item.MeasDefinitionList);

                                }

                                if (item.MeasDefinition_ExList != null && item.MeasDefinition_ExList.Count > 0)
                                {
                                    MeasDefinitionManagement.AddMeaDefitionEX(item.MeasDefinition_ExList);
                                }

                                // 5) 保存报警
                                if (item.AlarmDefinitions != null && item.AlarmDefinitions.Count > 0)
                                {
                                    AlarmDefinitionManage.AddAlarmDefinition(item.AlarmDefinitions);
                                }

                                if (item.modbusDefslist != null && item.modbusDefslist.Count > 0)
                                {
                                    item.modbusDefslist.ForEach(m => { TIMManagement.AddModbusunit(m); });
                                }


                                //服务表
                                if (item.serialServerslist != null && item.serialServerslist.Count > 0)
                                {
                                    item.serialServerslist.ForEach(mm => { TIMManagement.AddSerialServer(mm); });
                                }
                                //timcalibration表添加
                                if (item.timUnitList != null && item.timUnitList.Count > 0)
                                {
                                    item.timUnitList.ForEach(mm =>
                                    {
                                        mm.CalibAngleUpdateTime = mm.CalibAngleUpdateTime.ToLocalTime();
                                        TIMManagement.AddTimUnit(mm);
                                    });
                                }

                                if (item.modbusDefs != null && item.modbusDefs.Count > 0)
                                {
                                    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                                    {
                                        ctx.ModbusDefs.AddRange(item.modbusDefs);
                                        ctx.SaveChanges();
                                    }
                                }


                                //油液添加

                                if (item.oilUnitList != null && item.oilUnitList.Count > 0)
                                {
                                    TIMManagement.AddOilconfig(item.oilUnitList);
                                }

                                // svmunit

                                if (item.svmuList != null && item.svmuList.Count > 0)
                                {
                                    item.svmuList.ForEach(mm => { SVMManagement.AddSVM(mm); });
                                }


                                if (item.svmRegisterList != null && item.svmRegisterList.Count > 0)
                                {
                                    SVMManagement.AddSVMRegister(item.svmRegisterList);
                                }
                                //SVMManagement.AddSVMRegister(svmDataList);
                                //测试开始
                                if (item.svmMeasDataList != null && item.svmMeasDataList.Count > 0)
                                {
                                    SVMManagement.AddSVMMeasLoc(item.WindTurbineID, item.svmMeasDataList);
                                }

                                if (item.waveDef_SVMsList != null && item.waveDef_SVMsList.Count > 0)
                                {
                                    SVMManagement.AddSVMWaveDef(item.waveDef_SVMsList);
                                }

                                //if (item.oilAnalyzeConfigs != null && item.oilAnalyzeConfigs.Count > 0)
                                //{
                                //    AddOilAnalyseSettingLists(item.oilAnalyzeConfigs);
                                //}

                                if (item.ultrasonicList?.Count > 0)
                                {
                                    item.ultrasonicList.ForEach(mm => { DauManagement.AddUltrasonic(mm); });
                                }

                            }
                        }

                        msg = "导入成功";
                        Message = string.Format(Message, 1, msg);
                    }
                    catch (Exception ex)
                    {
                        msg = "导入失败，文件读取失败";
                        Message = string.Format(Message, 0, msg);
                    }
                }

            }

            return "{" + Message + "}";
        }

        #endregion

        #region 导出测量定义、报警定义、传感器状态、采集器状态为csv
        [AllowAnonymous]
        [HttpGet("ExportDauConfig")]
        public async Task<IActionResult> ExportDauConfig(string parkId)
        {
            //1. 导出测量定义
            //2. 导出报警定义
            //3. 传感器状态
            //4. 采集器状态
            var memoryStream = new MemoryStream();
            try
            {
                
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                using (ExcelPackage excelPackage = new ExcelPackage())
                {
                    // 添加第一个Sheet
                    ExcelWorksheet worksheet1 = excelPackage.Workbook.Worksheets.Add("时域波形定义");
                    ExcelWorksheet worksheet2 = excelPackage.Workbook.Worksheets.Add("电流电压波形定义");
                    ExcelWorksheet worksheet3 = excelPackage.Workbook.Worksheets.Add("工况波形定义");
                    ExcelWorksheet worksheet4 = excelPackage.Workbook.Worksheets.Add("转速波形定义");
                    ExcelWorksheet worksheet5 = excelPackage.Workbook.Worksheets.Add("采集单元状态");
                    ExcelWorksheet worksheet6 = excelPackage.Workbook.Worksheets.Add("传感器状态");
                    ExcelWorksheet worksheet7 = excelPackage.Workbook.Worksheets.Add("报警配置");

                    // 表头初始化
                    // 时域波形定义
                    string[] worksheet1Header = new string[] { "风场", "机组", "测量定义名称", "波形定义名称", "测量位置", "信号类型", "信号带宽(Hz)", "采样频率(Hz)", "采样长度(s)" };
                    // 电流电压波形定义
                    string[] worksheet2Header = new string[] { "风场", "机组", "测量定义名称", "波形定义名称", "测量位置", "信号类型", "信号带宽(Hz)", "采样频率(Hz)", "采样长度(s)" };
                    // 工况波形定义
                    string[] worksheet3Header = new string[] { "风场", "机组", "测量定义名称", "工况类型", "上限频率(Hz)", "采样长度(s)" };
                    // 转速波形定义
                    string[] worksheet4Header = new string[] { "风场", "机组", "测量定义名称", "测量位置", "编码器线数", "波形线数", "变速比" };
                    //采集单元状态
                    string[] worksheet5Header = new string[] { "风场", "机组", "采集单元名称", "ip地址", "采集间隔", "状态", "更新时间" };
                    // 传感器状态
                    string[] worksheet6Header = new string[] { "风场", "机组", "采集单元名称", "通道", "测量位置", "偏置电压(V)", "状态", "更新时间" };
                    // 报警配置
                    string[] worksheet7Header = new string[] { "风场", "机组", "测量位置", "特征值", "工况参数", "工况下限", "工况上限", "正向注意", "正向危险", "反向注意", "反向危险" };

                    SetWorksheetHeader(worksheet1, worksheet1Header);
                    SetWorksheetHeader(worksheet2, worksheet2Header);
                    SetWorksheetHeader(worksheet3, worksheet3Header);
                    SetWorksheetHeader(worksheet4, worksheet4Header);
                    SetWorksheetHeader(worksheet5, worksheet5Header);
                    SetWorksheetHeader(worksheet6, worksheet6Header);
                    SetWorksheetHeader(worksheet7, worksheet7Header);

                    int sheet1Row = 2, sheet2Row = 2, sheet3Row = 2, sheet4Row = 2, sheet5Row = 2, sheet6Row = 2, sheet7Row = 2;
                    WindPark park = DevTreeManagement.GetWindPark(parkId);
                    if (park != null)
                    {
                        foreach (var turbine in park.WindTurbineList)
                        {
                            // 测量定义
                            List<MeasDefinition> measDefs = MeasDefinitionManagement.GetMeasDefListByTurIdOverload(turbine.WindTurbineID);
                            foreach (var meas in measDefs)
                            {
                                // 振动波形定义
                                meas.WaveDefList.ForEach(t =>
                                {
                                    var measloc = DevTreeManagement.GetVibMeasLocByID(t.MeasLocationID);
                                    //"风场", "机组", "测量定义名称", "波形定义名称", "测量位置", "信号类型", "信号带宽", "采样频率", "采样长度" 
                                    worksheet1.Cells[sheet1Row, 1].Value = park.WindParkName;
                                    worksheet1.Cells[sheet1Row, 2].Value = turbine.WindTurbineName;
                                    worksheet1.Cells[sheet1Row, 3].Value = meas.MeasDefinitionName;
                                    worksheet1.Cells[sheet1Row, 4].Value = t.WaveDefinitionName;
                                    worksheet1.Cells[sheet1Row, 5].Value = measloc?.MeasLocName;
                                    worksheet1.Cells[sheet1Row, 6].Value = t.SignalType;
                                    

                                    if (t.WaveFormType == EnumWaveFormType.WDF_Envelope)
                                    {
                                        var mdfWaveDef = MeasDefinitionManagement.GetWaveDefById_Envlope(turbine.WindTurbineID, t.WaveDefinitionID);
                                        if(mdfWaveDef!= null)
                                        {
                                            worksheet1.Cells[sheet1Row, 7].Value = mdfWaveDef.EnvBandWidth;
                                            worksheet1.Cells[sheet1Row, 8].Value = mdfWaveDef.EnvFiterFreq;
                                            worksheet1.Cells[sheet1Row, 9].Value = mdfWaveDef.SampleLength / 1000;
                                        }
                                    }
                                    else
                                    {
                                        var mdfWaveDef = MeasDefinitionManagement.GetWaveDefById_Time(turbine.WindTurbineID, t.WaveDefinitionID);
                                        if (mdfWaveDef != null)
                                        {
                                            worksheet1.Cells[sheet1Row, 7].Value = mdfWaveDef.UpperLimitFreqency;
                                            worksheet1.Cells[sheet1Row, 8].Value = mdfWaveDef.UpperLimitFreqency * 2.56f;
                                            worksheet1.Cells[sheet1Row, 9].Value = mdfWaveDef.SampleLength / 1000;
                                        }
                                    }


                                    sheet1Row++;
                                });

                                // 过程量波形定义
                                meas.WaveDefVoltageCurrentList.ForEach(t =>
                                {
                                    var measloc = DevTreeManagement.GetVoltageCurrentMeasLocByID(t.MeasLocationID);
                                    var wave = MeasDefinitionManagement.GetWaveDefById_VoltageCurrent(turbine.WindTurbineID, t.WaveDefinitionID);
                                    //"风场", "机组", "测量定义名称", "波形定义名称", "测量位置", "信号类型", "信号带宽", "采样频率", "采样长度" 
                                    worksheet2.Cells[sheet2Row, 1].Value = park.WindParkName;
                                    worksheet2.Cells[sheet2Row, 2].Value = turbine.WindTurbineName;
                                    worksheet2.Cells[sheet2Row, 3].Value = meas.MeasDefinitionName;
                                    worksheet2.Cells[sheet2Row, 4].Value = t.WaveDefinitionName;
                                    worksheet2.Cells[sheet2Row, 5].Value = measloc?.MeasLocName;
                                    worksheet2.Cells[sheet2Row, 6].Value = t.SignalType;
                                    if (wave != null)
                                    {
                                        worksheet2.Cells[sheet2Row, 7].Value = wave.UpperLimitFreqency;
                                        worksheet2.Cells[sheet2Row, 8].Value = wave.UpperLimitFreqency * 2.56f;
                                        worksheet2.Cells[sheet2Row, 9].Value = wave.SampleLength / 1000;
                                    }

                                    sheet2Row++;
                                });

                                // 工况波形定义
                                meas.ProcessDefList.ForEach(t =>
                                {
                                    var measloc = DevTreeManagement.GetWorkCondMeasLocation(t.MeasLocationID);
                                    //"风场", "机组", "测量定义名称", "工况类型", "上限频率", "采样长度"
                                    worksheet3.Cells[sheet3Row, 1].Value = park.WindParkName;
                                    worksheet3.Cells[sheet3Row, 2].Value = turbine.WindTurbineName;
                                    worksheet3.Cells[sheet3Row, 3].Value = meas.MeasDefinitionName;
                                    worksheet3.Cells[sheet3Row, 4].Value = measloc.MeasLocName;
                                    worksheet3.Cells[sheet3Row, 5].Value = t.UpperLimitFreqency;
                                    worksheet3.Cells[sheet3Row, 6].Value = t.SampleLength;

                                    sheet3Row++;
                                });

                                // 转速波形定义
                                meas.RotSpdWaveDefList.ForEach(t =>
                                {
                                    var measloc = DevTreeManagement.GetRotSpdMeasLocation(t.MeasLoc_RotSpdID);
                                    //"风场", "机组", "测量定义名称", "测量位置", "编码器线数", "波形线数","变速比"
                                    worksheet4.Cells[sheet4Row, 1].Value = park.WindParkName;
                                    worksheet4.Cells[sheet4Row, 2].Value = turbine.WindTurbineName;
                                    worksheet4.Cells[sheet4Row, 3].Value = meas.MeasDefinitionName;
                                    worksheet4.Cells[sheet4Row, 4].Value = measloc?.MeasLocName;
                                    worksheet4.Cells[sheet4Row, 5].Value = measloc.LineCounts;
                                    worksheet4.Cells[sheet4Row, 6].Value = t.LineCounts;
                                    worksheet4.Cells[sheet4Row, 7].Value = measloc.GearRatio;

                                    sheet4Row++;
                                });
                            }

                            // 采集单元 
                            var dauList = DAUSManageModel.GetDAUListById(turbine.WindTurbineID);
                            if (dauList.Any())
                            {
                                List<MeasLoc_Vib> vibLoc = DevTreeManagement.GetVibMeasLocationByTurId(turbine.WindTurbineID);
                                foreach (var dau in dauList)
                                {
                                    // 采集单元状态
                                    RTAlarmStatus_DAU data = DAUSManageModel.GetDAURTAlarmStatusByWindTurbineIdAndDAUID(dau.WindTurbineID, dau.DauID);
                                    WindDAU dauData = DAUSManageModel.GetDAUByTrubineIdAndDauId(dau.WindTurbineID, dau.DauID);
                                    if (data != null)
                                    {
                                        //"风场", "机组", "采集单元名称", "ip地址", "采集间隔", "状态", "更新时间" 
                                        worksheet5.Cells[sheet5Row, 1].Value = park.WindParkName;
                                        worksheet5.Cells[sheet5Row, 2].Value = turbine.WindTurbineName;
                                        worksheet5.Cells[sheet5Row, 3].Value = dau.DAUName;
                                        worksheet5.Cells[sheet5Row, 4].Value = dau.IP;
                                        worksheet5.Cells[sheet5Row, 5].Value = dau.DataAcquisitionInterval;
                                        worksheet5.Cells[sheet5Row, 6].Value = data.AlarmState;
                                        worksheet5.Cells[sheet5Row, 7].Value = data.StatusUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");

                                        sheet5Row++;

                                        // 传感器状态
                                        data.sensorRTList.ForEach(t =>
                                        {
                                            //"风场", "机组", "采集单元名称", "通道", "测量位置", "偏置电压", "状态", "更新时间"
                                            worksheet6.Cells[sheet6Row, 1].Value = park.WindParkName;
                                            worksheet6.Cells[sheet6Row, 2].Value = turbine.WindTurbineName;
                                            worksheet6.Cells[sheet6Row, 3].Value = dau.DAUName;
                                            worksheet6.Cells[sheet6Row, 4].Value = t.ChannelNumber;
                                            worksheet6.Cells[sheet6Row, 5].Value = t.MeasLocationID;
                                            worksheet6.Cells[sheet6Row, 6].Value = t.DCDataValue;
                                            worksheet6.Cells[sheet6Row, 7].Value = t.AlarmState;
                                            worksheet6.Cells[sheet6Row, 8].Value = t.StatusUpdateTime.ToString("yyyy-MM-dd HH:mm:ss");

                                            var channel = dauData.DAUChannelList?.Find(p=>p.ChannelNumber == t.ChannelNumber);
                                            if (channel != null)
                                            {
                                                MeasLoc_Vib _vib = vibLoc.FirstOrDefault(p => p.MeasLocationID == channel.MeasLocVibID);
                                                if (_vib != null)
                                                {
                                                    worksheet6.Cells[sheet6Row, 5].Value = _vib.MeasLocName;
                                                }
                                            }

                                            sheet6Row++;
                                        });
                                    }
                                }
                            }

                            // 报警定义
                            var vib = WarnManager.alarmDefinitonUIModels(turbine.WindTurbineID, "1");
                            var svm = WarnManager.alarmDefinitonUIModels(turbine.WindTurbineID, "0");

                            var mergeList = vib.Concat(svm).ToList();
                            mergeList.ForEach(t =>
                            {
                                //"风场", "机组", "测量位置", "特征值", "工况参数", "工况下限", "工况上限", "正向注意", "正向危险", "反向注意", "反向危险"
                                worksheet7.Cells[sheet7Row, 1].Value = park.WindParkName;
                                worksheet7.Cells[sheet7Row, 2].Value = turbine.WindTurbineName;
                                worksheet7.Cells[sheet7Row, 3].Value = t.MeasLocationName;
                                worksheet7.Cells[sheet7Row, 4].Value = t.EigenValueName;
                                worksheet7.Cells[sheet7Row, 5].Value = t.WorkConParameterName;
                                worksheet7.Cells[sheet7Row, 6].Value = t.LowerLimitValue;
                                worksheet7.Cells[sheet7Row, 7].Value = t.UpperLimitValue;
                                worksheet7.Cells[sheet7Row, 8].Value = t.WarnValue;
                                worksheet7.Cells[sheet7Row, 9].Value = t.AlarmValue;
                                worksheet7.Cells[sheet7Row, 10].Value = t.ReverseWarnValue;
                                worksheet7.Cells[sheet7Row, 11].Value = t.ReverseAlarmValue;

                                sheet7Row++;
                            });
                        }
                    }
                   
                    //var stream = new MemoryStream(excelPackage.GetAsByteArray());
                    string fileDownName = park.WindParkName + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";

     
                    string encodedFileName = WebUtility.UrlEncode(fileDownName);
              
                    excelPackage.SaveAs(memoryStream);
                    memoryStream.Position = 0;

                    // 返回文件流
                    return File(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileDownName);


                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
            finally{
                //memoryStream.Dispose();
            }

        }
        private string EncodeFileNameForDownload(string fileName)
        {
            // 使用 RFC 5987 标准对文件名进行编码
            return $"attachment; filename*=UTF-8''{WebUtility.UrlEncode(fileName)}";
        }

        private void SetWorksheetHeader(ExcelWorksheet worksheet, string[] header)
        {
            for (var i = 0; i < header.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = header[i];
            }
        }
        #endregion

        #region 螺栓标定及基准导入

        /// <summary>
        /// 标定文件上传
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        [HttpPost("BoltBDFileUpdate")]
        public IActionResult BoltBDFileUpdateSelect(IFormFile file)
        {
            string Message = "state:{0},msg:'{1}'";
            string msg = "";

            if (file == null || file.Length == 0 || !Path.GetExtension(file.FileName).Equals(".zip", StringComparison.OrdinalIgnoreCase))
            {
                msg = "文件无效，请选择ZIP文件。";
                Message = string.Format(Message, 0, msg);
                //return "{" + Message + "}";
                return Ok(ApiResponse<string>.Error(msg));
            }
            string zipFilePath = Path.Combine(Path.GetTempPath(), file.FileName);
            //file.SaveAs(zipFilePath);
            using (var stream = new FileStream(zipFilePath, FileMode.Create))
            {
                file.CopyTo(stream);
            }
            // 获取保存路径
            //string uploadDirectory = Path.Combine(HttpContext.Server.MapPath("../Uploads"));
            //string _unzipDirectory = zipFilePath+DateTime.UtcNow.Ticks.ToString();

            string uploadDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Uploads");
            string _unzipDirectory = Path.Combine(uploadDirectory, Path.GetFileNameWithoutExtension(file.FileName) +"_"+ DateTime.UtcNow.Ticks.ToString());
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                System.IO.Compression.ZipFile.ExtractToDirectory(zipFilePath, _unzipDirectory, Encoding.GetEncoding("GBK"));
            }
            catch (Exception ex)
            {
                msg = $"解压失败: {ex.Message}";
                Message = string.Format(Message, 0, msg);
                //return "{" + Message + "}";
                return Ok(ApiResponse<string>.Error(Message));
            }
            HashSet<string> fileNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            // 获取解压目录中的所有文件
            var files = Directory.GetFiles(_unzipDirectory, "*", SearchOption.AllDirectories)
                                 .Select(f => Path.GetFullPath(f))
                                 .ToList();

            var fileDict = GetLatestFiles(files);

            try
            {
                //文件名格式为 "HN999-CCWE1500-M42_350_10.9-yyyyMMddHHmmss.ufc"
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    foreach (var _file in fileDict.Values)
                    {
                        // 解析文件名
                        var fname = _file.Split('-');
                        if (fname.Length < 4)
                        {
                            continue;
                        }
                        var boltModelName = fname[fname.Length - 2];
                        var wtModel = fname[fname.Length - 3];
                        var parkID = fname[fname.Length - 4].Split(Path.DirectorySeparatorChar).Last();

                        var ultrasonicParam = ctx.UltrasonicBoltParams.FirstOrDefault(item => item.WindParkID == parkID && item.WindTurbineModel == wtModel && item.BoltModel == boltModelName);
                        var jsonContent = GetJsonFileUltrasonicBoltParam(_file);
                        if (ultrasonicParam == null)
                        {
                            ctx.UltrasonicBoltParams.Add(new UltrasonicBoltParam()
                            {
                                WindParkID = parkID,
                                WindTurbineModel = wtModel,
                                BoltModel = boltModelName,
                                PreloadCalCoeffs = jsonContent?.PreloadCalCoeffs,
                                TempCalibCoeff = (float)(jsonContent?.TempCalibCoeff),
                            });
                        }
                        else
                        {
                            ultrasonicParam.TempCalibCoeff = (float)(jsonContent?.TempCalibCoeff);
                            ultrasonicParam.PreloadCalCoeffs = jsonContent?.PreloadCalCoeffs;
                        }
                        ctx.SaveChanges();
                    }

                }

            }catch(Exception ex)
            {
                msg = "上传失败，文件解析失败，请检查。";
                Message = string.Format(Message, 0, msg);
                //return "{" + Message + "}";
                return Ok(ApiResponse<string>.Error(Message));
            }
            msg = "上传成功";
            Message = string.Format(Message, 0, msg);
            //return "{" + Message + "}";
            return Ok(ApiResponse<string>.Success(Message));
        }
        private static UltrasonicBoltParam GetJsonFileUltrasonicBoltParam(string filePath)
        {
            string jsonContent = System.IO.File.ReadAllText(filePath);
            return JsonConvert.DeserializeObject<UltrasonicBoltParam>(jsonContent);
        }

        private static Dictionary<string,string> GetLatestFiles(List<string> files)
        {
            var fileDictionary = new Dictionary<string, string>();

            foreach (var file in files)
            {
                // 文件名格式为 "HN999-CCWE1500-M42_350_10.9-yyyyMMddHHmmss.ufc"
                // 提取文件名中的时间戳部分
                var timePart = file.Split('-').Last().Split('.')[0];
                var fileNameWithoutTime = file.Substring(0, file.LastIndexOf('-') + 1);
                if (!fileDictionary.ContainsKey(fileNameWithoutTime))
                {
                    // 保存到字典
                    fileDictionary[fileNameWithoutTime] = file;
                }
                else
                {
                    DateTime dateTime1;
                    DateTime dateTime2;
                    // 比较时间
                    var curFileDate = fileDictionary[fileNameWithoutTime].Split('-').Last().Split('.')[0];
                    if(DateTime.TryParseExact(curFileDate, "yyyyMMddHHmmss", null, System.Globalization.DateTimeStyles.None, out dateTime1) && DateTime.TryParseExact(timePart, "yyyyMMddHHmmss", null, System.Globalization.DateTimeStyles.None, out dateTime2))
                    {
                        if (dateTime1 < dateTime2)
                        {
                            fileDictionary[fileNameWithoutTime] = file;
                        }
                    }
                }
            }

            // 从字典中获取最新的文件名
            return fileDictionary;
        }


        /// <summary>
        /// 基准文件上传
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        /// 
        [AllowAnonymous]
        [HttpPost("BoltJZFileUpdate")]
        public IActionResult BoltJZFileUpdateSelect()
        {
            string windParkID = Request.Form["windParkID"]; //普通参数获取
            var file = Request.Form.Files["file"];

            string Message = "state:{0},msg:'{1}'";
            string msg = "";

            if (file == null || file.Length == 0 || !Path.GetExtension(file.FileName).Equals(".zip", StringComparison.OrdinalIgnoreCase))
            {
                msg = "文件无效，请上传ZIP文件。";
                Message = string.Format(Message, 0, msg);
                //return "{" + Message + "}";
                return Ok(ApiResponse<string>.Error("文件无效，请上传ZIP文件。"));
            }

            // 获取保存路径
            string uploadDirectory = Path.Combine(Directory.GetCurrentDirectory(), "Uploads");
            string zipFilePath = Path.Combine(Path.GetTempPath(), file.FileName);
           
            string _unzipDirectory = Path.Combine(uploadDirectory, Path.GetFileNameWithoutExtension(file.FileName) + "_" + DateTime.UtcNow.Ticks.ToString());

            try
            {
                // 确保上传目录存在
                Directory.CreateDirectory(uploadDirectory);

                // 保存上传的ZIP文件
                using (var stream = new FileStream(zipFilePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                // 解压ZIP文件
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                Directory.CreateDirectory(_unzipDirectory);
                ZipFile.ExtractToDirectory(zipFilePath, _unzipDirectory, Encoding.GetEncoding("GBK"));
            }
            catch (Exception ex)
            {
                msg = $"解压失败: {ex.Message}";
                Message = string.Format(Message, 0, msg);
                //return "{" + Message + "}";
                return Ok(ApiResponse<string>.Error(msg));
            }
   
            // 获取解压目录中的所有文件
            var files = Directory.GetFiles(_unzipDirectory, "*", SearchOption.AllDirectories)
                                 .Select(f => Path.GetFullPath(f))
                                 .ToList();

            var fileDict = GetLatestFiles(files);

            // 查询当前风场下的所有测点
            try
            {
                var dauchannels = DauManagement.GetDAUVibChannelListByParkID(windParkID);
                var measlocs = DevTreeManagement.GetVibMeasLocationByParkID(windParkID);

                // 标定获取
                List<UltrasonicBoltParam> ultrasonicBoltParams = DauManagement.GetUltrasonicBoltParamByParkId(windParkID);
                foreach (var dauchannel in dauchannels)
                {
                    var measloc = measlocs.FirstOrDefault(t => t.MeasLocationID == dauchannel.MeasLocVibID);
                    dauchannel.Description = measloc?.MeasLocName;
                }

                var urconfigs = DauManagement.GetUltrasonicChannelConfigByParkID(windParkID);
                foreach (var urconfig in urconfigs)
                {
                    // 查询测点名称
                    var measlocname = dauchannels.FirstOrDefault(t => t.DauID == urconfig.DauID && t.WindTurbineID == urconfig.WindTurbineID && t.ChannelNumber == urconfig.ChannelNumber);
                    if (measlocname == null) continue;
                    // 查询文件
                    var filePath = fileDict.Keys.FirstOrDefault(t =>
                    {
                        // 解析文件名
                        var fname = t.Split('-');

                        //var channelNumber = fname[fname.Length - 2];
                        //var measlocName = fname[fname.Length - 3];
                        //var boltModel = fname[fname.Length - 4];
                        //var turbineID = fname[fname.Length - 5].Split(Path.DirectorySeparatorChar).Last();

                        //if (urconfig.WindTurbineID == turbineID && urconfig.ChannelNumber.ToString() == channelNumber && measlocname.Description == measlocName)
                        var channelNumber = fname[4];
                        var measlocName = fname[3];
                        var boltModel = fname[2];
                        var turbineID = fname[1].Split(Path.DirectorySeparatorChar).Last();

                        if (urconfig.WindTurbineID == turbineID && measlocname.Description == measlocName)
                        {
                            return true;
                        }
                        else
                        {
                            return false;
                        }
                    });

                    if (filePath != null)
                    {
                        var fname = fileDict[filePath].Split('-');
                        //urconfig.StandardFilePath = fileDict[filePath];
                        //urconfig.StandardFilePath = System.IO.File.ReadAllText(fileDict[filePath]);
                        urconfig.BoltModel = fname[2];

                        urconfig.StandardContent = System.IO.File.ReadAllText(fileDict[filePath]);
                        urconfig.StandardFilePath = fileDict[filePath];

                        // 兼容一代， 保存系数和上下限
                        var boltmodel = ultrasonicBoltParams.FirstOrDefault(t => t.BoltModel == urconfig.BoltModel);
                        if (boltmodel != null)
                        {
                            urconfig.TempCalibCoeff = boltmodel.TempCalibCoeff;
                            urconfig.PreloadCalCoeffs = boltmodel.PreloadCalCoeffs;
                        }

                        // 解析文件，保存上下限
                        try
                        {
                            // 解析JSON内容
                            var jsonData = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(urconfig.StandardContent);
                            // 提取BoltProp对象
                            var boltProp = jsonData.GetProperty("BoltProp");
                            // 获取Upper和Lower的值
                            float upper = boltProp.GetProperty("Upper").GetSingle();
                            float lower = boltProp.GetProperty("Lower").GetSingle();

                            urconfig.PreloadUpperLimmit = upper;
                            urconfig.PreloadLowerLimmit = lower;
                        }
                        catch(Exception)
                        {
                            // 二代不做处理
                        }

                        DauManagement.EditUltrasonic(urconfig);
                    }
                }
            }catch(Exception ex)
            {
                msg = "上传失败，文件解析失败，请检查。";
                Message = string.Format(Message, 0, msg);
                //return "{" + Message + "}";
                return Ok(ApiResponse<string>.Error(msg));

            }
            msg = "上传成功";
            Message = string.Format(Message, 0, msg);
            //return "{" + Message + "}";
            return Ok(ApiResponse<string>.Success(Message));
        }


        /// <summary>
        /// 获取标定参数配置列表
        /// </summary>
        /// <returns></returns>
        [HttpGet("BoltBD")]
        public IActionResult GetBoltDB()
        {
            List<UltrasonicBoltParam> data = new();
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                data = ctx.UltrasonicBoltParams.ToList();
            }
            return Ok(data);
        }
        
        #endregion
    }
}