﻿using CMSFramework.BusinessEntity;

namespace WTCMSLive.WebSite.Models
{
    public class AlarmDefinitonUIModel : AlarmDefinition
    {
        private List<string> mylist;
        public List<string> AlarmThresholdList
        {
            get { return mylist; }
            set { mylist = value; }
        }

        public string WorkConParameterName
        {
            get;
            set;
        }
        /// <summary>
        /// 注意
        /// </summary>
        public double? WarnValue
        {
            get;
            set;
        }
        /// <summary>
        /// 危险
        /// </summary>
        public double? AlarmValue
        {
            get;
            set;
        }

        public double? ReverseWarnValue
        {
            get;
            set;
        }
        /// <summary>
        /// 危险
        /// </summary>
        public double? ReverseAlarmValue
        {
            get;
            set;
        }

        public string EigenValueName { get; set; }

        public string WorkConditionParamTypeCode { get; set; }
        public string MeasLocationName { get; set; }
    }

}
