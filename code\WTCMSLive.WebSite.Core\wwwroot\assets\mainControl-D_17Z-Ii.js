import{u as V,W as q}from"./table-C_s53ALS.js";import{O as z}from"./index-DyCH3qIX.js";import{W as K}from"./index-R80zldKD.js";import{r as l,u as j,y as G,j as B,w as $,h as H,f as C,d as w,o as h,i as J,b as E,s as Q,g as X,c as Y,m as n}from"./index-D9CxWmlM.js";import{u as Z}from"./configMainControl-DV_EqvnM.js";import{S as ee,c as te,a as ae,t as oe}from"./tools-DZBuE28U.js";import{B as le}from"./index-BGEB0Rhf.js";import{M as se}from"./index-DhZUqjZm.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";/* empty css                                                              */const ne={key:2},S=320,Me={__name:"mainControl",setup(ie){const R=V(),c=Z(),y=(t={isform:!1})=>[{title:"设备名称",dataIndex:"windTurbineID",inputType:"select",selectOptions:[],formItemWidth:S,isrequired:!0,...t&&t.isform?{}:{customRender:({record:e})=>e&&e.windTurbineName?e.windTurbineName:""}},{title:"主控IP",dataIndex:"mcsip",columnOperate:{type:"ip"},formItemWidth:S,validateRules:te({type:"ip",title:"主控IP",required:!0})},{title:"端口号",dataIndex:"mcsPort",formItemWidth:S,isrequired:!0}],T=l(!1),D=j(),u=l(""),o=l(""),x=l(),_=l(!1),k=l({}),d=l([]),i=l([]),m=l(D.params.id),g=l([]),p=G({tableColumns:y(),deviceList:[]}),A=B(()=>o.value==="batchAdd"||o.value==="batchEdit"?"1200px":"600px"),O=B(()=>o.value==="batchEdit"?["windTurbineID"]:["windTurbineID"]),M=async t=>{if(_.value=!0,b(),m.value){let e=await R.fetchDevTreedDevicelist({windParkID:m.value});p.deviceList=ae(e,{label:"windTurbineName",value:"windTurbineID"})}_.value=!1},b=async t=>{if(m.value){const e=await c.fetchMCSGetMCSInfoList({windParkID:m.value});d.value=e}};$(()=>D.params.id,t=>{m.value=t,M()}),H(()=>{D.params.id&&M()});const I=()=>{T.value=!0},r=()=>{T.value=!1,i.value=[],k.value={},o.value="",u.value="",g.value=[]},L=t=>{const{title:e,operateType:a,tableKey:s}=t;o.value=a,u.value="批量增加主控信息";let v=y({isform:!0});v[0].selectOptions=p.deviceList,i.value=[...v],I()},N=t=>{o.value="batchEdit",u.value="批量修改主控信息";let e=y({isform:!0});e[0].selectOptions=p.deviceList,e[0].disabled=!0,i.value=[...e],g.value=d.value.filter(a=>t.includes(a.windTurbineID)),I()},P=async(t={})=>{const{selectedkeys:e}=t,a=await c.fetchBatchDeleteMCS(e);a&&a.code===1?(b(),r(),n.success("删除成功")):n.error("删除失败:"+a.msg)},F=t=>{const{rowData:e,title:a,operateType:s,tableKey:v}=t;o.value=s,k.value=e,u.value="编辑主控信息";let f=y({isform:!0});f[0].selectOptions=p.deviceList,i.value=[...f],I()},W=async t=>{const e=await c.fetchBatchEditMCS([t]);e&&e.code===1?(b(),r(),n.success("提交成功")):n.error("提交失败:"+e.msg)},U=async t=>{let e=oe(t);if(o.value==="batchEdit"){const s=await c.fetchBatchEditMCS(e);s&&s.code===1?(b(),r(),n.success("提交成功")):n.error("提交失败:"+s.msg);return}const a=await c.fetchBatchAddMCS(e);a&&a.code===1?(b(),r(),n.success("提交成功")):n.error("提交失败:"+a.msg)};return(t,e)=>{const a=le,s=se,v=ee;return h(),C(v,{spinning:_.value,size:"large"},{default:w(()=>[J("div",null,[E(q,{ref:"table",noBatchApply:!0,size:"default","table-key":"0","table-title":"主控信息列表","table-columns":p.tableColumns,"table-operate":["edit","delete","batchDelete","batchAdd"],"record-key":"windTurbineID","table-datas":d.value,onAddRow:L,onDeleteRow:P,onEditRow:F},{rightButtons:w(({selectedRowKeys:f})=>[d.value&&d.value.length?(h(),C(a,{key:0,type:"primary",onClick:re=>N(f),disabled:!f.length},{default:w(()=>e[0]||(e[0]=[X(" 批量修改 ",-1)])),_:2,__:[0]},1032,["onClick","disabled"])):Q("",!0)]),_:1},8,["table-columns","table-datas"]),E(s,{maskClosable:!1,width:A.value,open:T.value,title:u.value,footer:"",onCancel:r},{default:w(()=>[o.value==="add"||o.value==="edit"?(h(),C(z,{key:0,titleCol:i.value,form:x.value,initFormData:k.value,onSubmit:W},null,8,["titleCol","form","initFormData"])):o.value==="batchAdd"||o.value=="batchEdit"?(h(),C(K,{key:1,size:"default","table-key":"0","table-columns":i.value,"table-operate":o.value=="batchEdit"?["copyUp","noAdd"]:["copyUp","delete"],"table-datas":g.value,"noCopyUp-keys":O.value,onSubmit:U,onCancel:r},null,8,["table-columns","table-operate","table-datas","noCopyUp-keys"])):(h(),Y("div",ne))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}};export{Me as default};
