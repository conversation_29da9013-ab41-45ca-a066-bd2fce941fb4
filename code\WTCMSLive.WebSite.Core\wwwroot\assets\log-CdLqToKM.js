import{O as D}from"./index-DyCH3qIX.js";import{W as T}from"./table-C_s53ALS.js";import{z as o,r as l,y as v,w as M,f as p,d as _,A as F,u as x,o as u,b as C,aP as S}from"./index-D9CxWmlM.js";import{u as I}from"./serverManager-8fz38JT3.js";import{S as R}from"./tools-DZBuE28U.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./index-DMbgFMzZ.js";import"./shallowequal-vFdacwF3.js";import"./index-BS38043a.js";import"./ActionButton-BRQ4acFZ.js";import"./index-RBozNpIx.js";const J={__name:"log",setup(k){const m=I(),d=x(),f=()=>[{title:"用户名",dataIndex:"operator"},{title:"内容",dataIndex:"logContent"},{title:"操作",dataIndex:"logTitle"},{title:"时间",dataIndex:"eventTime",headerOperations:{sorter:!0,date:!0},customRender:({record:t})=>t.eventTime?F("span",{},o(t.eventTime).format("YYYY-MM-DD HH:mm:ss")):""}];S("noHeaderBorder",!0);const g=[{title:"选择时间",dataIndex:"timeRange",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0,isrequired:!0},{title:"日志筛选",dataIndex:"logType",inputType:"select",formItemWidth:"200",selectOptions:[]}];let c=[o().subtract(3,"month"),o()];const r=l({timeRange:c}),n=l(!1),i=l(!1),a=v({formlist:g,tableDatas:[],tableColumns:f()}),s=async t=>{n.value=!0;let e={beginTime:o(t.timeRange[0]).format("YYYY-MM-DD"),endTime:o(t.timeRange[1]).format("YYYY-MM-DD")};t.logType&&(e={...e,logType:t.logType}),a.tableDatas=await m.fetchHomeSystemLog(e),n.value=!1},y=async()=>{let t=await m.fetchGetSystemLogType();if(t&&t.length){let e=[...a.formlist];e[1].selectOptions=[...t],a.formlist=e}};M(()=>d.params.id,()=>{y(),s(r.value)},{immediate:!0});const Y=async t=>{s(t)},h=()=>{r.value={timeRange:c},s(r.value),i.value=!i.value};return(t,e)=>{const b=R;return u(),p(b,{spinning:n.value,size:"large"},{default:_(()=>[(u(),p(D,{key:i.value,titleCol:a.formlist,initFormData:r.value,formlayout:"inline",actions:["cancel"],onCancelForm:h,onSubmit:Y},null,8,["titleCol","initFormData"])),C(T,{"table-key":"0","table-columns":a.tableColumns,noheader:!0,noBatchApply:!0,"record-key":"guid","table-datas":a.tableDatas},null,8,["table-columns","table-datas"])]),_:1},8,["spinning"])}}};export{J as default};
