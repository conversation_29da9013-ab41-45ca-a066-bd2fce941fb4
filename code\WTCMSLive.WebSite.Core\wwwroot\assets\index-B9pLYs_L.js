import{_ as v,b as w,aN as U,aO as pe,ej as ve,a2 as ne,y as q,r as ge,w as C,dy as me,h as le,ac as ie,a3 as be,a8 as Se,j as P,ad as z,a6 as X,au as ye,aC as Me,af as $e,X as Te,Y as we,av as xe,$ as k,d$ as He}from"./index-ClUwy-U2.js";import{R as Re,v as I}from"./styleChecker-D2z1GQZd.js";import{w as R}from"./index-ByAZPsB5.js";function te(){const e=t=>{e.current=t};return e}const V=(e,t)=>{let{height:o,offset:l,prefixCls:s,onInnerResize:i}=e,{slots:d}=t;var a;let f={},r={display:"flex",flexDirection:"column"};return l!==void 0&&(f={height:`${o}px`,position:"relative",overflow:"hidden"},r=v(v({},r),{transform:`translateY(${l}px)`,position:"absolute",left:0,right:0,top:0})),w("div",{style:f},[w(Re,{onResize:p=>{let{offsetHeight:h}=p;h&&i&&i()}},{default:()=>[w("div",{style:r,class:U({[`${s}-holder-inner`]:s})},[(a=d.default)===null||a===void 0?void 0:a.call(d)])]})])};V.displayName="Filter";V.inheritAttrs=!1;V.props={prefixCls:String,height:Number,offset:Number,onInnerResize:Function};const re=(e,t)=>{let{setRef:o}=e,{slots:l}=t;var s;const i=pe((s=l.default)===null||s===void 0?void 0:s.call(l));return i&&i.length?ve(i[0],{ref:o}):i};re.props={setRef:{type:Function,default:()=>{}}};const Ee=20;function oe(e){return"touches"in e?e.touches[0].pageY:e.pageY}const Ce=ne({compatConfig:{MODE:3},name:"ScrollBar",inheritAttrs:!1,props:{prefixCls:String,scrollTop:Number,scrollHeight:Number,height:Number,count:Number,onScroll:{type:Function},onStartMove:{type:Function},onStopMove:{type:Function}},setup(){return{moveRaf:null,scrollbarRef:te(),thumbRef:te(),visibleTimeout:null,state:q({dragging:!1,pageY:null,startTop:null,visible:!1})}},watch:{scrollTop:{handler(){this.delayHidden()},flush:"post"}},mounted(){var e,t;(e=this.scrollbarRef.current)===null||e===void 0||e.addEventListener("touchstart",this.onScrollbarTouchStart,I?{passive:!1}:!1),(t=this.thumbRef.current)===null||t===void 0||t.addEventListener("touchstart",this.onMouseDown,I?{passive:!1}:!1)},beforeUnmount(){this.removeEvents(),clearTimeout(this.visibleTimeout)},methods:{delayHidden(){clearTimeout(this.visibleTimeout),this.state.visible=!0,this.visibleTimeout=setTimeout(()=>{this.state.visible=!1},2e3)},onScrollbarTouchStart(e){e.preventDefault()},onContainerMouseDown(e){e.stopPropagation(),e.preventDefault()},patchEvents(){window.addEventListener("mousemove",this.onMouseMove),window.addEventListener("mouseup",this.onMouseUp),this.thumbRef.current.addEventListener("touchmove",this.onMouseMove,I?{passive:!1}:!1),this.thumbRef.current.addEventListener("touchend",this.onMouseUp)},removeEvents(){window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("mouseup",this.onMouseUp),this.scrollbarRef.current.removeEventListener("touchstart",this.onScrollbarTouchStart,I?{passive:!1}:!1),this.thumbRef.current&&(this.thumbRef.current.removeEventListener("touchstart",this.onMouseDown,I?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchmove",this.onMouseMove,I?{passive:!1}:!1),this.thumbRef.current.removeEventListener("touchend",this.onMouseUp)),R.cancel(this.moveRaf)},onMouseDown(e){const{onStartMove:t}=this.$props;v(this.state,{dragging:!0,pageY:oe(e),startTop:this.getTop()}),t(),this.patchEvents(),e.stopPropagation(),e.preventDefault()},onMouseMove(e){const{dragging:t,pageY:o,startTop:l}=this.state,{onScroll:s}=this.$props;if(R.cancel(this.moveRaf),t){const i=oe(e)-o,d=l+i,a=this.getEnableScrollRange(),f=this.getEnableHeightRange(),r=f?d/f:0,p=Math.ceil(r*a);this.moveRaf=R(()=>{s(p)})}},onMouseUp(){const{onStopMove:e}=this.$props;this.state.dragging=!1,e(),this.removeEvents()},getSpinHeight(){const{height:e,scrollHeight:t}=this.$props;let o=e/t*100;return o=Math.max(o,Ee),o=Math.min(o,e/2),Math.floor(o)},getEnableScrollRange(){const{scrollHeight:e,height:t}=this.$props;return e-t||0},getEnableHeightRange(){const{height:e}=this.$props,t=this.getSpinHeight();return e-t||0},getTop(){const{scrollTop:e}=this.$props,t=this.getEnableScrollRange(),o=this.getEnableHeightRange();return e===0||t===0?0:e/t*o},showScroll(){const{height:e,scrollHeight:t}=this.$props;return t>e}},render(){const{dragging:e,visible:t}=this.state,{prefixCls:o}=this.$props,l=this.getSpinHeight()+"px",s=this.getTop()+"px",i=this.showScroll(),d=i&&t;return w("div",{ref:this.scrollbarRef,class:U(`${o}-scrollbar`,{[`${o}-scrollbar-show`]:i}),style:{width:"8px",top:0,bottom:0,right:0,position:"absolute",display:d?void 0:"none"},onMousedown:this.onContainerMouseDown,onMousemove:this.delayHidden},[w("div",{ref:this.thumbRef,class:U(`${o}-scrollbar-thumb`,{[`${o}-scrollbar-thumb-moving`]:e}),style:{width:"100%",height:l,top:s,left:0,position:"absolute",background:"rgba(0, 0, 0, 0.5)",borderRadius:"99px",cursor:"pointer",userSelect:"none"},onMousedown:this.onMouseDown},null)])}});function Ie(e,t,o,l){const s=new Map,i=new Map,d=ge(Symbol("update"));C(e,()=>{d.value=Symbol("update")});let a;function f(){R.cancel(a)}function r(){f(),a=R(()=>{s.forEach((h,c)=>{if(h&&h.offsetParent){const{offsetHeight:g}=h;i.get(c)!==g&&(d.value=Symbol("update"),i.set(c,h.offsetHeight))}})})}function p(h,c){const g=t(h);s.get(g),c?(s.set(g,c.$el||c),r()):s.delete(g)}return me(()=>{f()}),[p,r,i,d]}function Le(e,t,o,l,s,i,d,a){let f;return r=>{if(r==null){a();return}R.cancel(f);const p=t.value,h=l.itemHeight;if(typeof r=="number")d(r);else if(r&&typeof r=="object"){let c;const{align:g}=r;"index"in r?{index:c}=r:c=p.findIndex($=>s($)===r.key);const{offset:b=0}=r,O=($,L)=>{if($<0||!e.value)return;const D=e.value.clientHeight;let S=!1,M=L;if(D){const F=L||g;let N=0,x=0,H=0;const Y=Math.min(p.length,c);for(let y=0;y<=Y;y+=1){const j=s(p[y]);x=N;const _=o.get(j);H=x+(_===void 0?h:_),N=H,y===c&&_===void 0&&(S=!0)}const B=e.value.scrollTop;let T=null;switch(F){case"top":T=x-b;break;case"bottom":T=H-D+b;break;default:{const y=B+D;x<B?M="top":H>y&&(M="bottom")}}T!==null&&T!==B&&d(T)}f=R(()=>{S&&i(),O($-1,M)},2)};O(5)}}}const De=typeof navigator=="object"&&/Firefox/i.test(navigator.userAgent),se=(e,t)=>{let o=!1,l=null;function s(){clearTimeout(l),o=!0,l=setTimeout(()=>{o=!1},50)}return function(i){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const a=i<0&&e.value||i>0&&t.value;return d&&a?(clearTimeout(l),o=!1):(!a||o)&&s(),!o&&a}};function Fe(e,t,o,l){let s=0,i=null,d=null,a=!1;const f=se(t,o);function r(h){if(!e.value)return;R.cancel(i);const{deltaY:c}=h;s+=c,d=c,!f(c)&&(De||h.preventDefault(),i=R(()=>{l(s*(a?10:1)),s=0}))}function p(h){e.value&&(a=h.detail===d)}return[r,p]}const Be=14/15;function Pe(e,t,o){let l=!1,s=0,i=null,d=null;const a=()=>{i&&(i.removeEventListener("touchmove",f),i.removeEventListener("touchend",r))},f=c=>{if(l){const g=Math.ceil(c.touches[0].pageY);let b=s-g;s=g,o(b)&&c.preventDefault(),clearInterval(d),d=setInterval(()=>{b*=Be,(!o(b,!0)||Math.abs(b)<=.1)&&clearInterval(d)},16)}},r=()=>{l=!1,a()},p=c=>{a(),c.touches.length===1&&!l&&(l=!0,s=Math.ceil(c.touches[0].pageY),i=c.target,i.addEventListener("touchmove",f,{passive:!1}),i.addEventListener("touchend",r))},h=()=>{};le(()=>{document.addEventListener("touchmove",h,{passive:!1}),C(e,c=>{t.value.removeEventListener("touchstart",p),a(),clearInterval(d),c&&t.value.addEventListener("touchstart",p,{passive:!1})},{immediate:!0})}),ie(()=>{document.removeEventListener("touchmove",h)})}var Oe=function(e,t){var o={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&t.indexOf(l)<0&&(o[l]=e[l]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,l=Object.getOwnPropertySymbols(e);s<l.length;s++)t.indexOf(l[s])<0&&Object.prototype.propertyIsEnumerable.call(e,l[s])&&(o[l[s]]=e[l[s]]);return o};const Ne=[],_e={overflowY:"auto",overflowAnchor:"none"};function ze(e,t,o,l,s,i){let{getKey:d}=i;return e.slice(t,o+1).map((a,f)=>{const r=t+f,p=s(a,r,{}),h=d(a);return w(re,{key:h,setRef:c=>l(a,c)},{default:()=>[p]})})}const Ge=ne({compatConfig:{MODE:3},name:"List",inheritAttrs:!1,props:{prefixCls:String,data:be.array,height:Number,itemHeight:Number,fullHeight:{type:Boolean,default:void 0},itemKey:{type:[String,Number,Function],required:!0},component:{type:[String,Object]},virtual:{type:Boolean,default:void 0},children:Function,onScroll:Function,onMousedown:Function,onMouseenter:Function,onVisibleChange:Function},setup(e,t){let{expose:o}=t;const l=P(()=>{const{height:n,itemHeight:u,virtual:m}=e;return!!(m!==!1&&n&&u)}),s=P(()=>{const{height:n,itemHeight:u,data:m}=e;return l.value&&m&&u*m.length>n}),i=q({scrollTop:0,scrollMoving:!1}),d=P(()=>e.data||Ne),a=z([]);C(d,()=>{a.value=$e(d.value).slice()},{immediate:!0});const f=z(n=>{});C(()=>e.itemKey,n=>{typeof n=="function"?f.value=n:f.value=u=>u==null?void 0:u[n]},{immediate:!0});const r=z(),p=z(),h=z(),c=n=>f.value(n),g={getKey:c};function b(n){let u;typeof n=="function"?u=n(i.scrollTop):u=n;const m=N(u);r.value&&(r.value.scrollTop=m),i.scrollTop=m}const[O,$,L,D]=Ie(a,c),S=q({scrollHeight:void 0,start:0,end:0,offset:void 0}),M=z(0);le(()=>{X(()=>{var n;M.value=((n=p.value)===null||n===void 0?void 0:n.offsetHeight)||0})}),ye(()=>{X(()=>{var n;M.value=((n=p.value)===null||n===void 0?void 0:n.offsetHeight)||0})}),C([l,a],()=>{l.value||v(S,{scrollHeight:void 0,start:0,end:a.value.length-1,offset:void 0})},{immediate:!0}),C([l,a,M,s],()=>{l.value&&!s.value&&v(S,{scrollHeight:M.value,start:0,end:a.value.length-1,offset:void 0}),r.value&&(i.scrollTop=r.value.scrollTop)},{immediate:!0}),C([s,l,()=>i.scrollTop,a,D,()=>e.height,M],()=>{if(!l.value||!s.value)return;let n=0,u,m,E;const W=a.value.length,ue=a.value,J=i.scrollTop,{itemHeight:Q,height:ee}=e,de=J+ee;for(let A=0;A<W;A+=1){const he=ue[A],fe=c(he);let K=L.get(fe);K===void 0&&(K=Q);const G=n+K;u===void 0&&G>=J&&(u=A,m=n),E===void 0&&G>de&&(E=A),n=G}u===void 0&&(u=0,m=0,E=Math.ceil(ee/Q)),E===void 0&&(E=W-1),E=Math.min(E+1,W),v(S,{scrollHeight:n,start:u,end:E,offset:m})},{immediate:!0});const F=P(()=>S.scrollHeight-e.height);function N(n){let u=n;return Number.isNaN(F.value)||(u=Math.min(u,F.value)),u=Math.max(u,0),u}const x=P(()=>i.scrollTop<=0),H=P(()=>i.scrollTop>=F.value),Y=se(x,H);function B(n){b(n)}function T(n){var u;const{scrollTop:m}=n.currentTarget;m!==i.scrollTop&&b(m),(u=e.onScroll)===null||u===void 0||u.call(e,n)}const[y,j]=Fe(l,x,H,n=>{b(u=>u+n)});Pe(l,r,(n,u)=>Y(n,u)?!1:(y({preventDefault(){},deltaY:n}),!0));function _(n){l.value&&n.preventDefault()}const Z=()=>{r.value&&(r.value.removeEventListener("wheel",y,I?{passive:!1}:!1),r.value.removeEventListener("DOMMouseScroll",j),r.value.removeEventListener("MozMousePixelScroll",_))};Me(()=>{X(()=>{r.value&&(Z(),r.value.addEventListener("wheel",y,I?{passive:!1}:!1),r.value.addEventListener("DOMMouseScroll",j),r.value.addEventListener("MozMousePixelScroll",_))})}),ie(()=>{Z()});const ae=Le(r,a,L,e,c,$,b,()=>{var n;(n=h.value)===null||n===void 0||n.delayHidden()});o({scrollTo:ae});const ce=P(()=>{let n=null;return e.height&&(n=v({[e.fullHeight?"height":"maxHeight"]:e.height+"px"},_e),l.value&&(n.overflowY="hidden",i.scrollMoving&&(n.pointerEvents="none"))),n});return C([()=>S.start,()=>S.end,a],()=>{if(e.onVisibleChange){const n=a.value.slice(S.start,S.end+1);e.onVisibleChange(n,a.value)}},{flush:"post"}),{state:i,mergedData:a,componentStyle:ce,onFallbackScroll:T,onScrollBar:B,componentRef:r,useVirtual:l,calRes:S,collectHeight:$,setInstance:O,sharedConfig:g,scrollBarRef:h,fillerInnerRef:p,delayHideScrollBar:()=>{var n;(n=h.value)===null||n===void 0||n.delayHidden()}}},render(){const e=v(v({},this.$props),this.$attrs),{prefixCls:t="rc-virtual-list",height:o,itemHeight:l,fullHeight:s,data:i,itemKey:d,virtual:a,component:f="div",onScroll:r,children:p=this.$slots.default,style:h,class:c}=e,g=Oe(e,["prefixCls","height","itemHeight","fullHeight","data","itemKey","virtual","component","onScroll","children","style","class"]),b=U(t,c),{scrollTop:O}=this.state,{scrollHeight:$,offset:L,start:D,end:S}=this.calRes,{componentStyle:M,onFallbackScroll:F,onScrollBar:N,useVirtual:x,collectHeight:H,sharedConfig:Y,setInstance:B,mergedData:T,delayHideScrollBar:y}=this;return w("div",Se({style:v(v({},h),{position:"relative"}),class:b},g),[w(f,{class:`${t}-holder`,style:M,ref:"componentRef",onScroll:F,onMouseenter:y},{default:()=>[w(V,{prefixCls:t,height:$,offset:L,onInnerResize:H,ref:"fillerInnerRef"},{default:()=>ze(T,D,S,B,p,Y)})]}),x&&w(Ce,{ref:"scrollBarRef",prefixCls:t,scrollTop:O,height:o,scrollHeight:$,count:T.length,onScroll:N,onStartMove:()=>{this.state.scrollMoving=!0},onStopMove:()=>{this.state.scrollMoving=!1}},null)])}}),Ye=new xe("antCheckboxEffect",{"0%":{transform:"scale(1)",opacity:.5},"100%":{transform:"scale(1.6)",opacity:0}}),Ae=e=>{const{checkboxCls:t}=e,o=`${t}-wrapper`;return[{[`${t}-group`]:v(v({},k(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[o]:v(v({},k(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${o}`]:{marginInlineStart:0},[`&${o}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:v(v({},k(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:v({},He(e))},[`${t}-inner`]:{boxSizing:"border-box",position:"relative",top:0,insetInlineStart:0,display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${e.lineWidth}px ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"21.5%",display:"table",width:e.checkboxSize/14*5,height:e.checkboxSize/14*8,border:`${e.lineWidthBold}px solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[t]:{"&-indeterminate":{[`${t}-inner`]:{"&:after":{top:"50%",insetInlineStart:"50%",width:e.fontSizeLG/2,height:e.fontSizeLG/2,backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}}}}},{[`${o}:hover ${t}:after`]:{visibility:"visible"},[`
        ${o}:not(${o}-disabled),
        ${t}:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${o}:not(${o}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}},"&:after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderRadius:e.borderRadiusSM,visibility:"hidden",border:`${e.lineWidthBold}px solid ${e.colorPrimary}`,animationName:Ye,animationDuration:e.motionDurationSlow,animationTimingFunction:"ease-in-out",animationFillMode:"backwards",content:'""',transition:`all ${e.motionDurationSlow}`}},[`
        ${o}-checked:not(${o}-disabled),
        ${t}-checked:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}:after`]:{borderColor:e.colorPrimaryHover}}},{[`${o}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function je(e,t){const o=we(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[Ae(o)]}const Xe=Te("Checkbox",(e,t)=>{let{prefixCls:o}=t;return[je(o,e)]});export{Ge as L,te as c,je as g,Xe as u};
