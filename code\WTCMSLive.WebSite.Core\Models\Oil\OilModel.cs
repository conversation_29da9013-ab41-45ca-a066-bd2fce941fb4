﻿namespace WTCMSLive.WebSite.Models
{
    /// <summary>
    /// 绘制趋势图数据类
    /// 表示一个图表
    /// </summary>
    public class OilAnalysisData
    {
        public string textName { get; set; }
        public string[] ParamTypeName { get; set; }
        public string[] timeValueData { get; set; }//
        public List<OilAnalysisLine> LineList { get; set; }
    }
    
    /// <summary>
    /// 趋势图中的一条线段
    /// </summary>
    public class OilAnalysisLine
    {
        public string name { get; set; }
        public string Unit { get; set; }
        public string type { get; set; }
        public int Waring { get; set; }
        public int Alarm { get; set; }

        public double?[] data { get; set; }
    }

  

    public class OilDataModel
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }
        /// <summary>
        /// DAU id
        /// </summary>
        public string DAUID { get; set; }
        /// <summary>
        /// 采集时间
        /// </summary>
        public DateTime OilAcquisitionTime { get; set; }
        /// <summary>
        /// 磨粒累加数据
        /// </summary>
        public int WearParticleNumTotal { get; set; }
        /// <summary>
        /// 磨粒原始采集数据
        /// </summary>
        public int WearParticleNumInterval { get; set; }


    }
}