﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    public static class RealTimeDataManage
    {
        public static List<WorkingConditionData> GetAllWorkingCondDataListByTurbineID(string _turID)
        {
            List<MeasEvent_EigenValue> measList = null;
             List<WorkingConditionData> wkConDataList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                measList = ctx.MeasEventEVs.Where(item => item.WindTurbineID == _turID).ToList();
                wkConDataList = ctx.WorkingConditionDatas.Where(item=>item.WindTurbineID==_turID).ToList();
            }
            var list = measList.GroupBy(x => new { x.MeasDefinitionID }).Select(group => new { MeasDefID = group.Key });
            foreach (var data in list)
            {
                DateTime time = measList.Where(i => i.MeasDefinitionID == data.MeasDefID.MeasDefinitionID).OrderByDescending(item => item.AcquisitionTime).First().AcquisitionTime;
                wkConDataList.RemoveAll(item => item.AcquisitionTime != time && item.MeasDefinitionID == data.MeasDefID.MeasDefinitionID);
            }
            return wkConDataList;
        }

        public static List<MeasEvent_EigenValue> GetEVDataRTMeasEventByTurbineID(string _turID)
        {
            List<MeasEvent_EigenValue> measList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                measList = ctx.MeasEventEVs.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return measList;
        }

        /// <summary>
        /// 获取机组的实时输出功率
        /// </summary>
        /// <param name="_turId"></param>
        /// <returns></returns>
        public static WorkingConditionData GetRTOutPowerDataByTurID(string _turId)
        {
            List<WorkingConditionData> wkConDataList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                wkConDataList = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == _turId).ToList();
            }
            WorkingConditionData PowerWorkCondition = wkConDataList.FirstOrDefault(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Power);
            return PowerWorkCondition;
        }

        public static void ResetOilWordCondent(string _turId)
        {
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                var wkConDataList = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == _turId && item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris).ToList();
                if (wkConDataList.Count > 0)
                {
                    ctx.WorkingConditionDatas.RemoveRange(wkConDataList);
                    ctx.SaveChanges();
                }
            }
            //复位后，给工况趋势表中新增一条为0 的数据
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                var wkConData= ctx.WorkingConditionDatas.FirstOrDefault(item => item.WindTurbineID == _turId && item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Oil_Debris);
                if (wkConData != null)
                {
                    WorkingConditionData workCondition = new WorkingConditionData()
                    {
                        WindTurbineID = wkConData.WindTurbineID,
                        AcquisitionTime = DateTime.Now,
                        Data_Qual_Type = wkConData.Data_Qual_Type,
                        MeasDefinitionID = wkConData.MeasDefinitionID,
                        MeasLocationID = wkConData.MeasLocationID,
                        Param_Type_Code = wkConData.Param_Type_Code,
                        Param_Value = 0,
                        MaxValue = 0,
                        MinValue = 0
                    };
                    ctx.WorkingConditionDatas.Add(workCondition);
                    ctx.SaveChanges();
                }
            }
        }


        /// <summary>
        /// 获取实时CMS转速,根据机组ID
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static double? GetRotSpdRTDAUByTurID(string _turID)
        {
            List<WorkingConditionData> wkConDataList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                wkConDataList = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == _turID).ToList();
            }
            WorkingConditionData PowerWorkCondition = wkConDataList.FirstOrDefault(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_RotSpeed);
            if (PowerWorkCondition == null)
                return null;
            return PowerWorkCondition.Param_Value;
        }

        /// <summary>
        /// 获取下特征值数据列表 振动
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static List<EigenValueData_Vib> GetRTEVDataListByTurID(string _turID)
        {
            List<EigenValueData_Vib> EigenVibList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                EigenVibList = ctx.EVData_Vibs.Where(item => item.WindTurbineID == _turID).OrderByDescending(obj=>obj.AcquisitionTime).ToList();
            }
            return EigenVibList;
        }
        /// <summary>
        /// 获取下特征值数据列表 晃动
        /// </summary>
        /// <param name="_turID"></param>
        /// <returns></returns>
        public static List<EigenValueData_SVM> GetSVMRTEVDataListByTurID(string _turID)
        {
            List<EigenValueData_SVM> EigenVibList = null;
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                EigenVibList = ctx.EVData_SVMs.Where(item => item.WindTurbineID == _turID).ToList();
            }
            return EigenVibList;
        }
    }
}
