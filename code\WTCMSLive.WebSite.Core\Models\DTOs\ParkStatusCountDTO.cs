namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// 风场状态统计DTO
    /// </summary>
    public class ParkStatusCountDTO
    {
        /// <summary>
        /// 风场总数
        /// </summary>
        public int TotalParkCount { get; set; }

        /// <summary>
        /// 机组总数统计
        /// </summary>
        public TurbineStatusSummaryDTO TotalTurbineStatus { get; set; }

        /// <summary>
        /// 各风场详细统计列表
        /// </summary>
        public List<ParkDetailStatusDTO> ParkDetailList { get; set; } = new List<ParkDetailStatusDTO>();

        /// <summary>
        /// 统计时间
        /// </summary>
        public DateTime StatisticsTime { get; set; }
    }

    /// <summary>
    /// 机组状态汇总DTO
    /// </summary>
    public class TurbineStatusSummaryDTO
    {
        /// <summary>
        /// 机组总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 正常状态数量（状态3）
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 注意状态数量（状态5）
        /// </summary>
        public int WarningCount { get; set; }

        /// <summary>
        /// 危险状态数量（状态6）
        /// </summary>
        public int DangerCount { get; set; }

        /// <summary>
        /// 其他状态数量
        /// </summary>
        public int OtherCount { get; set; }

        /// <summary>
        /// 正常状态百分比
        /// </summary>
        public double NormalPercentage { get; set; }

        /// <summary>
        /// 注意状态百分比
        /// </summary>
        public double WarningPercentage { get; set; }

        /// <summary>
        /// 危险状态百分比
        /// </summary>
        public double DangerPercentage { get; set; }

        /// <summary>
        /// 其他状态百分比
        /// </summary>
        public double OtherPercentage { get; set; }
    }

    /// <summary>
    /// 风场详细状态DTO
    /// </summary>
    public class ParkDetailStatusDTO
    {
        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// 风场名称
        /// </summary>
        public string WindParkName { get; set; }

        /// <summary>
        /// 风场编码
        /// </summary>
        public string WindParkCode { get; set; }

        /// <summary>
        /// 该风场下机组状态统计
        /// </summary>
        public TurbineStatusSummaryDTO TurbineStatus { get; set; }

        /// <summary>
        /// 机组详细列表
        /// </summary>
        public List<TurbineStatusDetailDTO> TurbineDetailList { get; set; } = new List<TurbineStatusDetailDTO>();
    }

    /// <summary>
    /// 机组状态详细DTO
    /// </summary>
    public class TurbineStatusDetailDTO
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 机组名称
        /// </summary>
        public string WindTurbineName { get; set; }

        /// <summary>
        /// 机组状态（3-正常，5-注意，6-危险）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string StatusDescription { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime? StatusUpdateTime { get; set; }
    }
}
