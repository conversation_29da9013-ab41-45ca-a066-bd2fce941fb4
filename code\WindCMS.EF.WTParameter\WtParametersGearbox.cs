﻿using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace WindCMS.EF.WTParameter
{
    /// <summary>
    /// 存储与齿轮箱每一级相关的详细参数
    /// </summary>
    public partial class WtParametersGearbox
    {
        /// <summary>
        /// 齿轮箱级别参数的唯一标识符
        /// </summary>
        public int StageId { get; set; }
        /// <summary>
        /// 齿轮箱参数表的外键
        /// </summary>
        public int NacelleId { get; set; }
        /// <summary>
        /// 级别编号（1、2、3）
        /// </summary>
        public sbyte StageNumber { get; set; }
        /// <summary>
        /// 级别类型（行星级或平行级）
        /// </summary>
        public string? StageType { get; set; }
        /// <summary>
        /// 行星轮个数（仅行星级适用）
        /// </summary>
        public int? PlanetCount { get; set; }
        /// <summary>
        /// 太阳轮齿数（仅行星级适用）
        /// </summary>
        public int? SunTeethCount { get; set; }
        /// <summary>
        /// 行星轮齿数（仅行星级适用）
        /// </summary>
        public int? PlanetTeethCount { get; set; }
        /// <summary>
        /// 内齿圈齿数（仅行星级适用）
        /// </summary>
        public int? RingTeethCount { get; set; }
        /// <summary>
        /// 大齿轮齿数（仅平行级适用）
        /// </summary>
        public int? LargeTeethCount { get; set; }
        /// <summary>
        /// 小齿轮齿数（仅平行级适用）
        /// </summary>
        public int? SmallTeethCount { get; set; }
        /// <summary>
        /// 轴承ID
        /// </summary>
        public string? BearingId { get; set; }
        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        [JsonIgnore]
        public virtual WtNacelleParameter? Nacelle { get; set; }

        public List<WtParametersBearing>? BearingList { get; set; }
    }
}
