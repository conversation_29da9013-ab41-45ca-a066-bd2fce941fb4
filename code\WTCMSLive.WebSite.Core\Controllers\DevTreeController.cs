using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using System.Data.Entity;
using AppFramework.IDUtility;
using Microsoft.AspNetCore.Mvc;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;
using WTCMSLive.WebSite.Core.Attributes;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using CMSFramework.DevTreeEntities;
using OfficeOpenXml.Sorting;
using Microsoft.AspNetCore.Authorization;


namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DevTreeController : ControllerBase
    {
        #region 集团公司
        /// <summary>
        /// 获取集团公司
        /// </summary>
        /// <returns></returns>
        [HttpGet("GroupCompany")]
        public IActionResult GetGroupCompanyDic()
        {
            return new JsonResult(AppFramework.IDUtility.CodeProvide.GetGroupCompanyDic());
        }
        #endregion

        #region 风场级别

        /// <summary>
        /// 通过风场ID获取风场信息
        /// </summary>
        /// <param name="windParkID"></param>
        /// <returns></returns>
        [HttpGet("park")]
        public IActionResult GetPark(string windParkID)
        {
            return Ok(DevTreeManagement.GetWindPark(windParkID));
        }
        /// <summary>
        /// 添加编辑风场信息
        /// </summary>
        /// <param name="dto">风场信息DTO</param>
        /// <returns></returns>
        [HttpPost("EditWindparkInformation")]
        public IActionResult EditWindparkInformation([FromBody] WindParkDTO dto)
        {
            WindPark _wpInformation = null;
            try
            {
                // 新增
                if (string.IsNullOrEmpty(dto.WindParkID))
                {
                    List<WindPark> windParkList = DevTreeManagement.GetWindParkList();
                    dto.WindParkCode = int.Parse(dto.WindParkCode).ToString("000");
                    var list = windParkList.Find(item => item.WindParkName == dto.WindParkName);
                    if (list != null)
                    {   //风场名称或编号重复，添加失败
                        return Ok(ApiResponse<string>.Error("风场名称重复"));
                    }
                    else
                    {
                        list = windParkList.Find(item => item.WindParkCode == dto.WindParkCode);
                        if (list != null)
                        {
                            return Ok(ApiResponse<string>.Error("风场编号重复"));
                        }
                    }
                    _wpInformation = new WindPark();
                    _wpInformation.WindParkName = dto.WindParkName;
                    //windParkCode必须是三位数字
                    _wpInformation.WindParkCode = dto.WindParkCode;
                    _wpInformation.OperationalDate = DateTime.Parse(dto.OperationalDate);
                    _wpInformation.ContactMan = dto.ContactMan;
                    _wpInformation.ContactTel = dto.ContactTel;
                    _wpInformation.Address = dto.Address;
                    _wpInformation.PostCode = dto.PostCode;
                    //获取所属集团公司key
                    _wpInformation.WindParkID = IDProvide.GetWindParkId(dto.WindParkGroupName, _wpInformation.WindParkCode);
                    _wpInformation.Description = dto.Description;
                    _wpInformation.Country = dto.Country;
                    _wpInformation.Area = dto.Area;
                    _wpInformation.location = dto.Location;

                    DevTreeManagement.AddWindPark(_wpInformation);
                }
                else
                {
                    // 修改风场信息
                    _wpInformation = DevTreeManagement.GetWindPark(dto.WindParkID);
                    _wpInformation.WindParkName = dto.WindParkName;
                    _wpInformation.OperationalDate = DateTime.Parse(dto.OperationalDate);
                    _wpInformation.ContactMan = dto.ContactMan;
                    _wpInformation.ContactTel = dto.ContactTel;
                    _wpInformation.Address = dto.Address;
                    _wpInformation.PostCode = dto.PostCode;
                    _wpInformation.Description = dto.Description;
                    _wpInformation.Country = dto.Country;
                    _wpInformation.Area = dto.Area;
                    _wpInformation.location = dto.Location;
                    DevTreeManagement.EditWindPark(_wpInformation);
                }
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = _wpInformation?.WindParkID?.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_风场({0})", dto.WindParkName, dto.WindParkName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("EditWindparkInformation_风场失败 ", ex);
                return Ok(ApiResponse<string>.Error("异常"));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 风场信息删除
        /// </summary>
        /// <param name="ID"></param>
        /// <returns></returns>
        [HttpGet("DeletewindPark")]
        public IActionResult DeletewindPark(string windParkID)
        {
            try
            {
                WindPark myWind = DevTreeManagement.GetWindPark(windParkID);
                if (myWind != null)
                {
                    if (myWind.WindTurbineList == null || myWind.WindTurbineList.Count == 0)
                    {
                        //风场下没有机组信息，可以删除
                        DevTreeManagement.DelDevWindParkByWindParkID(windParkID);
                    }
                    else
                    {

                        return Ok(ApiResponse<string>.Error("删除风场失败：风场下已有机组信息！"));
                    }
                }
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = windParkID;
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("删除_机组({0})", windParkID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeletewindPark]删除风场失败，风场ID：" + windParkID, ex);
                return Ok(ApiResponse<string>.Error("删除风场失败"));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 获取机组型号列表
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("modellist")]
        public IActionResult InitTurbineModelList()
        {
            List<WindTurbineModel> _listTurbineModel =
                    DevTreeManagement.GetWindTurbineModelList();
            return Ok(_listTurbineModel);
        }

        [HttpGet("GetModelStructureType")]
        public IActionResult GetModelStructureType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumTurModelStructureType value in Enum.GetValues(typeof(EnumTurModelStructureType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }

        /// <summary>
        /// 根据机型获取机型下的部件列表
        /// </summary>
        /// <param name="trubineModel"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetAllComPonentList")]
        public IActionResult GetComPonentListByModel()
        {

            return Ok(AppFramework.IDUtility.CodeProvide.GetComponentDic());
        }
        /// <summary>
        /// 批量添加机组型号信息
        /// </summary>
        /// <param name="models">机组型号列表</param>
        /// <returns></returns>
        ///
        [HttpPost("AddWindModel")]
        public IActionResult AddWindModel(List<WindModelDTO> models)
        {
            try
            {
                if (models == null || !models.Any())
                {
                    return Ok(ApiResponse<string>.Error("机组型号列表不能为空"));
                }

                // 获取现有的机组型号列表
                List<WindTurbineModel> _listTurbineModel = DevTreeManagement.GetWindTurbineModelList();

                // 第一步：验证所有机组型号，检查是否已存在
                var validationErrors = new List<string>();
                var validModels = new List<WindModelDTO>();

                foreach (var model in models)
                {
                    // 参数验证
                    if (string.IsNullOrEmpty(model.WindTurbineModelName))
                    {
                        validationErrors.Add("机组型号名称不能为空");
                        continue;
                    }

                    if (string.IsNullOrEmpty(model.Manufactory))
                    {
                        validationErrors.Add($"机组型号 {model.WindTurbineModelName} 的制造商不能为空");
                        continue;
                    }

                    // 检查是否已存在
                    if (_listTurbineModel.Find(item => item.TurbineModel == model.WindTurbineModelName) != null)
                    {
                        validationErrors.Add($"机组型号 {model.WindTurbineModelName} 已存在");
                        continue;
                    }

                    // 检查当前批次中是否有重复
                    if (validModels.Any(vm => vm.WindTurbineModelName == model.WindTurbineModelName))
                    {
                        validationErrors.Add($"批次中存在重复的机组型号 {model.WindTurbineModelName}");
                        continue;
                    }

                    validModels.Add(model);
                }

                // 如果有验证错误，返回错误信息
                if (validationErrors.Any())
                {
                    return Ok(ApiResponse<string>.Error(string.Join("; ", validationErrors)));
                }

                // 第二步：使用事务批量添加所有有效的机组型号
                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {
                    using (var transaction = ctx.Database.BeginTransaction())
                    {
                        try
                        {
                            foreach (var model in validModels)
                            {
                                // 创建机组型号实体
                                var turModel = new CMSFramework.BusinessEntity.WindTurbineModel
                                {
                                    TurbineModel = model.WindTurbineModelName,
                                    Manufactory = model.Manufactory,
                                    StructureType = (CMSFramework.BusinessEntity.EnumTurModelStructureType)model.StructureType,
                                    RatedPower = model.FactedPower,
                                    BladeNum = model.BladeNum,
                                    GridConnectedGeneratorSpeed = model.GridConnectedGeneratorSpeed,
                                    RatedGeneratorSpeed = model.RatedGeneratorSpeed,
                                    CreatedAt = DateTime.Now,
                                    UpdatedAt = DateTime.Now
                                };

                                ctx.DevWTurbineModels.Add(turModel);
                            }

                            // 保存所有更改
                            ctx.SaveChanges();

                            // 提交事务
                            transaction.Commit();

                            CMSFramework.Logger.Logger.LogInfoMessage($"[AddWindModel]批量添加机组型号成功，共添加 {validModels.Count} 个型号");
                            return Ok(ApiResponse<string>.Success("OK"));
                        }
                        catch (Exception ex)
                        {
                            // 回滚事务
                            transaction.Rollback();
                            CMSFramework.Logger.Logger.LogErrorMessage("[AddWindModel]批量添加机组型号失败", ex);
                            return Ok(ApiResponse<string>.Error($"批量添加失败: {ex.Message}"));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddWindModel]批量添加机组型号异常", ex);
                return Ok(ApiResponse<string>.Error($"操作异常: {ex.Message}"));
            }
        }

        /// <summary>
        /// 编辑机型
        /// </summary>
        /// <param name="WindTurbineModelName"></param>
        /// <param name="StructureType"></param>
        /// <param name="FactedPower"></param>
        /// <param name="BladeNum"></param>
        /// <param name="Manufactory"></param>
        /// <param name="RatedGeneratorSpeed"></param>
        /// <param name="GridConnectedGeneratorSpeed"></param>
        /// <returns></returns>
        /// 
        [HttpPost("UpdateTurbineModel")]
        public IActionResult UpdateTurbineModel(WindModelDTO model)
        {
            List<WindTurbineModel> _listTurbineModel = DevTreeManagement.GetWindTurbineModelList();

            WindTurbineModel existingModel = _listTurbineModel.Find(item => item.TurbineModel == model.WindTurbineModelName);
            if (existingModel != null)
            {
                // 更新机型信息
                existingModel.StructureType = (EnumTurModelStructureType)model.StructureType;
                existingModel.RatedPower = model.FactedPower;
                existingModel.BladeNum = model.BladeNum;
                existingModel.Manufactory = model.Manufactory;
                existingModel.RatedGeneratorSpeed = model.RatedGeneratorSpeed;
                existingModel.GridConnectedGeneratorSpeed = model.GridConnectedGeneratorSpeed;
                existingModel.UpdatedAt = DateTime.Now;

                // 调用更新方法
                DevTreeManagement.UpdateWindTurbineModel(existingModel);
            }
            else
            {

                return Ok(ApiResponse<string>.Error("未找到指定的机型"));
            }

            return Ok(ApiResponse<string>.Success("OK"));
        }

        #endregion

        #region 机组级别
        /// <summary>
        /// 通过风场id获取机组列表
        /// </summary>
        /// <param name="windParkID"></param>
        /// <returns></returns>
        [HttpGet("devicelist")]
        public ActionResult GetDeviceList(string windParkID)
        {
            List<WindTurbineDTO> tableModel = new List<WindTurbineDTO>();
            List<WindTurbine> windTurbineList = DevTreeManagement.GetTurbinesList(windParkID);
            List<MCS> mcsList = DAUMCS.GetMCSByWindParkId(windParkID);
            List<WindTurbineModel> TurbineModel = DevTreeManagement.GetWindTurbineModelList();
            List<Rows> rows = new List<Rows>();
            for (int i = 0; i < windTurbineList.Count; i++)
            {
                WindTurbine devWindTurbine = windTurbineList[i];


                var mcs = mcsList.Find(item => item.WindTurbineID == devWindTurbine.WindTurbineID);
                var model = TurbineModel.Find(item => item.TurbineModel == devWindTurbine.WindTurbineModel);
                tableModel.Add(new WindTurbineDTO()
                {
                    WindParkId = devWindTurbine.WindParkID,
                    WindTurbineID = devWindTurbine.WindTurbineID,
                    WindTurbineName = devWindTurbine.WindTurbineName,
                    WindTurbineModel = devWindTurbine.WindTurbineModel,
                    OperationalDate = devWindTurbine.OperationalDate.ToString("yyyy-MM-dd"),
                    MinWorkingRotSpeed = devWindTurbine.MinWorkingRotSpeed,
                    McsIP = mcs?.MCSIP,
                    WindTurbineCode = devWindTurbine.WindTurbineCode,
                    FactedPower = model?.RatedPower,
                    GridConnectedGeneratorSpeed = model?.GridConnectedGeneratorSpeed,
                    RatedGeneratorSpeed = model?.RatedGeneratorSpeed,
                    Location = devWindTurbine.Location,
                    ComponentName = devWindTurbine.TurComponentList.Select(t => t.ComponentName).ToList(),
                });
            }
            return Ok(tableModel.OrderBy(t => t.WindTurbineID));
        }

        /// <summary>
        /// 修改机组信息
        /// </summary>
        /// <param name="dtos">机组信息DTO列表</param>
        /// <returns></returns>
        [HttpPost("EditDevice")]
        public IActionResult EditDevtree([FromBody] List<WindTurbineDTO> dtos)
        {
            try
            {
                // 首先验证所有数据
                foreach (var dto in dtos)
                {
                    List<WindTurbine> turbineList = WTCMSLive.BusinessModel.DevTreeManagement.GetTurbinesListByWindParkId(dto.WindParkId);
                    if (turbineList != null)
                    {
                        var list = from m in turbineList
                                   where m.WindTurbineName == dto.WindTurbineName && m.WindTurbineID != dto.WindTurbineID
                                   select m;
                        if (list.Count() > 0)
                        {
                            return Ok(ApiResponse<string>.Error($"机组 {dto.WindTurbineName} 名称已存在"));
                        }
                    }
                }

                // 所有验证通过后，开始批量更新
                foreach (var dto in dtos)
                {
                    WindTurbine _devtree = DevTreeManagement.GetWindTurbine(dto.WindTurbineID);
                    _devtree.WindTurbineID = dto.WindTurbineID;
                    _devtree.WindTurbineName = dto.WindTurbineName;
                    _devtree.WindTurbineModel = dto.WindTurbineModel;
                    _devtree.OperationalDate = DateTime.Parse(dto.OperationalDate);
                    _devtree.MinWorkingRotSpeed = 0;
                    _devtree.Location = dto.Location;

                    DevTreeManagement.EditWindTurbine(_devtree);

                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = dto.WindTurbineID.ToString();
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription = string.Format("编辑_设备树({0})", dto.WindTurbineID);
                    LogManagement.UserlogWrite(logEntity);
                    #endregion
                }

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditDevtree]_批量编辑机组信息失败 ", ex);
                return Ok(ApiResponse<string>.Error($"批量编辑机组信息失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 新增机组信息
        /// </summary>
        /// <param name="dtos">机组信息DTO列表</param>
        /// <returns></returns>
        [HttpPost("AddDevice")]
        public IActionResult AddTurbineDevtree([FromBody] List<WindTurbineDTO> dtos)
        {
            try
            {
                // 首先验证所有数据
                foreach (var dto in dtos)
                {
                    if (string.IsNullOrEmpty(dto.ComponentIds))
                    {
                        return Ok(ApiResponse<string>.Error($"机组 {dto.WindTurbineName} 缺少部件信息"));
                    }

                    List<WindTurbine> turbineList = WTCMSLive.BusinessModel.DevTreeManagement.GetTurbinesListByWindParkId(dto.WindParkId);
                    WindTurbine windTurbine = new WindTurbine();
                    windTurbine.WindTurbineID = IDProvide.GetTurbineId(dto.WindParkId, dto.WindTurbineCode);

                    if (turbineList != null)
                    {
                        var list = from m in turbineList
                                   where m.WindTurbineName == dto.WindTurbineName
                                   select m;
                        if (list.Any())
                        {
                            return Ok(ApiResponse<string>.Error($"{dto.WindTurbineName} 名称已存在"));
                        }

                        list = from m in turbineList
                               where m.WindTurbineID == windTurbine.WindTurbineID
                               select m;
                        if (list.Any())
                        {
                            return Ok(ApiResponse<string>.Error($"{dto.WindTurbineCode} 编号已存在"));
                        }
                    }
                }

                // 所有验证通过后，开始批量添加
                foreach (var dto in dtos)
                {
                    WindTurbine windTurbine = new WindTurbine();
                    windTurbine.WindTurbineID = IDProvide.GetTurbineId(dto.WindParkId, dto.WindTurbineCode);
                    windTurbine.OperationalDate = DateTime.Parse(dto.OperationalDate);
                    windTurbine.WindTurbineCode = dto.WindTurbineCode;
                    windTurbine.WindTurbineModel = dto.WindTurbineModel;
                    windTurbine.WindTurbineName = dto.WindTurbineName;
                    windTurbine.MinWorkingRotSpeed = 0;
                    windTurbine.WindParkID = dto.WindParkId;
                    windTurbine.Location = dto.Location;

                    if (System.Configuration.ConfigurationManager.AppSettings["ViewModel"] != "BVM")
                    {
                        windTurbine.RotSpdMeasLoc = BuildDefaultRotSpd();
                        windTurbine.RotSpdMeasLoc.WindTurbineID = windTurbine.WindTurbineID;
                        windTurbine.RotSpdMeasLoc.MeasLocationID = IDProvide.GetRotSpdLocID(windTurbine.WindTurbineID, "");
                    }

                    List<WindTurbineComponent> ComponentList = new List<WindTurbineComponent>();
                    Dictionary<string, string> CompanyList = AppFramework.IDUtility.CodeProvide.GetComponentDic();
                    foreach (string ComponentModel in dto.ComponentIds.Split(','))
                    {
                        WindTurbineComponent Component = new WindTurbineComponent();
                        Component.ComponentName = ComponentModel;
                        Component.OrderSeq = int.Parse(new EigenValueManager().SetComponentOrder(Component.ComponentName));
                        if (Component.OrderSeq == 7 && ComponentList.Find(item => item.OrderSeq == Component.OrderSeq) != null)
                        {
                            Component.OrderSeq = ComponentList.Max(item => item.OrderSeq) + 1;
                        }
                        Component.ComponentID = IDProvide.GetCompotentID(windTurbine.WindTurbineID, ComponentModel);
                        if (Component.ComponentID.IndexOf(ComponentModel) > -1)
                        {
                            Component.ComponentID = windTurbine.WindTurbineID + "C" + (Component.OrderSeq - 6).ToString("00");
                        }
                        Component.WindTurbineID = windTurbine.WindTurbineID;
                        Component.ComponentModel = ComponentModel;
                        Component.CompManufacturer = "未知";
                        ComponentList.Add(Component);
                    }
                    windTurbine.TurComponentList = ComponentList;
                    DevTreeManagement.AddWindTurbine_Manager(windTurbine);

                    MCS Mcs = new MCS();
                    Mcs.WindTurbineID = windTurbine.WindTurbineID;
                    Mcs.MCSIP = dto.McsIP;
                    Mcs.FieldBusType = "0";
                    Mcs.MCSPort = 502;
                    DAUMCS.AddMCS(Mcs);

                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = windTurbine.WindTurbineName;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription = string.Format("增加_设备树({0})", windTurbine.WindTurbineID);
                    LogManagement.UserlogWrite(logEntity);
                    #endregion
                }

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddTurbineDevtree]_批量增加机组失败 ", ex);
                return Ok(ApiResponse<string>.Error($"批量增加机组失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 构建默认的"发电机转速"测量位置
        /// </summary>
        /// <returns></returns>
        public MeasLoc_RotSpd BuildDefaultRotSpd()
        {
            MeasLoc_RotSpd _rotspd = new MeasLoc_RotSpd();
            string rotspdDefaultName = "发电机转速";
            _rotspd.MeasLocName = rotspdDefaultName;
            //编码器线数默认是2 wangy 2015年12月14日
            _rotspd.LineCounts = 2;
            _rotspd.GearRatio = 1f;
            return _rotspd;
        }

        /// <summary>
        /// 删除机组信息
        /// </summary>
        /// <param name="ids">机组ID列表</param>
        /// <returns></returns>
        [HttpPost("DeleteDevice")]
        public IActionResult DeleteDevtree([FromBody] List<string> ids)
        {
            try
            {
                // 首先验证所有机组是否可以删除
                foreach (var id in ids)
                {
                    WindTurbine windTurbine = DevTreeManagement.GetWindTurbine(id);
                    if (windTurbine == null)
                    {
                        return Ok(ApiResponse<string>.Error($"机组ID {id} 不存在"));
                    }

                    //// 检查是否有关联数据
                    //using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
                    //{
                    //    //检查mdfmodbus
                    //    List<ModbusDef> defmodbuslist = ctx.ModbusDefs.Where(item => item.WindTurbineID == id).ToList();
                    //    if (defmodbuslist.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在Modbus定义，请先删除", 500));
                    //    }

                    //    // 检查measSolution
                    //    List<MeasSolution> measSolution = ctx.MeasSolutions.Where(t => t.WindTurbineID == id).ToList();
                    //    if (measSolution.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在测量方案，请先删除", 500));
                    //    }
                    //}

                    //using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                    //{
                    //    // 检查modbusunit
                    //    List<ModbusUnit> modbusUnitlist = ctx.ModbusUnits.Where(i => i.WindTurbineID == id).ToList();
                    //    if (modbusUnitlist.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在Modbus单元，请先删除", 500));
                    //    }

                    //    //检查timcalibration
                    //    List<TimCalibration> tcList = ctx.TimCalibrations.Where(i => i.WindTurbineID == id).ToList();
                    //    if (tcList.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在时间校准，请先删除", 500));
                    //    }

                    //    // 检查serialserver
                    //    List<SerialServer> serialServerList = ctx.SerialServers.Where(i => i.WindTurbineID == id).ToList();
                    //    if (serialServerList.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在串口服务器，请先删除", 500));
                    //    }

                    //    //检查OilUnit
                    //    List<OilUnit> oilUnitList = ctx.OilUnits.Where(i => i.WindTurbineID == id).ToList();
                    //    if (oilUnitList != null && oilUnitList.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在油液单元，请先删除", 500));
                    //    }

                    //    List<UltrasonicChannelConfig> deleteUltrasonicList = ctx.UltrasonicChannelConfigs.Where(i => i.WindTurbineID == id).ToList();
                    //    if (deleteUltrasonicList != null && deleteUltrasonicList.Count > 0)
                    //    {
                    //        return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在超声波通道配置，请先删除", 500));
                    //    }
                    //}

                    //// 检查测量定义
                    //List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(id);
                    //if (measDefList.Count > 0)
                    //{
                    //    return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在测量定义，请先删除", 500));
                    //}

                    //// 检查DAU
                    //List<WindDAU> daulist = DAUSManageModel.GetDAUListById(id);
                    //if (daulist.Count > 0)
                    //{
                    //    return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在DAU，请先删除", 500));
                    //}

                    //// 检查SVM
                    //SVMUnit svm = SVMManagement.GetSVMById(id);
                    //if (svm != null)
                    //{
                    //    return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在SVM，请先删除", 500));
                    //}

                    //// 检查主控
                    //MCS mscSystem = DAUMCS.GetMCSByTurbineId(id);
                    //if (mscSystem != null)
                    //{
                    //    return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在主控，请先删除", 500));
                    //}

                    //// 检查报警定义
                    //List<AlarmDefinition> alarmDefs = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(id, EnumMeasLocType.VibAlarmDef);
                    //if (alarmDefs.Count > 0)
                    //{
                    //    return Ok(ApiResponse<string>.Error($"机组 {windTurbine.WindTurbineName} 存在报警定义，请先删除", 500));
                    //}
                }

                // 所有验证通过后，执行批量删除
                foreach (var id in ids)
                {
                    DeleteTurbineByID(id);

                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = id;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription = string.Format("删除_机组({0})", id);
                    LogManagement.UserlogWrite(logEntity);
                    #endregion
                }

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteDevtree]批量删除机组失败", ex);
                return Ok(ApiResponse<string>.Error($"批量删除机组失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除机组
        /// </summary>
        /// <param name="ID"></param>

        internal void DeleteTurbineByID(string ID)
        {
            WindTurbine windTurbine = DevTreeManagement.GetWindTurbine(ID);
            //Modified by wjy 2021/08/18
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                //删除mdfmodbus
                List<ModbusDef> defmodbuslist = ctx.ModbusDefs.Where(item => item.WindTurbineID == ID).ToList();
                if (defmodbuslist.Count > 0)
                {

                    ctx.ModbusDefs.RemoveRange(defmodbuslist);
                }

                // 删除measSolution
                List<MeasSolution> measSolution = ctx.MeasSolutions.Where(t => t.WindTurbineID == ID).ToList();
                if (measSolution.Count > 0)
                {
                    ctx.MeasSolutions.RemoveRange(measSolution);
                }

                ctx.SaveChanges();
            }

            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                // 删除modbusunit
                List<ModbusUnit> modbusUnitlist = ctx.ModbusUnits.Where(i => i.WindTurbineID == ID).ToList();
                if (modbusUnitlist.Count > 0)
                {
                    ctx.ModbusUnits.RemoveRange(modbusUnitlist);
                    ctx.SaveChanges();
                }
                //删除timcalibration
                List<TimCalibration> tcList = ctx.TimCalibrations.Where(i => i.WindTurbineID == ID).ToList();
                if (tcList.Count > 0)
                {
                    ctx.TimCalibrations.RemoveRange(tcList);
                    ctx.SaveChanges();
                }
                // 删除serialserver
                List<SerialServer> serialServerList = ctx.SerialServers.Where(i => i.WindTurbineID == ID).ToList();
                if (serialServerList.Count > 0)
                {
                    ctx.SerialServers.RemoveRange(serialServerList);
                    ctx.SaveChanges();
                }
                //删除OilUnit
                List<OilUnit> oilUnitList = ctx.OilUnits.Where(i => i.WindTurbineID == ID).ToList();
                if (oilUnitList != null)
                {
                    ctx.OilUnits.RemoveRange(oilUnitList);
                    ctx.SaveChanges();
                }
                List<UltrasonicChannelConfig> deleteUltrasonicList = ctx.UltrasonicChannelConfigs.Where(i => i.WindTurbineID == ID).ToList();
                if (deleteUltrasonicList != null)
                {
                    ctx.UltrasonicChannelConfigs.RemoveRange(deleteUltrasonicList);
                    ctx.SaveChanges();
                }

            }
            //删除ultrasonicchannelconfig
            DauManagement.DeleteUltrasonicChannel(ID);
            List<MeasDefinition> measDefList = MeasDefinitionManagement.GetMeasDefListByTurId(ID);
            if (measDefList.Count > 0)
            {
                measDefList.ForEach(item =>
                MeasDefinitionManagement.DeleteMeasdefinition(item.WindTurbineID, item.MeasDefinitionID));
            }
            //WindDAU dau = DauManagement.GetDAUById(ID);
            //// Add by ZhangMai 2012-08-14 添加删除机组时同时删除关联的DAU
            //// Modified by SunQI 2020-05-23 同时删除多个关联DAU --- 时间已经过去8年了。。。
            //if (dau != null)
            //{
            //    DauManagement.DeleteDAU(ID);
            //}
            List<WindDAU> daulist = DAUSManageModel.GetDAUListById(ID);
            daulist.ForEach(item =>
            {
                DAUSManageModel.DeleteDAUByTrubineIdAndDauId(item.WindTurbineID, item.DauID);
            });

            SVMUnit svm = SVMManagement.GetSVMById(ID);
            //删除关联主控
            MCS mscSystem = DAUMCS.GetMCSByTurbineId(ID);
            if (mscSystem != null)
            {
                DAUMCS.DeleteMCSSystem(ID);
            }
            if (svm != null)
            {
                SVMManagement.DeleteSVM(ID, svm.ComponentID);
            }
            //删除报警定义
            AlarmDefinitionManage.DeleteAllAlarmDefinition(ID);
            DevTreeManagement.DeleteWindTurbine(ID);
        }
        //设备树单个页面----机组信息的修改
        /// <summary>
        /// 修改机组信息
        /// </summary>
        /// <param name="windTurbineID"></param>
        /// <param name="windTurbineName"></param>
        /// <param name="windTurbineCode"></param>
        /// <param name="wTurbineModel"></param>
        /// <param name="operationalD"></param>
        /// <param name="workingRotSpeed"></param>
        /// <param name="ratedPower"></param>
        /// <returns></returns>
        [HttpPost("EditWindTInformation")]
        public IActionResult EditWindTInformation(WindTurbineDTO trubine)
        {
            WindTurbine _wtInformation = DevTreeManagement.GetWindTurbine(trubine.WindTurbineID);
            _wtInformation.WindTurbineID = trubine.WindTurbineID;
            _wtInformation.WindTurbineName = trubine.WindTurbineName;
            _wtInformation.WindTurbineCode = trubine.WindTurbineCode;
            _wtInformation.WTurbineModel = new WindTurbineModel();
            _wtInformation.WTurbineModel.TurbineModel = trubine.WindTurbineModel;
            _wtInformation.OperationalDate = DateTime.Parse(trubine.OperationalDate);
            _wtInformation.MinWorkingRotSpeed = trubine.MinWorkingRotSpeed;
            try
            {
                DevTreeManagement.EditWindTurbine(_wtInformation);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = trubine.WindTurbineID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("编辑_风电场({0})", trubine.WindTurbineName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeletewindPark]编辑设备树失败，机组ID：" + trubine.WindTurbineID, ex);
                //return false;
                return Ok(ApiResponse<string>.Error("编辑设备树失败"));
            }
            //return true;
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 通过机组ID获取机组信息
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("turbineInfo")]
        public ActionResult<WindTurbine> GetWindTurbineInfo(string turbineID)
        {
            return Ok(DevTreeManagement.GetWindTurbine(turbineID));
        }

        #endregion

        #region 测量位置

        /// <summary>
        /// 获取振动测量位置列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        [HttpGet("GetVibMeaslocation")]
        public IActionResult GetVibMeaslocation(string turbineID)
        {
            return Ok(DevTreeManagement.GetVibMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToList());
        }

        /// <summary>
        /// 获取电流电压测量位置列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        [HttpGet("GetProcessMeaslocation")]
        public IActionResult GetProcessMeaslocation(string turbineID)
        {
            return Ok(DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToList());
        }

        //转速测量位置编辑
        public bool EditRotSpd(string _turbineID, string _rotSpdMeasLocid, string MeasLName, float gearRatio, int RotLineCount)
        {
            try
            {
                WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(_turbineID);
                MeasLoc_RotSpd _RotSpdInformation = turbine.DevMeasLocRotSpds.Find(item => item.MeasLocationID == _rotSpdMeasLocid);
                _RotSpdInformation.WindTurbineID = _turbineID;
                _RotSpdInformation.MeasLocationID = _rotSpdMeasLocid;
                _RotSpdInformation.MeasLocName = MeasLName;
                _RotSpdInformation.GearRatio = gearRatio;
                _RotSpdInformation.LineCounts = RotLineCount;
                DevTreeManagement.EditRotSpdMeasLocation(_RotSpdInformation);
                // 升级测量定义ID
                DauManagement.UpdateMeasDefVersion(_turbineID);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditRotSpd]编辑转速测量位置失败，机组ID：" + _turbineID, ex);
                return false;
            }
            return true;
        }

        [HttpPost("EditRotSpdLoc")]
        [BatchOperation(nameof(BatchEditRotSpdLoc))]
        public IActionResult EditRotSpdLoc([FromBody] BatchEditRotSpdLocRequest request)
        {
            if (request?.SourceData == null)
            {
                return Ok(ApiResponse<string>.Error("参数不能为空"));
            }

            var results = new List<string>();
            var measLoc_RotSpd = request.SourceData;
            try
            {
                WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(measLoc_RotSpd.WindTurbineID);
                MeasLoc_RotSpd _RotSpdInformation = turbine.DevMeasLocRotSpds.Find(item => item.MeasLocationID == measLoc_RotSpd.MeasLocationID);

                if (_RotSpdInformation == null)
                {
                    results.Add($"机组 {measLoc_RotSpd.WindTurbineID} 中未找到转速测量位置 {measLoc_RotSpd.MeasLocationID}");
                }
                else
                {
                    _RotSpdInformation.WindTurbineID = measLoc_RotSpd.WindTurbineID;
                    _RotSpdInformation.MeasLocationID = measLoc_RotSpd.MeasLocationID;
                    _RotSpdInformation.MeasLocName = measLoc_RotSpd.MeasLocName;
                    _RotSpdInformation.GearRatio = measLoc_RotSpd.GearRatio;
                    _RotSpdInformation.LineCounts = measLoc_RotSpd.LineCounts;
                    DevTreeManagement.EditRotSpdMeasLocation(_RotSpdInformation);

                    // 升级测量定义ID
                    DauManagement.UpdateMeasDefVersion(measLoc_RotSpd.WindTurbineID);

                    results.Add($"机组 {measLoc_RotSpd.WindTurbineID} 转速测量位置 {measLoc_RotSpd.MeasLocName} 编辑成功");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[EditRotSpd]编辑转速测量位置失败，机组ID：{measLoc_RotSpd.WindTurbineID}", ex);
                results.Add($"机组 {measLoc_RotSpd.WindTurbineID} 转速测量位置编辑失败: {ex.Message}");
            }

            return Ok(ApiResponse<List<string>>.Success(results));
        }

        [HttpPost("DeleteRotSpdLoc")]
        [BatchOperation(nameof(BatchDeleteRotSpdLoc))]
        public IActionResult DeleteRotSpdLoc([FromBody] BatchDeleteRotSpdLocRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("删除列表不能为空"));

            // 在删除前，先查询并缓存原始记录信息，供批量操作使用
            var originalRecords = new List<MeasLoc_RotSpd>();
            foreach (var id in request.SourceData)
            {
                try
                {

                    WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(id.WindTurbineID);
                    if (turbine != null)
                    {
                        var record = turbine.DevMeasLocRotSpds.Find(item => item.MeasLocationID == id.MeasLocationID);
                        if (record != null)
                        {
                            originalRecords.Add(record);
                        }
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[DeleteRotSpdLoc]获取原始记录失败，ID: {id}", ex);
                }
            }

            // 将原始记录缓存到HttpContext中，供批量操作使用
            HttpContext.Items["OriginalRotSpdLocRecords"] = originalRecords;

            var result = new List<string>();
            try
            {
                foreach (var id in request.SourceData)
                {
                    WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(id.WindTurbineID);
                    if (turbine == null)
                    {
                        result.Add($"转速测量位置ID {id} 对应的机组不存在");
                        continue;
                    }

                    MeasLoc_RotSpd _RotSpdInformation = turbine.DevMeasLocRotSpds.Find(item => item.MeasLocationID == id.MeasLocationID);
                    if (_RotSpdInformation == null)
                    {
                        result.Add($"转速测量位置ID {id} 不存在");
                        continue;
                    }

                    List<WindDAU> DAUs = DauManagement.GetDAUListByTurbineID(id.WindTurbineID);
                    foreach (var dau in DAUs)
                    {
                        if (dau.RotSpeedChannelList.FirstOrDefault(t => t.MeasLocRotSpdID == id.MeasLocationID) != null)
                        {
                            result.Add($"转速测量位置ID {id} 正在被使用，请先删除通道！");
                            continue;
                        }
                    }

                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        ctx.DevMeasLocRotSpds.Remove(_RotSpdInformation);
                        ctx.SaveChanges();
                    }

                    result.Add($"转速测量位置 {_RotSpdInformation.MeasLocName} 删除成功");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteRotSpdLoc]删除转速测量位置失败", ex);
                return Ok(ApiResponse<string>.Error("删除转速测量位置失败"));
            }
            return Ok(ApiResponse<List<string>>.Success(result));
        }


        [HttpPost("AddRotSpdLoc")]
        [BatchOperation(nameof(BatchAddRotSpdLoc))]
        public IActionResult AddRotSpdLoc([FromBody] BatchAddRotSpdLocRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("添加列表不能为空"));

            var result = new List<string>();
            try
            {
                foreach (var measLoc_RotSpd in request.SourceData)
                {
                    WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(measLoc_RotSpd.WindTurbineID);
                    if (turbine != null && turbine.DevMeasLocRotSpds.Count > 0)
                    {
                        result.Add($"机组 {measLoc_RotSpd.WindTurbineID} 已存在转速测量位置，请勿重复添加！");
                        continue;
                    }

                    MeasLoc_RotSpd RotSpdMeasLoc = new MeasLoc_RotSpd();
                    RotSpdMeasLoc.WindTurbineID = measLoc_RotSpd.WindTurbineID;
                    RotSpdMeasLoc.MeasLocationID = IDProvide.GetRotSpdLocID(measLoc_RotSpd.WindTurbineID, "");
                    RotSpdMeasLoc.MeasLocName = measLoc_RotSpd.MeasLocName;
                    RotSpdMeasLoc.GearRatio = measLoc_RotSpd.GearRatio;
                    RotSpdMeasLoc.LineCounts = measLoc_RotSpd.LineCounts;
                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        ctx.DevMeasLocRotSpds.Add(RotSpdMeasLoc);
                        ctx.SaveChanges();
                    }

                    result.Add($"机组 {measLoc_RotSpd.WindTurbineID} 转速测量位置 {measLoc_RotSpd.MeasLocName} 添加成功");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddRotSpdLoc]添加转速测量位置失败", ex);
                return Ok(ApiResponse<string>.Error("添加转速测量位置失败"));
            }
            return Ok(ApiResponse<List<string>>.Success(result));
        }

        /// <summary>
        /// 同一机组下的工况参数不能重复
        /// </summary>
        /// <param name="TurbineID">机组ID</param>
        /// <param name="MeasLName"></param>
        /// <returns></returns>
        public bool CheckConditionMeasData(string TurbineID, string MeasLName, string measLocName, string MDFWorkData, string isAddType, string Datafrom)
        {

            if (DevTreeManagement.IsWorkCondMeasLocUsedByLocName(TurbineID, MeasLName, Datafrom))
            {
                return false;
            }
            else
            {
                return true;
            }
        }

        /// <summary>
        /// 添加/编辑工况测量位置
        /// </summary>
        /// <param name="TurbineID"></param>
        /// <param name="MeasLName"></param>
        /// <param name="MDFWorkData"></param>
        /// <param name="Datafrom"></param>
        /// <param name="MeasLocationID"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public bool AddWorkingConditionMeas(string TurbineID, string MeasLName, string measLocName, string MDFWorkData, string Datafrom, string MeasLocationID, string isAddType)
        {
            MeasLoc_Process measLoc_Process = isAddType == "1" ? new MeasLoc_Process() : DevTreeManagement.GetWorkCondMeasLocation(MeasLocationID);
            measLoc_Process.WindTurbineID = TurbineID;
            measLoc_Process.MeasLocName = MeasLName;
            measLoc_Process.Param_Type_Code = (EnumWorkCondition_ParamType)EnumWorkCondParamTypeHelper.GetParamTypeList().Find(item => item.Value == MDFWorkData).Key;
            if (Datafrom == "主控系统") { measLoc_Process.FieldBusType = EnumWorkConDataSource.ModbusOnTcp; }
            if (Datafrom == "Wind DAU") { measLoc_Process.FieldBusType = EnumWorkConDataSource.WindDAU; }
            string actionName = "增加";
            try
            {
                if (isAddType == "1")
                {
                    //获取工况排序的index
                    measLoc_Process.OrderSeq = DevTree.GetMeasLocProcessOrderSeqList().FindIndex(item => item == measLocName) + 1;
                    measLoc_Process.MeasLocationID = IDProvide.GetWorkCondID(TurbineID, measLoc_Process.FieldBusType == EnumWorkConDataSource.WindDAU ? "1" : "0", measLocName);
                    // end by whr @2012-6-25
                    DevTreeManagement.AddWorkCondMeasLocation(measLoc_Process);
                }
                else
                {
                    actionName = "修改";
                    DevTreeManagement.EditWorkCondMeasLocation(measLoc_Process);
                }
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = TurbineID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_工况测量位置({0})", TurbineID, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditRotSpd]" + actionName + "工况测量位置失败，机组ID：" + TurbineID, ex);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 添加工况测量位置
        /// </summary>
        /// <param name="wcdatas"></param>
        /// <returns></returns>
        [HttpPost("AddWorkingConditionMeaslocs")]
        [BatchOperation(nameof(BatchAddWorkingConditionMeasLocs))]
        public IActionResult AddWorkingConditionMeaslocs([FromBody] BatchAddWorkingConditionMeasLocsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("添加列表不能为空"));

            // 检查所有项
            foreach (var item in request.SourceData)
            {
                var check = CheckConditionMeasData(item.WindTurbineID, item.MeasLocName, item.MeasLocName, item.MDFWorkData, "1", item.Datafrom);
                if (!check)
                {
                    return Ok(ApiResponse<string>.Error($"工况测量位置校验失败: {check}"));
                }
            }
            try
            {
                List<MeasLoc_Process> wclist = new();
                foreach (var item in request.SourceData)
                {

                    MeasLoc_Process measLoc_Process = new MeasLoc_Process();
                    measLoc_Process.WindTurbineID = item.WindTurbineID;
                    measLoc_Process.MeasLocName = item.MeasLocName;
                    measLoc_Process.Param_Type_Code = (EnumWorkCondition_ParamType)EnumWorkCondParamTypeHelper.GetParamTypeList().Find(t => t.Value == item.MDFWorkData).Key;
                    if (item.Datafrom == "主控系统") { measLoc_Process.FieldBusType = EnumWorkConDataSource.ModbusOnTcp; }
                    if (item.Datafrom == "Wind DAU") { measLoc_Process.FieldBusType = EnumWorkConDataSource.WindDAU; }

                    measLoc_Process.OrderSeq = DevTree.GetMeasLocProcessOrderSeqList().FindIndex(t => t == item.MeasLocName) + 1;
                    measLoc_Process.MeasLocationID = IDProvide.GetWorkCondID(item.WindTurbineID, measLoc_Process.FieldBusType == EnumWorkConDataSource.WindDAU ? "1" : "0", item.MeasLocName);
                    // end by whr @2012-6-25


                    wclist.Add(measLoc_Process);
                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = item.WindTurbineID.ToString();
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_工况测量位置({0})", item.WindTurbineID, "添加");
                    LogManagement.UserlogWrite(logEntity);
                    #endregion
                }
                DevTreeManagement.AddWorkCondMeasLocationRange(wclist);
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<string>.Error("添加失败"));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }

        /// <summary>
        /// 编辑工况测量位置
        /// </summary>
        /// <param name="wc"></param>
        /// <returns></returns>
        [HttpPost("EditWorkingConditionMeas")]
        public IActionResult EditWorkingConditionMeas(WorkingConditionMeasDTO wc)
        {
            MeasLoc_Process measLoc_Process = DevTreeManagement.GetWorkCondMeasLocation(wc.MeasLocationID);
            measLoc_Process.WindTurbineID = wc.WindTurbineID;
            measLoc_Process.MeasLocName = wc.MeasLocName;
            measLoc_Process.Param_Type_Code = (EnumWorkCondition_ParamType)EnumWorkCondParamTypeHelper.GetParamTypeList().Find(t => t.Value == wc.MDFWorkData).Key;
            if (wc.Datafrom == "主控系统") { measLoc_Process.FieldBusType = EnumWorkConDataSource.ModbusOnTcp; }
            if (wc.Datafrom == "Wind DAU") { measLoc_Process.FieldBusType = EnumWorkConDataSource.WindDAU; }
            string actionName = "增加";
            try
            {
                DevTreeManagement.EditWorkCondMeasLocation(measLoc_Process);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = wc.WindTurbineID.ToString();
                logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("{1}_工况测量位置({0})", wc.WindTurbineID, actionName);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditRotSpd]" + actionName + "工况测量位置失败，机组ID：" + wc.WindTurbineID, ex);
                return Ok(ApiResponse<string>.Error("编辑工况测量位置失败"));
            }
            return Ok(ApiResponse<string>.Success("OK"));
        }


        /// <summary>
        /// 删除工况测量位置
        /// </summary>
        /// <param name="TurbineID"></param>
        /// <param name="Datafrom"></param>
        /// <returns></returns>
        /// 
        [HttpGet("DeleteWorkingConditionMeas")]
        public IActionResult DeleteWorkingConditionMeas(string TurbineID, string MeasLocationID)
        {
            // add by whr @2012-05-31 "无功率计划：修改机组标识"
            MeasLoc_Process workCondition = DevTreeManagement.GetWorkCondMeasLocation(MeasLocationID);
            //采集单元
            List<DAUChannel_Process> dauList = DauManagement.GetDAUChannelProcessListByDAUId(TurbineID);
            if (dauList != null && dauList.Count != 0)
            {
                var list = from m in dauList
                           where m.MeasLoc_ProcessId == workCondition.MeasLocationID
                           select m;
                if (list.Any())
                {
                    return Ok(ApiResponse<string>.Error("请先删除采集单元配置中的工况通道信息"));
                }
            }
            //主控
            List<MCSChannel> MCSChannelList = DAUMCS.GetMCSChannelList(TurbineID);
            if (MCSChannelList != null && MCSChannelList.Count != 0)
            {
                var list = from m in MCSChannelList
                           where m.MeasLocProcessID == workCondition.MeasLocationID
                           select m;
                if (list.Any())
                {
                    return Ok(ApiResponse<string>.Error("请先删除主控配置中的寄存器列表信息"));
                }
            }
            if (workCondition.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Power)
            {
                WindTurbine CurrentTurbine = GetWindTurbine(TurbineID);
                EditWindTurbine(CurrentTurbine);
            }
            // end by whr @2012-6-25
            DevTreeManagement.DeleteWorkCondMeasLocation(MeasLocationID);
            return Ok(ApiResponse<string>.Success("OK"));
        }


        /// <summary>
        /// 批量删除工况测量位置（全部成功才删除，否则一个都不删）
        /// </summary>
        [HttpPost("DeleteWorkingConditionMeasBatch")]
        [BatchOperation(nameof(BatchDeleteWorkingConditionMeasLocs))]
        public IActionResult DeleteWorkingConditionMeasBatch([FromBody] BatchDeleteWorkingConditionMeasLocsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("删除列表不能为空"));

            // 在删除前，先查询并缓存原始记录信息，供批量操作使用
            var originalRecords = new List<MeasLoc_Process>();
            foreach (var item in request.SourceData)
            {
                try
                {
                    var record = DevTreeManagement.GetWorkCondMeasLocation(item.MeasLocationID);
                    if (record != null)
                    {
                        originalRecords.Add(record);
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[DeleteWorkingConditionMeasBatch]获取原始记录失败，ID: {item.MeasLocationID}", ex);
                }
            }

            // 将原始记录缓存到HttpContext中，供批量操作使用
            HttpContext.Items["OriginalWorkingConditionMeasLocRecords"] = originalRecords;

            string checkMsg = null;
            // 先全部校验
            foreach (var item in request.SourceData)
            {
                var workCondition = DevTreeManagement.GetWorkCondMeasLocation(item.MeasLocationID);
                if (workCondition == null) continue;

                //采集单元
                List<DAUChannel_Process> dauList = DauManagement.GetDAUChannelProcessListByDAUId(workCondition.WindTurbineID);
                if (dauList != null && dauList.Count != 0)
                {
                    var list = from m in dauList
                               where m.MeasLoc_ProcessId == workCondition.MeasLocationID
                               select m;
                    if (list.Count() > 0)
                    {
                        checkMsg = "请先删除采集单元配置中的工况通道信息！";
                        break;
                    }
                }
                //主控
                List<MCSChannel> MCSChannelList = DAUMCS.GetMCSChannelList(workCondition.WindTurbineID);
                if (MCSChannelList != null && MCSChannelList.Count != 0)
                {
                    var list = from m in MCSChannelList
                               where m.MeasLocProcessID == workCondition.MeasLocationID
                               select m;
                    if (list.Count() > 0)
                    {
                        checkMsg = "请先删除主控配置中的寄存器列表信息！";
                        break;
                    }
                }
            }
            if (!string.IsNullOrEmpty(checkMsg))
            {
                return Ok(ApiResponse<string>.Error(checkMsg));
            }

            // 全部校验通过，开始删除
            foreach (var id in request.SourceData)
            {
                var workCondition = DevTreeManagement.GetWorkCondMeasLocation(id.MeasLocationID);
                var item = new { MeasLocationID = id, WindTurbineID = workCondition?.WindTurbineID };
                if (workCondition.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Power)
                {
                    //WindTurbine CurrentTurbine = GetWindTurbine(item.WindTurbineID);
                    //EditWindTurbine(CurrentTurbine);
                }
                DevTreeManagement.DeleteWorkCondMeasLocation(id.MeasLocationID);
            }
            return Ok(ApiResponse<string>.Success("批量删除成功"));
        }

        /// <summary>
        /// 获取工况测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        [HttpGet("GetWorkCondMeasLocs")]
        public IActionResult GetWorkCondMeasLocTable(string turbineID)
        {
            List<MeasLoc_Process> workCondMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(turbineID);
            List<MeasLoc_Process> workCondMeasLocListOrder = workCondMeasLocList;//new List<MeasLoc_Process>();
            WindDAU dau = DauManagement.GetDAUById(turbineID);
            List<MCSChannel> mcsChannelList = DAUMCS.GetMCSChannelList(turbineID);
            //foreach (string processName in GetMeasLocProcessOrderSeqList())
            //{
            //    MeasLoc_Process process = workCondMeasLocList.Find(item => item.MeasLocName == processName);
            //    if (process != null)
            //    {
            //        workCondMeasLocListOrder.Add(process);
            //    }
            //}
            BaseTableModel tableModel = new BaseTableModel();
            tableModel.tableName = "WorkCondMeasLoc";

            return Ok(workCondMeasLocList);
        }

        /// <summary>
        /// 获取全部工况测量位置
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetWorkCondMeasLocDic")]
        public IActionResult GetWorkCondMeasLocDic()
        {
            // 构建成前端需要的格式
            return Ok(EnumWorkCondParamTypeHelper.GetParamTypeList());
        }

        /// <summary>
        /// 获取工况数据来源
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetEnumWorkConDataSource")]
        public IActionResult GetEnumWorkConDataSource()
        {
            // 构建成前端需要的格式
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumWorkConDataSource value in Enum.GetValues(typeof(EnumWorkConDataSource)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }
        /// <summary>
        /// 手动添加晃度仪测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        //public string AddSVMMeasloc(string turbineID)
        //{
        //    string Message = "state:{0},msg:'{1}'";
        //    try
        //    {
        //        var svmlist = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
        //        if (svmlist.Count > 0)
        //        {
        //            Message = string.Format(Message, 0, "无需重复添加测量位置！");
        //            return "{" + Message + "}";
        //        }

        //        //生产晃动测量位置信息
        //        List<WindTurbineComponent> list = DevTreeManagement.GetComListByTurbineId(turbineID);
        //        WindTurbineComponent myComponent = list.Find(i => i.ComponentName == "机舱" || i.ComponentName == "塔筒");
        //        if (myComponent == null)
        //        {
        //            Message = string.Format(Message, 0, "无部件信息！");
        //            return "{" + Message + "}";
        //        }
        //        SVMManagement.AutoAddSVMMeasLoc(turbineID, myComponent.ComponentName);
        //        #region
        //        LogEntity logEntity = new LogEntity();
        //        logEntity.LogDB = ConstDefine.UserManagementLog;
        //        logEntity.LogTime = DateTime.Now;
        //        logEntity.NodeID = turbineID.ToString();
        //        //logEntity.UserName = Request.Cookies["WindCMSUserName"];
        //        logEntity.OperationDescription
        //            = string.Format("增加_机组SVM({0})", turbineID);
        //        LogManagement.UserlogWrite(logEntity);
        //        #endregion
        //    }
        //    catch (Exception ex)
        //    {
        //        CMSFramework.Logger.Logger.LogErrorMessage("[AddSVM]增加机组SVM失败", ex);

        //    }
        //    Message = string.Format(Message, 1, "添加成功！");
        //    return "{" + Message + "}";
        //}

        public IActionResult GetSVMMeasLocList(string turbineID)
        {
            TurbineConfigManager configMgr = new TurbineConfigManager();
            try
            {
                //晃度仪测量位置
                var list = configMgr.GetMeasLoc_SVMList(turbineID).ToJson();
                return Ok(list);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DevtreeChannelSetting]取得晃度测量位置出错，机组ID： " + turbineID, ex);
                return Ok(ApiResponse<string>.Error("错误"));
            }

        }

        /// <summary>
        /// 添加晃度仪测量位置
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="ComponentName"></param>
        /// <param name="ComponentId"></param>
        /// <param name="SectionName"></param>
        /// <returns></returns>
        public string AddSVMMeasloc(string WindTurbineID, string ComponentId, string SectionName)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                var svmlist = SVMManagement.GetMeasLoc_SVMListByTurID(WindTurbineID);
                var svmlist_sel = svmlist.Where(item => item.ComponentID == ComponentId && item.SectionName == SectionName).ToList();
                if (svmlist_sel.Count > 0)
                {
                    Message = string.Format(Message, 0, "无需重复添加测量位置！");
                    return "{" + Message + "}";
                }

                //生产晃动测量位置信息
                List<WindTurbineComponent> list = DevTreeManagement.GetComListByTurbineId(WindTurbineID);
                //WindTurbineComponent myComponent = list.Find(i => i.ComponentName == "机舱" || i.ComponentName == "塔筒");
                WindTurbineComponent myComponent = list.Find(k => k.ComponentID == ComponentId);
                if (myComponent == null)
                {
                    Message = string.Format(Message, 0, "无部件信息！");
                    return "{" + Message + "}";
                }
                SVMManagement.AutoAddSVMMeasLoc(WindTurbineID, myComponent.ComponentID, SectionName);
                #region
                LogEntity logEntity = new LogEntity();
                logEntity.LogDB = ConstDefine.UserManagementLog;
                logEntity.LogTime = DateTime.Now;
                logEntity.NodeID = WindTurbineID.ToString();
                //logEntity.UserName = Request.Cookies["WindCMSUserName"];
                logEntity.OperationDescription
                    = string.Format("增加_机组SVM({0})", WindTurbineID);
                LogManagement.UserlogWrite(logEntity);
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddSVM]增加机组SVM失败", ex);

            }
            Message = string.Format(Message, 1, "添加成功！");
            return "{" + Message + "}";
        }


        //[HttpPost("AddModbusMeasloc")]
        //public string AddModbusMeasloc(List<ModbusMeasLocDTO> modbusloc)
        //{
        //    string Message = "state:{0},msg:'{1}'";
        //    try
        //    {
        //        var svmlist = SVMManagement.GetMeasLoc_SVMListByTurID(WindTurbineID);
        //        var svmlist_sel = svmlist.Where(item => item.ComponentID == ComponentId && item.SectionName == SectionName).ToList();
        //        if (svmlist_sel.Count > 0)
        //        {
        //            Message = string.Format(Message, 0, "无需重复添加测量位置！");
        //            return "{" + Message + "}";
        //        }

        //        //生产晃动测量位置信息
        //        List<WindTurbineComponent> list = DevTreeManagement.GetComListByTurbineId(WindTurbineID);
        //        //WindTurbineComponent myComponent = list.Find(i => i.ComponentName == "机舱" || i.ComponentName == "塔筒");
        //        WindTurbineComponent myComponent = list.Find(k => k.ComponentID == ComponentId);
        //        if (myComponent == null)
        //        {
        //            Message = string.Format(Message, 0, "无部件信息！");
        //            return "{" + Message + "}";
        //        }
        //        SVMManagement.AutoAddSVMMeasLoc(WindTurbineID, myComponent.ComponentID, SectionName);
        //        #region
        //        LogEntity logEntity = new LogEntity();
        //        logEntity.LogDB = ConstDefine.UserManagementLog;
        //        logEntity.LogTime = DateTime.Now;
        //        logEntity.NodeID = WindTurbineID.ToString();
        //        //logEntity.UserName = Request.Cookies["WindCMSUserName"];
        //        logEntity.OperationDescription
        //            = string.Format("增加_机组SVM({0})", WindTurbineID);
        //        LogManagement.UserlogWrite(logEntity);
        //        #endregion
        //    }
        //    catch (Exception ex)
        //    {
        //        CMSFramework.Logger.Logger.LogErrorMessage("[AddSVM]增加机组SVM失败", ex);

        //    }
        //    Message = string.Format(Message, 1, "添加成功！");
        //    return "{" + Message + "}";
        //}

        /// <summary>
        /// 获取机组实体
        /// </summary>
        /// <param name="_windTurbineId"></param>
        /// <returns></returns>
        public static WindTurbine GetWindTurbine(string _windTurbineId)
        {
            WindTurbine tur = new WindTurbine();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                tur = ctx.DevWindTurbines.FirstOrDefault(p => p.WindTurbineID == _windTurbineId);
                if (tur != null)
                {
                    tur.WTurbineModel = ctx.DevWTurbineModels.Find(tur.WindTurbineModel);
                    tur.DevWindPark = ctx.DevWindParks.Find(tur.WindParkID);
                }
            }
            return tur;
        }

        /// <summary>
        /// 修改机组实体
        /// </summary>
        /// <param name="_windTurbine"></param>
        public static void EditWindTurbine(WindTurbine _windTurbine)
        {
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                ctx.DevWindTurbines.Attach(_windTurbine);
                ctx.Entry(_windTurbine).State = (Microsoft.EntityFrameworkCore.EntityState)EntityState.Modified;
                ctx.SaveChanges();
            }
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                WindDAU dau = ctx.DAUnits.FirstOrDefault(p => p.WindTurbineID == _windTurbine.WindTurbineID);
                if (dau != null)
                {
                    dau.DAUName = _windTurbine.WindTurbineName;
                    dau.MeasDefVersion += 1;
                    ctx.DAUnits.Attach(dau);
                    ctx.Entry(dau).State = (Microsoft.EntityFrameworkCore.EntityState)EntityState.Modified;
                    ctx.SaveChanges();
                }
                SVMUnit svm = ctx.SVMUnits.FirstOrDefault(item => item.AssocWindTurbineID == _windTurbine.WindTurbineID);
                if (svm != null)
                {
                    svm.SVMName = _windTurbine.WindTurbineName;
                    ctx.SVMUnits.Attach(svm);
                    ctx.Entry(svm).State = (Microsoft.EntityFrameworkCore.EntityState)EntityState.Modified;
                    ctx.SaveChanges();
                }
            }
        }
        /// <summary>
        /// 初始化转速测量位置列表（添加振动位置）
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetRotSpdMeasLocList")]
        public IActionResult GetRotSpdMeasLocList(string turbineID)
        {
            List<MeasLoc_RotSpd> rotMeasLocList = DevTreeManagement.GetRotSpdMeasLocListByTurId(turbineID);
            return Ok(rotMeasLocList);
        }

        /// <summary>
        /// 获取机组下部件的名称
        /// </summary>
        /// <param name="turbineid"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetComponentList")]
        public IActionResult GetComPonentListByTurId(string turbineID)
        {
            List<WindTurbineComponent> myList = DevTreeManagement.GetComListByTurbineId(turbineID).OrderBy(item => item.OrderSeq).ToList();
            return Ok(myList);
        }

        /// <summary>
        /// 根據機組ID和部件ID獲取測量位置
        /// </summary>
        /// <param name="TurbineID"></param>
        /// <param name="ComponentID"></param>
        /// <returns></returns>
        public string GetComPonentMeasLocByTurAndCom(string TurbineID, string ComponentID)
        {
            List<MeasLoc_Vib> list = new List<MeasLoc_Vib>();
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                list = ctx.DevMeasLocVibrations.Where(item => item.WindTurbineID == TurbineID && item.ComponentID == ComponentID).ToList();
                List<WindTurbineComponent> component = ctx.DevTurComponents.Where(item => item.WindTurbineID == TurbineID).ToList();
                list.ForEach(item =>
                {
                    item.DevTurComponent = component.Find(c => c.ComponentID == item.ComponentID);
                });
            }
            return list.ToJson();
        }

        /// <summary>
        /// 根据部件名称获取部件截面信息
        /// </summary>
        /// <param name="ComponentName"></param>
        /// <returns></returns>
        /// 
        [HttpGet("GetSectionList")]
        public IActionResult GetSectionListByComponentName(string ComponentName, string turbineID)
        {
            Dictionary<string, string> list = CodeProvide.GetSectionDic(ComponentName);
            string componentId = DevTreeManagement.GetComListByTurbineId(turbineID).Find(item => item.ComponentName == ComponentName).ComponentID;
            List<MeasLoc_Vib> LocList = DevTreeManagement.GetVibMeasLocationByComId(componentId);
            List<MeasLoc_Vib> Loc_List = LocList.FindAll(item => item.DevTurComponent.ComponentName == ComponentName).GroupBy(item => item.SectionName).Select(item => item.First()).ToList();
            foreach (var data in Loc_List)
            {
                if (list.Keys.Contains(data.SectionName))
                    continue;
                else
                {
                    string key = data.MeasLocationID.Replace(data.ComponentID, "");
                    key = key.Substring(0, key.Length - 1);
                    list.Add(data.SectionName, key);
                }
            }
            return Ok(list);
        }
        /// <summary>
        /// 获取方向数据
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet("GetOrientation")]
        public IActionResult GetOrientationDic()
        {
            return new JsonResult(CodeProvide.GetOrientationDic());
        }

        private int GetSecCode(List<MeasLoc_Vib> list, string SecName)
        {
            int code = 0;
            if (list.Count == 0)
                return 1;
            MeasLoc_Vib SecNameLoc = list.Find(item => item.SectionName == SecName);
            if (SecNameLoc != null)
            {   //如果截面相同，就是添加不同的方向，直接把原截面code返回
                string temp = SecNameLoc.MeasLocationID.Substring(SecNameLoc.ComponentID.Length + 3);
                int SectionCode = int.Parse(temp.Substring(0, temp.Length - 1));
                return SectionCode;
            }
            Dictionary<string, string> Diclist = CodeProvide.GetSectionDic(list[0].DevTurComponent.ComponentName);
            //自定义截面测量位置列表
            //List<MeasLoc_Vib> Loc_List = new List<MeasLoc_Vib>();
            //获取自定义截面列表
            //list.FindAll(item => item.SectionName != SecName).ForEach(item =>
            //{
            //    if (!Diclist.Keys.Contains(item.SectionName) && Loc_List.Find(sec => sec.SectionName == item.SectionName) == null)
            //        Loc_List.Add(item);
            //});
            //Loc_List = list.FindAll(item => item.SectionName != SecName);
            foreach (var data in list.FindAll(item => item.SectionName != SecName))
            {
                if (Diclist.Keys.Contains(data.SectionName))
                    continue;
                string temp = data.MeasLocationID.Substring(data.ComponentID.Length + 3);
                int SectionCode = int.Parse(temp.Substring(0, temp.Length - 1));
                code = SectionCode > code ? SectionCode : code;
            }
            //如果有删除的截面数据，就以最后一次添加的截面code为主
            //if (code <= Loc_List.Count)
            //{
            //    code = Loc_List.Count + 1;
            //}
            //else {
            //    code++;
            //} 
            code++;
            return code;
        }

        /// <summary>
        /// 暂时先复制，后续整合
        /// </summary>
        /// <param name="list"></param>
        /// <param name="SecName"></param>
        /// <returns></returns>
        private int GetProcessSecCode(List<MeasLoc_VoltageCurrent> list, string SecName)
        {
            int code = 0;
            if (list.Count == 0)
                return 1;
            MeasLoc_VoltageCurrent SecNameLoc = list.Find(item => item.SectionName == SecName);
            if (SecNameLoc != null)
            {   //如果截面相同，就是添加不同的方向，直接把原截面code返回
                string temp = SecNameLoc.MeasLocationID.Substring(SecNameLoc.ComponentID.Length + 3);
                int SectionCode = int.Parse(temp.Substring(0, temp.Length - 1));
                return SectionCode;
            }
            Dictionary<string, string> Diclist = CodeProvide.GetSectionDic(list[0].DevTurComponent.ComponentName);
            //自定义截面测量位置列表
            //List<MeasLoc_Vib> Loc_List = new List<MeasLoc_Vib>();
            //获取自定义截面列表
            //list.FindAll(item => item.SectionName != SecName).ForEach(item =>
            //{
            //    if (!Diclist.Keys.Contains(item.SectionName) && Loc_List.Find(sec => sec.SectionName == item.SectionName) == null)
            //        Loc_List.Add(item);
            //});
            //Loc_List = list.FindAll(item => item.SectionName != SecName);
            foreach (var data in list.FindAll(item => item.SectionName != SecName))
            {
                if (Diclist.Keys.Contains(data.SectionName))
                    continue;
                string temp = data.MeasLocationID.Substring(data.ComponentID.Length + 3);
                int SectionCode = int.Parse(temp.Substring(0, temp.Length - 1));
                code = SectionCode > code ? SectionCode : code;
            }
            //如果有删除的截面数据，就以最后一次添加的截面code为主
            //if (code <= Loc_List.Count)
            //{
            //    code = Loc_List.Count + 1;
            //}
            //else {
            //    code++;
            //} 
            code++;
            return code;
        }

        //振动位置编辑
        public string EditVibList(string _vibMeasLocID, string windTurbineID, string MeasLocName,
            string ComponentName, string ComponentId, string SectionName, string Orientation,
            float GearRatio, string isAddType,
            string SvmName, string ModbusAddress)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                MeasLoc_Vib measLoc_Vib = null;
                if (isAddType == "1")
                {
                    string MeasLocationID = IDProvide.GetVibMeasLocID(windTurbineID, ComponentName, SectionName, Orientation);
                    List<MeasLoc_Vib> list = DevTreeManagement.GetVibMeasLocationByTurId(windTurbineID);
                    //GH0010018叶片1叶片截面H
                    string OrientationCode = MeasLocationID.Substring(MeasLocationID.Length - 1, 1);
                    if (MeasLocationID.IndexOf(ComponentName) > -1)
                    {
                        //说明部件是自定义的
                        //List<WindTurbineComponent> comList = DevTreeManagement.GetComListByTurbineId(windTurbineID);
                        //WindTurbineComponent component = comList.Find(item => item.ComponentName == ComponentName);
                        MeasLocationID = ComponentId + "SEC" + GetSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == ComponentName), SectionName) + OrientationCode;
                    }
                    else
                    {
                        //部件不是自定义，需要判断截面是不是自定义
                        if (MeasLocationID.IndexOf(SectionName) > -1)
                        {
                            MeasLocationID = ComponentId + "SEC" + GetSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == ComponentName), SectionName) + OrientationCode;
                        }
                    }
                    measLoc_Vib = list.Find(item => item.MeasLocationID == MeasLocationID && item.MeasLocName == MeasLocName);
                    if (measLoc_Vib == null || string.IsNullOrEmpty(measLoc_Vib.MeasLocationID))
                    {
                        measLoc_Vib = new MeasLoc_Vib();
                        if (list.Count == 0)
                        {
                            measLoc_Vib.OrderSeq = 1;
                        }
                        else
                        {
                            measLoc_Vib.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;
                        }
                        //measLoc_Vib.MeasLocationID = MeasLocationID;
                    }
                    else
                    {
                        if (SvmName == "-1")
                        {
                            Message = string.Format(Message, 0, "测量位置信息重复，请检查后重新添加！");
                            return "{" + Message + "}";
                        }
                    }
                    measLoc_Vib.MeasLocationID = MeasLocationID;
                }
                else
                {
                    measLoc_Vib = DevTreeManagement.GetVibMeasLocByID(_vibMeasLocID);
                }
                measLoc_Vib.MeasLocName = MeasLocName;
                measLoc_Vib.SectionName = SectionName;
                measLoc_Vib.Orientation = Orientation;
                measLoc_Vib.GearRatio = GearRatio;
                measLoc_Vib.WindTurbineID = windTurbineID;
                measLoc_Vib.ComponentID = ComponentId;
                if (isAddType == "1")
                {
                    if (SvmName == "-1")
                    { //如果不启用用晃度仪，就可以添加振动测量位置信息（机舱和塔筒下可以绑定加速度传感器）
                        DevTreeManagement.AddVibMeasLoc(measLoc_Vib);
                    }
                    //如果没有添加晃度仪，就添加晃度仪信息
                    if (!string.IsNullOrEmpty(SvmName) && SvmName != "-1")
                    {
                        #region 添加晃度仪
                        SVMUnit _svm = new SVMUnit();
                        _svm.AssocWindTurbineID = windTurbineID;
                        _svm.SVMName = SvmName;
                        _svm.ModbusAddress = ModbusAddress;
                        _svm.ComponentID = ComponentId;
                        _svm.SVMID = ModbusAddress;
                        try
                        {
                            SVMManagement.AddSVM(_svm);
                            //获取添加后的晃度仪实体
                            SVMUnit mySVM = SVMManagement.GetSVMById(windTurbineID);
                            if (mySVM == null)
                                return "{" + string.Format(Message, 0, "晃度仪信息添加失败！") + "}";
                            //生产晃动测量位置信息
                            //SVMManagement.AutoAddSVMMeasLoc(windTurbineID, ComponentName);
                            // 添加晃度仪寄存器
                            List<MeasLoc_SVM> measloc_svmList = SVMManagement.GetNotUsedMeasLoc_SVMList(windTurbineID);
                            List<SVMRegister> RegisterList = new List<SVMRegister>();
                            foreach (MeasLoc_SVM measloc in measloc_svmList)
                            {
                                SVMRegister register = new SVMRegister();
                                register.AssocWindTurbineID = mySVM.AssocWindTurbineID;
                                register.SVMRegisterAdr = SVMManagement.GetRegisterAdr(measloc);
                                register.ComponentID = measloc.ComponentID;
                                register.SVMMeasLocId = measloc.MeasLocationID;
                                register.RegisterType = (int)measloc.ParamType;
                                register.SVMID = ModbusAddress;
                                RegisterList.Add(register);
                            }
                            SVMManagement.AddSVMRegister(RegisterList);
                            #region
                            LogEntity logEntity = new LogEntity();
                            logEntity.LogDB = ConstDefine.UserManagementLog;
                            logEntity.LogTime = DateTime.Now;
                            logEntity.NodeID = SvmName;
                            logEntity.UserName = Request.Cookies["WindCMSUserName"];
                            logEntity.OperationDescription
                                = string.Format("增加_机组SVM({0})", SvmName);
                            LogManagement.UserlogWrite(logEntity);
                            #endregion
                        }
                        catch (Exception ex)
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage("[EditVibList]增加机组SVM失败", ex);
                        }
                        #endregion
                    }
                }
                else
                {
                    DevTreeManagement.EditVibMeasLoc(measLoc_Vib);
                }
                Message = string.Format(Message, 1, "");
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditVibList]编辑振动测量位置失败", ex);
                Message = string.Format(Message, 0, ex.Message);
                return "{" + Message + "}";
            }
            return "{" + Message + "}";
        }

        /// <summary>
        /// 删除振动测量位置
        /// </summary>
        /// <param name="_vibMeasLocID"></param>
        /// <returns></returns>
        public string DeleteVib(string _vibMeasLocID)
        {
            string Message = "state:{0},msg:'{1}'";
            MeasLoc_Vib measLoc_Vib = DevTreeManagement.GetVibMeasLocByID(_vibMeasLocID);
            measLoc_Vib.MeasLocationID = _vibMeasLocID;
            try
            {
                WindDAU data = DauManagement.GetDAUById(measLoc_Vib.WindTurbineID);
                if (data != null)
                {
                    DAUChannelV2 Channel = data.DAUChannelList.Find(item => item.MeasLocVibID == _vibMeasLocID);
                    if (Channel != null)
                    {
                        Message = string.Format(Message, 0, "振动通道已绑定，请删除通道后再试！");
                        return "{" + Message + "}";
                    }
                }
                if (DevTreeManagement.IsVibMeasLocUsed(_vibMeasLocID))
                {
                    Message = string.Format(Message, 0, "振动测量位置正在被波形定义使用");
                }
                else
                {
                    List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(measLoc_Vib.WindTurbineID, EnumMeasLocType.VibAlarmDef);
                    AlarmDefinition myDefData = alarmDef.Find(i => i.MeasLocationID == _vibMeasLocID);
                    if (myDefData != null)
                    {
                        Message = string.Format(Message, 0, "报警定义中正在使用该测量位置，请删除报警定义后再试！");
                        return "{" + Message + "}";
                    }
                    DevTreeManagement.DeleteVibMeasLoc(measLoc_Vib.MeasLocationID);
                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = _vibMeasLocID.ToString();
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("删除_机组振动测量位置({0})", _vibMeasLocID);
                    LogManagement.UserlogWrite(logEntity);
                    Message = string.Format(Message, 1, "");
                    #endregion
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteVib]删除机组振动测量位置失败", ex);
                Message = string.Format(Message, 0, ex.Message);
            }
            return "{" + Message + "}";
        }

        /// <summary>
        /// 过程量位置编辑
        /// </summary>
        /// <param name="_vibMeasLocID"></param>
        /// <param name="windTurbineID"></param>
        /// <param name="MeasLocName"></param>
        /// <param name="ComponentName"></param>
        /// <param name="ComponentId"></param>
        /// <param name="SectionName"></param>
        /// <param name="Orientation"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public string EditProcessList(string _vibMeasLocID, string windTurbineID, string MeasLocName,
            string ComponentName, string ComponentId, string SectionName, string Orientation, string isAddType)
        {
            string Message = "state:{0},msg:'{1}'";
            try
            {
                MeasLoc_VoltageCurrent measLoc_Vib = null;
                if (isAddType == "1")
                {
                    //string MeasLocationID = IDProvide.GetVibMeasLocID(windTurbineID, ComponentName, SectionName, Orientation);
                    string MeasLocationID = CommonUtility.GetVibMeasLocID(windTurbineID, ComponentName, SectionName, Orientation);
                    List<MeasLoc_VoltageCurrent> list = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(windTurbineID);
                    //GH0010018叶片1叶片截面H
                    string OrientationCode = MeasLocationID.Substring(MeasLocationID.Length - 1, 1);
                    if (MeasLocationID.IndexOf(ComponentName) > -1)
                    {
                        //说明部件是自定义的
                        //List<WindTurbineComponent> comList = DevTreeManagement.GetComListByTurbineId(windTurbineID);
                        //WindTurbineComponent component = comList.Find(item => item.ComponentName == ComponentName);
                        MeasLocationID = ComponentId + "SEC" + GetProcessSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == ComponentName), SectionName) + OrientationCode;
                    }
                    else
                    {
                        //部件不是自定义，需要判断截面是不是自定义
                        if (MeasLocationID.IndexOf(SectionName) > -1)
                        {
                            MeasLocationID = ComponentId + "SEC" + GetProcessSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == ComponentName), SectionName) + OrientationCode;
                        }
                    }
                    measLoc_Vib = list.Find(item => item.MeasLocationID == MeasLocationID && item.MeasLocName == MeasLocName);
                    if (measLoc_Vib == null || string.IsNullOrEmpty(measLoc_Vib.MeasLocationID))
                    {
                        measLoc_Vib = new MeasLoc_VoltageCurrent();
                        if (list.Count == 0)
                        {
                            measLoc_Vib.OrderSeq = 1;
                        }
                        else
                        {
                            measLoc_Vib.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;
                        }
                        //measLoc_Vib.MeasLocationID = MeasLocationID;
                    }
                    measLoc_Vib.MeasLocationID = MeasLocationID;
                }
                else
                {
                    measLoc_Vib = DevTreeManagement.GetVoltageCurrentMeasLocByID(_vibMeasLocID);
                }
                measLoc_Vib.MeasLocName = MeasLocName;
                measLoc_Vib.SectionName = SectionName;
                measLoc_Vib.Orientation = Orientation;
                measLoc_Vib.WindTurbineID = windTurbineID;
                measLoc_Vib.ComponentID = ComponentId;
                if (isAddType == "1")
                {
                    DevTreeManagement.AddVoltageCurrentMeasLoc(measLoc_Vib);

                }
                else
                {
                    DevTreeManagement.EditVoltageCurrentMeasLoc(measLoc_Vib);
                }
                Message = string.Format(Message, 1, "");
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditVibList]编辑电流电压测量位置失败", ex);
                Message = string.Format(Message, 0, ex.Message);
                return "{" + Message + "}";
            }
            return "{" + Message + "}";
        }



        /// <summary>
        /// 删除过程量测量位置
        /// </summary>
        /// <param name="_vibMeasLocID"></param>
        /// <returns></returns>
        public string DeleteProcess(string _vibMeasLocID)
        {
            string Message = "state:{0},msg:'{1}'";
            MeasLoc_VoltageCurrent measLoc_Vib = DevTreeManagement.GetVoltageCurrentMeasLocByID(_vibMeasLocID);
            measLoc_Vib.MeasLocationID = _vibMeasLocID;
            try
            {
                //TODO:修改删除电流电压测量位置逻辑
                WindDAU data = DauManagement.GetDAUById(measLoc_Vib.WindTurbineID);
                if (data != null)
                {
                    DAUChannel_VoltageCurrent Channel = data.VoltageCurrentList.Find(item => item.MeasLoc_ProcessId == _vibMeasLocID);
                    if (Channel != null)
                    {
                        Message = string.Format(Message, 0, "通道已绑定，请删除通道后再试！");
                        return "{" + Message + "}";
                    }
                }
                if (DevTreeManagement.IsVoltageCurrentMeasLocUsed(_vibMeasLocID))
                {
                    Message = string.Format(Message, 0, "测量位置正在被波形定义使用");
                }
                else
                {
                    //List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(measLoc_Vib.WindTurbineID, EnumMeasLocType.VibAlarmDef);
                    //AlarmDefinition myDefData = alarmDef.Find(i => i.MeasLocationID == _vibMeasLocID);
                    //if (myDefData != null)
                    //{
                    //    Message = string.Format(Message, 0, "报警定义中正在使用该测量位置，请删除报警定义后再试！");
                    //    return "{" + Message + "}";
                    //}
                    DevTreeManagement.DeleteVoltageCurrentsMeasLoc(measLoc_Vib.MeasLocationID);
                    #region
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = _vibMeasLocID.ToString();
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("删除_机组电压电流测量位置({0})", _vibMeasLocID);
                    LogManagement.UserlogWrite(logEntity);
                    Message = string.Format(Message, 1, "");
                    #endregion
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteVib]删除机组振动测量位置失败", ex);
                Message = string.Format(Message, 0, ex.Message);
            }
            return "{" + Message + "}";
        }


        #endregion

        #region 振动测量位置

        /// <summary>
        /// 批量添加振动测量位置
        /// </summary>
        /// <param name="request">批量添加振动测量位置请求</param>
        /// <returns></returns>
        [HttpPost("AddVibMeasLocs")]
        [BatchOperation(nameof(BatchAddVibMeasLocs))]
        public IActionResult AddVibMeasLocs([FromBody] BatchAddVibMeasLocsRequest request)
        {
            var result = new List<string>();
            try
            {
                List<MeasLoc_Vib> locs = new();
                foreach (var measLoc in request.SourceData)
                {

                    MeasLoc_Vib? measLoc_Vib = null;
                    // 生成测量位置ID
                    string MeasLocationID = IDProvide.GetVibMeasLocID(measLoc.WindTurbineID, measLoc.ComponentName, measLoc.SectionName, measLoc.Orientation);

                    // 检查是否已存在相同测量位置
                    var list = DevTreeManagement.GetVibMeasLocationByTurId(measLoc.WindTurbineID);

                    //GH0010018叶片1叶片截面H
                    string OrientationCode = MeasLocationID.Substring(MeasLocationID.Length - 1, 1);
                    if (MeasLocationID.IndexOf(measLoc.ComponentName) > -1)
                    {
                        //说明部件是自定义的
                        MeasLocationID = measLoc.ComponentID + "SEC" + GetSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == measLoc.ComponentName), measLoc.SectionName) + OrientationCode;
                    }
                    else
                    {
                        //部件不是自定义，需要判断截面是不是自定义
                        if (MeasLocationID.IndexOf(measLoc.SectionName) > -1)
                        {
                            MeasLocationID = measLoc.ComponentID + "SEC" + GetSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == measLoc.ComponentName), measLoc.SectionName) + OrientationCode;
                        }
                    }
                    //measLoc_Vib = list.Find(item => item.MeasLocationID == MeasLocationID && item.MeasLocName == measLoc.MeasLocName);
                    measLoc_Vib = list.Find(item => item.MeasLocationID == MeasLocationID);
                    if (measLoc_Vib == null || string.IsNullOrEmpty(measLoc_Vib.MeasLocationID))
                    {
                        measLoc_Vib = new MeasLoc_Vib();
                        if (list.Count == 0)
                        {
                            measLoc_Vib.OrderSeq = 1;
                        }
                        else
                        {
                            measLoc_Vib.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;
                        }
                    }
                    else
                    {
                        return Ok(ApiResponse<string>.Error($"测量位置 {measLoc.MeasLocName} 名称已存在"));
                    }
                    measLoc_Vib.MeasLocationID = MeasLocationID;

                    // 设置OrderSeq
                    if (list.Count == 0)
                        measLoc.OrderSeq = 1;
                    else
                        measLoc.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;

                    measLoc_Vib.MeasLocName = measLoc.MeasLocName;
                    measLoc_Vib.SectionName = measLoc.SectionName;
                    measLoc_Vib.Orientation = measLoc.Orientation;
                    measLoc_Vib.GearRatio = measLoc.GearRatio;
                    measLoc_Vib.WindTurbineID = measLoc.WindTurbineID;
                    measLoc_Vib.ComponentID = measLoc.ComponentID;

                    locs.Add(measLoc_Vib);
                    result.Add($"测量位置 {measLoc.MeasLocName} 添加成功");
                }
                DevTreeManagement.AddVibMeasLocRange(locs);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddVibMeasLocs]批量添加振动测量位置失败", ex);
                return Ok(ApiResponse<string>.Error($"批量添加振动测量位置失败"));
            }

            return Ok(ApiResponse<List<string>>.Success(result));
        }

        /// <summary>
        /// 编辑单个振动测量位置
        /// </summary>
        /// <param name="request">批量编辑振动测量位置请求</param>
        /// <returns></returns>
        [HttpPost("EditVibMeasLoc")]
        [BatchOperation(nameof(BatchEditVibMeasLoc))]
        public IActionResult EditVibMeasLoc([FromBody] BatchEditVibMeasLocRequest request)
        {
            try
            {
                var measLoc_Vib = DevTreeManagement.GetVibMeasLocByID(request.SourceData.MeasLocationID);
                if (measLoc_Vib == null)
                {
                    return Ok(ApiResponse<string>.Error($"未找到测来位置！"));
                }
                measLoc_Vib.MeasLocName = request.SourceData.MeasLocName;
                measLoc_Vib.SectionName = request.SourceData.SectionName;
                measLoc_Vib.Orientation = request.SourceData.Orientation;
                measLoc_Vib.GearRatio = request.SourceData.GearRatio;
                measLoc_Vib.WindTurbineID = request.SourceData.WindTurbineID;
                measLoc_Vib.ComponentID = request.SourceData.ComponentID;
                DevTreeManagement.EditVibMeasLoc(measLoc_Vib);
                return Ok(ApiResponse<string>.Success("编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditVibMeasLoc]编辑振动测量位置失败", ex);
                return Ok(ApiResponse<string>.Error($"编辑失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量删除振动测量位置
        /// </summary>
        /// <param name="request">批量删除振动测量位置请求</param>
        /// <returns></returns>
        [HttpPost("DeleteVibMeasLocs")]
        [BatchOperation(nameof(BatchDeleteVibMeasLocs))]
        public IActionResult DeleteVibMeasLocs([FromBody] BatchDeleteVibMeasLocsRequest request)
        {
            // 在删除前，先查询并缓存原始记录信息，供批量操作使用
            var originalRecords = new List<MeasLoc_Vib>();
            foreach (var id in request.SourceData)
            {
                try
                {
                    var record = DevTreeManagement.GetVibMeasLocByID(id);
                    if (record != null)
                    {
                        originalRecords.Add(record);
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[DeleteVibMeasLocs]获取原始记录失败，ID: {id}", ex);
                }
            }

            // 将原始记录缓存到HttpContext中，供批量操作使用
            HttpContext.Items["OriginalMeasLocRecords"] = originalRecords;

            var result = new List<string>();
            foreach (var id in request.SourceData)
            {
                try
                {
                    var measLoc_Vib = DevTreeManagement.GetVibMeasLocByID(id);
                    if (measLoc_Vib == null)
                    {
                        result.Add($"测量位置ID {id} 不存在");
                        continue;
                    }
                    // 校验是否可删除（如通道绑定、报警定义等）
                    WindDAU data = DauManagement.GetDAUById(measLoc_Vib.WindTurbineID);
                    if (data != null)
                    {
                        DAUChannelV2 Channel = data.DAUChannelList.Find(item => item.MeasLocVibID == id);
                        if (Channel != null)
                        {
                            result.Add($"测量位置ID {id} 振动通道已绑定，请删除通道后再试！");
                            continue;
                        }
                    }
                    if (DevTreeManagement.IsVibMeasLocUsed(id))
                    {
                        result.Add($"测量位置ID {id} 正在被波形定义使用");
                        continue;
                    }
                    List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(measLoc_Vib.WindTurbineID, EnumMeasLocType.VibAlarmDef);
                    AlarmDefinition myDefData = alarmDef.Find(i => i.MeasLocationID == id);
                    if (myDefData != null)
                    {
                        result.Add($"测量位置ID {id} 报警定义中正在使用该测量位置，请删除报警定义后再试！");
                        continue;
                    }
                    DevTreeManagement.DeleteVibMeasLoc(measLoc_Vib.MeasLocationID);
                    result.Add($"测量位置ID {id} 删除成功");
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage("[DeleteVibMeasLocs]批量删除振动测量位置失败", ex);
                    result.Add($"测量位置ID {id} 删除失败: {ex.Message}");
                }
            }
            return Ok(ApiResponse<List<string>>.Success(result));
        }

        /// <summary>
        /// 批量添加电流电压测量位置
        /// </summary>
        /// <param name="measLocs">振动测量位置列表</param>
        /// <returns></returns>
        [HttpPost("AddProcessMeasLocs")]
        [BatchOperation(nameof(BatchAddProcessMeasLocs))]
        public IActionResult AddProcessMeasLocs([FromBody] BatchAddProcessMeasLocsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("添加列表不能为空"));

            var result = new List<string>();
            try
            {
                List<MeasLoc_VoltageCurrent> locs = new();
                foreach (var measLoc in request.SourceData)
                {

                    MeasLoc_VoltageCurrent? measLoc_Vib = null;
                    // 生成测量位置ID
                    string MeasLocationID = IDProvide.GetVibMeasLocID(measLoc.WindTurbineID, measLoc.ComponentName, measLoc.SectionName, measLoc.Orientation);

                    // 检查是否已存在相同测量位置
                    var list = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(measLoc.WindTurbineID);

                    //GH0010018叶片1叶片截面H
                    string OrientationCode = MeasLocationID.Substring(MeasLocationID.Length - 1, 1);
                    if (MeasLocationID.IndexOf(measLoc.ComponentName) > -1)
                    {
                        //说明部件是自定义的
                        MeasLocationID = measLoc.ComponentID + "SEC" + GetProcessSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == measLoc.ComponentName), measLoc.SectionName) + OrientationCode;
                    }
                    else
                    {
                        //部件不是自定义，需要判断截面是不是自定义
                        if (MeasLocationID.IndexOf(measLoc.SectionName) > -1)
                        {
                            MeasLocationID = measLoc.ComponentID + "SEC" + GetProcessSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == measLoc.ComponentName), measLoc.SectionName) + OrientationCode;
                        }
                    }
                    measLoc_Vib = list.Find(item => item.MeasLocationID == MeasLocationID && item.MeasLocName == measLoc.MeasLocName);
                    if (measLoc_Vib == null || string.IsNullOrEmpty(measLoc_Vib.MeasLocationID))
                    {
                        measLoc_Vib = new MeasLoc_VoltageCurrent();
                        if (list.Count == 0)
                        {
                            measLoc_Vib.OrderSeq = 1;
                        }
                        else
                        {
                            measLoc_Vib.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;
                        }
                    }
                    else
                    {
                        return Ok(ApiResponse<string>.Error($"测量位置 {measLoc.MeasLocName} 名称已存在"));
                    }
                    measLoc_Vib.MeasLocationID = MeasLocationID;

                    // 设置OrderSeq
                    if (list.Count == 0)
                        measLoc.OrderSeq = 1;
                    else
                        measLoc.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;

                    measLoc_Vib.MeasLocName = measLoc.MeasLocName;
                    measLoc_Vib.SectionName = measLoc.SectionName;
                    measLoc_Vib.Orientation = measLoc.Orientation;
                    measLoc_Vib.WindTurbineID = measLoc.WindTurbineID;
                    measLoc_Vib.ComponentID = measLoc.ComponentID;

                    locs.Add(measLoc_Vib);
                    result.Add($"测量位置 {measLoc.MeasLocName} 添加成功");
                }
                DevTreeManagement.AddVoltageCurrentMeasLocRange(locs);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddVibMeasLocs]批量添加振动测量位置失败", ex);
                return Ok(ApiResponse<string>.Error($"批量添加振动测量位置失败"));
            }

            return Ok(ApiResponse<List<string>>.Success(result));
        }

        /// <summary>
        /// 编辑单个电流电压测量位置
        /// </summary>
        /// <param name="measLoc">振动测量位置</param>
        /// <returns></returns>
        [HttpPost("EditProcessMeasLoc")]
        public IActionResult EditProcessMeasLoc([FromBody] MeasLoc_Vib measLoc)
        {
            try
            {
                var measLoc_Vib = DevTreeManagement.GetVoltageCurrentMeasLocByID(measLoc.MeasLocationID);
                if (measLoc_Vib == null)
                {
                    return Ok(ApiResponse<string>.Error($"未找到测来位置！"));
                }
                measLoc_Vib.MeasLocName = measLoc.MeasLocName;
                measLoc_Vib.SectionName = measLoc.SectionName;
                measLoc_Vib.Orientation = measLoc.Orientation;
                measLoc_Vib.WindTurbineID = measLoc.WindTurbineID;
                measLoc_Vib.ComponentID = measLoc.ComponentID;
                DevTreeManagement.EditVoltageCurrentMeasLoc(measLoc_Vib);
                return Ok(ApiResponse<string>.Success("编辑成功"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditVibMeasLoc]编辑测量位置失败", ex);
                return Ok(ApiResponse<string>.Error($"编辑失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 批量删除振动测量位置
        /// </summary>
        /// <param name="_vibMeasLocIDs">振动测量位置ID列表</param>
        /// <returns></returns>
        [HttpPost("DeleteProcessMeasLocs")]
        [BatchOperation(nameof(BatchDeleteProcessMeasLocs))]
        public IActionResult DeleteProcessMeasLocs([FromBody] BatchDeleteProcessMeasLocsRequest request)
        {
            if (request?.SourceData == null || request.SourceData.Count == 0)
                return Ok(ApiResponse<string>.Error("删除列表不能为空"));

            // 在删除前，先查询并缓存原始记录信息，供批量操作使用
            var originalRecords = new List<MeasLoc_VoltageCurrent>();
            foreach (var id in request.SourceData)
            {
                try
                {
                    var record = DevTreeManagement.GetVoltageCurrentMeasLocByID(id);
                    if (record != null)
                    {
                        originalRecords.Add(record);
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[DeleteProcessMeasLocs]获取原始记录失败，ID: {id}", ex);
                }
            }

            // 将原始记录缓存到HttpContext中，供批量操作使用
            HttpContext.Items["OriginalProcessMeasLocRecords"] = originalRecords;

            var result = new List<string>();
            foreach (var id in request.SourceData)
            {
                try
                {
                    var measLoc_Vib = DevTreeManagement.GetVoltageCurrentMeasLocByID(id);
                    if (measLoc_Vib == null)
                    {
                        result.Add($"测量位置ID {id} 不存在");
                        continue;
                    }
                    // 校验是否可删除（如通道绑定、报警定义等）
                    WindDAU data = DauManagement.GetDAUById(measLoc_Vib.WindTurbineID);
                    if (data != null)
                    {
                        DAUChannel_VoltageCurrent Channel = data.VoltageCurrentList.Find(item => item.MeasLoc_ProcessId == id);
                        if (Channel != null)
                        {
                            result.Add($"测量位置ID {id} 振动通道已绑定，请删除通道后再试！");
                            continue;
                        }
                    }
                    if (DevTreeManagement.IsVoltageCurrentMeasLocUsed(id))
                    {
                        result.Add($"测量位置ID {id} 正在被波形定义使用");
                        continue;
                    }
                    List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(measLoc_Vib.WindTurbineID, EnumMeasLocType.VibAlarmDef);
                    AlarmDefinition myDefData = alarmDef.Find(i => i.MeasLocationID == id);
                    if (myDefData != null)
                    {
                        result.Add($"测量位置ID {id} 报警定义中正在使用该测量位置，请删除报警定义后再试！");
                        continue;
                    }
                    DevTreeManagement.DeleteVoltageCurrentsMeasLoc(measLoc_Vib.MeasLocationID);
                    result.Add($"测量位置ID {id} 删除成功");
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage("[DeleteVibMeasLocs]批量删除测量位置失败", ex);
                    result.Add($"测量位置ID {id} 删除失败: {ex.Message}");
                }
            }
            return Ok(ApiResponse<List<string>>.Success(result));
        }

        #endregion

        #region Modbus测量位置管理

        /// <summary>
        /// 批量添加Modbus测量位置
        /// </summary>
        /// <param name="measLocList">测量位置列表</param>
        /// <returns></returns>
        [HttpPost("AddModbusMeasloc")]
        [BatchOperation(nameof(BatchAddModbusMeasLoc))]
        public IActionResult AddModbusMeaslocBatch([FromBody] BatchAddModbusMeasLocRequest request)
        {
            bool isSuccess = true;
            try
            {
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    return Ok(ApiResponse<string>.Error("测量位置列表不能为空"));
                }

                var results = new List<string>();

                foreach (var measLoc in request.SourceData)
                {
                    try
                    {
                        if (string.IsNullOrEmpty(measLoc.WindTurbineID) ||
                            string.IsNullOrEmpty(measLoc.MeasLocName) ||
                            string.IsNullOrEmpty(measLoc.MeasType))
                        {
                            isSuccess = false;
                            results.Add($"测量位置 {measLoc.MeasLocName} 参数不完整");
                            continue;
                        }

                        // 根据MeasType判断存储位置
                        if (measLoc.MeasType.ToLower() == "svm")
                        {
                            if(measLoc.PhysicalType == null || string.IsNullOrEmpty(measLoc.PhysicalType))
                            {
                                isSuccess = false;
                                results.Add($"测量位置 {measLoc.MeasLocName} 参数不完整");
                                continue;
                            }
                            // SVM测量位置存储到DevMeasLocSVMs表
                            var svmMeasLoc = new MeasLoc_SVM
                            {
                                MeasLocationID = AppFramework.IDUtility.IDProvide.GetSVMLocID("", measLoc.ComponentID, "", measLoc.MeasLocName),
                                WindTurbineID = measLoc.WindTurbineID,
                                MeasLocName = measLoc.MeasLocName,
                                SectionName = measLoc.SectionName,
                                ParamType = SVMManagement.GetParamType(measLoc.PhysicalType),
                                ComponentID = measLoc.ComponentID,
                                Orientation = measLoc.Orientation,
                            };

                            // 使用SVM管理类添加
                            SVMManagement.AddSVMMeasLoc(svmMeasLoc.WindTurbineID, new List<MeasLoc_SVM>() { svmMeasLoc });
                            results.Add($"SVM测量位置 {measLoc.MeasLocName} 添加成功");
                        }
                        else
                        {
                            // 其他类型测量位置存储到MeasLoc_Modbus表（通过DevContext）
                            // 使用默认值生成ID，如果参数为空
                            string componentName = measLoc.ComponentID;
                            string sectionName = measLoc.SectionName;
                            string orientation = measLoc.Orientation;

                            string measLocationId = CommonUtility.GetVibMeasLocID(
                                "",
                                componentName,
                                sectionName,
                                orientation);

                           
                            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                            {
                                // 判断测量位置是否重复
                                var _measloc = ctx.MeasLoc_Modbus.FirstOrDefault(t => t.MeasLocationID == measLocationId);
                                if (_measloc != null)
                                {
                                    isSuccess = false;
                                    results.Add($"Modbus测量位置 {measLoc.MeasLocName} 重复，跳过添加！");
                                    continue;
                                }

                                // 创建MeasLoc_Vib实体（作为Modbus测量位置的存储）
                                var modbusLoc = new MeasLoc_Modbus
                                {
                                    MeasLocationID = measLocationId,
                                    WindTurbineID = measLoc.WindTurbineID,
                                    MeasLocName = measLoc.MeasLocName,
                                    SectionName = measLoc.SectionName,
                                    Orientation = measLoc.Orientation,
                                    ComponentID = measLoc.ComponentID,
                                    OrderSeq = 1
                                };

                                ctx.MeasLoc_Modbus.Add(modbusLoc);
                                ctx.SaveChanges();
                            }

                            results.Add($"Modbus测量位置 {measLoc.MeasLocName} 添加成功");
                        }
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[AddModbusMeasloc]添加测量位置失败: {measLoc.MeasLocName}", ex);
                        results.Add($"测量位置 {measLoc.MeasLocName} 添加失败: {ex.Message}");
                        return Ok(ApiResponse<string>.Error($"批量添加失败: {ex.Message}"));
                    }
                }

                if (isSuccess)
                {
                    return Ok(ApiResponse<string>.Success("OK"));
                }
                else
                {
                    return Ok(ApiResponse<string>.Error(string.Join(",",results)));
                }
                
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddModbusMeasloc]批量添加Modbus测量位置失败", ex);
                return Ok(ApiResponse<string>.Error($"批量添加失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取SVM和Modbus测量位置列表
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        [HttpGet("GetModbusMeasLocList")]
        public IActionResult GetModbusMeasLocList(string turbineID)
        {
            try
            {
                if (string.IsNullOrEmpty(turbineID))
                {
                    return Ok(ApiResponse<List<UnifiedMeasLocDTO>>.Error("机组ID不能为空"));
                }

                var result = new List<UnifiedMeasLocDTO>();

                // 获取SVM测量位置
                try
                {
                    var svmList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
                    if (svmList != null)
                    {
                        foreach (var svm in svmList)
                        {
                            result.Add(new UnifiedMeasLocDTO
                            {
                                MeasLocationID = svm.MeasLocationID,
                                WindTurbineID = svm.WindTurbineID,
                                MeasLocName = svm.MeasLocName,
                                SectionName = svm.SectionName,
                                Orientation = svm.Orientation,
                                ComponentID = svm.ComponentID,
                                MeasType = "svm",
                                OrderSeq = 1,
                                ComponentName = DevTreeManagement.GetTurbComponent(svm?.ComponentID)?.ComponentName,
                                PhysicalType = AppFramework.Utility.EnumHelper.GetDescription(svm.ParamType),
                            }) ;
                        }
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage("[GetSVMMeasLocList]获取SVM测量位置失败", ex);
                }

                // 获取Modbus测量位置（从MeasLoc_Vib表）
                try
                {
                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        var modbusList = ctx.MeasLoc_Modbus
                            .Where(item => item.WindTurbineID == turbineID)
                            .ToList();

                        foreach (var modbus in modbusList)
                        {
                            result.Add(new UnifiedMeasLocDTO
                            {
                                MeasLocationID = modbus.MeasLocationID,
                                WindTurbineID = modbus.WindTurbineID,
                                MeasLocName = modbus.MeasLocName,
                                SectionName = modbus.SectionName,
                                Orientation = modbus.Orientation,
                                ComponentID = modbus.ComponentID,
                                MeasType = "modbus",
                                OrderSeq = modbus.OrderSeq,
                                ComponentName = DevTreeManagement.GetTurbComponent(modbus?.ComponentID)?.ComponentName,
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage("[GetSVMMeasLocList]获取Modbus测量位置失败", ex);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetSVMMeasLocList]获取测量位置列表失败", ex);
                return Ok(new List<UnifiedMeasLocDTO>());
            }
        }

        /// <summary>
        /// 批量删除测量位置（同时删除SVM和Modbus测量位置）
        /// </summary>
        /// <param name="deleteRequest">删除请求</param>
        /// <returns></returns>
        [HttpPost("BatchDeleteMeasLoc")]
        [BatchOperation(nameof(BatchDeleteModbusMeasLoc))]
        public IActionResult BatchDeleteMeasLoc([FromBody] BatchDeleteMeasLocRequest request)
        {
            try
            {
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    return Ok(ApiResponse<string>.Error("测量位置ID列表不能为空"));
                }

                // 在删除前，先查询并缓存原始记录信息，供批量操作使用
                var originalSvmRecords = new List<MeasLoc_SVM>();
                var originalModbusRecords = new List<MeasLoc_Modbus>();

                foreach (var id in request.SourceData)
                {
                    try
                    {
                        // 尝试从SVM表查询
                        using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                        {
                            var svmRecord = ctx.DevMeasLocSVMs.FirstOrDefault(x => x.MeasLocationID == id);
                            if (svmRecord != null)
                            {
                                originalSvmRecords.Add(svmRecord);
                            }
                            else
                            {
                                // 尝试从Modbus表查询
                                var modbusRecord = ctx.MeasLoc_Modbus.FirstOrDefault(x => x.MeasLocationID == id);
                                if (modbusRecord != null)
                                {
                                    originalModbusRecords.Add(modbusRecord);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]获取原始记录失败，ID: {id}", ex);
                    }
                }

                // 将原始记录缓存到HttpContext中，供批量操作使用
                HttpContext.Items["OriginalSvmMeasLocRecords"] = originalSvmRecords;
                HttpContext.Items["OriginalModbusMeasLocRecords"] = originalModbusRecords;

                var results = new List<string>();

                foreach (var measLocationId in request.SourceData)
                {
                    try
                    {
                        bool deleted = false;

                        // 尝试从SVM表删除
                        try
                        {
                            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                            {
                                var svmMeasLoc = ctx.DevMeasLocSVMs.FirstOrDefault(x => x.MeasLocationID == measLocationId);
                                if (svmMeasLoc != null)
                                {
                                    ctx.DevMeasLocSVMs.Remove(svmMeasLoc);
                                    ctx.SaveChanges();
                                    results.Add($"SVM测量位置 {measLocationId} 删除成功");
                                    deleted = true;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]删除SVM测量位置失败: {measLocationId}", ex);
                        }

                        // 尝试从Modbus表删除
                        if (!deleted)
                        {
                            try
                            {
                                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                                {
                                    var modbusMeasLoc = ctx.MeasLoc_Modbus.FirstOrDefault(x => x.MeasLocationID == measLocationId);
                                    if (modbusMeasLoc != null)
                                    {
                                        ctx.MeasLoc_Modbus.Remove(modbusMeasLoc);
                                        ctx.SaveChanges();
                                        results.Add($"Modbus测量位置 {measLocationId} 删除成功");
                                        deleted = true;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]删除Modbus测量位置失败: {measLocationId}", ex);
                            }
                        }

                        if (!deleted)
                        {
                            results.Add($"测量位置 {measLocationId} 未找到");
                        }
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]删除测量位置失败: {measLocationId}", ex);
                        results.Add($"测量位置 {measLocationId} 删除失败: {ex.Message}");
                    }
                }

                return Ok(ApiResponse<string>.Success("OK"));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[BatchDeleteMeasLoc]批量删除测量位置失败", ex);
                return Ok(ApiResponse<string>.Error($"批量删除失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取svm测量位置
        /// </summary>
        /// <returns></returns>
        [HttpGet("GetSVMParamType")]
        public IActionResult GetSVMParamType()
        {
            List<KeyValuePair<int, string>> list = new List<KeyValuePair<int, string>>();
            foreach (EnumSVMParamType value in Enum.GetValues(typeof(EnumSVMParamType)))
            {
                list.Add(new KeyValuePair<int, string>((int)value, CommonUtility.GetDecscription(value)));
            }

            return Ok(list);
        }
        #endregion

        #region 批量操作方法

        /// <summary>
        /// 批量添加振动测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddVibMeasLocs(string targetTurbineId, BatchAddVibMeasLocsRequest request)
        {
            var result = new List<string>();

            try
            {
                List<MeasLoc_Vib> locs = new();
                foreach (var measLoc in request.SourceData)
                {
                    // 创建新的测量位置对象，使用目标机组ID
                    var newMeasLoc = new MeaslocDTO
                    {
                        WindTurbineID = targetTurbineId, // 使用目标机组ID
                        MeasLocName = measLoc.MeasLocName,
                        ComponentID = measLoc.ComponentID.Replace(measLoc.WindTurbineID, targetTurbineId),
                        ComponentName = measLoc.ComponentName,
                        SectionName = measLoc.SectionName,
                        Orientation = measLoc.Orientation,
                        GearRatio = measLoc.GearRatio
                    };

                    // 检查是否存在部件
                    var comp = DevTreeManagement.GetTurbComponent(newMeasLoc.ComponentID);
                    if (comp == null)
                    {
                        result.Add($"机组 {targetTurbineId} 部件 {newMeasLoc.ComponentID} 名称不存在");
                        continue;
                    }

                    MeasLoc_Vib? measLoc_Vib = null;
                    // 生成测量位置ID
                    string MeasLocationID = IDProvide.GetVibMeasLocID(newMeasLoc.WindTurbineID, newMeasLoc.ComponentName, newMeasLoc.SectionName, newMeasLoc.Orientation);

                    // 检查是否已存在相同测量位置
                    var list = DevTreeManagement.GetVibMeasLocationByTurId(newMeasLoc.WindTurbineID);

                    //GH0010018叶片1叶片截面H
                    string OrientationCode = MeasLocationID.Substring(MeasLocationID.Length - 1, 1);
                    if (MeasLocationID.IndexOf(newMeasLoc.ComponentName) > -1)
                    {
                        //说明部件是自定义的
                        MeasLocationID = newMeasLoc.ComponentID + "SEC" + GetSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == newMeasLoc.ComponentName), newMeasLoc.SectionName) + OrientationCode;
                    }
                    else
                    {
                        //部件不是自定义，需要判断截面是不是自定义
                        if (MeasLocationID.IndexOf(newMeasLoc.SectionName) > -1)
                        {
                            MeasLocationID = newMeasLoc.ComponentID + "SEC" + GetSecCode(list.FindAll(item => item.DevTurComponent.ComponentName == newMeasLoc.ComponentName), newMeasLoc.SectionName) + OrientationCode;
                        }
                    }

                    measLoc_Vib = list.Find(item => item.MeasLocationID == MeasLocationID);
                    if (measLoc_Vib == null || string.IsNullOrEmpty(measLoc_Vib.MeasLocationID))
                    {
                        measLoc_Vib = new MeasLoc_Vib();
                        if (list.Count == 0)
                        {
                            measLoc_Vib.OrderSeq = 1;
                        }
                        else
                        {
                            measLoc_Vib.OrderSeq = list.OrderByDescending(i => i.OrderSeq).First().OrderSeq + 1;
                        }
                    }
                    else
                    {
                        result.Add($"机组 {targetTurbineId} 测量位置 {newMeasLoc.MeasLocName} 名称已存在");
                        continue;
                    }

                    measLoc_Vib.MeasLocationID = MeasLocationID;
                    measLoc_Vib.MeasLocName = newMeasLoc.MeasLocName;
                    measLoc_Vib.SectionName = newMeasLoc.SectionName;
                    measLoc_Vib.Orientation = newMeasLoc.Orientation;
                    measLoc_Vib.GearRatio = newMeasLoc.GearRatio;
                    measLoc_Vib.WindTurbineID = newMeasLoc.WindTurbineID;
                    measLoc_Vib.ComponentID = newMeasLoc.ComponentID;

                    locs.Add(measLoc_Vib);
                    result.Add($"机组 {targetTurbineId} 测量位置 {newMeasLoc.MeasLocName} 添加成功");
                }

                if (locs.Any())
                {
                    DevTreeManagement.AddVibMeasLocRange(locs);
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddVibMeasLocs]机组 {targetTurbineId} 批量添加振动测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量添加振动测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量编辑振动测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<string> BatchEditVibMeasLoc(string targetTurbineId, BatchEditVibMeasLocRequest request)
        {
            try
            {
                // 根据原始测量位置的相对信息，在目标机组中找到对应的测量位置
                var originalMeasLoc = request.SourceData;

                // 在目标机组中查找对应的测量位置
                var targetMeasLocList = DevTreeManagement.GetVibMeasLocationByTurId(targetTurbineId);
                var targetMeasLoc = targetMeasLocList.Find(item =>
                    item.MeasLocName == originalMeasLoc.MeasLocName ||
                    (
                     item.SectionName == originalMeasLoc.SectionName &&
                     item.Orientation == originalMeasLoc.Orientation));

                if (targetMeasLoc == null)
                {
                    return $"机组 {targetTurbineId} 未找到对应的测量位置";
                }

                // 更新目标测量位置的信息
                targetMeasLoc.MeasLocName = originalMeasLoc.MeasLocName;
                targetMeasLoc.SectionName = originalMeasLoc.SectionName;
                targetMeasLoc.Orientation = originalMeasLoc.Orientation;
                targetMeasLoc.GearRatio = originalMeasLoc.GearRatio;
                targetMeasLoc.ComponentID = originalMeasLoc.ComponentID;

                DevTreeManagement.EditVibMeasLoc(targetMeasLoc);
                return $"机组 {targetTurbineId} 编辑成功";
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditVibMeasLoc]机组 {targetTurbineId} 编辑振动测量位置失败", ex);
                return $"机组 {targetTurbineId} 编辑失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 批量删除振动测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteVibMeasLocs(string targetTurbineId, BatchDeleteVibMeasLocsRequest request)
        {
            var result = new List<string>();

            // 从HttpContext中获取缓存的原始记录信息
            var originalRecords = HttpContext.Items["OriginalMeasLocRecords"] as List<MeasLoc_Vib>;
            if (originalRecords == null || !originalRecords.Any())
            {
                result.Add($"机组 {targetTurbineId} 无法获取原始记录信息，批量删除失败");
                return result;
            }

            // 获取目标机组的所有测量位置
            var targetMeasLocList = DevTreeManagement.GetVibMeasLocationByTurId(targetTurbineId);

            foreach (var originalMeasLoc in originalRecords)
            {
                try
                {

                    // 在目标机组中查找对应的测量位置
                    //var targetMeasLoc = targetMeasLocList.Find(item =>
                    //    item.MeasLocName == originalMeasLoc.MeasLocName ||
                    //    (item.ComponentID == originalMeasLoc.ComponentID &&
                    //     item.SectionName == originalMeasLoc.SectionName &&
                    //     item.Orientation == originalMeasLoc.Orientation));

                    var targetMeasLoc = targetMeasLocList.Find(item =>
                        item.MeasLocName == originalMeasLoc.MeasLocName);

                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组 {targetTurbineId} 未找到对应的测量位置 {originalMeasLoc.MeasLocName}");
                        continue;
                    }

                    // 校验是否可删除（如通道绑定、报警定义等）
                    WindDAU data = DauManagement.GetDAUById(targetTurbineId);
                    if (data != null)
                    {
                        DAUChannelV2 Channel = data.DAUChannelList.Find(item => item.MeasLocVibID == targetMeasLoc.MeasLocationID);
                        if (Channel != null)
                        {
                            result.Add($"机组 {targetTurbineId} 测量位置ID {targetMeasLoc.MeasLocationID} 振动通道已绑定，请删除通道后再试！");
                            continue;
                        }
                    }

                    if (DevTreeManagement.IsVibMeasLocUsed(targetMeasLoc.MeasLocationID))
                    {
                        result.Add($"机组 {targetTurbineId} 测量位置ID {targetMeasLoc.MeasLocationID} 正在被波形定义使用");
                        continue;
                    }

                    List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(targetTurbineId, EnumMeasLocType.VibAlarmDef);
                    AlarmDefinition myDefData = alarmDef.Find(i => i.MeasLocationID == targetMeasLoc.MeasLocationID);
                    if (myDefData != null)
                    {
                        result.Add($"机组 {targetTurbineId} 测量位置ID {targetMeasLoc.MeasLocationID} 报警定义中正在使用该测量位置，请删除报警定义后再试！");
                        continue;
                    }

                    DevTreeManagement.DeleteVibMeasLoc(targetMeasLoc.MeasLocationID);
                    result.Add($"机组 {targetTurbineId} 测量位置ID {targetMeasLoc.MeasLocationID} 删除成功");
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteVibMeasLocs]机组 {targetTurbineId} 批量删除振动测量位置失败", ex);
                    result.Add($"机组 {targetTurbineId} 删除失败: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 批量添加电流电压测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddProcessMeasLocs(string targetTurbineId, BatchAddProcessMeasLocsRequest request)
        {
            var result = new List<string>();

            try
            {
                List<MeasLoc_VoltageCurrent> locs = new();
                foreach (var measLoc in request.SourceData)
                {
                    // 创建新的测量位置，使用目标机组ID
                    var newMeasLoc = new MeaslocDTO
                    {
                        WindTurbineID = targetTurbineId,
                        ComponentName = measLoc.ComponentName,
                        SectionName = measLoc.SectionName,
                        Orientation = measLoc.Orientation,
                        MeasLocName = measLoc.MeasLocName?.Replace(measLoc.WindTurbineID, targetTurbineId)
                    };

                    // 生成测量位置ID
                    string MeasLocationID = IDProvide.GetVibMeasLocID(newMeasLoc.WindTurbineID, newMeasLoc.ComponentName, newMeasLoc.SectionName, newMeasLoc.Orientation);

                    // 检查是否已存在相同测量位置
                    var list = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(newMeasLoc.WindTurbineID);

                    string OrientationCode = MeasLocationID.Substring(MeasLocationID.Length - 1, 1);
                    //if (MeasLocationID.IndexOf(newMeasLoc.ComponentName) > -1)
                    //{
                    var existingMeasLoc = list.Find(item => item.MeasLocationID == MeasLocationID);
                    if (existingMeasLoc != null)
                    {
                        result.Add($"机组 {targetTurbineId} 测量位置 {newMeasLoc.MeasLocName} 已存在");
                        continue;
                    }

                    // 检查部件是否存在
                    string compid = measLoc.ComponentID.Replace(measLoc.WindTurbineID, targetTurbineId);
                    var comp = DevTreeManagement.GetTurbComponent(compid);
                    if (comp == null)
                    {
                        result.Add($"机组 {targetTurbineId} 部件 {compid} 不存在");
                        continue;
                    }

                    var measLoc_VoltageCurrent = new MeasLoc_VoltageCurrent
                    {
                        MeasLocationID = MeasLocationID,
                        WindTurbineID = newMeasLoc.WindTurbineID,
                        ComponentID = comp.ComponentID,
                        SectionName = newMeasLoc.SectionName,
                        Orientation = OrientationCode,
                        MeasLocName = newMeasLoc.MeasLocName
                    };

                    locs.Add(measLoc_VoltageCurrent);
                    result.Add($"机组 {targetTurbineId} 测量位置 {newMeasLoc.MeasLocName} 添加成功");
                    //}
                }

                if (locs.Count > 0)
                {
                    DevTreeManagement.AddVoltageCurrentMeasLocRange(locs);
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddProcessMeasLocs]机组 {targetTurbineId} 批量添加电流电压测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量添加电流电压测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除电流电压测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteProcessMeasLocs(string targetTurbineId, BatchDeleteProcessMeasLocsRequest request)
        {
            var result = new List<string>();

            // 从HttpContext中获取缓存的原始记录信息
            var originalRecords = HttpContext.Items["OriginalProcessMeasLocRecords"] as List<MeasLoc_VoltageCurrent>;
            if (originalRecords == null || !originalRecords.Any())
            {
                result.Add($"机组 {targetTurbineId} 无法获取原始记录信息，批量删除失败");
                return result;
            }

            // 获取目标机组的所有测量位置
            var targetMeasLocList = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(targetTurbineId);

            foreach (var originalMeasLoc in originalRecords)
            {
                try
                {
                    // 在目标机组中查找对应的测量位置
                    var targetMeasLoc = targetMeasLocList.Find(item =>
                        item.MeasLocName == originalMeasLoc.MeasLocName ||
                        (item.ComponentID == originalMeasLoc.ComponentID &&
                         item.SectionName == originalMeasLoc.SectionName &&
                         item.Orientation == originalMeasLoc.Orientation));

                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组 {targetTurbineId} 未找到对应的测量位置 {originalMeasLoc.MeasLocName}");
                        continue;
                    }

                    // 校验是否可删除（如通道绑定、报警定义等）
                    WindDAU data = DauManagement.GetDAUById(targetTurbineId);
                    if (data != null)
                    {
                        DAUChannel_VoltageCurrent Channel = data.VoltageCurrentList.Find(item => item.MeasLoc_ProcessId == targetMeasLoc.MeasLocationID);
                        if (Channel != null)
                        {
                            result.Add($"机组 {targetTurbineId} 测量位置 {targetMeasLoc.MeasLocName} 通道已绑定，请删除通道后再试！");
                            continue;
                        }
                    }

                    // 执行删除
                    DevTreeManagement.DeleteVoltageCurrentsMeasLoc(targetMeasLoc.MeasLocationID);
                    result.Add($"机组 {targetTurbineId} 测量位置 {targetMeasLoc.MeasLocName} 删除成功");
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteProcessMeasLocs]机组 {targetTurbineId} 删除测量位置失败", ex);
                    result.Add($"机组 {targetTurbineId} 删除失败: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 批量添加工况测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddWorkingConditionMeasLocs(string targetTurbineId, BatchAddWorkingConditionMeasLocsRequest request)
        {
            var result = new List<string>();

            try
            {
                List<MeasLoc_Process> wclist = new();
                foreach (var item in request.SourceData)
                {
                    // 创建新的工况测量位置，使用目标机组ID
                    var newItem = new WorkingConditionMeasDTO
                    {
                        WindTurbineID = targetTurbineId,
                        MeasLocName = item.MeasLocName,
                        MDFWorkData = item.MDFWorkData,
                        Datafrom = item.Datafrom
                    };

                    // 检查工况测量位置数据
                    var check = CheckConditionMeasData(newItem.WindTurbineID, newItem.MeasLocName, newItem.MeasLocName, newItem.MDFWorkData, "1", newItem.Datafrom);
                    if (!check)
                    {
                        result.Add($"机组 {targetTurbineId} 工况测量位置 {newItem.MeasLocName} 校验失败");
                        continue;
                    }

                    MeasLoc_Process measLoc_Process = new MeasLoc_Process();
                    measLoc_Process.WindTurbineID = newItem.WindTurbineID;
                    measLoc_Process.MeasLocName = newItem.MeasLocName;
                    measLoc_Process.Param_Type_Code = (EnumWorkCondition_ParamType)EnumWorkCondParamTypeHelper.GetParamTypeList().Find(t => t.Value == newItem.MDFWorkData).Key;
                    measLoc_Process.MeasLocationID = IDProvide.GetWorkCondID(newItem.WindTurbineID, measLoc_Process.FieldBusType == EnumWorkConDataSource.WindDAU ? "1" : "0", newItem.MeasLocName);
                    measLoc_Process.FieldBusType = (EnumWorkConDataSource)Enum.Parse(typeof(EnumWorkConDataSource), newItem.Datafrom);

                    wclist.Add(measLoc_Process);
                    result.Add($"机组 {targetTurbineId} 工况测量位置 {newItem.MeasLocName} 添加成功");
                }

                if (wclist.Count > 0)
                {
                    DevTreeManagement.AddWorkCondMeasLocationRange(wclist);
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddWorkingConditionMeasLocs]机组 {targetTurbineId} 批量添加工况测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量添加工况测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除工况测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWorkingConditionMeasLocs(string targetTurbineId, BatchDeleteWorkingConditionMeasLocsRequest request)
        {
            var result = new List<string>();

            // 从HttpContext中获取缓存的原始记录信息
            var originalRecords = HttpContext.Items["OriginalWorkingConditionMeasLocRecords"] as List<MeasLoc_Process>;
            if (originalRecords == null || !originalRecords.Any())
            {
                result.Add($"机组 {targetTurbineId} 无法获取原始记录信息，批量删除失败");
                return result;
            }

            // 获取目标机组的所有工况测量位置
            var targetMeasLocList = DevTreeManagement.GetWorkCondMeasLocByTurID(targetTurbineId);

            foreach (var originalMeasLoc in originalRecords)
            {
                try
                {
                    // 在目标机组中查找对应的测量位置
                    var targetMeasLoc = targetMeasLocList.Find(item =>
                        item.MeasLocName == originalMeasLoc.MeasLocName && item.Param_Type_Code == originalMeasLoc.Param_Type_Code && item.FieldBusType == originalMeasLoc.FieldBusType);

                    if (targetMeasLoc == null)
                    {
                        result.Add($"机组 {targetTurbineId} 未找到对应的工况测量位置 {originalMeasLoc.MeasLocName}");
                        continue;
                    }

                    // 校验是否可删除
                    List<DAUChannel_Process> dauList = DauManagement.GetDAUChannelProcessListByDAUId(targetTurbineId);
                    if (dauList != null && dauList.Count != 0)
                    {
                        var list = from m in dauList
                                   where m.MeasLoc_ProcessId == targetMeasLoc.MeasLocationID
                                   select m;
                        if (list.Count() > 0)
                        {
                            result.Add($"机组 {targetTurbineId} 工况测量位置 {targetMeasLoc.MeasLocName} 通道已绑定，请删除通道后再试！");
                            continue;
                        }
                    }

                    //主控
                    List<MCSChannel> MCSChannelList = DAUMCS.GetMCSChannelList(targetTurbineId);
                    if (MCSChannelList != null && MCSChannelList.Count != 0)
                    {
                        var list = from m in MCSChannelList
                                   where m.MeasLocProcessID == targetMeasLoc.MeasLocationID
                                   select m;
                        if (list.Count() > 0)
                        {
                            result.Add($"机组 {targetTurbineId} 工况测量位置 {targetMeasLoc.MeasLocName} 主控已绑定，请删除主控寄存器后再试！");
                            continue;
                        }
                    }

                    // 执行删除
                    DevTreeManagement.DeleteWorkCondMeasLocation(targetMeasLoc.MeasLocationID);
                    result.Add($"机组 {targetTurbineId} 工况测量位置 {targetMeasLoc.MeasLocName} 删除成功");
                }
                catch (Exception ex)
                {
                    CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteWorkingConditionMeasLocs]机组 {targetTurbineId} 删除工况测量位置失败", ex);
                    result.Add($"机组 {targetTurbineId} 删除失败: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// 批量添加转速测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddRotSpdLoc(string targetTurbineId, BatchAddRotSpdLocRequest request)
        {
            var result = new List<string>();

            try
            {
                foreach (var measLoc_RotSpd in request.SourceData)
                {
                    // 检查目标机组是否已存在转速测量位置
                    WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(targetTurbineId);
                    if (turbine != null && turbine.DevMeasLocRotSpds.Count > 0)
                    {
                        result.Add($"机组 {targetTurbineId} 已存在转速测量位置，请勿重复添加！");
                        continue;
                    }

                    // 创建新的转速测量位置，使用目标机组ID
                    MeasLoc_RotSpd newRotSpdMeasLoc = new MeasLoc_RotSpd();
                    newRotSpdMeasLoc.WindTurbineID = targetTurbineId;
                    newRotSpdMeasLoc.MeasLocationID = IDProvide.GetRotSpdLocID(targetTurbineId, "");
                    newRotSpdMeasLoc.MeasLocName = measLoc_RotSpd.MeasLocName?.Replace(measLoc_RotSpd.WindTurbineID, targetTurbineId);
                    newRotSpdMeasLoc.GearRatio = measLoc_RotSpd.GearRatio;
                    newRotSpdMeasLoc.LineCounts = measLoc_RotSpd.LineCounts;

                    using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                    {
                        ctx.DevMeasLocRotSpds.Add(newRotSpdMeasLoc);
                        ctx.SaveChanges();
                    }

                    result.Add($"机组 {targetTurbineId} 转速测量位置 {newRotSpdMeasLoc.MeasLocName} 添加成功");
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddRotSpdLoc]机组 {targetTurbineId} 批量添加转速测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量添加转速测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除转速测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteRotSpdLoc(string targetTurbineId, BatchDeleteRotSpdLocRequest request)
        {
            var result = new List<string>();

            // 从HttpContext中获取缓存的原始记录信息
            var originalRecords = HttpContext.Items["OriginalRotSpdLocRecords"] as List<MeasLoc_RotSpd>;
            if (originalRecords == null || !originalRecords.Any())
            {
                result.Add($"机组 {targetTurbineId} 无法获取原始记录信息，批量删除失败");
                return result;
            }

            try
            {
                WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(targetTurbineId);
                if (turbine == null)
                {
                    result.Add($"机组 {targetTurbineId} 不存在");
                    return result;
                }

                foreach (var originalRecord in originalRecords)
                {
                    try
                    {
                        // 在目标机组中查找对应的转速测量位置
                        var targetRotSpdLoc = turbine.DevMeasLocRotSpds.Find(item =>
                            item.MeasLocName == originalRecord.MeasLocName ||
                            (item.GearRatio == originalRecord.GearRatio && item.LineCounts == originalRecord.LineCounts));

                        if (targetRotSpdLoc == null)
                        {
                            result.Add($"机组 {targetTurbineId} 未找到对应的转速测量位置 {originalRecord.MeasLocName}");
                            continue;
                        }

                        // 校验是否正在被使用
                        List<WindDAU> DAUs = DauManagement.GetDAUListByTurbineID(targetTurbineId);
                        bool isInUse = false;
                        foreach (var dau in DAUs)
                        {
                            if (dau.RotSpeedChannelList.FirstOrDefault(t => t.MeasLocRotSpdID == targetRotSpdLoc.MeasLocationID) != null)
                            {
                                result.Add($"机组 {targetTurbineId} 转速测量位置 {targetRotSpdLoc.MeasLocName} 正在被使用，请先删除通道！");
                                isInUse = true;
                                break;
                            }
                        }

                        if (isInUse) continue;

                        // 执行删除
                        using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                        {
                            ctx.DevMeasLocRotSpds.Remove(targetRotSpdLoc);
                            ctx.SaveChanges();
                        }

                        result.Add($"机组 {targetTurbineId} 转速测量位置 {targetRotSpdLoc.MeasLocName} 删除成功");
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteRotSpdLoc]机组 {targetTurbineId} 删除转速测量位置失败", ex);
                        result.Add($"机组 {targetTurbineId} 删除失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteRotSpdLoc]机组 {targetTurbineId} 批量删除转速测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量删除转速测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量编辑转速测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditRotSpdLoc(string targetTurbineId, BatchEditRotSpdLocRequest request)
        {
            var result = new List<string>();

            try
            {
                if (request?.SourceData == null)
                {
                    result.Add($"机组 {targetTurbineId} 源数据为空，批量编辑失败");
                    return result;
                }

                // 映射源数据到目标机组
                var mappedData = MapEditRotSpdLocToTargetTurbine(request.SourceData, targetTurbineId);
                if (mappedData == null || !mappedData.Any())
                {
                    result.Add($"机组 {targetTurbineId} 数据映射失败，批量编辑失败");
                    return result;
                }

                foreach (var measLoc_RotSpd in mappedData)
                {
                    try
                    {
                        WindTurbine turbine = DevTreeManagement.GetAllWindTurbine(measLoc_RotSpd.WindTurbineID);
                        MeasLoc_RotSpd _RotSpdInformation = turbine.DevMeasLocRotSpds.Find(item => item.MeasLocationID == measLoc_RotSpd.MeasLocationID);

                        if (_RotSpdInformation == null)
                        {
                            result.Add($"机组 {targetTurbineId} 中未找到转速测量位置 {measLoc_RotSpd.MeasLocationID}");
                            continue;
                        }

                        _RotSpdInformation.WindTurbineID = measLoc_RotSpd.WindTurbineID;
                        _RotSpdInformation.MeasLocationID = measLoc_RotSpd.MeasLocationID;
                        _RotSpdInformation.MeasLocName = measLoc_RotSpd.MeasLocName;
                        _RotSpdInformation.GearRatio = measLoc_RotSpd.GearRatio;
                        _RotSpdInformation.LineCounts = measLoc_RotSpd.LineCounts;
                        DevTreeManagement.EditRotSpdMeasLocation(_RotSpdInformation);

                        // 升级测量定义ID
                        DauManagement.UpdateMeasDefVersion(measLoc_RotSpd.WindTurbineID);

                        result.Add($"机组 {targetTurbineId} 转速测量位置 {measLoc_RotSpd.MeasLocName} 编辑成功");
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditRotSpdLoc]机组 {targetTurbineId} 编辑转速测量位置失败", ex);
                        result.Add($"机组 {targetTurbineId} 转速测量位置编辑失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchEditRotSpdLoc]机组 {targetTurbineId} 批量编辑转速测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量编辑转速测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量添加Modbus测量位置到指定机组
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddModbusMeasLoc(string targetTurbineId, BatchAddModbusMeasLocRequest request)
        {
            var result = new List<string>();
            try
            {
                foreach (var measLoc in request.SourceData)
                {
                    try
                    {
                        if (measLoc.MeasType.ToLower() == "svm")
                        {
                            var targetCompID = measLoc.ComponentID.Replace(measLoc.WindTurbineID, targetTurbineId);
                            // SVM测量位置存储到DevMeasLocSVMs表
                            var svmMeasLoc = new MeasLoc_SVM
                            {
                                MeasLocationID = AppFramework.IDUtility.IDProvide.GetSVMLocID("", targetCompID, "", measLoc.MeasLocName),
                                WindTurbineID = targetTurbineId,
                                MeasLocName = measLoc.MeasLocName,
                                SectionName = measLoc.SectionName,
                                ParamType = SVMManagement.GetParamType(measLoc.PhysicalType),
                                ComponentID = targetCompID,
                                Orientation = measLoc.Orientation,
                            };

                            // 使用SVM管理类添加
                            SVMManagement.AddSVMMeasLoc(svmMeasLoc.WindTurbineID, new List<MeasLoc_SVM>() { svmMeasLoc });
                            result.Add($"SVM测量位置 {measLoc.MeasLocName} 添加成功");
                        }
                        else
                        {
                            // 创建新的Modbus测量位置，使用目标机组ID
                            var newMeasLoc = new ModbusMeasLocDTO
                            {
                                WindTurbineID = targetTurbineId,
                                MeasLocName = measLoc.MeasLocName,
                                MeasType = measLoc.MeasType,
                                SectionName = measLoc.SectionName,
                                Orientation = measLoc.Orientation,
                                ComponentID = measLoc.ComponentID?.Replace(measLoc.WindTurbineID, targetTurbineId),
                            };

                            if (string.IsNullOrEmpty(newMeasLoc.WindTurbineID) ||
                                string.IsNullOrEmpty(newMeasLoc.MeasLocName) ||
                                string.IsNullOrEmpty(newMeasLoc.MeasType))
                            {
                                result.Add($"机组 {targetTurbineId} 测量位置 {newMeasLoc.MeasLocName} 参数不完整");
                                continue;
                            }

                            // 生成测量位置ID
                            string componentName = newMeasLoc.ComponentID.Replace(measLoc.WindTurbineID, targetTurbineId);
                            string sectionName = newMeasLoc.SectionName;
                            string orientation = newMeasLoc.Orientation;

                            string measLocationId = CommonUtility.GetVibMeasLocID(
                                "",
                                componentName,
                                sectionName,
                                orientation);

                            // 检查是否已存在
                            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                            {
                                var existingMeasLoc = ctx.MeasLoc_Modbus.FirstOrDefault(x =>
                                    x.WindTurbineID == newMeasLoc.WindTurbineID &&
                                    x.MeasLocName == newMeasLoc.MeasLocName);

                                if (existingMeasLoc != null)
                                {
                                    result.Add($"机组 {targetTurbineId} Modbus测量位置 {newMeasLoc.MeasLocName} 已存在");
                                    continue;
                                }

                                // 创建新的Modbus测量位置
                                var newModbusMeasLoc = new MeasLoc_Modbus
                                {
                                    MeasLocationID = measLocationId,
                                    WindTurbineID = newMeasLoc.WindTurbineID,
                                    MeasLocName = newMeasLoc.MeasLocName,
                                    SectionName = newMeasLoc.SectionName,
                                    Orientation = newMeasLoc.Orientation,
                                    ComponentID = newMeasLoc.ComponentID,
                                    OrderSeq = 1
                                };

                                ctx.MeasLoc_Modbus.Add(newModbusMeasLoc);
                                ctx.SaveChanges();

                                result.Add($"机组 {targetTurbineId} Modbus测量位置 {newMeasLoc.MeasLocName} 添加成功");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddModbusMeasLoc]机组 {targetTurbineId} 添加测量位置失败: {measLoc.MeasLocName}", ex);
                        result.Add($"机组 {targetTurbineId} 测量位置 {measLoc.MeasLocName} 添加失败: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchAddModbusMeasLoc]机组 {targetTurbineId} 批量添加Modbus测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量添加Modbus测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 批量删除测量位置到指定机组（同时删除SVM和Modbus测量位置）
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">原始请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteModbusMeasLoc(string targetTurbineId, BatchDeleteMeasLocRequest request)
        {
            var result = new List<string>();

            // 从HttpContext中获取缓存的原始记录信息
            var originalSvmRecords = HttpContext.Items["OriginalSvmMeasLocRecords"] as List<MeasLoc_SVM>;
            var originalModbusRecords = HttpContext.Items["OriginalModbusMeasLocRecords"] as List<MeasLoc_Modbus>;

            if ((originalSvmRecords == null || !originalSvmRecords.Any()) &&
                (originalModbusRecords == null || !originalModbusRecords.Any()))
            {
                result.Add($"机组 {targetTurbineId} 无法获取原始记录信息，批量删除失败");
                return result;
            }

            try
            {
                // 处理SVM测量位置
                if (originalSvmRecords != null && originalSvmRecords.Any())
                {
                    foreach (var originalRecord in originalSvmRecords)
                    {
                        try
                        {
                            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                            {
                                // 在目标机组中查找对应的SVM测量位置
                                var targetMeasLoc = ctx.DevMeasLocSVMs.FirstOrDefault(x =>
                                    x.WindTurbineID == targetTurbineId &&
                                    (x.MeasLocName == originalRecord.MeasLocName ||
                                     x.MeasLocationID.EndsWith(originalRecord.MeasLocationID.Substring(originalRecord.MeasLocationID.Length - 10))));

                                if (targetMeasLoc == null)
                                {
                                    result.Add($"机组 {targetTurbineId} 未找到对应的SVM测量位置 {originalRecord.MeasLocName}");
                                    continue;
                                }

                                ctx.DevMeasLocSVMs.Remove(targetMeasLoc);
                                ctx.SaveChanges();
                                result.Add($"机组 {targetTurbineId} SVM测量位置 {targetMeasLoc.MeasLocName} 删除成功");
                            }
                        }
                        catch (Exception ex)
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]机组 {targetTurbineId} 删除SVM测量位置失败", ex);
                            result.Add($"机组 {targetTurbineId} 删除SVM测量位置失败: {ex.Message}");
                        }
                    }
                }

                // 处理Modbus测量位置
                if (originalModbusRecords != null && originalModbusRecords.Any())
                {
                    foreach (var originalRecord in originalModbusRecords)
                    {
                        try
                        {
                            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                            {
                                // 在目标机组中查找对应的Modbus测量位置
                                var targetMeasLoc = ctx.MeasLoc_Modbus.FirstOrDefault(x =>
                                    x.WindTurbineID == targetTurbineId &&
                                    (x.MeasLocName == originalRecord.MeasLocName ||
                                     x.MeasLocationID.EndsWith(originalRecord.MeasLocationID.Substring(originalRecord.MeasLocationID.Length - 10))));

                                if (targetMeasLoc == null)
                                {
                                    result.Add($"机组 {targetTurbineId} 未找到对应的Modbus测量位置 {originalRecord.MeasLocName}");
                                    continue;
                                }

                                ctx.MeasLoc_Modbus.Remove(targetMeasLoc);
                                ctx.SaveChanges();
                                result.Add($"机组 {targetTurbineId} Modbus测量位置 {targetMeasLoc.MeasLocName} 删除成功");
                            }
                        }
                        catch (Exception ex)
                        {
                            CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]机组 {targetTurbineId} 删除Modbus测量位置失败", ex);
                            result.Add($"机组 {targetTurbineId} 删除Modbus测量位置失败: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[BatchDeleteMeasLoc]机组 {targetTurbineId} 批量删除测量位置失败", ex);
                result.Add($"机组 {targetTurbineId} 批量删除测量位置失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 映射编辑转速测量位置数据到目标机组
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <returns></returns>
        private List<MeasLoc_RotSpd> MapEditRotSpdLocToTargetTurbine(MeasLoc_RotSpd sourceData, string targetTurbineId)
        {
            var mappedData = new List<MeasLoc_RotSpd>();

            try
            {
                using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
                {

                    // 在目标机组中查找对应的转速测量位置（通过名称匹配）
                    var targetRotSpd = ctx.DevMeasLocRotSpds.FirstOrDefault(x =>
                        x.WindTurbineID == targetTurbineId &&
                        x.MeasLocName == sourceData.MeasLocName);

                    if (targetRotSpd != null)
                    {
                        // 创建映射后的数据
                        var mappedRotSpd = new MeasLoc_RotSpd
                        {
                            WindTurbineID = targetTurbineId,
                            MeasLocationID = targetRotSpd.MeasLocationID,
                            MeasLocName = sourceData.MeasLocName,
                            GearRatio = sourceData.GearRatio,
                            LineCounts = sourceData.LineCounts
                        };

                        mappedData.Add(mappedRotSpd);
                    }
                    else
                    {
                        CMSFramework.Logger.Logger.LogErrorMessage($"[MapEditRotSpdLocToTargetTurbine]在目标机组 {targetTurbineId} 中未找到对应的转速测量位置: {sourceData.MeasLocName}", new Exception("未找到对应的转速测量位置"));
                    }

                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[MapEditRotSpdLocToTargetTurbine]映射转速测量位置数据失败", ex);
                return null;
            }

            return mappedData;
        }

        #endregion

    }
}
