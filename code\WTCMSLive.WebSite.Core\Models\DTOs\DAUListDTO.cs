using CMSFramework.BusinessEntity;
using System.Runtime.Serialization;

namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// DAU列表响应DTO
    /// </summary>
    public class DAUListResponseDTO
    {
        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// DAU状态统计
        /// </summary>
        public DAUStatusSummaryDTO StatusSummary { get; set; }

        /// <summary>
        /// DAU详细列表
        /// </summary>
        public List<DAUItemDTO> DAUList { get; set; } = new List<DAUItemDTO>();

        /// <summary>
        /// 数据获取时间
        /// </summary>
        public DateTime DataTime { get; set; }
    }

    /// <summary>
    /// DAU状态统计DTO
    /// </summary>
    public class DAUStatusSummaryDTO
    {
        /// <summary>
        /// DAU总数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 正常状态数量
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 未知状态数量
        /// </summary>
        public int UnknownCount { get; set; }

        /// <summary>
        /// 通信错误数量
        /// </summary>
        public int CommunicationErrorCount { get; set; }

        /// <summary>
        /// 无数据到达数量
        /// </summary>
        public int NoDataArriveCount { get; set; }

        /// <summary>
        /// 传感器故障数量
        /// </summary>
        public int SensorFaultCount { get; set; }

        /// <summary>
        /// 转速故障数量
        /// </summary>
        public int RotSpdFaultCount { get; set; }
    }

    /// <summary>
    /// DAU项目DTO
    /// </summary>
    public class DAUItemDTO
    {
        /// <summary>
        /// DAU ID
        /// </summary>
        public string DauID { get; set; }

        /// <summary>
        /// DAU名称
        /// </summary>
        public string DAUName { get; set; }

        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// 机组名称
        /// </summary>
        public string WindTurbineName { get; set; }

        /// <summary>
        /// 风场ID
        /// </summary>
        public string WindParkID { get; set; }

        /// <summary>
        /// DAU IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// DAU端口
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable { get; set; }

        /// <summary>
        /// DAU报警状态
        /// </summary>
        public int AlarmState { get; set; }

        /// <summary>
        /// DAU报警状态描述
        /// </summary>
        public string AlarmStateDescription { get; set; }

        /// <summary>
        /// 状态更新时间
        /// </summary>
        public DateTime? StatusUpdateTime { get; set; }

        /// <summary>
        /// 振动通道数量
        /// </summary>
        public int VibChannelCount { get; set; }

        /// <summary>
        /// 转速通道数量
        /// </summary>
        public int RotSpeedChannelCount { get; set; }

        /// <summary>
        /// 工况通道数量
        /// </summary>
        public int ProcessChannelCount { get; set; }

        /// <summary>
        /// 电压电流通道数量
        /// </summary>
        public int VoltageCurrentChannelCount { get; set; }
    }


    public class WindDAUDTO
    {
        public string? WindTurbineName { get; set; }
       
        public string WindTurbineID { get; set; }

   
        public string DauID { get; set; }

    
        public string DAUName { get; set; }

        public string SerialNumber { get; set; }

        public string IP { get; set; }


        public int Port { get; set; }


        public byte DeviceID { get; set; }

        public int WaveSaveInterval { get; set; }

        public int TrendSaveInterval { get; set; }

        public int DataAcquisitionInterval { get; set; }

        public bool IsAvailable { get; set; }

        public int MeasDefVersion { get; set; }

        public int DAUMeasDefVersion { get; set; }

        public string DAUSoftwareVersion { get; set; }

        public string WindParkID { get; set; }


        public EnumDAUType DAUType { get; set; }


        public WindDAUDTO()
        {
            DauID = "1";
            MeasDefVersion = 0;
            DAUMeasDefVersion = 0;
            DAUSoftwareVersion = "0";
        }
    }

    /// <summary>
    /// DAU网络配置更新项
    /// </summary>
    public class DAUNetworkUpdateItem
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// DAU ID
        /// </summary>
        public string DauID { get; set; }

        /// <summary>
        /// 新的IP地址
        /// </summary>
        public string IP { get; set; }

        /// <summary>
        /// 新的端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// DAU名称（用于显示和日志记录）
        /// </summary>
        public string? DAUName { get; set; }
    }

    /// <summary>
    /// 单个DAU网络配置更新结果
    /// </summary>
    public class DAUNetworkUpdateResult
    {
        /// <summary>
        /// 机组ID
        /// </summary>
        public string WindTurbineID { get; set; }

        /// <summary>
        /// DAU ID
        /// </summary>
        public string DauID { get; set; }

        /// <summary>
        /// DAU名称
        /// </summary>
        public string DAUName { get; set; }

        /// <summary>
        /// 操作是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误信息（如果失败）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 原始IP地址
        /// </summary>
        public string OriginalIP { get; set; }

        /// <summary>
        /// 新IP地址
        /// </summary>
        public string NewIP { get; set; }

        /// <summary>
        /// 原始端口
        /// </summary>
        public int OriginalPort { get; set; }

        /// <summary>
        /// 新端口
        /// </summary>
        public int NewPort { get; set; }
    }
}
