import{W as O,a as A}from"./table-CWAoXMlp.js";import{O as U}from"./index-QAVfwe37.js";import{u as B}from"./account-CkoM2RwK.js";import{u as F}from"./role-BlaYU5wG.js";import{S as q,d as k}from"./tools-BpvUNSHS.js";import{_ as P}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{M as V}from"./index-DkT7RQa1.js";import{r as i,y as G,h as K,f as b,d as m,o as p,i as C,b as S,s as L,c as $,m as a}from"./index-ClUwy-U2.js";import"./ActionButton-p4-l6Zij.js";import"./styleChecker-D2z1GQZd.js";import"./index-ByAZPsB5.js";import"./initDefaultProps-CbhiVpW4.js";import"./shallowequal-DorGB6RW.js";import"./index-CUxYOaAq.js";import"./index-Cg9xkC5X.js";import"./index-B9pLYs_L.js";import"./index-BUWlNxdP.js";import"./index-CgagFv0b.js";const z={key:1},J={__name:"users",setup(j){const n=B(),f=F();let h=window.localStorage.getItem("user")?JSON.parse(window.localStorage.getItem("user")):{};const I=(e={isform:!1,isEdit:!1})=>{let s=[{title:"用户名称",dataIndex:"userName",align:"center",columnWidth:150,formItemWidth:320,isrequired:!0},{title:"账号",dataIndex:"account",align:"center",columnWidth:150,formItemWidth:320,isrequired:!0,isdisplay:!e.isEdit,customRender:({record:t})=>t.userID||"",...e&&e.isform?{}:{customRender:({record:t})=>t.userID||""}},{title:"角色名称",dataIndex:"role",columnWidth:120,formItemWidth:320,align:"center",isrequired:!0,isdisplay:!e.isEdit,inputType:"select",selectOptions:[],...e&&e.isform?{}:{customRender:({record:t})=>t.userRole&&t.userRole.roleName?t.userRole.roleName:""}},{title:"邮箱",dataIndex:"email",align:"center",columnWidth:100,formItemWidth:320,validateRules:k({type:"email",title:"邮箱",required:!0})},{title:"电话",dataIndex:"phone",align:"center",canEdit:!0,columnWidth:100,formItemWidth:320,validateRules:k({type:"phone",title:"电话"})},{title:"状态",dataIndex:"userState",align:"center",columnWidth:80,formItemWidth:320,isdisplay:!1}];return e&&e.isform&&!e.isEdit?[...s,{title:"密码",dataIndex:"password",inputType:"password",align:"center",columnWidth:80,formItemWidth:320,isrequired:!0}]:s},d=i(""),r=i(""),_=i({}),l=i([]),v=i(!1),y=G({table:[],tableColumns:I()}),g=i(!1),u=async()=>{v.value=!0,y.table=await n.fetchGetuserlist(),v.value=!1};K(()=>{u()});const D=async()=>{f.roleOptions.length===0&&await f.fetchGetrolelist();let e=l.value;e[2].selectOptions=f.roleOptions,l.value=[...e]},W=()=>{g.value=!0},w=e=>{g.value=!1,l.value=[],_.value={},d.value="",r.value=""},x=e=>{const{title:o,operateType:s}=e;d.value="添加用户",r.value=s,l.value=[...I({isform:!0})],D(),W()},E=async e=>{const{tableKey:o,selectedkeys:s,record:t}=e;if(h&&h.userId&&h.userId===t.userID){a.error("当前用户不能删除!与登录用户一致！");return}let c=await n.fetchDeleteUser({account:t.userID});c&&c.code===1?(u(),a.success("提交成功")):a.error("提交失败:"+c.msg)},N=e=>{const{title:o,operateType:s,rowData:t}=e;d.value="编辑用户",r.value=s,_.value={...t,role:t.userRole&&t.userRole.roleID?t.userRole.roleID:"",account:t.userID},l.value=[...I({isform:!0,isEdit:!0})],D(),W()},T=async e=>{let o=await n.fetchResetUser({account:e.userID});o&&o.code===1?(u(),a.success("密码重置成功")):a.error("密码重置失败:"+o.msg)},M=async e=>{switch(r.value){case"add":let o=await n.fetchAdduser({...e,phone:e.phone||""});o&&o.code===1?(u(),a.success("提交成功"),w()):a.error("提交失败:"+o.msg);break;case"edit":let s=await n.fetchEdituser(e);s&&s.code===1?(u(),a.success("提交成功"),w()):a.error("提交失败:"+s.msg);break}};return(e,o)=>{const s=A,t=V,c=q;return p(),b(c,{spinning:v.value,size:"large"},{default:m(()=>[C("div",null,[S(O,{tableTitle:"用户列表","table-key":"0","table-columns":y.tableColumns,"table-operate":["add","edit","delete"],"record-key":"ModbusUnitID","table-datas":y.table,noBatchApply:!0,onAddRow:x,onEditRow:N,onDeleteRow:E,actionCloumnProps:{width:170,align:"center"},hideOperateColumnDataKey:{editkeys:{userID:"ADMIN_SUPER"},deletekeys:{userID:"ADMIN_SUPER"}}},{otherOperate:m(({record:R})=>[R.userID!=="ADMIN_SUPER"?(p(),b(s,{key:0,placement:"bottomRight","ok-text":"是",title:`${R.userName}密码将被重置为666666？`,"cancel-text":"否",onConfirm:H=>T(R)},{default:m(()=>o[0]||(o[0]=[C("span",{class:"editBtn"},"重置密码",-1)])),_:2,__:[0]},1032,["title","onConfirm"])):L("",!0)]),_:1},8,["table-columns","table-datas"])]),S(t,{maskClosable:!1,width:"600px",open:g.value,title:d.value,footer:"",onCancel:w},{default:m(()=>[r.value==="add"||r.value==="edit"?(p(),b(U,{key:0,titleCol:l.value,initFormData:_.value,onSubmit:M},null,8,["titleCol","initFormData"])):(p(),$("div",z))]),_:1},8,["open","title"])]),_:1},8,["spinning"])}}},fe=P(J,[["__scopeId","data-v-4ddd5159"]]);export{fe as default};
