﻿using CMSFramework.BusinessEntity;
using CMSFramework.DataStorageLogic;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using Microsoft.AspNetCore.Mvc;

namespace WTCMSLive.WebSite.Controllers
{
    public class AlarmReportController : Controller
    {
        #region 机组基本信息
        /// <summary>
        /// 机组视图页面
        /// </summary>
        /// <returns></returns>
        public ActionResult ShowTurbineAlarmReport(string windParkID, string turbineID)
        {
            // ① 获取指定报警级别设备树
            TreeManager treeManager = new TreeManager();
            ViewData["leftTree"] = treeManager.GetTreeModel(EnumAlarmDegree.AlarmDeg_Alarm).ToJson();
            // ② 机组基本信息
            ViewBag.info = windParkID + "/" + turbineID;
            WindTurbine windTurbine = DevTreeManagement.GetWindTurbine(turbineID);
            ViewBag.WindTurbineName = windTurbine.WindTurbineName;//机组名称
            ViewBag.OperationalDate = windTurbine.OperationalDate.ToString("yyyy-MM-dd");//投运日期
            ViewBag.WTurbineModel = windTurbine.WindTurbineModel;//机组型号
            ViewBag.Manufactory = windTurbine.WTurbineModel.Manufactory;//机组厂商
            ViewBag.RatedPower = windTurbine.WTurbineModel.RatedPower.ToString("G5");//额定功率
            ViewBag.WorkingRotSpeed = windTurbine.MinWorkingRotSpeed.ToString();//并网转速
            ViewBag.WindTurbineCode = windTurbine.WindTurbineCode;//机组编号
            ViewBag.ReportDate = DateTime.Now;

            // ③ 特征值报警信息

            // ④ 机组趋势图

            // ⑤ 机组时域波形

            // ⑥ 机组频谱波形

            if (Request.IsAjaxRequest())
            {
                return PartialView();
            }
            return View();
        }
        #endregion
        #region 特征值
        /// <summary>
        /// 获取机组下参与报警的特征值数据
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        public string GetTurbineAlarmEnv(string windParkID, string turbineID)
        {
            // 获取机组实时特征值
            EigenValueManager mgr = new EigenValueManager();
            List<EigenValueData_Vib> rtdata = mgr.GetRTEigenValueList(turbineID);
            //获取机组设置的报警特征值(VDI+自定义)
            return mgr.GetAlarmRTEigenValue(turbineID);
        }
        #endregion

        /// <summary>
        /// 风场视图页面
        /// </summary>
        /// <returns></returns>
        public ActionResult ShowParkAlarmReoprt(string windParkID)
        {
            //ViewData["id"] = "windParkID:" + windParkID;
            // ① 获取指定报警级别设备树
            TreeManager treeManager = new TreeManager();
            ViewData["leftTree"] = treeManager.GetTreeModel(EnumAlarmDegree.AlarmDeg_Alarm).ToJson();
            // ② 获取指定报警级别的机组列表
            DataManager windParkMgr = new DataManager();
            if (windParkID == "group") { }
            List<WindPark> parkList = BusinessModel.DevTreeManagement.GetWindParkList();
            List<OverViewModel> viewModel = new List<OverViewModel>();
            for (int i = 0; i < parkList.Count; i++)
            {
                OverViewModel model = new OverViewModel();
                model.name = parkList[i].WindParkName;
                model.id = parkList[i].WindParkID;
                model.description = parkList[i].Description;
                List<FiterModel> fiterList = new List<FiterModel>();
                model.DataList = GetTurbineStatusList(parkList[i].WindParkID, out fiterList);
                model.FiterList = fiterList;
                viewModel.Add(model);
            }
            ViewData["ViewMode"] = viewModel.ToJson();
            if (Request.IsAjaxRequest())
            {
                return PartialView();
            }
            return View();
        }
        /// <summary>
        /// 获取机组状态
        /// </summary>
        /// <param name="p"></param>
        /// <returns></returns>
        private List<DataModel> GetTurbineStatusList(string windParkID, out List<FiterModel> fiterList)
        {
            List<DataModel> modelList = new List<DataModel>();
            fiterList = new List<FiterModel>();
            List<AlarmStatus_Turbine> statusList = new List<AlarmStatus_Turbine>();
            List<AlarmStatus_Turbine> statusOrderList = DevRTStateManagement.GetTurRTAlarmStatusList(windParkID);
            statusOrderList.RemoveAll(item => item.AlarmDegree != EnumAlarmDegree.AlarmDeg_Alarm);
            WindPark windpark = DevTreeManagement.GetWindPark(windParkID);
            List<WindTurbine> list = windpark.WindTurbineList;
            //添加机组状态列表排序规则（按机组编号排序）
            list.ForEach(item =>
            {
                var turbineState = statusOrderList.Find(s => s.WindTurbineID == item.WindTurbineID);
                if (turbineState != null)
                    statusList.Add(turbineState);
            });
            FiterModel fiterSuccess = new FiterModel();
            fiterSuccess.name = "正常";
            fiterSuccess.count = 0;
            fiterSuccess.id = "_Success";
            fiterSuccess.className = "btn btn-success";

            FiterModel systemError = new FiterModel();
            systemError.name = "系统异常";
            systemError.id = "_primary";
            systemError.className = "btn btn-primary";

            FiterModel fiterWaring = new FiterModel();
            fiterWaring.name = "注意";
            fiterWaring.count = 0;
            fiterWaring.id = "_warning";
            fiterWaring.className = "btn btn-warning";

            FiterModel fiterError = new FiterModel();
            fiterError.name = "危险";
            fiterError.count = 0;
            fiterError.id = "_danger";
            fiterError.className = "btn btn-danger";

            FiterModel fiterUnknow = new FiterModel();
            fiterUnknow.name = "未知";
            fiterUnknow.count = 0;
            fiterUnknow.id = "_default";
            fiterUnknow.className = "btn btn-unknow";

            systemError.count = 0;
            for (int i = 0; i < statusList.Count; i++)
            {
                DataModel model = new DataModel();
                model.name = windpark.WindTurbineList.Find(item => item.WindTurbineID == statusList[i].DevSegmentID).WindTurbineName;
                model.id = statusList[i].DevSegmentID;
                model.level = ((int)statusList[i].AlarmDegree).ToString();
                switch ((int)statusList[i].AlarmDegree)
                {
                    case 2:
                        ++fiterUnknow.count;
                        break;
                    case 3:
                        ++fiterSuccess.count;
                        break;
                    case 4:
                        ++systemError.count;
                        break;
                    case 5:
                        ++fiterWaring.count;
                        break;
                    case 6:
                        ++fiterError.count;
                        break;
                }
                modelList.Add(model);
            }
            fiterList.Add(fiterUnknow);
            fiterList.Add(fiterSuccess);
            fiterList.Add(systemError);
            fiterList.Add(fiterWaring);
            fiterList.Add(fiterError);
            //移除未出现的状态
            fiterList.RemoveAll(item => item.count == 0);
            return modelList;
        }
        #region 特征值趋势
        public JsonResult GetEnvTrendData(string windParkID, string turbineID)
        {
            //获取当前机组近一个月的特征值数据(不支持前台筛选时间)
            //List<EigenValueData_Vib> TrendList = TrendEVDataManage.GetVibEigenValueTrendByTurId(turbineID, DateTime.Now.AddDays(-30), DateTime.Now.AddDays(1));
            // 获取机组实时特征值
            EigenValueManager mgr = new EigenValueManager();
            List<EigenValueData_Vib> rtdata = mgr.GetRTEigenValueList(turbineID);
            // 删除非危险级别的特征值。
            rtdata.RemoveAll(item => item.AlarmDegree != EnumAlarmDegree.AlarmDeg_Alarm);

            // 获取特征值趋势
            List<AnalysisData> anlysisDataList = GetTurbineAlarmEigenValueTrend(turbineID, rtdata, DateTime.Now.AddDays(-30), DateTime.Now.AddDays(1));
            return Json(anlysisDataList);
        }

        private List<AnalysisData> GetTurbineAlarmEigenValueTrend(string turbineID, List<EigenValueData_Vib> rtdata, DateTime beginTime, DateTime endTime)
        {
            //CMSFramework.EigenValue.Storage.EVDataProvider evProvider = new CMSFramework.EigenValue.Storage.EVDataProvider(ConfigInfo.DBConnName, ConfigInfo.DBConnNameTrend);
            IReadMeasDataLogic readProvider = StorageFactoryLogic.GetReadDataLogic();
            List<AnalysisData> dataList = new List<AnalysisData>();
            //获取VDI3834报警定义设置
            List<AlarmDefinition> turbineAlarmList = AlarmDefConfig.GetAlarmListByTurID(turbineID);
            //获取数据
            List<EigenValueData_Vib> _EigenVibList = readProvider.GetEigenValueData(turbineID, beginTime, endTime);
            foreach (var item in rtdata)
            {
                List<EigenValueData_Vib> _list = _EigenVibList.FindAll(vib => vib.EigenValueID == item.EigenValueID);
                if (_list.Count != 0)
                {
                    dataList.Add(CreateTrendChart(turbineAlarmList, item.MeasLocationID, item.EigenValueID, item.EigenValueCode, _list));
                }
            }
            return dataList;
        }

        private AnalysisData CreateTrendChart(List<AlarmDefinition> turbineAlarmList, string measlocID, string eigenValueID, string eigenValueCode, List<EigenValueData_Vib> _EVDataList)
        {
            //获取测量位置名称
            MeasLoc_Vib local = DevTreeManagement.GetVibMeasLocByID(measlocID);
            //获取特征值中文名称
            AnalysisData analysisChart = new AnalysisData();
            analysisChart.titleName = local.MeasLocName;
            analysisChart.subText = local.MeasLocName + EigenValueManage.GetFreBandByCode(eigenValueCode);
            List<double> eigenValueList = new List<double>();//有效值
            List<double> waringValueData = new List<double>();//警告 
            List<double> errorValueData = new List<double>();//危险 
            List<string> workConditionValueData = new List<string>();//工况
            List<string> timeValueData = new List<string>();//时间
            double error = 0;
            double waring = 0;
            for (int i = 0; i < _EVDataList.Count; i++)
            {
                if (_EVDataList[i] != null)
                {
                    eigenValueList.Add(Math.Round(_EVDataList[i].Eigen_Value, 5));
                    timeValueData.Add(_EVDataList[i].AcquisitionTime.ToString());
                    //workConditionValueData.Add(_EVDataList[i].OutPowerBandCode);
                    if (error == 0 || waring == 0)
                    {
                        //获取VDI3834报警定义设置
                        AlarmDefinition AlarmDef = turbineAlarmList.Find(item => item.EigenValueID == _EVDataList[i].EigenValueID);
                        if (AlarmDef == null)
                            continue;
                        AlarmDefThreshold alarmhold = AlarmDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning);
                        if (alarmhold != null)
                        {
                            waring = (double)alarmhold.ThresholdValue;
                        }
                        alarmhold = AlarmDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm);
                        if (alarmhold != null)
                        {
                            error = (double)alarmhold.ThresholdValue;
                        }
                    }
                }
            }
            if (error > 0 || waring > 0)
            {
                for (int j = 0; j < eigenValueList.Count; j++)
                {
                    errorValueData.Add(error);
                    waringValueData.Add(waring);
                }
            }
            analysisChart.eigenValueData = eigenValueList.ToArray();
            analysisChart.workConditionValueData = workConditionValueData.ToArray();
            analysisChart.timeValueData = timeValueData.ToArray();
            analysisChart.waringValueData = waringValueData.ToArray();
            analysisChart.errorValueData = errorValueData.ToArray();
            return analysisChart;
        }
        #endregion

        #region 波形分析
        public string GetWaveChart(string windParkID, string turbineID)
        {
            try
            {
                // 获取测量位置最新状态。
                List<AlarmStatus_MeasLocVib> vibList = DevRTStateManagement.GetAlarmStatus_MeasLocVibListByTurId(turbineID);
                // 移除状态不是危险的。
                vibList.RemoveAll(item => item.AlarmDegree != EnumAlarmDegree.AlarmDeg_Alarm);

                if (vibList.Count == 0)
                {
                    return null;
                }
                //循环获取波形数据并生成图表对象
                List<WaveChartModel> dataList = new List<WaveChartModel>();
                WaveManager mgr = new WaveManager();
                foreach (var item in vibList)
                {
                    MeasLoc_Vib loc = DevTreeManagement.GetVibMeasLocByID(item.MeasLocationID);
                    //获取时域波形
                    VibWaveFormData vibWaveData = mgr.GetWaveFromData(turbineID, item.MeasLocationID, EnumWaveFormType.WDF_Time, item.AlarmUpdateTime);
                    if (vibWaveData == null)
                    {
                        continue;
                    }
                    vibWaveData.WaveData = mgr.GetWaveFormData(vibWaveData);
                    if (vibWaveData.WaveData == null)
                    {
                        continue;
                    }
                    double[] dataF = ConverByteToDouble(vibWaveData.WaveData);
                    //获取时域波形图形对象
                    WaveChartModel TDChartModel = GetTDChart(dataF, vibWaveData.SampleRate);
                    TDChartModel.OrderSeq = loc.OrderSeq * 10 + 1;
                    TDChartModel.TreadName = loc.MeasLocName + "@时域" + item.AlarmUpdateTime.ToString();
                    dataList.Add(TDChartModel);
                    //获取频域波形图形对象
                    float[] flTime = new float[vibWaveData.WaveLength];
                    System.Buffer.BlockCopy(vibWaveData.WaveData, 0, flTime, 0, vibWaveData.WaveData.Length);
                    double[] dbTime = new double[flTime.Length];
                    for (int i = 0; i < flTime.Length; i++)
                    {
                        dbTime[i] = flTime[i];
                    }
                    double[] freWaveData = AppFramework.Analysis.FrepSpectrum.AmplitudeSpectrum(dbTime, vibWaveData.SampleRate);
                    WaveChartModel FreChartModel = GetFreChart(vibWaveData, freWaveData, vibWaveData.SampleRate);
                    FreChartModel.OrderSeq = loc.OrderSeq * 10 + 2;
                    FreChartModel.TreadName = loc.MeasLocName + "@频谱" + item.AlarmUpdateTime.ToString();
                    dataList.Add(FreChartModel);
                }
                dataList = dataList.OrderBy(item => item.OrderSeq).ToList();
                return dataList.ToJson();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetWaveChart]", ex);
                return "";
            }
        }

        /// <summary>
        /// 根据波形，转换为频域波形数据
        /// </summary>
        /// <param name="waveData"></param>
        /// <returns></returns>
        private static WaveChartModel GetFreChart(VibWaveFormData vibWaveData, double[] waveData, double fs)
        {
            WaveChartModel chartData = new WaveChartModel();
            YAxis y1 = new YAxis();
            try
            {
                y1.Data = new double[waveData.Length];//  waveData;
                for (int i = 0; i < waveData.Length; i++)
                {
                    y1.Data[i] = Math.Round(waveData[i], 7);
                }
                chartData.XAxis = GetFreXData(1 / (vibWaveData.WaveLength / vibWaveData.SampleRate), waveData.Length);
            }
            catch (Exception ex)
            {
                return chartData;
            }
            List<YAxis> yList = new List<YAxis>();
            List<string> yName = new List<string>();
            chartData.Formart = "{value} Hz";
            chartData.Yformart = "";
            y1.Name = "幅值(g)";
            y1.Type = "line";
            //y1.data = waveData;
            yName.Add("幅值(g)");
            yList.Add(y1);
            chartData.YAxisList = yList;
            chartData.DataName = yName.ToArray();
            return chartData;
        }
        private static string[] GetFreXData(double FeqRes, int WaveNum)
        {
            //// XWaveDataIndex = new List<double>();
            //List<string> xs = new List<string>();
            //double s = (double)fs / length;
            //for (int i = 0; i < length; i++)
            //{
            //    double val = Math.Round(s * i, 3);
            //    // XWaveDataIndex.Add(val);
            //    xs.Add(val + "Hz");
            //}
            //return xs.ToArray();
            List<string> xs = new List<string>();
            double[] FeqArr = new double[WaveNum];
            for (int i = 0; i < WaveNum; i++)
            {
                xs.Add((FeqRes * i).ToString());
                //FeqArr[i] = FeqRes * i;
            }
            return xs.ToArray();
        }
        /// <summary>
        /// 根据波形，转换为时域波形数据
        /// </summary>
        /// <param name="waveData"></param>
        /// <returns></returns>
        private static WaveChartModel GetTDChart(double[] waveData, double SampleRate)
        {
            WaveChartModel chartData = new WaveChartModel();
            chartData.XAxis = GetTDXData(SampleRate, waveData.Length);
            chartData.Formart = "{value} g";
            chartData.Yformart = "";
            List<YAxis> yList = new List<YAxis>();
            List<string> yName = new List<string>();
            YAxis y1 = new YAxis();
            y1.Name = "幅值(g)";
            y1.Type = "line";
            y1.Data = waveData;
            yName.Add("幅值(g)");
            yList.Add(y1);
            chartData.YAxisList = yList;
            chartData.DataName = yName.ToArray();
            return chartData;
        }
        /// <summary>
        /// 计算X轴
        /// </summary>
        /// <param name="fs"></param>
        /// <param name="samplingPointData"></param>
        /// <returns></returns>
        private static string[] GetTDXData(double fs, int samplingPointData)
        {
            List<string> xs = new List<string>();
            double s = (double)1 / fs;
            for (int i = 1; i <= samplingPointData; i++)
            {
                xs.Add((s * i).ToString());
            }
            return xs.ToArray();
        }

        public static double[] ConverByteToDouble(byte[] wave)
        {
            float[] fltData = new float[wave.Length / sizeof(float)];
            Buffer.BlockCopy(wave, 0, fltData, 0, wave.Length);

            List<double> douData = new List<double>();
            for (int i = 0; i < fltData.Length; i++)
            {
                douData.Add(Math.Round(fltData[i], 5));
            }
            return douData.ToArray();
        }
        #endregion
    }
    internal class WaveDataChart
    {
        /// <summary>
        ///  图表名称
        /// </summary>
        public string Name { get; set; }
        /// <summary>
        /// 子标题
        /// </summary>
        public string SubText { get; set; }

        public double Ydata { get; set; }

        public int Xdata { get; set; }
    }
}
