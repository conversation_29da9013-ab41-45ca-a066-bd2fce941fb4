import{u as me,c as fe,W as ve}from"./table-C_s53ALS.js";import{r as n,u as ye,y as we,j as De,h as be,w as he,f as k,d as i,A as ge,z as N,o as b,i as R,b as v,c as Ie,F as ke,e as _e,g as _,t as Te,s as U,m as s}from"./index-D9CxWmlM.js";import{O as xe}from"./index-DyCH3qIX.js";import{W as Ce}from"./index-R80zldKD.js";import{S as Le,c as C,t as Pe,a as Oe}from"./tools-DZBuE28U.js";import{u as We,g as Fe}from"./configRoot-CCVOK_kf.js";import{u as Ne}from"./model-CfkjPzDn.js";import{u as Re}from"./devTree-BD8aiOqS.js";import{D as Se,a as Me}from"./index-pF9HjW4G.js";import{B as qe}from"./index-BGEB0Rhf.js";import{M as Ae}from"./index-DhZUqjZm.js";import{_ as Be}from"./index-BdyfTxoi.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";/* empty css                                                              */import"./useRefs-DVBILCMZ.js";const Ve={class:"border"},$e={style:{float:"left"}},h=320,L="YYYY-MM-DD",mt={__name:"device",setup(je){const g=Ne(),o=me(),y=We(),l=Re(),P=e=>[{title:"设备编号",dataIndex:"windTurbineCode",columnWidth:"130",formItemWidth:h,hidden:e&&e.edit,isrequired:!0,headerOperations:{sorter:!0},columnOperate:{type:"number"}},{title:"设备名称",dataIndex:"windTurbineName",columnWidth:"130",formItemWidth:h,isrequired:!0,columnOperate:{type:"number"}},{title:"设备型号",dataIndex:"windTurbineModel",columnWidth:"140",formItemWidth:h,inputType:"select",isrequired:!0,selectOptions:[],disabled:e&&e.edit,headerOperations:{filters:[]}},{title:"部件",dataIndex:"componentIds",columnWidth:"200",formItemWidth:h,inputType:"select",mode:"tags",isrequired:!0,selectOptions:[],slotName:"part",hidden:e&&e.edit,...e&&e.isForm?{}:{customRender:({record:t})=>t.componentName?ge("span",{style:{textAlign:"left",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"500px"},title:t.componentName.join(",")},t.componentName.join(",")):""}},{title:"投递日期",dataIndex:"operationalDate",columnWidth:"120",formItemWidth:h,inputType:"datepicker",timeFormat:L,headerOperations:{sorter:!0,date:!0}},{title:"主控IP",dataIndex:"mcsIP",columnWidth:"180",formItemWidth:h,hidden:e&&e.edit,columnOperate:{type:"ip"},validateRules:C({type:"ip",title:"主控IP",required:!0})},{title:"设备坐标",dataIndex:"location",formItemWidth:h,validateRules:[{pattern:/^(\-?\d+(\.\d+)?),\s*(\-?\d+(\.\d+)?)$/,message:"请输入两个数字并用英文逗号隔开!"}]}],Y={strokeColor:{"0%":"#108ee9","100%":"#87d068"},strokeWidth:3,format:e=>`${parseFloat(e.toFixed(2))}%`,class:"test"},A=window.localStorage.getItem("templateManagement")==="true",B=n(null),O=n([]),I=n(""),r=n(""),w=n({}),V=n([]),u=n([]),W=n(!1),$=ye(),c=n($.params.id),H=n({}),T=n(!1),j=we({partValue:"",partList:[]});let S=[...Fe({isEdit:!0})];const E=(e={})=>[{label:"名称",value:e.windParkName},{label:"编号",value:e.windParkCode},{label:"投运日期",value:e.operationalDate?N(e.operationalDate).format(L):""},{label:"联系人",value:e.contactMan},{label:"联系人电话",value:e.contactTel},{label:"区域",value:`${e.country} - ${e.area}`},{label:"地址",value:e.address},{label:"经纬度",value:e.location},{label:"邮编",value:e.postCode},{label:"场站概况",value:e.description}],z=n(P()),J=De(()=>r.value==="batchAdd"?"1200px":"600px"),G=n([E({})]),x=async e=>{c.value&&(T.value=!0,V.value=await o.fetchDevTreedDevicelist({windParkID:c.value}),T.value=!1,z.value=P())},M=async()=>{if(c.value){const e=await o.fetchParkInfo({windParkID:c.value});H.value=e,G.value=E(e)}};be(()=>{M(),x()}),he(()=>$.params.id,e=>{o.reset(),c.value=e,M(),x()});const K=async()=>{r.value="editPark",I.value="编辑厂站信息";const e=o.parkInfo;let t=e.operationalDate?N(e.operationalDate,L):"";w.value={...e,operationalDate:t},y.groupCompanyList&&y.groupCompanyList.length>0?S[1].selectOptions=y.groupCompanyList:await Q(),u.value=[...S],F()},Q=async()=>{const e=await y.fetchGroupCompanyList();e&&e.length>0&&(S[1].selectOptions=e)},q=async()=>{g.modelOptions&&g.modelOptions.length>0||await g.fetchModellist()},X=async()=>{(!o.comPonentList||o.comPonentList.length<1)&&await o.fetchGetAllComPonentList()},F=()=>{W.value=!0},D=e=>{W.value=!1,j.partList=[],j.partValue="",w.value={},u.value=[],I.value="",r.value=""},Z=async e=>{const{operateType:t}=e;r.value=t,I.value="批量增加设备",await q(),await X();let a=P({isForm:!0});a[2].selectOptions=g.modelOptions,a[3].selectOptions=o.comPonentList,u.value=[...a],F()},ee=async(e={})=>{const{selectedkeys:t}=e,a=await o.fetchDeletetDevice(t);a&&a.code===1?(x(),D(),s.success("提交成功"),l.getDevTreeDatas()):s.error("提交失败:"+a.msg)},te=async e=>{const{rowData:t,operateType:a}=e;r.value=a;let p=t.operationalDate?N(t.operationalDate,L):"";w.value={...t,componentIds:t.componentIds&&t.componentIds.length?t.componentIds.split(","):[],operationalDate:p},I.value="编辑设备",await q();let d=P({edit:!0,isForm:!0});d[2].selectOptions=g.modelOptions,d[3].inputType="checkboxGroup",u.value=[...d],F()},ae=async()=>{r.value="copyDevice",I.value="复制机组",await l.getTemplateParkList(),await y.fetchParkList(),await q();let e=[],t=[];l.templateDeviceList&&l.templateDeviceList.length>0&&(e.push({label:"模版风场",value:l.templateDeviceList[0].id}),l.templateDevicoptions&&l.templateDevicoptions.length>0&&(t=l.templateDevicoptions)),e=[...e,...y.parkOptions];let a=ue({parklist:e,turbineList:t,modelList:g.modelOptions});u.value=a,F()},oe=async e=>(O.value=[e],!1),ie=async e=>{if(!e.file||e.fileList.length<1){s.error("请选择文件");return}T.value=!0;const t=new FormData;t.append("file",e.file);let a=await o.fetchTemplateUpload(t);a.code===1?(O.value=[],s.success("上传成功")):s.error("上传失败！"+a.msg),T.value=!1},le=()=>{console.log("导出"),o.fetchTemplateDownload({parkId:c.value})},ne=async()=>{o.fetchExportDauConfig({parkId:c.value})},se=async(e,t,a)=>{if(e.dataIndex==="windParkID"){if(e.value==l.templateDeviceList[0].windParkID)u.value[0].rows[1].cols[0].selectOptions=l.templateDevicoptions||[];else{let p=await o.fetchDevTreedDevicelist({windParkID:e.value});if(p&&p.length>0){let d=Oe(p,{label:"windTurbineName",value:"windTurbineID"},{nother:!0});u.value[0].rows[1].cols[0].selectOptions=d}else u.value[0].rows[1].cols[0].selectOptions=[]}B.value.setFieldValue("_windTurbineIDOld",null)}},re=async e=>{if(r.value=="editPark"){const t=await y.fetchEditWindparkInformation({...e,windParkID:c.value,windParkGroupName:e.windParkName});t&&t.code===1?(M(),s.success("提交成功"),e.windParkName!==o.parkInfo.windParkName&&l.getDevTreeDatas(),D()):s.error("提交失败:"+t.msg)}else if(r.value=="copyDevice"){const t=await o.fetchCopyTurbine({...e,_curParkId:c.value,_prefix:e._prefix||"",_suffix:e._suffix||"",startNum:e.startNum||""});t&&t.code===1?(s.success("复制成功"),D()):s.error("提交失败:"+t.msg)}else{let t=[{...e,componentIds:e.componentIds&&e.componentIds.length?e.componentIds.join(","):"",windTurbineID:w.value.windTurbineID,windParkID:w.value.windParkId}];const a=await o.fetchEditDevices(t);a&&a.code===1?(x(),s.success("提交成功"),e.windTurbineName!==w.value.windTurbineName&&l.getDevTreeDatas(),D()):s.error("提交失败:"+a.msg)}},de=async e=>{if(r.value=="batchAdd"){let t={WindTurbineID:"",WindParkId:c.value},p=Pe(e).map(m=>({...m,...t,componentIds:m.componentIds&&m.componentIds.length?m.componentIds.join(","):"",operationalDate:m.operationalDate?N(m.operationalDate).format(L):""}));const d=await o.fetchAddDevice(p);d&&d.code===1?(x(),D(),s.success("提交成功"),l.getDevTreeDatas()):s.error("提交失败:"+d.msg)}},ue=(e={parklist:[],turbineList:[],modelList:[]})=>[{title:"设备模版",key:"设备模版",isrequired:!0,rows:[{cols:[{inputType:"select",selectOptions:e.parklist,formItemWidth:400,dataIndex:"windParkID",hasChangeEvent:!0,validateRules:C({title:"风场",required:!0})}]},{cols:[{dataIndex:"_windTurbineIDOld",inputType:"select",selectOptions:e.turbineList,formItemWidth:400,validateRules:C({title:"设备",required:!0})}]}]},{title:"复制数量",key:"复制数量",isrequired:!0,rows:[{cols:[{dataIndex:"_num",type:"input",formItemWidth:400,validateRules:C({type:"integer",title:"复制数量",required:!0})}]}]},{title:"复制后机组名称",key:"机组名称模板",rows:[{cols:[{title:"前缀",dataIndex:"_prefix",type:"input",formItemWidth:160},{title:"后缀",dataIndex:"_suffix",type:"input",formItemWidth:160}]},{cols:[{dataIndex:"startNum",title:"起始编号",type:"input",formItemWidth:140}]}]},{title:"机组型号",key:"机组型号",isrequired:!0,rows:[{cols:[{dataIndex:"turbineModel",inputType:"select",formItemWidth:400,selectOptions:e.modelList,validateRules:C({title:"机组型号",required:!0})}]}]}];return(e,t)=>{const a=qe,p=Me,d=Se,m=Be,ce=Ae,pe=Le;return b(),k(pe,{spinning:T.value,size:"large"},{default:i(()=>[R("div",null,[v(fe,{tableTitle:"厂站信息",defaultCollapse:!0,batchApply:!1},{rightButtons:i(()=>[v(a,{type:"primary",onClick:t[0]||(t[0]=f=>K())},{default:i(()=>t[5]||(t[5]=[_(" 编辑 ",-1)])),_:1,__:[5]})]),content:i(()=>[R("div",Ve,[v(d,{column:5,size:"small"},{default:i(()=>[(b(!0),Ie(ke,null,_e(G.value,f=>(b(),k(p,{label:f.label},{default:i(()=>[_(Te(f.value),1)]),_:2},1032,["label"]))),256))]),_:1})])]),_:1}),R("div",null,[v(ve,{ref:"table",size:"default","table-key":"1","table-title":"设备列表","table-columns":z.value,"table-operate":["edit","delete","add","batchDelete","batchAdd"],"record-key":"windTurbineID","table-datas":V.value,onAddRow:Z,onDeleteRow:ee,onEditRow:te,noBatchApply:!0},{rightButtons:i(()=>[R("span",$e,[A?(b(),k(m,{key:0,"file-list":O.value,"onUpdate:fileList":t[1]||(t[1]=f=>O.value=f),name:"file",progress:Y,onChange:ie,"before-upload":oe,"show-upload-list":!1},{default:i(()=>[v(a,{type:"primary",style:{float:"none"}},{default:i(()=>t[6]||(t[6]=[_(" 导入 ",-1)])),_:1,__:[6]})]),_:1},8,["file-list"])):U("",!0)]),A?(b(),k(a,{key:0,type:"primary",onClick:t[2]||(t[2]=f=>le())},{default:i(()=>t[7]||(t[7]=[_(" 导出 ",-1)])),_:1,__:[7]})):U("",!0),v(a,{type:"primary",onClick:t[3]||(t[3]=f=>ae())},{default:i(()=>t[8]||(t[8]=[_(" 复制机组 ",-1)])),_:1,__:[8]}),v(a,{type:"primary",onClick:t[4]||(t[4]=f=>ne())},{default:i(()=>t[9]||(t[9]=[_(" 导出配置 ",-1)])),_:1,__:[9]})]),_:1},8,["table-columns","table-datas"])]),v(ce,{maskcolsable:!1,width:J.value,open:W.value,title:I.value,footer:"",onCancel:D},{default:i(()=>[r.value==="batchAdd"?(b(),k(Ce,{key:0,ref:"table",size:"default","table-key":"0","table-columns":u.value,"table-operate":["copyUp","delete"],"table-datas":[],onSubmit:de,onCancel:D},{footer:i(()=>t[10]||(t[10]=[])),_:1},8,["table-columns"])):(b(),k(xe,{key:W.value,titleCol:u.value,initFormData:w.value,ref_key:"operateFormRef",ref:B,onChange:se,onSubmit:re,formlayout:r.value=="copyDevice"?"table":"horizontal"},null,8,["titleCol","initFormData","formlayout"]))]),_:1},8,["width","open","title"])])]),_:1},8,["spinning"])}}};export{mt as default};
