using System.Xml.Serialization;
using AppFramework.Utility;

namespace WTCMSLive.WebSite.Core.Models
{
    /// <summary>
    /// 报警阈值配置文件根节点
    /// </summary>
    [XmlRoot("AlarmThreshold_ConfigFile")]
    public class AlarmThreshold_ConfigFile
    {
        private static AlarmThreshold_ConfigFile _configFile;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 振动报警阈值配置
        /// </summary>
        [XmlElement("VibrationAlarmThreshold")]
        public VibrationAlarmThreshold VibrationAlarmThreshold { get; set; }

        /// <summary>
        /// 晃度报警阈值配置
        /// </summary>
        [XmlElement("SVMAlarmThreshold")]
        public SVMAlarmThreshold SVMAlarmThreshold { get; set; }

        /// <summary>
        /// 油液报警阈值配置
        /// </summary>
        [XmlElement("OilAlarmThreshold")]
        public OilAlarmThreshold OilAlarmThreshold { get; set; }

        /// <summary>
        /// 获取报警阈值配置文件实例（单例模式）
        /// </summary>
        /// <returns></returns>
        public static AlarmThreshold_ConfigFile GetAlarmThresholdConfigFile()
        {
            if (_configFile == null)
            {
                lock (_lockObject)
                {
                    if (_configFile == null)
                    {
                        LoadConfigFile();
                    }
                }
            }
            return _configFile;
        }

        /// <summary>
        /// 加载配置文件
        /// </summary>
        private static void LoadConfigFile()
        {
            try
            {
                string configPath = Path.Combine(AppContext.BaseDirectory, "AlarmThresholdConfig.xml");
                if (File.Exists(configPath))
                {
                    _configFile = XmlFileHelper<AlarmThreshold_ConfigFile>.Load(configPath);
                    CMSFramework.Logger.Logger.LogInfoMessage($"成功加载报警阈值配置文件: {configPath}");
                }
                else
                {
                    _configFile = CreateDefaultConfig();
                    CMSFramework.Logger.Logger.LogWarningMessage($"报警阈值配置文件不存在，使用默认配置: {configPath}");
                }
            }
            catch (Exception ex)
            {
                _configFile = CreateDefaultConfig();
                CMSFramework.Logger.Logger.LogErrorMessage("加载报警阈值配置文件失败，使用默认配置", ex);
            }
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        /// <returns></returns>
        private static AlarmThreshold_ConfigFile CreateDefaultConfig()
        {
            return new AlarmThreshold_ConfigFile
            {
                VibrationAlarmThreshold = new VibrationAlarmThreshold { ThresholdList = new List<AlarmThreshold_ConfigItem>() },
                SVMAlarmThreshold = new SVMAlarmThreshold { ThresholdList = new List<AlarmThreshold_ConfigItem>() },
                OilAlarmThreshold = new OilAlarmThreshold { ThresholdList = new List<AlarmThreshold_ConfigItem>() }
            };
        }

        /// <summary>
        /// 获取所有阈值配置列表
        /// </summary>
        /// <returns></returns>
        public List<AlarmThreshold_ConfigItem> GetAllThresholdList()
        {
            var allList = new List<AlarmThreshold_ConfigItem>();
            
            if (VibrationAlarmThreshold?.ThresholdList != null)
                allList.AddRange(VibrationAlarmThreshold.ThresholdList);
            
            if (SVMAlarmThreshold?.ThresholdList != null)
                allList.AddRange(SVMAlarmThreshold.ThresholdList);
            
            if (OilAlarmThreshold?.ThresholdList != null)
                allList.AddRange(OilAlarmThreshold.ThresholdList);
            
            return allList;
        }
    }

    /// <summary>
    /// 振动报警阈值配置
    /// </summary>
    public class VibrationAlarmThreshold
    {
        [XmlArray("ThresholdList")]
        [XmlArrayItem("AlarmThreshold_ConfigItem")]
        public List<AlarmThreshold_ConfigItem> ThresholdList { get; set; }
    }

    /// <summary>
    /// 晃度报警阈值配置
    /// </summary>
    public class SVMAlarmThreshold
    {
        [XmlArray("ThresholdList")]
        [XmlArrayItem("AlarmThreshold_ConfigItem")]
        public List<AlarmThreshold_ConfigItem> ThresholdList { get; set; }
    }

    /// <summary>
    /// 油液报警阈值配置
    /// </summary>
    public class OilAlarmThreshold
    {
        [XmlArray("ThresholdList")]
        [XmlArrayItem("AlarmThreshold_ConfigItem")]
        public List<AlarmThreshold_ConfigItem> ThresholdList { get; set; }
    }

    /// <summary>
    /// 报警阈值配置项
    /// </summary>
    public class AlarmThreshold_ConfigItem
    {
        /// <summary>
        /// 部件名称
        /// </summary>
        [XmlElement("ComponentName")]
        public string ComponentName { get; set; } = string.Empty;

        /// <summary>
        /// 截面名称
        /// </summary>
        [XmlElement("SectionName")]
        public string SectionName { get; set; } = string.Empty;

        /// <summary>
        /// 方向
        /// </summary>
        [XmlElement("Orientation")]
        public string Orientation { get; set; } = string.Empty;

        /// <summary>
        /// 特征值代码
        /// </summary>
        [XmlElement("EigenValueCode")]
        public string EigenValueCode { get; set; } = string.Empty;

        /// <summary>
        /// 特征值名称
        /// </summary>
        [XmlElement("EigenValueName")]
        public string EigenValueName { get; set; } = string.Empty;

        /// <summary>
        /// 正向注意阈值
        /// </summary>
        [XmlElement("ForwardWarning")]
        public string ForwardWarningStr { get; set; } = string.Empty;

        /// <summary>
        /// 正向危险阈值
        /// </summary>
        [XmlElement("ForwardAlarm")]
        public string ForwardAlarmStr { get; set; } = string.Empty;

        /// <summary>
        /// 反向注意阈值
        /// </summary>
        [XmlElement("ReverseWarning")]
        public string ReverseWarningStr { get; set; } = string.Empty;

        /// <summary>
        /// 反向危险阈值
        /// </summary>
        [XmlElement("ReverseAlarm")]
        public string ReverseAlarmStr { get; set; } = string.Empty;

        /// <summary>
        /// 工况条件
        /// </summary>
        [XmlElement("WorkCondition")]
        public string WorkCondition { get; set; } = "WCPT_NOWORKCONDTION";

        /// <summary>
        /// 排序
        /// </summary>
        [XmlElement("Order")]
        public int Order { get; set; }

        // 属性转换器，将字符串转换为可空的double
        [XmlIgnore]
        public double? ForwardWarning => ParseDoubleValue(ForwardWarningStr);

        [XmlIgnore]
        public double? ForwardAlarm => ParseDoubleValue(ForwardAlarmStr);

        [XmlIgnore]
        public double? ReverseWarning => ParseDoubleValue(ReverseWarningStr);

        [XmlIgnore]
        public double? ReverseAlarm => ParseDoubleValue(ReverseAlarmStr);

        /// <summary>
        /// 解析字符串为可空double值
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private double? ParseDoubleValue(string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return null;
            
            if (double.TryParse(value, out double result))
                return result;
            
            return null;
        }
    }

    /// <summary>
    /// 阈值数据传输对象
    /// </summary>
    public class ThresholdValues
    {
        public double? ForwardWarning { get; set; }
        public double? ForwardAlarm { get; set; }
        public double? ReverseWarning { get; set; }
        public double? ReverseAlarm { get; set; }
        public string WorkCondition { get; set; } = "WCPT_NOWORKCONDTION";
    }
}
