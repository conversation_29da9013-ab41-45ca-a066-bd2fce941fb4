import{W as N}from"./table-C_s53ALS.js";import{O as V}from"./index-DyCH3qIX.js";import{C as Z,Q as $,R as E,S as P,r,y as A,w as K,f as x,d as i,u as Q,o as y,i as o,b as l,g as c,x as I,c as j,m}from"./index-D9CxWmlM.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{S as H}from"./tools-DZBuE28U.js";import{_ as X}from"./index-BdyfTxoi.js";import{B as Y}from"./index-BGEB0Rhf.js";import{U}from"./UploadOutlined-B1LxZD_u.js";import{M as ee}from"./index-DhZUqjZm.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";import"./useRefs-DVBILCMZ.js";const te=Z("parameterImport",{state:()=>({modelList:[],modelOptions:[]}),actions:{reset(){this.$reset()},async fetchCopyTurbineBoltBD(p){try{return await P(p)}catch(a){throw console.error("获取失败:",a),a}},async fetchBoltBDFileUpdate(p){try{return await E(p)}catch(a){throw console.error("操作失败:",a),a}},async fetchBoltJZFileUpdate(p){try{return await $(p)}catch(a){throw console.error("操作失败:",a),a}}}}),ae={class:"uploadFileBox"},oe={class:"uploadBox"},le={class:"uploadItem"},se={class:"uploadItem"},ne={key:1},re={__name:"parameterImport",setup(p){const a=te(),k=Q(),B=r(""),f=r(""),h=r({}),_=r([]),v=r(!1),b=r(!1),u=r([]),d=r([]),C=A({tableData:[]}),w=async()=>{v.value=!0,C.tableData=await a.fetchCopyTurbineBoltBD(),v.value=!1};K(()=>k.params.id,async t=>{w()},{immediate:!0});const T=t=>(u.value=[t],!1),L=t=>(d.value=[t],!1),S=t=>{(!t.fileList||t.fileList.length===0)&&(u.value=[])},W=t=>{(!t.fileList||t.fileList.length===0)&&(d.value=[])},M=async t=>{const{rowData:e,tableKey:s,operateType:n,title:g}=t;f.value=n,h.value={...e},B.value="编辑偏移量",_.value=[F[4]],O()},z=async t=>{},D=async t=>{let e=[];if(t==="1"?e=[...u.value]:e=[...d.value],e.length===0){m.error("请选择文件");return}const s=new FormData;s.append("file",e[0]);let n={};t==="1"?n=await a.fetchBoltJZFileUpdate(s):n=await a.fetchBoltBDFileUpdate(s),n.code===1?(t==="1"?(m.success("导入螺栓基准成功"),u.value=[]):(m.success("导入螺栓标定成功"),d.value=[]),w()):m.error("上传失败！"+n.msg)},F=[{title:"场站",dataIndex:"windParkID",columnWidth:80},{title:"螺栓型号",dataIndex:"boltModel",columnWidth:100},{title:"预紧力系数",dataIndex:"preloadCalCoeffs",columnWidth:70},{title:"温度系数",dataIndex:"tempCalibCoeff",columnWidth:110},{title:"偏移量",dataIndex:"",columnWidth:100}],O=()=>{b.value=!0},R=t=>{b.value=!1,_.value=[]};return(t,e)=>{const s=Y,n=X,g=ee,q=H;return y(),x(q,{spinning:v.value,size:"large"},{default:i(()=>[o("div",null,[o("div",ae,[o("ul",oe,[o("li",null,[e[4]||(e[4]=o("span",null,"上传: ",-1)),o("div",le,[l(n,{"file-list":u.value,"before-upload":T,accept:".zip",onChange:S},{default:i(()=>[l(s,null,{default:i(()=>[l(I(U)),e[2]||(e[2]=c(" 导入螺栓基准 ",-1))]),_:1,__:[2]})]),_:1},8,["file-list"])]),l(s,{type:"primary",class:"uploadFileBtn",onClick:e[0]||(e[0]=J=>D("1"))},{default:i(()=>e[3]||(e[3]=[c("确定",-1)])),_:1,__:[3]})]),o("li",null,[e[7]||(e[7]=o("span",null,"上传: ",-1)),o("div",se,[l(n,{"file-list":d.value,"before-upload":L,accept:".zip",onChange:W},{default:i(()=>[l(s,null,{default:i(()=>[l(I(U)),e[5]||(e[5]=c(" 导入螺栓标定 ",-1))]),_:1,__:[5]})]),_:1},8,["file-list"])]),l(s,{type:"primary",class:"uploadFileBtn",onClick:e[1]||(e[1]=J=>D("2"))},{default:i(()=>e[6]||(e[6]=[c("确定",-1)])),_:1,__:[6]})])])]),o("div",null,[l(N,{ref:"table",size:"default","table-key":"0","table-title":"螺栓标定批量配置列表","table-columns":F,"table-operate":["edit"],"record-key":"boltModel","table-datas":C.tableData,noBatchApply:!0,onEditRow:M},null,8,["table-datas"])]),l(g,{maskClosable:!1,width:"600px",open:b.value,title:B.value,footer:"",onCancel:R},{default:i(()=>[f.value==="add"||f.value==="edit"?(y(),x(V,{key:0,titleCol:_.value,initFormData:h.value,onSubmit:z},null,8,["titleCol","initFormData"])):(y(),j("div",ne))]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},Ie=G(re,[["__scopeId","data-v-5f0eed13"]]);export{Ie as default};
