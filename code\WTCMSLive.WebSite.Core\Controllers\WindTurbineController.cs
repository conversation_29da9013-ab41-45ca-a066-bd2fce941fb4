﻿using System.Web;
using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using CMSFramework.FSDB;
using CMSFramework.BusinessEntity.SVM;
using CMSFramework.EigenValueDef;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Net;
using WTCMSLive.WebSite.Core.Models.DTOs;
using AppFramework.Utility;
using Microsoft.AspNetCore.Authorization;
namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WindTurbineController : ControllerBase
    {
        //存储用户自定义趋势图特征值列表信息
        public static EigenTrendConfig EigenTrendConfigData = new EigenTrendConfig();
        private static string EigenConfigPath = "";


        #region 机组级总览页面


        private string GetNowWorkingCondTime(string turbineID)
        {
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                //modified by sq 20190222 只显示包含晃度仪的最新时间。
                WorkingConditionData NowWorkingCond = ctx.WorkingConditionDatas.Where(p => p.WindTurbineID == turbineID && p.MeasLocationID.Contains("TOWER41")).OrderByDescending(item => item.AcquisitionTime).FirstOrDefault();
                if(NowWorkingCond != null)
                {
                    return NowWorkingCond.AcquisitionTime.ToString();
                }
                return "";
            }
        }

        //工况最新记录
        /// <summary>
        /// 获取最新的工况波形
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        private string GetNowWavePath( string turbineID)
        {
            //获取最新的工况时间
            WorkingConditionData NowWorkingCond = new WorkingConditionData();
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                NowWorkingCond = ctx.WorkingConditionDatas.Where(p => p.WindTurbineID == turbineID).OrderByDescending(item => item.AcquisitionTime).FirstOrDefault();
            }
            if (NowWorkingCond != null)
            {
                //根据工况最新时间点查找工况波形
                //List<WorkConditionWaveFormData> list = new List<WorkConditionWaveFormData>();
                string NowWKwave = null;
                using (CMSFramework.EF.WFDataContext_Day ctx = new CMSFramework.EF.WFDataContext_Day(ConfigInfo.DBConnName))
                {
                    WorkConditionWaveFormData WorkCondWfDay = ctx.WorkConditionWaves.Where(p => p.WindTurbineID == turbineID && p.AcquisitionTime == NowWorkingCond.AcquisitionTime).FirstOrDefault();
                    if (WorkCondWfDay != null)
                    {
                        NowWKwave =  WorkCondWfDay.WaveDataPath;
                    }
                };
                using (CMSFramework.EF.WFDataContext_His ctx = new CMSFramework.EF.WFDataContext_His(ConfigInfo.DBConnName))
                {
                    WorkConditionWaveFormData WorkCondWfHis = ctx.WorkConditionWaves.Where(p => p.WindTurbineID == turbineID && p.AcquisitionTime == NowWorkingCond.AcquisitionTime).FirstOrDefault();

                    if (WorkCondWfHis != null)
                    {
                        NowWKwave = WorkCondWfHis.WaveDataPath;
                    }
                };
                using (CMSFramework.EF.WFDataContext_Hour ctx = new CMSFramework.EF.WFDataContext_Hour(ConfigInfo.DBConnName))
                {
                    WorkConditionWaveFormData WorkCondWfHour = ctx.WorkConditionWaves.Where(p => p.WindTurbineID == turbineID && p.AcquisitionTime == NowWorkingCond.AcquisitionTime).FirstOrDefault();
                    if (WorkCondWfHour != null)
                    {
                        NowWKwave = WorkCondWfHour.WaveDataPath;
                    }
                }
                return NowWKwave;
            }else
            {
                return null;
            }
            
        }


        //public JsonResult GetTowOverViewData(string windParkID, string turbineID)
        //{
        //    List<TowPoint> pointList = new List<TowPoint>();
        //    //List<List<double>> list = new List<List<double>>();
        //    //判断角度所在的象限
        //    List<WorkingConditionData> TowWkData = RealTimeDataManage.GetAllWorkingCondDataListByTurbineID(turbineID);
        //    if (TowWkData.Count > 0)
        //    {
        //        DateTime lastAcqDate = TowWkData.OrderByDescending(item => item.AcquisitionTime).FirstOrDefault().AcquisitionTime;
        //        //如果有采集到的数据，获取最新的工况波形文件
        //        string filePath = System.Configuration.ConfigurationManager.AppSettings["EVDBDirectory"];
        //        filePath += string.Format(@"\WorkCondDB\{0}\{1}\{2}\{3}_{1}_",
        //            windParkID, turbineID, DateTime.Now.ToString("yyyyMMdd"), lastAcqDate.ToString("yyyyMMddHHmmss"));
        //        string theta_XFile = filePath + "TOWERTIMX";
        //        string theta_YFile = filePath + "TOWERTIMY";
        //        string theta_offsetFile = filePath + "TOWER42";
        //        string theta_actrualFile = filePath + "TOWER41";
        //        if (System.IO.File.Exists(theta_XFile) == false)
        //        {
        //            return Json(pointList, JsonRequestBehavior.AllowGet);
        //        }
        //        List<float> theta_XList = AppFramework.Utility.XmlFileHelper<List<float>>.Load(theta_XFile);
        //        List<float> theta_YList = AppFramework.Utility.XmlFileHelper<List<float>>.Load(theta_YFile);
        //        List<float> theta_offsetList = AppFramework.Utility.XmlFileHelper<List<float>>.Load(theta_offsetFile);
        //        List<float> theta_actrualList = AppFramework.Utility.XmlFileHelper<List<float>>.Load(theta_actrualFile);
        //        for (int i = 0; i < theta_XList.Count; i++)
        //        {
        //            TowPoint point = new TowPoint();
        //            if (i == 0)
        //                point.AcquisitionTime = lastAcqDate.ToString("yyyy-MM-dd HH:mm:ss");
        //            GetPoint(point, theta_XList[i], theta_YList[i], theta_offsetList[i], theta_actrualList[i]);
        //            pointList.Add(point);
        //        }
        //    }
        //    return Json(pointList, JsonRequestBehavior.AllowGet);
        //}


        /// <summary>
        /// 获取风场采集单元状态列表和统计信息
        /// </summary>
        /// <param name="WindParkID">风场ID</param>
        /// <returns>DAU列表和状态统计</returns>
        [HttpGet("GetDAUListByParkID")]
        public IActionResult GetDAUListByParkID(string WindParkID)
        {
            try
            {
                if (string.IsNullOrEmpty(WindParkID))
                {
                    return BadRequest("风场ID不能为空");
                }

                // 获取DAU基本信息列表
                List<WindDAU> dauList = DauManagement.GetDAUListByWindParkID(WindParkID);

                // 获取机组信息用于关联机组名称
                List<WindTurbine> turbineList = DevTreeManagement.GetTurbinesListByWindParkId(WindParkID);

                // 获取DAU实时状态信息
                List<RTAlarmStatus_DAU> statusList = DAUConditionManagement.GetDAURTAlarmStatusByWindParkId(WindParkID);

                // 转换为DTO格式
                var result = ConvertToDAUListResponse(dauList, turbineList, statusList, WindParkID);

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetDAUListByParkID]获取风场DAU列表失败 - 风场:{WindParkID}", ex);
                return Ok(new DAUListResponseDTO
                {
                    WindParkID = WindParkID ?? "",
                    StatusSummary = new DAUStatusSummaryDTO(),
                    DAUList = new List<DAUItemDTO>(),
                    DataTime = DateTime.Now
                });
            }
        }


        /// <summary>
        /// 获取全部DAU状态统计及各风场的DAU状态详情
        /// </summary>
        /// <returns>DAU状态统计总览</returns>
        [HttpGet("GetDauStatusCount")]
        public IActionResult GetDauStatusCount()
        {
            try
            {
                // 获取所有风场信息
                List<WindPark> parkList = DevTreeManagement.GetWindParkList();

                // 获取所有DAU信息和状态
                List<WindDAU> allDAUList = new List<WindDAU>();
                List<RTAlarmStatus_DAU> allStatusList = new List<RTAlarmStatus_DAU>();
                List<WindTurbine> allTurbineList = new List<WindTurbine>();

                foreach (var park in parkList)
                {
                    // 获取风场下的机组（用于获取机组名称）
                    var turbines = DevTreeManagement.GetTurbinesListByWindParkId(park.WindParkID);
                    allTurbineList.AddRange(turbines);

                    // 获取风场下的DAU
                    var dauList = DauManagement.GetDAUListByWindParkID(park.WindParkID);
                    allDAUList.AddRange(dauList);

                    // 获取风场下的DAU状态
                    var statusList = DAUConditionManagement.GetDAURTAlarmStatusByWindParkId(park.WindParkID);
                    allStatusList.AddRange(statusList);
                }

                // 转换为统计数据
                var result = ConvertToDAUStatusCountResponse(parkList, allTurbineList, allDAUList, allStatusList);

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetDauStatusCount]获取DAU状态统计失败", ex);
                return Ok(new DAUStatusCountResponseDTO
                {
                    OverallSummary = new DAUStatusSummaryDTO(),
                    WindParkStatusList = new List<WindParkDAUStatusDTO>(),
                    DataTime = DateTime.Now
                });
            }
        }


        public static List<float> ByteArrayToFloatArray(byte[] byteArray)
        {
            //float[] array = new float[byteArray.Length / 4];
            List<float> list = new List<float>();
            for (int i = 0; i < byteArray.Length; i += 4)
            {
                list.Add(ConvertToFloat(byteArray,i));
            }

            return list;
        }

        //  转换 byte[] 为float
        public static float ConvertToFloat(byte[] data, int index)
        {
            var nTemp = new float[1];

            Buffer.BlockCopy(data, index, nTemp, 0, 4);

            return nTemp[0];
        }

        private static void GetPoint(TowPoint point, double theta_X, double theta_Y, double theta_offset, double theta_actrual)
        {
            double pointX, pointY;
            pointX = theta_actrual * Math.Sin(theta_offset);
            pointY = theta_actrual * Math.Cos(theta_offset);
            if (theta_X >= 0 && theta_Y < 0)
            {
                pointX = pointX * -1;
                point.LimitField = 4;
            }
            else if (theta_X < 0 && theta_Y < 0)
            {
                pointX = pointX * -1;
                pointY = pointY * -1;
                point.LimitField = 3;
            }
            else if (theta_X < 0 && theta_Y >= 0)
            {
                pointY = pointY * -1;
                point.LimitField = 2;
            }
            else {
                point.LimitField = 1;
            }
            point.X = pointX;
            point.Y = pointY;
        }
        #endregion 机组级总览页面



        #region 特征值详细页面

        /// <summary>
        /// 获取机组实时特征值数据
        /// </summary>
        /// <param name="windParkID">风场ID</param>
        /// <param name="turbineID">机组ID</param>
        /// <returns>特征值数据列表（通过type区分）</returns>
        [HttpGet("GetEigenValueRT")]
        public ActionResult GetEigenValueRT(string windParkID, string turbineID)
        {
            try
            {
                if (string.IsNullOrEmpty(turbineID))
                {
                    return BadRequest("机组ID不能为空");
                }

                EigenValueManager eigenValueManager = new EigenValueManager();

                // 获取振动特征值数据
                List<EigenValueData_Vib> vibrationDataList = eigenValueManager.GetRTEigenValueList(turbineID);

                // 获取SVM特征值数据
                List<EigenValueData_SVM> svmDataList = RealTimeDataManage.GetSVMRTEVDataListByTurID(turbineID);

                // 合并数据到统一列表
                var result = new List<EigenValueItemDTO>();

                // 添加振动数据
                result.AddRange(ConvertVibrationDataToUnified(vibrationDataList,turbineID));

                // 添加SVM数据
                result.AddRange(ConvertSVMDataToUnified(svmDataList, turbineID));

                // 按测量位置和特征值名称排序
                result = result.OrderBy(x => x.MeasLocationName).ThenBy(x => x.EigenValueName).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[GetEigenValueRT]获取机组 {turbineID} 特征值数据失败", ex);
                return Ok(new List<EigenValueItemDTO>());
            }
        }


        /// <summary>
        /// 获取工况实时特征值
        /// </summary>
        /// <param name="windParkID"></param>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        [HttpGet("GetWorkCondEVRT")]
        public IActionResult GetWorkCondEVRT(string windParkID, string turbineID)
        {
            return Ok(GetWorkingConditionInfo(turbineID, ""));
        }

        #region 特征值数据转换辅助方法

        /// <summary>
        /// 转换振动特征值数据
        /// </summary>
        /// <param name="vibrationDataList">振动特征值数据列表</param>
        /// <returns>振动特征值DTO列表</returns>
        private List<VibrationEigenValueDTO> ConvertVibrationData(List<EigenValueData_Vib> vibrationDataList,string turbineID)
        {
            var result = new List<VibrationEigenValueDTO>();

            if (vibrationDataList == null || vibrationDataList.Count == 0)
            {
                return result;
            }
            List<MeasLoc_Vib> measDefList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            foreach (var item in vibrationDataList)
            {
                // 过滤掉固有频率和ICEI特征值（与原逻辑保持一致）
                if (item.EigenValueCode.Contains("NF") || item.EigenValueCode.Contains("ICEI"))
                {
                    continue;
                }
                var measloc = measDefList.FirstOrDefault(t => t.MeasLocationID == item.MeasLocationID);
                var dto = new VibrationEigenValueDTO
                {
                    MeasLocationID = item.MeasLocationID,
                    MeasLocationName = measloc?.MeasLocName ?? "",
                    EigenValueCode = item.EigenValueCode,
                    EigenValueName = EigenValueManage.GetFreBandByCode(item.EigenValueCode).Equals("") ? item.EigenValueCode : EigenValueManage.GetFreBandByCode(item.EigenValueCode),
                    EigenValue = item.Eigen_Value,
                    EngUnit = item.EngUnitName ?? "",
                    EngUnitName = item.EngUnitName ?? "",
                    AcquisitionTime = item.AcquisitionTime,
                    WindTurbineID = item.WindTurbineID,
                    MeasDefinitionID = item.MeasDefinitionID ?? "",
                    AlarmDegree = (int)item.AlarmDegree,
                    DataType = "1"
                };

                result.Add(dto);
            }

            return result.OrderBy(x => x.MeasLocationName).ThenBy(x => x.EigenValueName).ToList();
        }

        /// <summary>
        /// 转换SVM特征值数据
        /// </summary>
        /// <param name="svmDataList">SVM特征值数据列表</param>
        /// <param name="turbineID">机组ID</param>
        /// <returns>SVM特征值DTO列表</returns>
        private List<SVMEigenValueDTO> ConvertSVMData(List<EigenValueData_SVM> svmDataList, string turbineID)
        {
            var result = new List<SVMEigenValueDTO>();

            if (svmDataList == null || svmDataList.Count == 0)
            {
                return result;
            }

            // 获取SVM测量位置信息
            List<MeasLoc_SVM> locList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);

            foreach (var item in svmDataList)
            {
                var measLoc = locList.FirstOrDefault(l => l.MeasLocationID == item.MeasLocationID);

                var dto = new SVMEigenValueDTO
                {
                    MeasLocationID = item.MeasLocationID,
                    MeasLocationName = measLoc?.MeasLocName ?? item.MeasLocationID,
                    EigenValueCode = item.EigenValueID,
                    EigenValueName = CompoundSVMEVName(item.EigenValueID) ?? EnumHelper.GetDescription(item.EigenValueType),
                    EigenValue = item.Eigen_Value,
                    EngUnit = item.EngUnitName ?? "",
                    EngUnitName = item.EngUnitName ?? "",
                    AcquisitionTime = item.AcquisitionTime,
                    WindTurbineID = item.WindTurbineID,
                    MeasDefinitionID = item.MeasDefinitionID ?? "",
                    AlarmDegree = (int)item.AlarmDegree,
                    DataType = "0",
                    SVMParamType = measLoc?.MeasLocName ?? ""
                };

                result.Add(dto);
            }

            return result.OrderBy(x => x.MeasLocationName).ThenBy(x => x.EigenValueName).ToList();
        }
        private string CompoundSVMEVName(string evid)
        {
            string str = null;
            if (evid.Contains("&&Actrual"))
            {
                str = "合成倾角";
            }
            else if (evid.Contains("&&Offset"))
            {
                str = "合成方向";
            }
            return str;
        }

        /// <summary>
        /// 获取报警等级描述
        /// </summary>
        /// <param name="alarmDegree">报警等级</param>
        /// <returns>报警等级描述</returns>
        private string GetAlarmDegreeDescription(EnumAlarmDegree alarmDegree)
        {
            return alarmDegree switch
            {
                EnumAlarmDegree.AlarmDeg_Unknown => "未知",
                EnumAlarmDegree.AlarmDeg_Normal => "正常",
                EnumAlarmDegree.AlarmDeg_SystemError => "系统异常",
                EnumAlarmDegree.AlarmDeg_Warning => "注意",
                EnumAlarmDegree.AlarmDeg_Alarm => "危险",
                _ => "其他"
            };
        }

        /// <summary>
        /// 获取报警颜色
        /// </summary>
        /// <param name="alarmDegree">报警等级</param>
        /// <returns>报警颜色</returns>
        private string GetAlarmColor(EnumAlarmDegree alarmDegree)
        {
            return alarmDegree switch
            {
                EnumAlarmDegree.AlarmDeg_Unknown => "#A0A0A0",
                EnumAlarmDegree.AlarmDeg_Normal => "#00FF00",
                EnumAlarmDegree.AlarmDeg_SystemError => "#0000FF",
                EnumAlarmDegree.AlarmDeg_Warning => "#FFFF00",
                EnumAlarmDegree.AlarmDeg_Alarm => "#FF0000",
                _ => "#808080"
            };
        }

        /// <summary>
        /// 转换振动特征值数据为统一格式
        /// </summary>
        /// <param name="vibrationDataList">振动特征值数据列表</param>
        /// <returns>统一特征值数据列表</returns>
        private List<EigenValueItemDTO> ConvertVibrationDataToUnified(List<EigenValueData_Vib> vibrationDataList,string turbineID)
        {
            var result = new List<EigenValueItemDTO>();

            if (vibrationDataList == null || vibrationDataList.Count == 0)
            {
                return result;
            }
            List<MeasLoc_Vib> measDefList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            foreach (var item in vibrationDataList)
            {
                // 过滤掉固有频率和ICEI特征值（与原逻辑保持一致）
                if (item.EigenValueCode.Contains("NF") || item.EigenValueCode.Contains("ICEI"))
                {
                    continue;
                }
                var measloc = measDefList.FirstOrDefault(t => t.MeasLocationID == item.MeasLocationID);
                var dto = new EigenValueItemDTO
                {
                    MeasLocationID = item.MeasLocationID,
                    MeasLocationName = measloc?.MeasLocName?? "",
                    EigenValueCode = item.EigenValueCode,
                    EigenValueName = EigenValueManage.GetFreBandByCode(item.EigenValueCode).Equals("") ? item.EigenValueCode : EigenValueManage.GetFreBandByCode(item.EigenValueCode),
                    EigenValue = item.Eigen_Value,
                    EngUnit = item.EngUnitName ?? "",
                    EngUnitName = item.EngUnitName ?? "",
                    AcquisitionTime = item.AcquisitionTime,
                    WindTurbineID = item.WindTurbineID,
                    MeasDefinitionID = item.MeasDefinitionID ?? "",
                    AlarmDegree = (int)item.AlarmDegree,
                    AlarmDegreeDescription = GetAlarmDegreeDescription(item.AlarmDegree),
                    AlarmColor = GetAlarmColor(item.AlarmDegree),
                    DataType = "1",
                    SVMParamType = "", // 振动数据无SVM参数类型
                    CompName = DevTreeManagement.GetTurbComponent(measloc?.ComponentID)?.ComponentName,
            };

                result.Add(dto);
            }

            return result;
        }

        /// <summary>
        /// 转换SVM特征值数据为统一格式
        /// </summary>
        /// <param name="svmDataList">SVM特征值数据列表</param>
        /// <param name="turbineID">机组ID</param>
        /// <returns>统一特征值数据列表</returns>
        private List<EigenValueItemDTO> ConvertSVMDataToUnified(List<EigenValueData_SVM> svmDataList, string turbineID)
        {
            var result = new List<EigenValueItemDTO>();

            if (svmDataList == null || svmDataList.Count == 0)
            {
                return result;
            }

            // 获取SVM测量位置信息
            List<MeasLoc_SVM> locList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);

            foreach (var item in svmDataList)
            {
                var measLoc = locList.FirstOrDefault(l => l.MeasLocationID == item.MeasLocationID);

                var dto = new EigenValueItemDTO
                {
                    MeasLocationID = item.MeasLocationID,
                    MeasLocationName = measLoc?.MeasLocName ?? item.MeasLocationID,
                    EigenValueCode = item.EigenValueID,
                    EigenValueName = CompoundSVMEVName(item.EigenValueID) ?? EnumHelper.GetDescription(item.EigenValueType),
                    EigenValue = item.Eigen_Value,
                    EngUnit = item.EngUnitName ?? "",
                    EngUnitName = item.EngUnitName ?? "",
                    AcquisitionTime = item.AcquisitionTime,
                    WindTurbineID = item.WindTurbineID,
                    MeasDefinitionID = item.MeasDefinitionID ?? "",
                    AlarmDegree = (int)item.AlarmDegree,
                    AlarmDegreeDescription = GetAlarmDegreeDescription(item.AlarmDegree),
                    AlarmColor = GetAlarmColor(item.AlarmDegree),
                    DataType = "0",
                    SVMParamType = measLoc?.MeasLocName ?? "",
                    CompName = DevTreeManagement.GetTurbComponent(measLoc?.ComponentID).ComponentName,
                };

                result.Add(dto);
            }

            return result;
        }

        #endregion

        public List<WorkingCondition> GetWorkingConditionInfo(string turbineID, string time)
        {
            //无时间，取得实时工况信息
            if (string.IsNullOrEmpty(time))
            {
                //获取测量定义下的工况信息
                List<WorkingConditionData> businessWKConDataDatas = RealTimeDataManage.GetAllWorkingCondDataListByTurbineID(turbineID);
                if (businessWKConDataDatas != null && businessWKConDataDatas.Count > 0)
                {
                    //businessWKConDataDatas 数据中按测量位置分组，所以数据不唯一
                    //WorkingConditionData mydata = businessWKConDataDatas.OrderByDescending(item => item.AcquisitionTime).First();
                    //ViewBag.time = mydata.AcquisitionTime.ToString();
                    //把不是最新的测量定义的工况信息删除
                    //businessWKConDataDatas.RemoveAll(item => item.MeasDefinitionID != mydata.MeasDefinitionID);
                }
                return GetWKCondInfo(businessWKConDataDatas);
            }
            return new List<WorkingCondition>();
        }

        private List<WorkingCondition> GetWKCondInfo(List<WorkingConditionData> WKList)
        {
            List<WorkingCondition> workingConditionList = new List<WorkingCondition>();
            if (WKList.Count == 0) return workingConditionList;
            WKList = WKList
                .GroupBy(wc => wc.MeasLocationID)
                .Select(g => g.OrderByDescending(h => h.AcquisitionTime).FirstOrDefault())
                .ToList();

            //List<WorkingConditionData> newWC = new List<WorkingConditionData>();
            //foreach (var it in WKList.GroupBy(t => t.MeasLocationID))
            //{
            //    newWC.Add(it.AsEnumerable().OrderByDescending(t => t.AcquisitionTime).FirstOrDefault());
            //}
            
            WindDAU dau = DauManagement.GetDAUById(WKList[0].WindTurbineID);
            WorkingCondition cmsRotSpd = null;
            WKList.RemoveAll(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Tower_TIMx);
            WKList.RemoveAll(item => item.Param_Type_Code == EnumWorkCondition_ParamType.WCPT_Tower_TIMy);
            
            for (int i = 0; i < WKList.Count; i++)
            {
/*                if(dau != null)
                {*/
                    WorkingCondition condition = new WorkingCondition();
                    //如果工况是转速，需要单独判断是工况转速还是CMS转速
                    if (WKList[i].Param_Type_Code == EnumWorkCondition_ParamType.WCPT_RotSpeed)
                    {
                        var rotSpdMeasLoc = dau.RotSpeedChannelList.Find(item => item.MeasLocRotSpdID == WKList[i].MeasLocationID);
                        if (rotSpdMeasLoc != null)
                        {
                            condition.Name = "发电机转速(CMS)";
                            condition.Value = WKList[i].Param_Value.ToString("0.00") + "RPM";
                            cmsRotSpd = condition;
                            continue;
                        }
                    }
                    EnumWorkCondition_ParamType ParmaType = WKList[i].Param_Type_Code;
                    condition.Name = AppFramework.Utility.EnumHelper.GetDescription(ParmaType);

                    condition.Value = WKList[i].Param_Value.ToString("0.00") + EnumWorkCondParamTypeHelper.GetEngUnit(ParmaType);
                    //ViewBag.time = WKList[i].AcquisitionTime.ToString();
                    workingConditionList.Add(condition);
/*                }*/
            }
            //工况排序
            List<WorkingCondition> workingOrderConditionList = new List<WorkingCondition>();
            DevTree.GetMeasLocProcessOrderSeqList().ForEach(item =>
            {
                WorkingCondition work = workingConditionList.Find(workcon => workcon.Name == item);
                if (work != null)
                {
                    workingOrderConditionList.Add(work);
                }
            });
            workingConditionList = workingOrderConditionList;
            if (cmsRotSpd != null)
            {
                workingConditionList.Insert(0, cmsRotSpd);
            }
            //获取最新的工况时间
            //if (WKList.Count != 0)
            //{
            //    WorkingCondition WkTime = new WorkingCondition();
            //    WkTime.Name = "时间";
            //    WkTime.Value = WKList[0].AcquisitionTime.ToString();
            //    workingConditionList.Insert(0,WkTime);
            //}
            return workingConditionList;
        }

     
        /// <summary>
        /// 根据测量定义，时间，取得对应工况信息
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="time"></param>
        /// <param name="measDef"></param>
        /// <returns></returns>
        public IActionResult GetWorkConData(string turbineID, string time, string measDef)
        {
            DateTime searchTime = Convert.ToDateTime(time);
            List<WorkingConditionData> dataList = WTCMSLive.BusinessModel.HistoryDataManage.GetCondataList_His(turbineID, measDef, searchTime);
            List<WorkingCondition> workingConditionList = new List<WorkingCondition>();
            if (dataList == null)
            {
                dataList = new List<WorkingConditionData>();
            }
            //取得机组下转速测量位置
            List<MeasLoc_RotSpd> rotList = DevTreeManagement.GetRotSpdMeasLocListByTurId(turbineID);
            for (int i = 0; i < dataList.Count; i++)
            {
                //偏航数据不显示
                WorkingCondition condition = new WorkingCondition();

                if (dataList[i].Param_Type_Code == EnumWorkCondition_ParamType.WCPT_YAWState)
                { continue; }
                //取得机组下转速测量位置
                string ProcssName = AppFramework.Utility.EnumHelper.GetDescription(dataList[i].Param_Type_Code);
                if (ProcssName == "发电机转速")
                {
                    //判断数据来源
                    MeasLoc_RotSpd rotSpd = rotList.Find(item => item.MeasLocationID == dataList[i].MeasLocationID);
                    if (rotSpd != null)
                    {
                        condition.Name = ProcssName + "(CMS)";
                        condition.Value = dataList[i].Param_Value.ToString() + EnumWorkCondParamTypeHelper.GetEngUnit(dataList[i].Param_Type_Code);
                        workingConditionList.Add(condition);
                        continue;
                    }
                }
                condition.Name = ProcssName;
                condition.Value = dataList[i].Param_Value.ToString() + EnumWorkCondParamTypeHelper.GetEngUnit(dataList[i].Param_Type_Code);
                workingConditionList.Add(condition);
            }
            return Ok(workingConditionList);
        }

        #endregion 特征值详细

        #region 数据分析页面

        public string GetMeasLocList(string turbineID)
        {
            // 新增电流电压过程量展示
            //List<MeasLoc_VoltageCurrent> measLoc_VoltageCurrents = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID);
            //List<MeasLoc_Vib> data = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            //if (measLoc_VoltageCurrents.Count > 0)
            //{
            //    foreach(var item in measLoc_VoltageCurrents)
            //    {
            //        data.Add(item.MeasLocVoltageCurrentConvertToVib());
            //    }
            //}
            //return data.OrderBy(item => item.OrderSeq).ToJson();

            // 区分振动和过程量
            return DevTreeManagement.GetVibMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToJson();
        }

        public string GetVoltageCurrentMeasLocList(string turbineID)
        {
            return DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID).OrderBy(item => item.OrderSeq).ToJson();
        }

        public string GetSVMMeasLocList(string turbineID)
        {
            SVMUnit mySVM = SVMManagement.GetSVMById(turbineID);
            List<MeasLoc_SVM> SvmList = null;
            List<MeasLoc_SVM> list = new List<MeasLoc_SVM>();
            if (mySVM == null)
                return "";
            else
            {
                SvmList = SVMManagement.GetMeasLoc_SVMListByTurID(mySVM.AssocWindTurbineID);
            }
            // string[] arr = new string[] { "水平加速度", "轴向加速度", "垂直加速度", "横滚角", "俯仰角", "SVM温度" };
            foreach (EnumSVMParamType data in Enum.GetValues(typeof(EnumSVMParamType)))
            {
                MeasLoc_SVM alarmStatus = SvmList.Find(i => i.MeasLocName == AppFramework.Utility.EnumHelper.GetDescription(data));
                if (alarmStatus != null)
                {
                    list.Add(alarmStatus);
                }
            }
            return list.ToJson();
        }

        public string GetEigenValueTypeByMeasLocId(string turbineID, string timeBegin, string timeEnd, string workConValue, string workConParame, string measLoc, string measType)
        {
            if (string.IsNullOrEmpty(measLoc)) return "";
            TrendImageManager trendImageManager = new TrendImageManager();
            DateTime beginTime = Convert.ToDateTime(timeBegin);
            DateTime endTime = Convert.ToDateTime(timeEnd).AddDays(1);
            List<EigenValueData> list = null;
            //string[] arr = workConParame?.Split(',');
            string[] arr = workConParame == null ? new string[1] {""} : workConParame.Split(',');
            string[] arrValue = workConValue.Split(',');

            //string userName = Server.HtmlDecode(Request.Cookies["WindCMSUserName"].Value);
            string userName = Request.Cookies["WindCMSUserName"];
            User user = UserManagement.GetUserById(userName);

            for (int i = 0; i < arr.Length; i++)
            {
                //EnumWorkCondition_ParamType paramType = string.IsNullOrEmpty(arr[0]) ? EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION : (arr[0] == "功率" ? EnumWorkCondition_ParamType.WCPT_Power : EnumWorkCondition_ParamType.WCPT_RotSpeed_MCS);
                EnumWorkCondition_ParamType paramType = EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION;
                if (!string.IsNullOrEmpty(arr[0]))
                {
                    if (arr[0].Equals("功率"))
                    {
                        paramType = EnumWorkCondition_ParamType.WCPT_Power;
                    }else if (arr[0].Equals("转速"))
                    {
                        paramType = EnumWorkCondition_ParamType.WCPT_RotSpeed;
                    }else if (arr[0].Equals("工况转速"))
                    {
                        paramType = EnumWorkCondition_ParamType.WCPT_RotSpeed_MCS;
                    }
                }
                string minvalue = ""; string maxvalue = "";
                if (arrValue.Length > 1)
                {
                    minvalue = arrValue[i * 2];
                    maxvalue = arrValue[i * 2 + 1];
                }
                if (measType == "1")
                {
                    //用户自定义特征值配置筛选
                    List<string> EigenConfigList = new List<string>();
                    MeasLoc_Vib vib = DevTreeManagement.GetVibMeasLocByID(measLoc);
                    if (EigenTrendConfigData != null && EigenTrendConfigData.MeasLocList != null && EigenTrendConfigData.MeasLocList.Count > 0)
                    {
                        var eigenList = EigenTrendConfigData.MeasLocList.FindAll(item => item.ComponentName == vib.DevTurComponent.ComponentName && item.SectionName == vib.SectionName && item.Orientation == vib.Orientation);
                        eigenList.ForEach(item =>
                        {
                            EigenConfigList.Add(vib.MeasLocationID + "&&" + item.EigenCode);
                        });
                    }
                    List<EigenValueData_Vib> Viblist = trendImageManager.GetEigenDataByParame(turbineID, beginTime, endTime, measLoc, paramType, minvalue, maxvalue);
                    //人为去掉PK和PPK
                    //Viblist.RemoveAll(item => item.EigenValueCode.IndexOf("PK") > -1);
                    //人为去掉峰峰值指标
                    //Viblist.RemoveAll(item => item.EigenValueCode.IndexOf("CF") > -1);

                    //根据用户筛选。
                    //设置权限。super用户显示全部的特征值，其他用户只显示vdi特征值。
                    List<EigenValueData_Vib> resViblist = new List<EigenValueData_Vib>();
                    if (user == null || user.UserRole == null )
                    {
                        //1. 获取VDI特征值ID
                        List<AlarmDefinition> alarmDefList = AlarmDefConfig.GetAlarmListByTurID(turbineID);
                        foreach (var items in Viblist)
                        {
                            var data = alarmDefList.FirstOrDefault(obj => obj.EigenValueID == items.EigenValueID);
                            if (data != null)
                            {
                                resViblist.Add(items);
                            }
                        }
                    }else
                    {
                        resViblist = Viblist;
                    }

                    //筛选 用户自定义特征值数据
                    string WebSiteType = System.Configuration.ConfigurationManager.AppSettings["ViewModel"];
                    string ShowEigenValue = System.Configuration.ConfigurationManager.AppSettings["showComplexEigenValue"];
                    if (WebSiteType == "BVM")
                    {
                        if (ShowEigenValue.ToUpper() == "FALSE" || ShowEigenValue.ToUpper() == "0")
                        {
                            resViblist.RemoveAll(item =>
                            {
                                return item.EigenValueCode.Contains("NF");
                            });
                            resViblist.RemoveAll(item =>
                            {
                                return item.EigenValueCode.Contains("ICEI");
                            });
                        }
                        else {
                            // 去除固有频率、修正固有频率和ICEI覆冰指标
                            List<string> BVMEigenConfigList = new List<string>() 
                    { "1NF", "2NF", "3NF", "4NF", "5NF", "6NF", 
                        "1NFC", "2NFC", "3NFC", "4NFC", "5NFC", "6NFC",
                        "1NFDevia", "2NFDevia", "3NFDevia", "4NFDevia", "5NFDevia", "6NFDevia",
                        "ICEI" };
                            List<EigenValueData_Vib> tempList = new List<EigenValueData_Vib>();
                            var listSimpleness = resViblist.FindAll(item => BVMEigenConfigList.Contains(item.EigenValueCode)==false);
                            tempList.AddRange(listSimpleness);
                            foreach (var code in BVMEigenConfigList)
                            {
                                var eigenCodeEntity = resViblist.Find(item => item.EigenValueCode == code);
                                if (eigenCodeEntity != null)
                                {
                                    tempList.Add(eigenCodeEntity);
                                }
                            }
                            resViblist = tempList;
                        }
                    }
                    // 根据配置文件过滤特征值信息
                    if (EigenConfigList.Count > 0)
                    {
                        resViblist.RemoveAll(item =>
                        {
                            return !EigenConfigList.Contains(item.EigenValueID);
                        });
                        List<EigenValueData> OrderList = new List<EigenValueData>();
                        foreach (var eigenid in EigenConfigList)
                        {
                            EigenValueData eigenData = resViblist.Find(item => item.EigenValueID == eigenid);
                            if (eigenData != null)
                                OrderList.Add(eigenData);
                        }
                        list = OrderList;
                    }
                    else
                    {
                        list = new List<EigenValueData>();
                        resViblist.ForEach(item =>
                        {
                            list.Add(item);
                        });
                    }
                }else if(measType == "2")
                {
                    List<string> EigenConfigList = new List<string>();
                    MeasLoc_VoltageCurrent vib = DevTreeManagement.GetVoltageCurrentMeasLocByID(measLoc);
                    if (EigenTrendConfigData != null && EigenTrendConfigData.MeasLocList != null && EigenTrendConfigData.MeasLocList.Count > 0)
                    {
                        var eigenList = EigenTrendConfigData.MeasLocList.FindAll(item => item.ComponentName == vib.DevTurComponent.ComponentName && item.SectionName == vib.SectionName && item.Orientation == vib.Orientation);
                        eigenList.ForEach(item =>
                        {
                            EigenConfigList.Add(vib.MeasLocationID + "&&" + item.EigenCode);
                        });
                    }
                    List<EigenValueData_Vib> Viblist = trendImageManager.GetEigenDataByParame(turbineID, beginTime, endTime, measLoc, paramType, minvalue, maxvalue,1);

                    List<EigenValueData_Vib> resViblist = new List<EigenValueData_Vib>();
                    if (user == null || user.UserRole == null)
                    {
                        //1. 获取VDI特征值ID
                        List<AlarmDefinition> alarmDefList = AlarmDefConfig.GetAlarmListByTurID(turbineID);
                        foreach (var items in Viblist)
                        {
                            var data = alarmDefList.FirstOrDefault(obj => obj.EigenValueID == items.EigenValueID);
                            if (data != null)
                            {
                                resViblist.Add(items);
                            }
                        }
                    }
                    else
                    {
                        resViblist = Viblist;
                    }

                    // 根据配置文件过滤特征值信息
                    if (EigenConfigList.Count > 0)
                    {
                        resViblist.RemoveAll(item =>
                        {
                            return !EigenConfigList.Contains(item.EigenValueID);
                        });
                        List<EigenValueData> OrderList = new List<EigenValueData>();
                        foreach (var eigenid in EigenConfigList)
                        {
                            EigenValueData eigenData = resViblist.Find(item => item.EigenValueID == eigenid);
                            if (eigenData != null)
                                OrderList.Add(eigenData);
                        }
                        list = OrderList;
                    }
                    else
                    {
                        list = new List<EigenValueData>();
                        resViblist.ForEach(item =>
                        {
                            list.Add(item);
                        });
                    }
                }
                else
                {
                    list = new List<EigenValueData>();
                    List<EigenValueData_SVM> SVMlist = trendImageManager.GetSVMEigenDataByParame(turbineID, beginTime, endTime, measLoc, paramType, minvalue, maxvalue);
                    SVMlist.ForEach(item =>
                    {
                        list.Add(item);
                    });
                }
            }
            List<EigenValueData_SVM> SVMEigen = null;
            if (measType == "0")
            {
                SVMEigen = GetSVMPublicEigenValueList(turbineID, measLoc);
            }
            if (list != null && list.Count() > 0)
            {
                list.ForEach(i =>
                {
                    if (measType == "1" || measType == "2")
                    {
                        if (i.EngUnitName == "8")
                        {
                            i.EngUnitName = ((EigenValueData_Vib)i).EigenValueCode;
                        }
                        else {
                            i.EngUnitName = WTCMSLive.BusinessModel.EigenValueManage.GetFreBandByCode(((EigenValueData_Vib)i).EigenValueCode);
                        }
                        
                    }
                    else
                    {
                        EigenValueData_SVM SVMEigenData = SVMEigen.Find(item => item.EigenValueID == ((EigenValueData_SVM)i).EigenValueID);
                        if (SVMEigenData != null)
                            //i.EngUnitName = AppFramework.Utility.EnumHelper.GetDescription(SVMEigenData.EigenValueType);
                            i.EngUnitName = i.EigenValueID;
                        else
                        {
                            if (((EigenValueData_SVM)i).EigenValueID == "VA_Horizontal")
                            {
                                i.EngUnitName = "垂直加速度 RMS";
                            }

                            if(((EigenValueData_SVM)i).EigenValueID == "TLB")
                            {
                                i.EngUnitName = "塔顶倾角偏移";  
                            }
                            //俯仰角TDBMAX和TDBAVG逻辑代码修改
                       /*     i.EngUnitName = "TDBAVG";*/
                            if (((EigenValueData_SVM)i).EigenValueID.Contains("TDBMAX"))
                            {
                                i.EngUnitName = "TDBMAX";
                                i.EngUnitName = "TDBMAX";
                            }
                            if (((EigenValueData_SVM)i).EigenValueID.Contains("TDBAVG"))
                            {
                                i.EngUnitName = "TDBAVG";
                                i.EngUnitName = "TDBAVG";
                            }
                            if (((EigenValueData_SVM)i).EigenValueID.Contains(i.EigenValueID)&&(!((EigenValueData_SVM)i).EigenValueID.Contains("TDBMAX")) &&(!((EigenValueData_SVM)i).EigenValueID.Contains("TDBAVG")))
                            {
                                i.EngUnitName = i.EigenValueID;
                            }
                        }
                        //if (((EigenValueData_SVM)i).EigenValueID.Contains("TDBAVG")==false && (((EigenValueData_SVM)i).EigenValueID.Contains("TDBMAX"))==false) { 
                        //    i.EigenValueID = measLoc + "&&" + i.EigenValueID;
                        //}
                        i.EigenValueID = measLoc + "&&" + i.EigenValueID;
                    }

                    if (string.IsNullOrEmpty(i.EngUnitName))
                    {
                        i.EngUnitName = ((EigenValueData_Vib)i).EigenValueCode;
                    }
                });
            }
            
            if (UserEigenConfigData != null && list.Count > 0)
            {
                if (userName.IndexOf("%") > -1)
                    userName = WebUtility.UrlDecode(userName);
                var userEigen = UserEigenConfigData.EigenRightList.Find(item => item.UserId == userName);
                if (userEigen != null)
                {
                    List<EigenValueData> UserEigenList = new List<EigenValueData>();
                    userEigen.EigenCodeList.ForEach(eigencode =>
                    {
                        UserEigenList.AddRange(list.Where(item => item.EigenValueID.Contains("_" + eigencode)));
                    });
                    list = UserEigenList;
                }
                else
                {
                    //如果没有配置权限，就清空列表中的所有信息
                    list.Clear();
                }
            }
            return list.ToJson();
        }


        private static List<EigenValueData_SVM> GetSVMPublicEigenValueList(string turbineID, string measLocID)
        {
            List<EigenValueData_SVM> eigenValueList = new List<EigenValueData_SVM>();
            //根据晃度仪测量位置ID
            MeasLoc_SVM _measLoc = SVMManagement.GetMeasloc_SVMByMeasLocID(measLocID);
            //根据测量位置类型，返回对应列表。
            switch (_measLoc.ParamType)
            {
                case EnumSVMParamType.Pitch://俯仰角
                case EnumSVMParamType.Roll://横滚角

                    //弯曲角
                    EigenValueData_SVM ba = new EigenValueData_SVM();
                    ba.EigenValueID = "BA" + '_' + _measLoc.ParamType.ToString();
                    ba.EigenValueType = EnumSVMEigenValueType.BendingAngle;
                    ba.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(ba);
                    //摆动角
                    EigenValueData_SVM ha = new EigenValueData_SVM();
                    ha.EigenValueID = "HA" + '_' + _measLoc.ParamType.ToString();
                    ha.EigenValueType = EnumSVMEigenValueType.SwingAngle;
                    ha.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(ha);
                    //最大值
                    EigenValueData_SVM Max = new EigenValueData_SVM();
                    Max.EigenValueID = "Max" + '_' + _measLoc.ParamType.ToString();
                    Max.EigenValueType = EnumSVMEigenValueType.MaxValue;
                    Max.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(Max);
                    //最小值
                    EigenValueData_SVM Min = new EigenValueData_SVM();
                    Min.EigenValueID = "Min" + '_' + _measLoc.ParamType.ToString();
                    Min.EigenValueType = EnumSVMEigenValueType.MinValue;
                    Min.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(Min);
                    //平均值
                    EigenValueData_SVM Ave = new EigenValueData_SVM();
                    Ave.EigenValueID = _measLoc.ParamType.ToString();
                    Ave.EigenValueType = EnumSVMEigenValueType.AvgValue;
                    Ave.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(Ave);
                    break;
                case EnumSVMParamType.Vertical://垂直加速度
                    EigenValueData_SVM VA = new EigenValueData_SVM();
                    VA.EigenValueID = "VA" + '_' + _measLoc.ParamType.ToString();
                    VA.EigenValueType = EnumSVMEigenValueType.VerticalAcceleration;
                    VA.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(VA);
                    break;
                case EnumSVMParamType.Horizontal://水平加速度
                    EigenValueData_SVM LA = new EigenValueData_SVM();
                    LA.EigenValueID = "LA" + '_' + _measLoc.ParamType.ToString();
                    LA.EigenValueType = EnumSVMEigenValueType.LateralAcceleration;
                    LA.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(LA);
                    EigenValueData_SVM TNF = new EigenValueData_SVM();
                    TNF.EigenValueID = "TNF" + '_' + _measLoc.ParamType.ToString();
                    TNF.EigenValueType = EnumSVMEigenValueType.NaturalFrequency;
                    TNF.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(TNF);
                    EigenValueData_SVM TNFMA = new EigenValueData_SVM();
                    TNFMA.EigenValueID = "TNFMA" + '_' + _measLoc.ParamType.ToString();
                    TNFMA.EigenValueType = EnumSVMEigenValueType.NFAmplitude;
                    TNFMA.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(TNFMA);
                    break;
                case EnumSVMParamType.Axisl://轴向加速度
                    EigenValueData_SVM AA = new EigenValueData_SVM();
                    AA.EigenValueID = "AA" + '_' + _measLoc.ParamType.ToString();
                    AA.EigenValueType = EnumSVMEigenValueType.AxialAcceleration;
                    AA.WindTurbineID = _measLoc.WindTurbineID;
                    eigenValueList.Add(AA);
                    break;
                default:
                    break;
            }
            return eigenValueList;
        }




        //存储用户自定义趋势图特征值列表信息
        public static UserEigenConfig UserEigenConfigData = null;



        /// <summary>
        /// 获取趋势分析图表信息
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="timeBegin">开始时间</param>
        /// <param name="timeEnd">结束时间</param>
        /// <param name="workCon">工况参数</param>
        /// <param name="workConParame">参数数值</param>
        /// <returns></returns>
        /// 
        [HttpGet("GetTrendAnalyseByTime")]
        public IActionResult GetTrendAnalyseByTime(string turbineID, string timeBegin, string timeEnd,  string measType, string EigenCode, string measLoc, bool UseAlarmDef, string? workConValue, string? workConParame)
        {
            DateTime beginTime = Convert.ToDateTime(timeBegin);
            DateTime endTime = Convert.ToDateTime(timeEnd).AddDays(1);
            TrendImageManager trendImageManager = new TrendImageManager();
            List<AnalysisData> dataList = new List<AnalysisData>();

            List<MeasLoc_Vib> myMeasLocList = new List<MeasLoc_Vib>();

            if (string.IsNullOrEmpty(workConParame))
            {
                if (measType == "1")
                {
                    myMeasLocList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID).ToList();
                    trendImageManager.GetEVDataTrendByTime(turbineID, beginTime, endTime, measLoc, measType, EigenCode).ForEach(item =>
                    {
                        if (item != null)
                            dataList.Add(item);
                    });
                }else if(measType == "2")
                {
                    var measLoc_VoltageCurrents = DevTreeManagement.GetVoltageCurrentMeasLocationByTurId(turbineID).ToList();
                    foreach(var t in measLoc_VoltageCurrents)
                    {
                        myMeasLocList.Add(t.MeasLocVoltageCurrentConvertToVib());
                    }
                    trendImageManager.GetEVDataTrendByTime(turbineID, beginTime, endTime, measLoc, measType, EigenCode).ForEach(item =>
                    {
                        if (item != null)
                            dataList.Add(item);
                    });
                }
                else
                {
                    SVMManagement.GetMeasLoc_SVMListByTurID(turbineID).ForEach(item =>
                    myMeasLocList.Add(new MeasLoc_Vib()
                    {
                        MeasLocationID = item.MeasLocationID,
                        MeasLocName = item.MeasLocName,
                        OrderSeq = item.OrderSeq
                    }));

                    trendImageManager.GetSVMEVDataTrendByTime(turbineID, beginTime, endTime, measLoc, measType, EigenCode).ForEach(item =>
                    {
                        if (item != null)
                            dataList.Add(item);
                    });
                }
            }
            else
            {
                string[] arr = workConParame.Split(',');
                string[] arrValue = workConValue?.Split(',');
                for (int i = 0; i < arr.Length; i++)
                {
                    EnumWorkCondition_ParamType paramType = string.IsNullOrEmpty(arr[0]) ? EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION : arr[0] == "功率" ? EnumWorkCondition_ParamType.WCPT_Power : (arr[0] == "转速"? EnumWorkCondition_ParamType.WCPT_RotSpeed: EnumWorkCondition_ParamType.WCPT_RotSpeed_MCS);
                    if (measType == "1")
                    {
                        trendImageManager.GetEVDataTrendByTime(turbineID, beginTime, endTime, measLoc, measType, EigenCode, paramType, arrValue[i * 2], arrValue[i * 2 + 1]).ForEach(item =>
                        {
                            if (item != null)
                                dataList.Add(item);
                        });
                    }
                    else
                    {
                        trendImageManager.GetSVMEVDataTrendByTime(turbineID, beginTime, endTime, measLoc, measType, EigenCode, paramType, arrValue[i * 2], arrValue[i * 2 + 1]).ForEach(item =>
                        {
                            if (item != null)
                                dataList.Add(item);
                        });
                    }
                }
            }
            if (!UseAlarmDef)
            {
                //如果没有选择VDI报警设置， 则启用自定义报警规则。20190124 by sq
                //修改逻辑。20190220 by sq
                AlarmDefThreshold alarmThresholdparameWarning = null;
                AlarmDefThreshold alarmThresholdparameAlarm = null;

                List<ThresholdLineData> ThresholdLineDatas = new List<ThresholdLineData>();
                using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
                {
                    AlarmDefinition alarmlist = ctx.AlarmDefLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.MeasLocationID == measLoc && item.EigenValueID == EigenCode);
                    
                    if (alarmlist != null)
                    {
                        alarmThresholdparameWarning = ctx.AlarmDefThresholdGroupLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ThresholdGroup == alarmlist.ThresholdGroup &&item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning);
                        alarmThresholdparameAlarm = ctx.AlarmDefThresholdGroupLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ThresholdGroup == alarmlist.ThresholdGroup &&item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm);

                    }

                    // 用户报警定义配置
                    AlarmDefThreshold useralarmThresholdparameWarning = null;
                    AlarmDefThreshold useralarmThresholdparameAlarm = null;
                    List<AlarmDefinition> userAlarmList = ctx.AlarmDefLists.Where(item => item.WindTurbineID == turbineID && item.MeasLocationID == measLoc && item.EigenValueID == EigenCode).ToList();
                    foreach(var k in userAlarmList)
                    {
                        useralarmThresholdparameWarning = ctx.AlarmDefThresholdGroupLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ThresholdGroup == k.ThresholdGroup && item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Forward);
                        useralarmThresholdparameAlarm = ctx.AlarmDefThresholdGroupLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ThresholdGroup == k.ThresholdGroup && item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Forward);

                        if(useralarmThresholdparameWarning != null || useralarmThresholdparameAlarm != null)
                        {
                            //int[] arr = Enumerable.Repeat(5, 500).ToArray();
                            ThresholdLineDatas.Add(new ThresholdLineData()
                            {
                                AlarmLine = useralarmThresholdparameAlarm?.ThresholdValue.ToString(),
                                WarningLine = useralarmThresholdparameWarning?.ThresholdValue.ToString(),
                                LineType = useralarmThresholdparameAlarm?.ThresholdValueType.ToString() ?? useralarmThresholdparameWarning?.ThresholdValueType.ToString(),

                                LineName = (useralarmThresholdparameAlarm?.ThresholdValueType ?? useralarmThresholdparameWarning?.ThresholdValueType) == EnumThresholdValueType.Forward ? "正向" : "反向",
                            });
                        }

                        useralarmThresholdparameWarning = ctx.AlarmDefThresholdGroupLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ThresholdGroup == k.ThresholdGroup && item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Reverse);
                        useralarmThresholdparameAlarm = ctx.AlarmDefThresholdGroupLists.FirstOrDefault(item => item.WindTurbineID == turbineID && item.ThresholdGroup == k.ThresholdGroup && item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Reverse);
                        if (useralarmThresholdparameWarning != null || useralarmThresholdparameAlarm != null)
                        {
                            //int[] arr = Enumerable.Repeat(5, 500).ToArray();
                            ThresholdLineDatas.Add(new ThresholdLineData()
                            {
                                AlarmLine = useralarmThresholdparameAlarm?.ThresholdValue.ToString(),
                                WarningLine = useralarmThresholdparameWarning?.ThresholdValue.ToString(),
                                LineType = useralarmThresholdparameAlarm?.ThresholdValueType.ToString() ?? useralarmThresholdparameWarning?.ThresholdValueType.ToString(),

                                LineName = (useralarmThresholdparameAlarm?.ThresholdValueType ?? useralarmThresholdparameWarning?.ThresholdValueType) == EnumThresholdValueType.Forward ? "正向" : "反向",
                            });
                        }

                    }


                }
                bool isAcceleratedSpeed = false;
                dataList.ForEach(items =>
                {
                    isAcceleratedSpeed = measType == "0" && items.subText.IndexOf("加速度") > -1 ? true : false;
                    double WarnValue = 0;
                    double AlarmValue = 0;
                    List<double> waringValueData = new List<double>();//警告 
                    List<double> errorValueData = new List<double>();//危险 

                    if (alarmThresholdparameWarning != null)
                    {
                        WarnValue = (double)alarmThresholdparameWarning.ThresholdValue;
                        if (isAcceleratedSpeed)
                        {
                            WarnValue = (double)alarmThresholdparameWarning.ThresholdValue * 1000;

                            if (ThresholdLineDatas.Count > 0 && string.IsNullOrEmpty(ThresholdLineDatas[0].WarningLine))
                            {
                                ThresholdLineDatas[0].WarningLine = (Convert.ToDouble(ThresholdLineDatas[1].WarningLine) *1000).ToString();
                            }
                        }
                        for (int k = 0; k < items.eigenValueData.Count(); k++)
                        {
                            waringValueData.Add(WarnValue);
                        }
                    }
                    if (alarmThresholdparameAlarm != null)
                    {
                        AlarmValue = (double)alarmThresholdparameAlarm.ThresholdValue;
                        if (isAcceleratedSpeed)
                        {
                            AlarmValue = (double)alarmThresholdparameAlarm.ThresholdValue * 1000;

                            if (ThresholdLineDatas.Count > 0 && string.IsNullOrEmpty(ThresholdLineDatas[0].AlarmLine))
                            {
                                ThresholdLineDatas[0].AlarmLine = (Convert.ToDouble(ThresholdLineDatas[1].AlarmLine) * 1000).ToString();
                            }
                        }
                        for (int k = 0; k < items.eigenValueData.Count(); k++)
                        {
                            errorValueData.Add(AlarmValue);
                        }

                    }
                    //items.errorValueData = errorValueData.ToArray();
                    //items.waringValueData = waringValueData.ToArray();

                    // 用户报警配置更改为ThresholdLineDatas，而不是errorValueData和waringValueData
                    items.errorValueData = null;
                    items.waringValueData = null;
                    items.ThresholdLineDatas = ThresholdLineDatas;

                });
            }
            return Ok(dataList);
        }

        public IActionResult GetTowTrendAnalyseByTime(string turbineID, string timeBegin, string timeEnd)
        {
            DateTime beginTime = Convert.ToDateTime(timeBegin);
            DateTime endTime = Convert.ToDateTime(timeEnd).AddDays(1);
            TrendImageManager trendImageManager = new TrendImageManager();
            AnalysisData dataList = new AnalysisData();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                var list = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == turbineID && item.Param_Type_Code== EnumWorkCondition_ParamType.WCPT_Tower_TIMactrual && item.AcquisitionTime >= beginTime && item.AcquisitionTime <= endTime).ToList();
                dataList = CreateAnalysisChartByEigenValue(turbineID, "", "", list);
            }
            return Ok(dataList);
        }

        private AnalysisData CreateAnalysisChartByEigenValue(string turbineID, string titleName, string subTitleName, List<WorkingConditionData> _WkDataList)
        {
            //获取VDI3834报警定义设置
            //List<AlarmDefinition> turbineAlarmList = AlarmDefConfig.GetAlarmListByTurID(turbineID);
            AnalysisData analysisChart = new AnalysisData();
            analysisChart.titleName = titleName;
            analysisChart.subText = subTitleName;
            List<double> eigenValueList = new List<double>();//有效值
            List<double> waringValueData = new List<double>();//警告 
            List<double> errorValueData = new List<double>();//危险 
            List<string> workConditionValueData = new List<string>();//工况
            List<string> timeValueData = new List<string>();//时间
            double error = 0;
            double waring = 0;
            //if (TowConfig != null)
            //{
            //    waring = TowConfig.Warn;
            //    error = TowConfig.Alarm;
            //}
            for (int i = 0; i < _WkDataList.Count; i++)
            {
                if (_WkDataList[i] != null)
                {
                    eigenValueList.Add(Math.Round(_WkDataList[i].Param_Value, 5));
                    timeValueData.Add(_WkDataList[i].AcquisitionTime.ToString());
                }
            }
            if (error > 0 || waring > 0)
            {
                for (int j = 0; j < eigenValueList.Count; j++)
                {
                    errorValueData.Add(error);
                    waringValueData.Add(waring);
                }
            }
            analysisChart.eigenValueData = eigenValueList.ToArray();
            analysisChart.workConditionValueData = workConditionValueData.ToArray();
            analysisChart.timeValueData = timeValueData.ToArray();
            analysisChart.waringValueData = waringValueData.ToArray();
            analysisChart.errorValueData = errorValueData.ToArray();
            return analysisChart;
        }

        /// <summary>
        /// 获取要设置的特征值列表
        /// </summary>
        /// <param name="measLocId"></param>
        /// <param name="measType"></param>
        /// <returns></returns>
        public string SetTrendAnalyseEigenList(string turbineID, string measLocId, string measType)
        {
            MeasLoc_Vib vib = DevTreeManagement.GetVibMeasLocByID(measLocId);
            List<string> EigenConfigList = new List<string>();
            if (EigenTrendConfigData != null && EigenTrendConfigData.MeasLocList != null)
            {
                EigenTrendConfigData.MeasLocList.FindAll(item => item.ComponentName == vib.DevTurComponent.ComponentName && item.Orientation == vib.Orientation && item.SectionName == vib.SectionName).ForEach(item =>
                {
                    EigenConfigList.Add(item.EigenCode);
                });
            }
            List<SetEigenConfig> eigenDic = new List<SetEigenConfig>();
            //由于振动数据的特征值比较多，所以就先自定义振动特征值，晃度特征值暂时不考虑
            if (measType == "1")
            {
                WTCMSLive.BusinessModel.EigenValueManage.GetPublicEigenValueList(turbineID, measLocId).ForEach(item =>
                {
                    eigenDic.Add(new SetEigenConfig()
                    {
                        EigenCode = item.EigenValueCode,
                        isChecked = EigenConfigList.Contains(item.EigenValueCode),
                        EigenName = string.IsNullOrEmpty(EVNameUtility.GetEVNameByEVCode(item.EigenValueCode)) ? item.EigenValueCode : EVNameUtility.GetEVNameByEVCode(item.EigenValueCode),
                    });
                });
                List<SetEigenConfig> OrderList = new List<SetEigenConfig>();
                foreach (var eigenid in EigenConfigList)
                {
                    SetEigenConfig eigenData = eigenDic.Find(item => item.EigenCode == eigenid);
                    if (eigenData != null)
                    {
                        OrderList.Add(eigenData);
                        eigenDic.Remove(eigenData);
                    }
                }
                if (eigenDic.Count > 0)
                {
                    OrderList.AddRange(eigenDic);
                }
                eigenDic = OrderList;
            }
            else
            {
                //晃度特征值不多，所以暂时不用筛选
            }
            string dicList = eigenDic.ToJson();
            return dicList;
        }

        public string SetTrendEigenData(string measLocId, string eigenCodeList)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                MeasLoc_Vib vib = DevTreeManagement.GetVibMeasLocByID(measLocId);
                if (EigenTrendConfigData == null)
                {
                    EigenTrendConfigData = new EigenTrendConfig();
                }
                if (EigenTrendConfigData.MeasLocList == null)
                {
                    EigenTrendConfigData.MeasLocList = new List<EigenTrendConfigMeasLoction>();
                }
                //保存前，先删除已有的
                EigenTrendConfigData.MeasLocList.RemoveAll(item => item.ComponentName == vib.DevTurComponent.ComponentName && item.Orientation == vib.Orientation && item.SectionName == vib.SectionName);
                if (!string.IsNullOrEmpty(eigenCodeList))
                {
                    foreach (string eigen in eigenCodeList.Split(','))
                    {
                        EigenTrendConfigMeasLoction eigenConfig = new EigenTrendConfigMeasLoction()
                        {
                            ComponentName = vib.DevTurComponent.ComponentName,
                            EigenCode = eigen,
                            Orientation = vib.Orientation,
                            SectionName = vib.SectionName
                        };
                        EigenTrendConfigData.MeasLocList.Add(eigenConfig);
                    }
                }
                AppFramework.Utility.XmlFileHelper<EigenTrendConfig>.Save(EigenTrendConfigData, EigenConfigPath);
                message = string.Format(message, 1, "");
            }
            catch (Exception ex)
            {
                message = string.Format(message, 0, ex.Message);
            }
            return "{" + message + "}";
        }

        #endregion

        #region DAU运行详细
     

        /// <summary>
        /// 搜索DAU运行日志
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="dauID">DAU ID</param>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>DAU运行日志列表</returns>
        [HttpGet("SearchDAULog")]
        public IActionResult SearchDAULog(string turbineID, string dauID, DateTime beginTime, DateTime endTime)
        {
            try
            {
                if (string.IsNullOrEmpty(turbineID))
                {
                    return BadRequest("机组ID不能为空");
                }

                if (string.IsNullOrEmpty(dauID))
                {
                    return BadRequest("DAU ID不能为空");
                }

                // 获取DAU运行日志数据
                List<DAURunLog> logList = DauManagement.GetDAULogByTime(turbineID, dauID, beginTime, endTime.AddDays(1));

                // 转换为DTO列表
                var result = ConvertToDAULogDTOList(logList);

                return Ok(result);
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage($"[SearchDAULog]获取DAU运行日志失败 - 机组ID: {turbineID}, DAU ID: {dauID}", ex);
                return Ok(new List<DAULogDTO>());
            }
        }

        /// <summary>
        /// 转换DAU运行日志为DTO列表
        /// </summary>
        /// <param name="logList">DAU运行日志列表</param>
        /// <returns>DAU运行日志DTO列表</returns>
        private List<DAULogDTO> ConvertToDAULogDTOList(List<DAURunLog> logList)
        {
            var result = new List<DAULogDTO>();

            if (logList == null || logList.Count == 0)
            {
                return result;
            }

            // 按时间倒序排列，最新的在前面
            var sortedLogList = logList.OrderByDescending(item => item.EventTime).ToList();

            // 限制返回数量，避免数据过多
            int logCount = sortedLogList.Count > 1000 ? 1000 : sortedLogList.Count;

            for (int i = 0; i < logCount; i++)
            {
                var log = sortedLogList[i];
                var logDTO = new DAULogDTO
                {
                    WindTurbineID = log.WindTurbineID,
                    DauId = log.DauId,
                    EventTime = log.EventTime,
                    AlarmState = (int)log.AlarmState,
                    AlarmStateDescription = AppFramework.Utility.EnumHelper.GetDescription((EnumDAUStatus)log.AlarmState),
                    LogTitle = log.LogTitle ?? "",
                    EventTimeFormatted = log.EventTime.ToString("yyyy-MM-dd HH:mm:ss")
                };

                result.Add(logDTO);
            }

            return result;
        }

        [HttpGet("GetChannelDCTrendChart")]
        public IActionResult GetChannelDCTrendChart(string turbineID, DateTime beginTime, DateTime endTime, int channelID,string DAUID)
        {
            DAUManager dauManager = new DAUManager();
            return Ok(dauManager.GetSensorDCChartByChannel(turbineID, channelID, beginTime, endTime.AddDays(1),DAUID));
        }
        #endregion DAU详细

        #region 机组运行日志


        /// <summary>
        /// 获取特征值报警历史
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="beginTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        [HttpGet("SearchMonitorLog")]
        public IActionResult SearchMonitorLog(string turbineID, string beginTime, string endTime)
        {
            DateTime begin = Convert.ToDateTime(beginTime);
            DateTime end = Convert.ToDateTime(endTime);
            return Ok(MonitorManager.GetTurbineRunLogByTimeV2(turbineID, begin, end.AddDays(1)));
        }
        #endregion 机组运行日志

        #region 单个机组实时部件状态
        public List<Cell> GetWindComponentStatus(string trubineID)
        {
            List<WindTurbineComponent> windTurbineComponent = null;
            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                windTurbineComponent = ctx.DevTurComponents.ToList();
            }

            List<AlarmStatus_Component> alarmStatusList = null;
            List<Cell> cellList = new List<Cell>();
            using (CMSFramework.EF.MonContext ctx = new CMSFramework.EF.MonContext(ConfigInfo.DBConnName))
            {
                alarmStatusList = ctx.AlarmStatus_Components.Where(p => p.WindTurbineID == trubineID).ToList();
                if (windTurbineComponent != null && windTurbineComponent.Count > 0)
                {
                    alarmStatusList.ForEach(item =>
                    {
                        Cell cell = new Cell();
                        cell.title = windTurbineComponent.Find(com => com.ComponentID == item.ComponentID).ComponentName;
                        cell.displayValue = AppFramework.Utility.EnumHelper.GetDescription(item.AlarmDegree);
                        cell.type = item.AlarmUpdateTime.ToString();
                        switch ((int)item.AlarmDegree)
                        {
                            case 3:
                                cell.color = "#00FF00";
                                break;
                            case 5:
                                cell.color = "#FFFF00";
                                break;
                            case 6:
                                cell.color = "#FF0000";
                                break;
                            default:
                                cell.color = "#C4C4C4";
                                break;
                        }
                        cellList.Add(cell);
                        item.DevSegmentName = windTurbineComponent.Find(com => com.ComponentID == item.ComponentID).ComponentName;
                    });
                }
            }

            return cellList;
        }
        #endregion

        #region
        /// <summary>
        /// 工况实时是否有倾角仪数据，如果有，证明获取塔筒数据。
        /// </summary>
        /// <param name="trubineId"></param>
        /// <returns></returns>
        public bool IsTowOverViewImage(string trubineId)
        {
            bool isShow = false;
            List<WorkingConditionData> workinglist = new List<WorkingConditionData>();
            using (CMSFramework.EF.ValueDataContext_RT ctx = new CMSFramework.EF.ValueDataContext_RT(ConfigInfo.DBConnName))
            {
                workinglist = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == trubineId).ToList();
            }
            for(int i = 0; i < workinglist.Count; i++)
            {
                if (workinglist[i].MeasLocationID.Contains("TOW"))
                {
                    isShow = true;
                    break;
                }
            }
            return isShow;
        }
        #endregion

        

   

 





        


        
        




    
        public class MyValue
        {
            public string key { get; set; }
            public string value { get; set; }
        }

        #region DAU列表辅助方法

        /// <summary>
        /// 转换DAU列表数据为响应DTO格式
        /// </summary>
        /// <param name="dauList">DAU列表</param>
        /// <param name="turbineList">机组列表</param>
        /// <param name="statusList">DAU状态列表</param>
        /// <param name="windParkID">风场ID</param>
        /// <returns>DAU列表响应DTO</returns>
        private DAUListResponseDTO ConvertToDAUListResponse(
            List<WindDAU> dauList,
            List<WindTurbine> turbineList,
            List<RTAlarmStatus_DAU> statusList,
            string windParkID)
        {
            var result = new DAUListResponseDTO
            {
                WindParkID = windParkID,
                StatusSummary = new DAUStatusSummaryDTO(),
                DAUList = new List<DAUItemDTO>(),
                DataTime = DateTime.Now
            };

            // 按机组名称排序
            var orderedTurbineList = turbineList.OrderBy(t => t.WindTurbineName).ToList();

            foreach (var turbine in orderedTurbineList)
            {
                // 获取该机组下的DAU列表
                var turbineDAUs = dauList.Where(d => d.WindTurbineID == turbine.WindTurbineID).OrderBy(d => d.DAUName).ToList();

                foreach (var dau in turbineDAUs)
                {
                    // 查找对应的状态信息
                    var status = statusList.FirstOrDefault(s => s.WindTurbineID == dau.WindTurbineID && s.DauID == dau.DauID);

                    var dauItem = new DAUItemDTO
                    {
                        DauID = dau.DauID,
                        DAUName = dau.DAUName,
                        WindTurbineID = dau.WindTurbineID,
                        WindTurbineName = turbine.WindTurbineName,
                        WindParkID = dau.WindParkID,
                        IPAddress = dau.IP ?? "",
                        Port = dau.Port,
                        IsAvailable = dau.IsAvailable,
                        AlarmState = status != null ? (int)status.AlarmState : 2, // 2表示未知
                        AlarmStateDescription = status !=null ? AppFramework.Utility.EnumHelper.GetDescription(status.AlarmState) : "",
                        StatusUpdateTime = status?.StatusUpdateTime,
                        VibChannelCount = dau.DAUChannelList?.Count ?? 0,
                        RotSpeedChannelCount = dau.RotSpeedChannelList?.Count ?? 0,
                        ProcessChannelCount = dau.ProcessChannelList?.Count ?? 0,
                        VoltageCurrentChannelCount = dau.VoltageCurrentList?.Count ?? 0
                    };

                    result.DAUList.Add(dauItem);
                }
            }

            // 计算状态统计
            result.StatusSummary = CalculateDAUStatusSummary(result.DAUList);

            return result;
        }

        /// <summary>
        /// 计算DAU状态统计
        /// </summary>
        /// <param name="dauList">DAU列表</param>
        /// <returns>状态统计</returns>
        private DAUStatusSummaryDTO CalculateDAUStatusSummary(List<DAUItemDTO> dauList)
        {
            var summary = new DAUStatusSummaryDTO
            {
                TotalCount = dauList.Count
            };

            foreach (var dau in dauList)
            {
                switch (dau.AlarmState)
                {
                    case (int)EnumDAUStatus.Normal: // 正常
                        summary.NormalCount++;
                        break;
                    case (int)EnumDAUStatus.Unknown: // 未知
                        summary.UnknownCount++;
                        break;
                    case (int)EnumDAUStatus.CommunicationError: // 通信错误
                        summary.CommunicationErrorCount++;
                        break;
                    case (int)EnumDAUStatus.NoDataArrive: // 无数据到达
                        summary.NoDataArriveCount++;
                        break;
                    case (int)EnumDAUStatus.SensorFault: // 传感器故障
                        summary.SensorFaultCount++;
                        break;
                    case (int)EnumDAUStatus.RotSpdFault: // 转速故障
                        summary.RotSpdFaultCount++;
                        break;
                    default: // 离线（无状态数据）
                        summary.UnknownCount++;
                        break;
                }
            }
            return summary;
        }

     

        /// <summary>
        /// 转换为DAU状态统计响应数据
        /// </summary>
        /// <param name="parkList">风场列表</param>
        /// <param name="turbineList">机组列表</param>
        /// <param name="dauList">DAU列表</param>
        /// <param name="statusList">DAU状态列表</param>
        /// <returns>DAU状态统计响应DTO</returns>
        private DAUStatusCountResponseDTO ConvertToDAUStatusCountResponse(
            List<WindPark> parkList,
            List<WindTurbine> turbineList,
            List<WindDAU> dauList,
            List<RTAlarmStatus_DAU> statusList)
        {
            var result = new DAUStatusCountResponseDTO
            {
                OverallSummary = new DAUStatusSummaryDTO(),
                WindParkStatusList = new List<WindParkDAUStatusDTO>(),
                DataTime = DateTime.Now
            };

            // 创建全局DAU列表用于全局统计
            var allDAUItems = new List<DAUItemDTO>();

            // 按风场处理数据
            foreach (var park in parkList)
            {
                var parkTurbines = turbineList.Where(t => t.WindParkID == park.WindParkID).ToList();
                var parkDAUs = dauList.Where(d => d.WindParkID == park.WindParkID).ToList();
                var parkStatuses = statusList.Where(s => parkDAUs.Any(d => d.WindTurbineID == s.WindTurbineID && d.DauID == s.DauID)).ToList();

                var windParkStatus = new WindParkDAUStatusDTO
                {
                    WindParkID = park.WindParkID,
                    WindParkName = park.WindParkName,
                    DAUList = new List<DAUItemDTO>(),
                    StatusStatistics = new List<DAUStatusStatisticDTO>(),
                    NameStatistics = new List<DAUNameStatisticDTO>()
                };

                // 转换风场下的所有DAU
                foreach (var dau in parkDAUs.OrderBy(d => d.DAUName))
                {
                    var turbine = parkTurbines.FirstOrDefault(t => t.WindTurbineID == dau.WindTurbineID);
                    var status = parkStatuses.FirstOrDefault(s => s.WindTurbineID == dau.WindTurbineID && s.DauID == dau.DauID);

                    var dauItem = new DAUItemDTO
                    {
                        DauID = dau.DauID,
                        DAUName = dau.DAUName,
                        WindTurbineID = dau.WindTurbineID,
                        WindTurbineName = turbine?.WindTurbineName ?? "未知机组",
                        WindParkID = dau.WindParkID,
                        IPAddress = dau.IP ?? "",
                        Port = dau.Port,
                        IsAvailable = dau.IsAvailable,
                        AlarmState = status != null ? (int)status.AlarmState : 2,
                        AlarmStateDescription = status != null? AppFramework.Utility.EnumHelper.GetDescription(status.AlarmState) :"",
                        StatusUpdateTime = status?.StatusUpdateTime,
                    };

                    windParkStatus.DAUList.Add(dauItem);
                    allDAUItems.Add(dauItem);
                }

                // 计算风场DAU状态统计
                windParkStatus.StatusSummary = CalculateDAUStatusSummary(windParkStatus.DAUList);

                // 计算风场内部按状态和DAU名称的统计
                //windParkStatus.StatusStatistics = CalculateStatusStatistics(windParkStatus.DAUList);
                windParkStatus.NameStatistics = CalculateNameStatistics(windParkStatus.DAUList);

                result.WindParkStatusList.Add(windParkStatus);
            }

            // 计算全部DAU状态统计
            result.OverallSummary = CalculateDAUStatusSummary(allDAUItems);

            return result;
        }







        /// <summary>
        /// 计算按状态的统计
        /// </summary>
        /// <param name="dauList">DAU列表</param>
        /// <returns>状态统计列表</returns>
        private List<DAUStatusStatisticDTO> CalculateStatusStatistics(List<DAUItemDTO> dauList)
        {
            var statusGroups = dauList.GroupBy(d => d.AlarmState).ToList();
            var totalCount = dauList.Count;
            var statistics = new List<DAUStatusStatisticDTO>();

            foreach (var group in statusGroups.OrderBy(g => g.Key))
            {
                var statusStat = new DAUStatusStatisticDTO
                {
                    StatusValue = group.Key,
                    StatusDescription = AppFramework.Utility.EnumHelper.GetDescription(GetEnumDAUStatusFromInt(group.Key)),
                    Count = group.Count(),
                    DAUList = group.Select(d => $"{d.WindTurbineName}_{d.DAUName}").OrderBy(s => s).ToList(),
                    Percentage = Math.Round((double)group.Count() / totalCount * 100, 2)
                };

                statistics.Add(statusStat);
            }

            return statistics;
        }

        /// <summary>
        /// 计算按DAU名称的统计
        /// </summary>
        /// <param name="dauList">DAU列表</param>
        /// <returns>DAU名称统计列表</returns>
        private List<DAUNameStatisticDTO> CalculateNameStatistics(List<DAUItemDTO> dauList)
        {
            var nameGroups = dauList.GroupBy(d => d.DAUName).ToList();
            var totalCount = dauList.Count;
            var statistics = new List<DAUNameStatisticDTO>();

            foreach (var group in nameGroups.OrderBy(g => g.Key))
            {
                

                // 计算该名称下的状态分布
                var statusGroups = group.GroupBy(d => d.AlarmState).ToList();
                foreach (var statusGroup in statusGroups.OrderBy(sg => sg.Key))
                {
                    


                    var nameStat = new DAUNameStatisticDTO
                    {
                        DAUName = group.Key,
                        //Count = group.Count(),
                        Percentage = Math.Round((double)group.Count() / totalCount * 100, 2),
                        StatusDistribution = new List<DAUNameStatusDistributionDTO>(),
                        StatusValue = statusGroup.Key,
                        StatusDescription = AppFramework.Utility.EnumHelper.GetDescription(GetEnumDAUStatusFromInt(statusGroup.Key)),
                        Count = statusGroup.Count(),
                        DAUIdentifiers = statusGroup.Select(d => $"{d.WindTurbineID}_{d.DauID}").OrderBy(s => s).ToList()
                    };
                    statistics.Add(nameStat);
                }

                
            }

            return statistics;
        }

        /// <summary>
        /// 从整数值获取EnumDAUStatus枚举
        /// </summary>
        /// <param name="statusValue">状态值</param>
        /// <returns>EnumDAUStatus枚举</returns>
        private EnumDAUStatus? GetEnumDAUStatusFromInt(int statusValue)
        {
            if (statusValue == -1)
                return null; // 离线状态

            if (Enum.IsDefined(typeof(EnumDAUStatus), statusValue))
                return (EnumDAUStatus)statusValue;

            return null;
        }


        #endregion

    }
}
