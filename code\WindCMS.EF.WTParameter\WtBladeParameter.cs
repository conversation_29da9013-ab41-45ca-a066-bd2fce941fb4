﻿using System;
using System.Collections.Generic;

namespace WindCMS.EF.WTParameter
{
    /// <summary>
    /// 存储与叶轮相关的全部参数
    /// </summary>
    public partial class WtBladeParameter
    {
        public WtBladeParameter()
        {
            WtParametersFlanges = new HashSet<WtParametersFlange>();
        }

        /// <summary>
        /// 叶轮的唯一标识符
        /// </summary>
        public int RotorId { get; set; }
        /// <summary>
        /// 对应的机组型号ID（外键）
        /// </summary>
        public string ModelId { get; set; } = null!;
        /// <summary>
        /// 叶片生产厂家
        /// </summary>
        public string? BladeManufacturer { get; set; }
        /// <summary>
        /// 叶片型号
        /// </summary>
        public string? BladeModel { get; set; }
        /// <summary>
        /// 叶片长度（单位：m）
        /// </summary>
        public decimal? BladeLength { get; set; }
        /// <summary>
        /// 叶片材料
        /// </summary>
        public string? BladeMaterial { get; set; }
        /// <summary>
        /// 叶片质量（单位：吨）
        /// </summary>
        public decimal? BladeMass { get; set; }
        /// <summary>
        /// 扫风面积（单位：m²）
        /// </summary>
        public decimal? SweptArea { get; set; }
        /// <summary>
        /// 叶片数量
        /// </summary>
        public int? BladeCount { get; set; }
        /// <summary>
        /// 叶片挥舞1阶固有频率（单位：Hz）
        /// </summary>
        public decimal? BladeFlapFrequency1 { get; set; }
        /// <summary>
        /// 叶片挥舞2阶固有频率（单位：Hz）
        /// </summary>
        public decimal? BladeFlapFrequency2 { get; set; }
        /// <summary>
        /// 叶片挥舞3阶固有频率（单位：Hz）
        /// </summary>
        public decimal? BladeFlapFrequency3 { get; set; }
        /// <summary>
        /// 叶片摆振1阶固有频率（单位：Hz）
        /// </summary>
        public decimal? BladeEdgeFrequency1 { get; set; }
        /// <summary>
        /// 叶片摆振2阶固有频率（单位：Hz）
        /// </summary>
        public decimal? BladeEdgeFrequency2 { get; set; }
        /// <summary>
        /// 叶片摆振3阶固有频率（单位：Hz）
        /// </summary>
        public decimal? BladeEdgeFrequency3 { get; set; }
        /// <summary>
        /// 叶片振动传感器数量
        /// </summary>
        public int? BladeVibrationSensorCount { get; set; }
        /// <summary>
        /// 叶片振动传感器类型（如：加速度计、应变计等）
        /// </summary>
        public string? BladeVibrationSensorType { get; set; }
        /// <summary>
        /// 叶片振动数据采样率（单位：Hz）
        /// </summary>
        public decimal? BladeVibrationDataRate { get; set; }
        /// <summary>
        /// 变桨轴承1厂家
        /// </summary>
        public string? PitchBearingManufacturer1 { get; set; }
        /// <summary>
        /// 变桨轴承1型号
        /// </summary>
        public string? PitchBearingModel1 { get; set; }
        /// <summary>
        /// 变桨轴承2厂家
        /// </summary>
        public string? PitchBearingManufacturer2 { get; set; }
        /// <summary>
        /// 变桨轴承2型号
        /// </summary>
        public string? PitchBearingModel2 { get; set; }
        /// <summary>
        /// 变桨轴承3厂家
        /// </summary>
        public string? PitchBearingManufacturer3 { get; set; }
        /// <summary>
        /// 变桨轴承3型号
        /// </summary>
        public string? PitchBearingModel3 { get; set; }
        /// <summary>
        /// 变桨轴承外圈直径（单位：mm）
        /// </summary>
        public decimal? PitchBearingOuterDiameter { get; set; }
        /// <summary>
        /// 变桨轴承内圈直径（单位：mm）
        /// </summary>
        public decimal? PitchBearingInnerDiameter { get; set; }
        /// <summary>
        /// 变桨轴承外圈高度（单位：mm）
        /// </summary>
        public decimal? PitchBearingOuterHeight { get; set; }
        /// <summary>
        /// 变桨轴承内圈高度（单位：mm）
        /// </summary>
        public decimal? PitchBearingInnerHeight { get; set; }
        /// <summary>
        /// 变桨轴承外圈滚道直径（单位：mm）
        /// </summary>
        public decimal? PitchBearingOuterRollwayDiameter { get; set; }
        /// <summary>
        /// 变桨轴承内圈滚道直径（单位：mm）
        /// </summary>
        public decimal? PitchBearingInnerRollwayDiameter { get; set; }
        /// <summary>
        /// 变桨轴承滚道数量
        /// </summary>
        public int? PitchBearingRollNumber { get; set; }
        /// <summary>
        /// 变桨轴承滚子类型
        /// </summary>
        public string? PitchBearingRollType { get; set; }
        /// <summary>
        /// 变桨轴承滚子直径（单位：mm）
        /// </summary>
        public decimal? PitchBearingRollDiameter { get; set; }
        /// <summary>
        /// 变桨轴承滚子个数
        /// </summary>
        public int? PitchBearingRollCount { get; set; }
        /// <summary>
        /// 变桨方式（如：液压变桨、电动变桨）
        /// </summary>
        public string? PitchMethod { get; set; }
        /// <summary>
        /// 变桨轴承振动传感器数量
        /// </summary>
        public int? PitchBearingVibrationSensorCount { get; set; }
        /// <summary>
        /// 变桨轴承振动传感器类型（如：加速度计、应变计等）
        /// </summary>
        public string? PitchBearingVibrationSensorType { get; set; }
        /// <summary>
        /// 变桨轴承振动数据采样率（单位：Hz）
        /// </summary>
        public decimal? PitchBearingVibrationDataRate { get; set; }
        /// <summary>
        /// 叶根螺栓监测系统类型（如：有/无）
        /// </summary>
        public string? RootBoltMonitoringSystem { get; set; }
        /// <summary>
        /// 叶根螺栓传感器数量
        /// </summary>
        public int? RootBoltSensorCount { get; set; }
        /// <summary>
        /// 叶根螺栓传感器类型（如：应变计、压力传感器等）
        /// </summary>
        public string? RootBoltSensorType { get; set; }
        /// <summary>
        /// 叶根螺栓数据采样率（单位：Hz）
        /// </summary>
        public decimal? RootBoltDataRate { get; set; }
        /// <summary>
        /// 叶片齿圈模数（单位：mm）
        /// </summary>
        public decimal? GearCirclePitch { get; set; }
        /// <summary>
        /// 叶片齿圈齿数
        /// </summary>
        public int? GearCircleToothNumber { get; set; }
        /// <summary>
        /// 叶片齿圈变位系数
        /// </summary>
        public decimal? GearCircleProfileShiftFactor { get; set; }
        /// <summary>
        /// 叶片齿圈齿顶圆直径（单位：mm）
        /// </summary>
        public decimal? GearCircleToothTipDiameter { get; set; }
        /// <summary>
        /// 叶片齿圈齿轮长度（单位：mm）
        /// </summary>
        public decimal? GearCircleGearLength { get; set; }
        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
        /// <summary>
        /// 记录更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        public virtual ICollection<WtParametersFlange> WtParametersFlanges { get; set; }


        public decimal? PitchBearingRollBackDiameter { get; set; }
    }
}
