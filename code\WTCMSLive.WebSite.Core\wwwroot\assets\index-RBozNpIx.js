import{a2 as Ee,aP as Wt,j as N,ae as Xt,ad as G,b as P,a3 as me,_ as O,y as Yt,e4 as En,h as Jt,au as Qt,a8 as Q,aN as fe,aH as qe,aR as Nn,r as ce,aC as Oe,w as ve,ac as Pn,aB as $n,p as Dn,v as Tn,a6 as gt,F as In,dy as _n,af as Nt,am as De,a9 as An,aG as Ln,ej as jn,X as Fn,Y as Mn,$ as Bn,av as Hn,d$ as Pt,a5 as Zt,as as Re,ar as se,ap as Rn,az as Pe,ao as ft}from"./index-D9CxWmlM.js";import{o as bt,c as zn,g as Vn}from"./styleChecker-z-opWSaj.js";import{L as Gn,g as qn}from"./index-BS38043a.js";import{g as en,d as Un}from"./index-BGEB0Rhf.js";import{p as tn,K as $e}from"./shallowequal-vFdacwF3.js";import{e as ue}from"./ActionButton-BRQ4acFZ.js";import{i as mt}from"./initDefaultProps-C2vKchlZ.js";import{d as Wn}from"./index-DMbgFMzZ.js";const nn=Symbol("TreeContextKey"),Xn=Ee({compatConfig:{MODE:3},name:"TreeContext",props:{value:{type:Object}},setup(e,t){let{slots:n}=t;return Wt(nn,N(()=>e.value)),()=>{var o;return(o=n.default)===null||o===void 0?void 0:o.call(n)}}}),Kt=()=>Xt(nn,N(()=>({}))),on=Symbol("KeysStateKey"),Yn=e=>{Wt(on,e)},an=()=>Xt(on,{expandedKeys:G([]),selectedKeys:G([]),loadedKeys:G([]),loadingKeys:G([]),checkedKeys:G([]),halfCheckedKeys:G([]),expandedKeysSet:N(()=>new Set),selectedKeysSet:N(()=>new Set),loadedKeysSet:N(()=>new Set),loadingKeysSet:N(()=>new Set),checkedKeysSet:N(()=>new Set),halfCheckedKeysSet:N(()=>new Set),flattenNodes:G([])}),Jn=e=>{let{prefixCls:t,level:n,isStart:o,isEnd:a}=e;const r=`${t}-indent-unit`,l=[];for(let s=0;s<n;s+=1)l.push(P("span",{key:s,class:{[r]:!0,[`${r}-start`]:o[s],[`${r}-end`]:a[s]}},null));return P("span",{"aria-hidden":"true",class:`${t}-indent`},[l])},ln={eventKey:[String,Number],prefixCls:String,title:me.any,data:{type:Object,default:void 0},parent:{type:Object,default:void 0},isStart:{type:Array},isEnd:{type:Array},active:{type:Boolean,default:void 0},onMousemove:{type:Function},isLeaf:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},selectable:{type:Boolean,default:void 0},disabled:{type:Boolean,default:void 0},disableCheckbox:{type:Boolean,default:void 0},icon:me.any,switcherIcon:me.any,domRef:{type:Function}},Qn={prefixCls:{type:String},motion:{type:Object},focusable:{type:Boolean},activeItem:{type:Object},focused:{type:Boolean},tabindex:{type:Number},checkable:{type:Boolean},selectable:{type:Boolean},disabled:{type:Boolean},height:{type:Number},itemHeight:{type:Number},virtual:{type:Boolean},onScroll:{type:Function},onKeydown:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onActiveChange:{type:Function},onContextmenu:{type:Function},onListChangeStart:{type:Function},onListChangeEnd:{type:Function}},rn=()=>({prefixCls:String,focusable:{type:Boolean,default:void 0},activeKey:[Number,String],tabindex:Number,children:me.any,treeData:{type:Array},fieldNames:{type:Object},showLine:{type:[Boolean,Object],default:void 0},showIcon:{type:Boolean,default:void 0},icon:me.any,selectable:{type:Boolean,default:void 0},expandAction:[String,Boolean],disabled:{type:Boolean,default:void 0},multiple:{type:Boolean,default:void 0},checkable:{type:Boolean,default:void 0},checkStrictly:{type:Boolean,default:void 0},draggable:{type:[Function,Boolean]},defaultExpandParent:{type:Boolean,default:void 0},autoExpandParent:{type:Boolean,default:void 0},defaultExpandAll:{type:Boolean,default:void 0},defaultExpandedKeys:{type:Array},expandedKeys:{type:Array},defaultCheckedKeys:{type:Array},checkedKeys:{type:[Object,Array]},defaultSelectedKeys:{type:Array},selectedKeys:{type:Array},allowDrop:{type:Function},dropIndicatorRender:{type:Function},onFocus:{type:Function},onBlur:{type:Function},onKeydown:{type:Function},onContextmenu:{type:Function},onClick:{type:Function},onDblclick:{type:Function},onScroll:{type:Function},onExpand:{type:Function},onCheck:{type:Function},onSelect:{type:Function},onLoad:{type:Function},loadData:{type:Function},loadedKeys:{type:Array},onMouseenter:{type:Function},onMouseleave:{type:Function},onRightClick:{type:Function},onDragstart:{type:Function},onDragenter:{type:Function},onDragover:{type:Function},onDragleave:{type:Function},onDragend:{type:Function},onDrop:{type:Function},onActiveChange:{type:Function},filterTreeNode:{type:Function},motion:me.any,switcherIcon:me.any,height:Number,itemHeight:Number,virtual:{type:Boolean,default:void 0},direction:{type:String},rootClassName:String,rootStyle:Object});var Zn=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const $t="open",Dt="close",eo="---",vt=Ee({compatConfig:{MODE:3},name:"ATreeNode",inheritAttrs:!1,props:ln,isTreeNode:1,setup(e,t){let{attrs:n,slots:o,expose:a}=t;en(!("slots"in e.data),`treeData slots is deprecated, please use ${Object.keys(e.data.slots||{}).map(i=>"`v-slot:"+i+"` ")}instead`);const r=G(!1),l=Kt(),{expandedKeysSet:s,selectedKeysSet:h,loadedKeysSet:v,loadingKeysSet:b,checkedKeysSet:f,halfCheckedKeysSet:k}=an(),{dragOverNodeKey:x,dropPosition:C,keyEntities:m}=l.value,c=N(()=>ze(e.eventKey,{expandedKeysSet:s.value,selectedKeysSet:h.value,loadedKeysSet:v.value,loadingKeysSet:b.value,checkedKeysSet:f.value,halfCheckedKeysSet:k.value,dragOverNodeKey:x,dropPosition:C,keyEntities:m})),K=ue(()=>c.value.expanded),A=ue(()=>c.value.selected),_=ue(()=>c.value.checked),D=ue(()=>c.value.loaded),T=ue(()=>c.value.loading),W=ue(()=>c.value.halfChecked),U=ue(()=>c.value.dragOver),j=ue(()=>c.value.dragOverGapTop),y=ue(()=>c.value.dragOverGapBottom),E=ue(()=>c.value.pos),F=G(),J=N(()=>{const{eventKey:i}=e,{keyEntities:p}=l.value,{children:R}=p[i]||{};return!!(R||[]).length}),q=N(()=>{const{isLeaf:i}=e,{loadData:p}=l.value,R=J.value;return i===!1?!1:i||!p&&!R||p&&D.value&&!R}),oe=N(()=>q.value?null:K.value?$t:Dt),te=N(()=>{const{disabled:i}=e,{disabled:p}=l.value;return!!(p||i)}),Ke=N(()=>{const{checkable:i}=e,{checkable:p}=l.value;return!p||i===!1?!1:p}),re=N(()=>{const{selectable:i}=e,{selectable:p}=l.value;return typeof i=="boolean"?i:p}),H=N(()=>{const{data:i,active:p,checkable:R,disableCheckbox:Y,disabled:Z,selectable:ee}=e;return O(O({active:p,checkable:R,disableCheckbox:Y,disabled:Z,selectable:ee},i),{dataRef:i,data:i,isLeaf:q.value,checked:_.value,expanded:K.value,loading:T.value,selected:A.value,halfChecked:W.value})}),ye=En(),X=N(()=>{const{eventKey:i}=e,{keyEntities:p}=l.value,{parent:R}=p[i]||{};return O(O({},Ve(O({},e,c.value))),{parent:R})}),ne=Yt({eventData:X,eventKey:N(()=>e.eventKey),selectHandle:F,pos:E,key:ye.vnode.key});a(ne);const ie=i=>{const{onNodeDoubleClick:p}=l.value;p(i,X.value)},he=i=>{if(te.value)return;const{onNodeSelect:p}=l.value;i.preventDefault(),p(i,X.value)},Se=i=>{if(te.value)return;const{disableCheckbox:p}=e,{onNodeCheck:R}=l.value;if(!Ke.value||p)return;i.preventDefault();const Y=!_.value;R(i,X.value,Y)},Ne=i=>{const{onNodeClick:p}=l.value;p(i,X.value),re.value?he(i):Se(i)},Te=i=>{const{onNodeMouseEnter:p}=l.value;p(i,X.value)},Xe=i=>{const{onNodeMouseLeave:p}=l.value;p(i,X.value)},Ye=i=>{const{onNodeContextMenu:p}=l.value;p(i,X.value)},Je=i=>{const{onNodeDragStart:p}=l.value;i.stopPropagation(),r.value=!0,p(i,ne);try{i.dataTransfer.setData("text/plain","")}catch{}},Qe=i=>{const{onNodeDragEnter:p}=l.value;i.preventDefault(),i.stopPropagation(),p(i,ne)},Ze=i=>{const{onNodeDragOver:p}=l.value;i.preventDefault(),i.stopPropagation(),p(i,ne)},Le=i=>{const{onNodeDragLeave:p}=l.value;i.stopPropagation(),p(i,ne)},et=i=>{const{onNodeDragEnd:p}=l.value;i.stopPropagation(),r.value=!1,p(i,ne)},tt=i=>{const{onNodeDrop:p}=l.value;i.preventDefault(),i.stopPropagation(),r.value=!1,p(i,ne)},je=i=>{const{onNodeExpand:p}=l.value;T.value||p(i,X.value)},Fe=()=>{const{data:i}=e,{draggable:p}=l.value;return!!(p&&(!p.nodeDraggable||p.nodeDraggable(i)))},Me=()=>{const{draggable:i,prefixCls:p}=l.value;return i&&(i!=null&&i.icon)?P("span",{class:`${p}-draggable-icon`},[i.icon]):null},nt=()=>{var i,p,R;const{switcherIcon:Y=o.switcherIcon||((i=l.value.slots)===null||i===void 0?void 0:i[(R=(p=e.data)===null||p===void 0?void 0:p.slots)===null||R===void 0?void 0:R.switcherIcon])}=e,{switcherIcon:Z}=l.value,ee=Y||Z;return typeof ee=="function"?ee(H.value):ee},Be=()=>{const{loadData:i,onNodeLoad:p}=l.value;T.value||i&&K.value&&!q.value&&!J.value&&!D.value&&p(X.value)};Jt(()=>{Be()}),Qt(()=>{Be()});const ot=()=>{const{prefixCls:i}=l.value,p=nt();if(q.value)return p!==!1?P("span",{class:fe(`${i}-switcher`,`${i}-switcher-noop`)},[p]):null;const R=fe(`${i}-switcher`,`${i}-switcher_${K.value?$t:Dt}`);return p!==!1?P("span",{onClick:je,class:R},[p]):null},at=()=>{var i,p;const{disableCheckbox:R}=e,{prefixCls:Y}=l.value,Z=te.value;return Ke.value?P("span",{class:fe(`${Y}-checkbox`,_.value&&`${Y}-checkbox-checked`,!_.value&&W.value&&`${Y}-checkbox-indeterminate`,(Z||R)&&`${Y}-checkbox-disabled`),onClick:Se},[(p=(i=l.value).customCheckable)===null||p===void 0?void 0:p.call(i)]):null},He=()=>{const{prefixCls:i}=l.value;return P("span",{class:fe(`${i}-iconEle`,`${i}-icon__${oe.value||"docu"}`,T.value&&`${i}-icon_loading`)},null)},we=()=>{const{disabled:i,eventKey:p}=e,{draggable:R,dropLevelOffset:Y,dropPosition:Z,prefixCls:ee,indent:d,dropIndicatorRender:u,dragOverNodeKey:g,direction:w}=l.value;return!i&&R!==!1&&g===p?u({dropPosition:Z,dropLevelOffset:Y,indent:d,prefixCls:ee,direction:w}):null},lt=()=>{var i,p,R,Y,Z,ee;const{icon:d=o.icon,data:u}=e,g=o.title||((i=l.value.slots)===null||i===void 0?void 0:i[(R=(p=e.data)===null||p===void 0?void 0:p.slots)===null||R===void 0?void 0:R.title])||((Y=l.value.slots)===null||Y===void 0?void 0:Y.title)||e.title,{prefixCls:w,showIcon:I,icon:$,loadData:S}=l.value,M=te.value,V=`${w}-node-content-wrapper`;let L;if(I){const ae=d||((Z=l.value.slots)===null||Z===void 0?void 0:Z[(ee=u==null?void 0:u.slots)===null||ee===void 0?void 0:ee.icon])||$;L=ae?P("span",{class:fe(`${w}-iconEle`,`${w}-icon__customize`)},[typeof ae=="function"?ae(H.value):ae]):He()}else S&&T.value&&(L=He());let B;typeof g=="function"?B=g(H.value):B=g,B=B===void 0?eo:B;const z=P("span",{class:`${w}-title`},[B]);return P("span",{ref:F,title:typeof g=="string"?g:"",class:fe(`${V}`,`${V}-${oe.value||"normal"}`,!M&&(A.value||r.value)&&`${w}-node-selected`),onMouseenter:Te,onMouseleave:Xe,onContextmenu:Ye,onClick:Ne,onDblclick:ie},[L,z,we()])};return()=>{const i=O(O({},e),n),{eventKey:p,isLeaf:R,isStart:Y,isEnd:Z,domRef:ee,active:d,data:u,onMousemove:g,selectable:w}=i,I=Zn(i,["eventKey","isLeaf","isStart","isEnd","domRef","active","data","onMousemove","selectable"]),{prefixCls:$,filterTreeNode:S,keyEntities:M,dropContainerKey:V,dropTargetKey:L,draggingNodeKey:B}=l.value,z=te.value,ae=tn(I,{aria:!0,data:!0}),{level:pe}=M[p]||{},de=Z[Z.length-1],le=Fe(),ke=!z&&le,Ie=B===p,rt=w!==void 0?{"aria-selected":!!w}:void 0;return P("div",Q(Q({ref:ee,class:fe(n.class,`${$}-treenode`,{[`${$}-treenode-disabled`]:z,[`${$}-treenode-switcher-${K.value?"open":"close"}`]:!R,[`${$}-treenode-checkbox-checked`]:_.value,[`${$}-treenode-checkbox-indeterminate`]:W.value,[`${$}-treenode-selected`]:A.value,[`${$}-treenode-loading`]:T.value,[`${$}-treenode-active`]:d,[`${$}-treenode-leaf-last`]:de,[`${$}-treenode-draggable`]:ke,dragging:Ie,"drop-target":L===p,"drop-container":V===p,"drag-over":!z&&U.value,"drag-over-gap-top":!z&&j.value,"drag-over-gap-bottom":!z&&y.value,"filter-node":S&&S(X.value)}),style:n.style,draggable:ke,"aria-grabbed":Ie,onDragstart:ke?Je:void 0,onDragenter:le?Qe:void 0,onDragover:le?Ze:void 0,onDragleave:le?Le:void 0,onDrop:le?tt:void 0,onDragend:le?et:void 0,onMousemove:g},rt),ae),[P(Jn,{prefixCls:$,level:pe,isStart:Y,isEnd:Z},null),Me(),ot(),at(),lt()])}}});function ge(e,t){if(!e)return[];const n=e.slice(),o=n.indexOf(t);return o>=0&&n.splice(o,1),n}function xe(e,t){const n=(e||[]).slice();return n.indexOf(t)===-1&&n.push(t),n}function kt(e){return e.split("-")}function dn(e,t){return`${e}-${t}`}function to(e){return e&&e.type&&e.type.isTreeNode}function no(e,t){const n=[],o=t[e];function a(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach(l=>{let{key:s,children:h}=l;n.push(s),a(h)})}return a(o.children),n}function oo(e){if(e.parent){const t=kt(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function ao(e){const t=kt(e.pos);return Number(t[t.length-1])===0}function Tt(e,t,n,o,a,r,l,s,h,v){var b;const{clientX:f,clientY:k}=e,{top:x,height:C}=e.target.getBoundingClientRect(),c=((v==="rtl"?-1:1)*(((a==null?void 0:a.x)||0)-f)-12)/o;let K=s[n.eventKey];if(k<x+C/2){const E=l.findIndex(q=>q.key===K.key),F=E<=0?0:E-1,J=l[F].key;K=s[J]}const A=K.key,_=K,D=K.key;let T=0,W=0;if(!h.has(A))for(let E=0;E<c&&oo(K);E+=1)K=K.parent,W+=1;const U=t.eventData,j=K.node;let y=!0;return ao(K)&&K.level===0&&k<x+C/2&&r({dragNode:U,dropNode:j,dropPosition:-1})&&K.key===n.eventKey?T=-1:(_.children||[]).length&&h.has(D)?r({dragNode:U,dropNode:j,dropPosition:0})?T=0:y=!1:W===0?c>-1.5?r({dragNode:U,dropNode:j,dropPosition:1})?T=1:y=!1:r({dragNode:U,dropNode:j,dropPosition:0})?T=0:r({dragNode:U,dropNode:j,dropPosition:1})?T=1:y=!1:r({dragNode:U,dropNode:j,dropPosition:1})?T=1:y=!1,{dropPosition:T,dropLevelOffset:W,dropTargetKey:K.key,dropTargetPos:K.pos,dragOverNodeKey:D,dropContainerKey:T===0?null:((b=K.parent)===null||b===void 0?void 0:b.key)||null,dropAllowed:y}}function It(e,t){if(!e)return;const{multiple:n}=t;return n?e.slice():e.length?[e[0]]:e}function it(e){if(!e)return null;let t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(typeof e=="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return null;return t}function yt(e,t){const n=new Set;function o(a){if(n.has(a))return;const r=t[a];if(!r)return;n.add(a);const{parent:l,node:s}=r;s.disabled||l&&o(l.key)}return(e||[]).forEach(a=>{o(a)}),[...n]}var lo=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};function Ae(e,t){return e??t}function Ue(e){const{title:t,_title:n,key:o,children:a}=e||{},r=t||"title";return{title:r,_title:n||[r],key:o||"key",children:a||"children"}}function ht(e){function t(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return qe(n).map(a=>{var r,l,s,h;if(!to(a))return null;const v=a.children||{},b=a.key,f={};for(const[E,F]of Object.entries(a.props))f[Nn(E)]=F;const{isLeaf:k,checkable:x,selectable:C,disabled:m,disableCheckbox:c}=f,K={isLeaf:k||k===""||void 0,checkable:x||x===""||void 0,selectable:C||C===""||void 0,disabled:m||m===""||void 0,disableCheckbox:c||c===""||void 0},A=O(O({},f),K),{title:_=(r=v.title)===null||r===void 0?void 0:r.call(v,A),icon:D=(l=v.icon)===null||l===void 0?void 0:l.call(v,A),switcherIcon:T=(s=v.switcherIcon)===null||s===void 0?void 0:s.call(v,A)}=f,W=lo(f,["title","icon","switcherIcon"]),U=(h=v.default)===null||h===void 0?void 0:h.call(v),j=O(O(O({},W),{title:_,icon:D,switcherIcon:T,key:b,isLeaf:k}),K),y=t(U);return y.length&&(j.children=y),j})}return t(e)}function ro(e,t,n){const{_title:o,key:a,children:r}=Ue(n),l=new Set(t===!0?[]:t),s=[];function h(v){let b=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return v.map((f,k)=>{const x=dn(b?b.pos:"0",k),C=Ae(f[a],x);let m;for(let K=0;K<o.length;K+=1){const A=o[K];if(f[A]!==void 0){m=f[A];break}}const c=O(O({},bt(f,[...o,a,r])),{title:m,key:C,parent:b,pos:x,children:null,data:f,isStart:[...b?b.isStart:[],k===0],isEnd:[...b?b.isEnd:[],k===v.length-1]});return s.push(c),t===!0||l.has(C)?c.children=h(f[r]||[],c):c.children=[],c})}return h(e),s}function io(e,t,n){let o={};typeof n=="object"?o=n:o={externalGetKey:n},o=o||{};const{childrenPropName:a,externalGetKey:r,fieldNames:l}=o,{key:s,children:h}=Ue(l),v=a||h;let b;r?typeof r=="string"?b=k=>k[r]:typeof r=="function"&&(b=k=>r(k)):b=(k,x)=>Ae(k[s],x);function f(k,x,C,m){const c=k?k[v]:e,K=k?dn(C.pos,x):"0",A=k?[...m,k]:[];if(k){const _=b(k,K),D={node:k,index:x,pos:K,key:_,parentPos:C.node?C.pos:null,level:C.level+1,nodes:A};t(D)}c&&c.forEach((_,D)=>{f(_,D,{node:k,pos:K,level:C?C.level+1:-1},A)})}f(null)}function sn(e){let{initWrapper:t,processEntity:n,onProcessFinished:o,externalGetKey:a,childrenPropName:r,fieldNames:l}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;const h=a||s,v={},b={};let f={posEntities:v,keyEntities:b};return t&&(f=t(f)||f),io(e,k=>{const{node:x,index:C,pos:m,key:c,parentPos:K,level:A,nodes:_}=k,D={node:x,nodes:_,index:C,key:c,pos:m,level:A},T=Ae(c,m);v[m]=D,b[T]=D,D.parent=v[K],D.parent&&(D.parent.children=D.parent.children||[],D.parent.children.push(D)),n&&n(D,f)},{externalGetKey:h,childrenPropName:r,fieldNames:l}),o&&o(f),f}function ze(e,t){let{expandedKeysSet:n,selectedKeysSet:o,loadedKeysSet:a,loadingKeysSet:r,checkedKeysSet:l,halfCheckedKeysSet:s,dragOverNodeKey:h,dropPosition:v,keyEntities:b}=t;const f=b[e];return{eventKey:e,expanded:n.has(e),selected:o.has(e),loaded:a.has(e),loading:r.has(e),checked:l.has(e),halfChecked:s.has(e),pos:String(f?f.pos:""),parent:f.parent,dragOver:h===e&&v===0,dragOverGapTop:h===e&&v===-1,dragOverGapBottom:h===e&&v===1}}function Ve(e){const{data:t,expanded:n,selected:o,checked:a,loaded:r,loading:l,halfChecked:s,dragOver:h,dragOverGapTop:v,dragOverGapBottom:b,pos:f,active:k,eventKey:x}=e,C=O(O({dataRef:t},t),{expanded:n,selected:o,checked:a,loaded:r,loading:l,halfChecked:s,dragOver:h,dragOverGapTop:v,dragOverGapBottom:b,pos:f,active:k,eventKey:x,key:x});return"props"in C||Object.defineProperty(C,"props",{get(){return e}}),C}function cn(e,t){const n=new Set;return e.forEach(o=>{t.has(o)||n.add(o)}),n}function so(e){const{disabled:t,disableCheckbox:n,checkable:o}=e||{};return!!(t||n)||o===!1}function co(e,t,n,o){const a=new Set(e),r=new Set;for(let s=0;s<=n;s+=1)(t.get(s)||new Set).forEach(v=>{const{key:b,node:f,children:k=[]}=v;a.has(b)&&!o(f)&&k.filter(x=>!o(x.node)).forEach(x=>{a.add(x.key)})});const l=new Set;for(let s=n;s>=0;s-=1)(t.get(s)||new Set).forEach(v=>{const{parent:b,node:f}=v;if(o(f)||!v.parent||l.has(v.parent.key))return;if(o(v.parent.node)){l.add(b.key);return}let k=!0,x=!1;(b.children||[]).filter(C=>!o(C.node)).forEach(C=>{let{key:m}=C;const c=a.has(m);k&&!c&&(k=!1),!x&&(c||r.has(m))&&(x=!0)}),k&&a.add(b.key),x&&r.add(b.key),l.add(b.key)});return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(cn(r,a))}}function uo(e,t,n,o,a){const r=new Set(e);let l=new Set(t);for(let h=0;h<=o;h+=1)(n.get(h)||new Set).forEach(b=>{const{key:f,node:k,children:x=[]}=b;!r.has(f)&&!l.has(f)&&!a(k)&&x.filter(C=>!a(C.node)).forEach(C=>{r.delete(C.key)})});l=new Set;const s=new Set;for(let h=o;h>=0;h-=1)(n.get(h)||new Set).forEach(b=>{const{parent:f,node:k}=b;if(a(k)||!b.parent||s.has(b.parent.key))return;if(a(b.parent.node)){s.add(f.key);return}let x=!0,C=!1;(f.children||[]).filter(m=>!a(m.node)).forEach(m=>{let{key:c}=m;const K=r.has(c);x&&!K&&(x=!1),!C&&(K||l.has(c))&&(C=!0)}),x||r.delete(f.key),C&&l.add(f.key),s.add(f.key)});return{checkedKeys:Array.from(r),halfCheckedKeys:Array.from(cn(l,r))}}function dt(e,t,n,o,a,r){let l;r?l=r:l=so;const s=new Set(e.filter(v=>!!n[v]));let h;return t===!0?h=co(s,a,o,l):h=uo(s,t.halfCheckedKeys,a,o,l),h}function fo(e){const t=ce(0),n=G();return Oe(()=>{const o=new Map;let a=0;const r=e.value||{};for(const l in r)if(Object.prototype.hasOwnProperty.call(r,l)){const s=r[l],{level:h}=s;let v=o.get(h);v||(v=new Set,o.set(h,v)),v.add(s),a=Math.max(a,h)}t.value=a,n.value=o}),{maxLevel:t,levelEntities:n}}var _t=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const vo=Ee({compatConfig:{MODE:3},name:"MotionTreeNode",inheritAttrs:!1,props:O(O({},ln),{active:Boolean,motion:Object,motionNodes:{type:Array},onMotionStart:Function,onMotionEnd:Function,motionType:String}),setup(e,t){let{attrs:n,slots:o}=t;const a=G(!0),r=Kt(),l=G(!1),s=N(()=>e.motion?e.motion:zn()),h=(v,b)=>{var f,k,x,C;b==="appear"?(k=(f=s.value)===null||f===void 0?void 0:f.onAfterEnter)===null||k===void 0||k.call(f,v):b==="leave"&&((C=(x=s.value)===null||x===void 0?void 0:x.onAfterLeave)===null||C===void 0||C.call(x,v)),l.value||e.onMotionEnd(),l.value=!0};return ve(()=>e.motionNodes,()=>{e.motionNodes&&e.motionType==="hide"&&a.value&&gt(()=>{a.value=!1})},{immediate:!0,flush:"post"}),Jt(()=>{e.motionNodes&&e.onMotionStart()}),Pn(()=>{e.motionNodes&&h()}),()=>{const{motion:v,motionNodes:b,motionType:f,active:k,eventKey:x}=e,C=_t(e,["motion","motionNodes","motionType","active","eventKey"]);return b?P($n,Q(Q({},s.value),{},{appear:f==="show",onAfterAppear:m=>h(m,"appear"),onAfterLeave:m=>h(m,"leave")}),{default:()=>[Dn(P("div",{class:`${r.value.prefixCls}-treenode-motion`},[b.map(m=>{const c=_t(m.data,[]),{title:K,key:A,isStart:_,isEnd:D}=m;return delete c.children,P(vt,Q(Q({},c),{},{title:K,active:k,data:m.data,key:A,eventKey:A,isStart:_,isEnd:D}),o)})]),[[Tn,a.value]])]}):P(vt,Q(Q({class:n.class,style:n.style},C),{},{active:k,eventKey:x}),o)}}});function yo(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];const n=e.length,o=t.length;if(Math.abs(n-o)!==1)return{add:!1,key:null};function a(r,l){const s=new Map;r.forEach(v=>{s.set(v,!0)});const h=l.filter(v=>!s.has(v));return h.length===1?h[0]:null}return n<o?{add:!0,key:a(e,t)}:{add:!1,key:a(t,e)}}function At(e,t,n){const o=e.findIndex(l=>l.key===n),a=e[o+1],r=t.findIndex(l=>l.key===n);if(a){const l=t.findIndex(s=>s.key===a.key);return t.slice(r+1,l)}return t.slice(r+1)}var Lt=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const jt={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},ho=()=>{},Ce=`RC_TREE_MOTION_${Math.random()}`,pt={key:Ce},un={key:Ce,level:0,index:0,pos:"0",node:pt,nodes:[pt]},Ft={parent:null,children:[],pos:un.pos,data:pt,title:null,key:Ce,isStart:[],isEnd:[]};function Mt(e,t,n,o){return t===!1||!n?e:e.slice(0,Math.ceil(n/o)+1)}function Bt(e){const{key:t,pos:n}=e;return Ae(t,n)}function po(e){let t=String(e.key),n=e;for(;n.parent;)n=n.parent,t=`${n.key} > ${t}`;return t}const go=Ee({compatConfig:{MODE:3},name:"NodeList",inheritAttrs:!1,props:Qn,setup(e,t){let{expose:n,attrs:o}=t;const a=ce(),r=ce(),{expandedKeys:l,flattenNodes:s}=an();n({scrollTo:m=>{a.value.scrollTo(m)},getIndentWidth:()=>r.value.offsetWidth});const h=G(s.value),v=G([]),b=ce(null);function f(){h.value=s.value,v.value=[],b.value=null,e.onListChangeEnd()}const k=Kt();ve([()=>l.value.slice(),s],(m,c)=>{let[K,A]=m,[_,D]=c;const T=yo(_,K);if(T.key!==null){const{virtual:W,height:U,itemHeight:j}=e;if(T.add){const y=D.findIndex(J=>{let{key:q}=J;return q===T.key}),E=Mt(At(D,A,T.key),W,U,j),F=D.slice();F.splice(y+1,0,Ft),h.value=F,v.value=E,b.value="show"}else{const y=A.findIndex(J=>{let{key:q}=J;return q===T.key}),E=Mt(At(A,D,T.key),W,U,j),F=A.slice();F.splice(y+1,0,Ft),h.value=F,v.value=E,b.value="hide"}}else D!==A&&(h.value=A)}),ve(()=>k.value.dragging,m=>{m||f()});const x=N(()=>e.motion===void 0?h.value:s.value),C=()=>{e.onActiveChange(null)};return()=>{const m=O(O({},e),o),{prefixCls:c,selectable:K,checkable:A,disabled:_,motion:D,height:T,itemHeight:W,virtual:U,focusable:j,activeItem:y,focused:E,tabindex:F,onKeydown:J,onFocus:q,onBlur:oe,onListChangeStart:te,onListChangeEnd:Ke}=m,re=Lt(m,["prefixCls","selectable","checkable","disabled","motion","height","itemHeight","virtual","focusable","activeItem","focused","tabindex","onKeydown","onFocus","onBlur","onListChangeStart","onListChangeEnd"]);return P(In,null,[E&&y&&P("span",{style:jt,"aria-live":"assertive"},[po(y)]),P("div",null,[P("input",{style:jt,disabled:j===!1||_,tabindex:j!==!1?F:null,onKeydown:J,onFocus:q,onBlur:oe,value:"",onChange:ho,"aria-label":"for screen reader"},null)]),P("div",{class:`${c}-treenode`,"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden"}},[P("div",{class:`${c}-indent`},[P("div",{ref:r,class:`${c}-indent-unit`},null)])]),P(Gn,Q(Q({},bt(re,["onActiveChange"])),{},{data:x.value,itemKey:Bt,height:T,fullHeight:!1,virtual:U,itemHeight:W,prefixCls:`${c}-list`,ref:a,onVisibleChange:(H,ye)=>{const X=new Set(H);ye.filter(ie=>!X.has(ie)).some(ie=>Bt(ie)===Ce)&&f()}}),{default:H=>{const{pos:ye}=H,X=Lt(H.data,[]),{title:ne,key:ie,isStart:he,isEnd:Se}=H,Ne=Ae(ie,ye);return delete X.key,delete X.children,P(vo,Q(Q({},X),{},{eventKey:Ne,title:ne,active:!!y&&ie===y.key,data:H.data,isStart:he,isEnd:Se,motion:D,motionNodes:ie===Ce?v.value:null,motionType:b.value,onMotionStart:te,onMotionEnd:f,onMousemove:C}),null)}})])}}});function bo(e){let{dropPosition:t,dropLevelOffset:n,indent:o}=e;const a={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:"2px"};switch(t){case-1:a.top=0,a.left=`${-n*o}px`;break;case 1:a.bottom=0,a.left=`${-n*o}px`;break;case 0:a.bottom=0,a.left=`${o}`;break}return P("div",{style:a},null)}const mo=10,Ko=Ee({compatConfig:{MODE:3},name:"Tree",inheritAttrs:!1,props:mt(rn(),{prefixCls:"vc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,expandAction:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:bo,allowDrop:()=>!0}),setup(e,t){let{attrs:n,slots:o,expose:a}=t;const r=G(!1);let l={};const s=G(),h=G([]),v=G([]),b=G([]),f=G([]),k=G([]),x=G([]),C={},m=Yt({draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null}),c=G([]);ve([()=>e.treeData,()=>e.children],()=>{c.value=e.treeData!==void 0?e.treeData.slice():ht(Nt(e.children))},{immediate:!0,deep:!0});const K=G({}),A=G(!1),_=G(null),D=G(!1),T=N(()=>Ue(e.fieldNames)),W=G();let U=null,j=null,y=null;const E=N(()=>({expandedKeysSet:F.value,selectedKeysSet:J.value,loadedKeysSet:q.value,loadingKeysSet:oe.value,checkedKeysSet:te.value,halfCheckedKeysSet:Ke.value,dragOverNodeKey:m.dragOverNodeKey,dropPosition:m.dropPosition,keyEntities:K.value})),F=N(()=>new Set(x.value)),J=N(()=>new Set(h.value)),q=N(()=>new Set(f.value)),oe=N(()=>new Set(k.value)),te=N(()=>new Set(v.value)),Ke=N(()=>new Set(b.value));Oe(()=>{if(c.value){const d=sn(c.value,{fieldNames:T.value});K.value=O({[Ce]:un},d.keyEntities)}});let re=!1;ve([()=>e.expandedKeys,()=>e.autoExpandParent,K],(d,u)=>{let[g,w]=d,[I,$]=u,S=x.value;if(e.expandedKeys!==void 0||re&&w!==$)S=e.autoExpandParent||!re&&e.defaultExpandParent?yt(e.expandedKeys,K.value):e.expandedKeys;else if(!re&&e.defaultExpandAll){const M=O({},K.value);delete M[Ce],S=Object.keys(M).map(V=>M[V].key)}else!re&&e.defaultExpandedKeys&&(S=e.autoExpandParent||e.defaultExpandParent?yt(e.defaultExpandedKeys,K.value):e.defaultExpandedKeys);S&&(x.value=S),re=!0},{immediate:!0});const H=G([]);Oe(()=>{H.value=ro(c.value,x.value,T.value)}),Oe(()=>{e.selectable&&(e.selectedKeys!==void 0?h.value=It(e.selectedKeys,e):!re&&e.defaultSelectedKeys&&(h.value=It(e.defaultSelectedKeys,e)))});const{maxLevel:ye,levelEntities:X}=fo(K);Oe(()=>{if(e.checkable){let d;if(e.checkedKeys!==void 0?d=it(e.checkedKeys)||{}:!re&&e.defaultCheckedKeys?d=it(e.defaultCheckedKeys)||{}:c.value&&(d=it(e.checkedKeys)||{checkedKeys:v.value,halfCheckedKeys:b.value}),d){let{checkedKeys:u=[],halfCheckedKeys:g=[]}=d;e.checkStrictly||({checkedKeys:u,halfCheckedKeys:g}=dt(u,!0,K.value,ye.value,X.value)),v.value=u,b.value=g}}}),Oe(()=>{e.loadedKeys&&(f.value=e.loadedKeys)});const ne=()=>{O(m,{dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})},ie=d=>{W.value.scrollTo(d)};ve(()=>e.activeKey,()=>{e.activeKey!==void 0&&(_.value=e.activeKey)},{immediate:!0}),ve(_,d=>{gt(()=>{d!==null&&ie({key:d})})},{immediate:!0,flush:"post"});const he=d=>{e.expandedKeys===void 0&&(x.value=d)},Se=()=>{m.draggingNodeKey!==null&&O(m,{draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),U=null,y=null},Ne=(d,u)=>{const{onDragend:g}=e;m.dragOverNodeKey=null,Se(),g==null||g({event:d,node:u.eventData}),j=null},Te=d=>{Ne(d,null),window.removeEventListener("dragend",Te)},Xe=(d,u)=>{const{onDragstart:g}=e,{eventKey:w,eventData:I}=u;j=u,U={x:d.clientX,y:d.clientY};const $=ge(x.value,w);m.draggingNodeKey=w,m.dragChildrenKeys=no(w,K.value),s.value=W.value.getIndentWidth(),he($),window.addEventListener("dragend",Te),g&&g({event:d,node:I})},Ye=(d,u)=>{const{onDragenter:g,onExpand:w,allowDrop:I,direction:$}=e,{pos:S,eventKey:M}=u;if(y!==M&&(y=M),!j){ne();return}const{dropPosition:V,dropLevelOffset:L,dropTargetKey:B,dropContainerKey:z,dropTargetPos:ae,dropAllowed:pe,dragOverNodeKey:de}=Tt(d,j,u,s.value,U,I,H.value,K.value,F.value,$);if(m.dragChildrenKeys.indexOf(B)!==-1||!pe){ne();return}if(l||(l={}),Object.keys(l).forEach(le=>{clearTimeout(l[le])}),j.eventKey!==u.eventKey&&(l[S]=window.setTimeout(()=>{if(m.draggingNodeKey===null)return;let le=x.value.slice();const ke=K.value[u.eventKey];ke&&(ke.children||[]).length&&(le=xe(x.value,u.eventKey)),he(le),w&&w(le,{node:u.eventData,expanded:!0,nativeEvent:d})},800)),j.eventKey===B&&L===0){ne();return}O(m,{dragOverNodeKey:de,dropPosition:V,dropLevelOffset:L,dropTargetKey:B,dropContainerKey:z,dropTargetPos:ae,dropAllowed:pe}),g&&g({event:d,node:u.eventData,expandedKeys:x.value})},Je=(d,u)=>{const{onDragover:g,allowDrop:w,direction:I}=e;if(!j)return;const{dropPosition:$,dropLevelOffset:S,dropTargetKey:M,dropContainerKey:V,dropAllowed:L,dropTargetPos:B,dragOverNodeKey:z}=Tt(d,j,u,s.value,U,w,H.value,K.value,F.value,I);m.dragChildrenKeys.indexOf(M)!==-1||!L||(j.eventKey===M&&S===0?m.dropPosition===null&&m.dropLevelOffset===null&&m.dropTargetKey===null&&m.dropContainerKey===null&&m.dropTargetPos===null&&m.dropAllowed===!1&&m.dragOverNodeKey===null||ne():$===m.dropPosition&&S===m.dropLevelOffset&&M===m.dropTargetKey&&V===m.dropContainerKey&&B===m.dropTargetPos&&L===m.dropAllowed&&z===m.dragOverNodeKey||O(m,{dropPosition:$,dropLevelOffset:S,dropTargetKey:M,dropContainerKey:V,dropTargetPos:B,dropAllowed:L,dragOverNodeKey:z}),g&&g({event:d,node:u.eventData}))},Qe=(d,u)=>{y===u.eventKey&&!d.currentTarget.contains(d.relatedTarget)&&(ne(),y=null);const{onDragleave:g}=e;g&&g({event:d,node:u.eventData})},Ze=function(d,u){let g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var w;const{dragChildrenKeys:I,dropPosition:$,dropTargetKey:S,dropTargetPos:M,dropAllowed:V}=m;if(!V)return;const{onDrop:L}=e;if(m.dragOverNodeKey=null,Se(),S===null)return;const B=O(O({},ze(S,Nt(E.value))),{active:((w=R.value)===null||w===void 0?void 0:w.key)===S,data:K.value[S].node});I.indexOf(S);const z=kt(M),ae={event:d,node:Ve(B),dragNode:j?j.eventData:null,dragNodesKeys:[j.eventKey].concat(I),dropToGap:$!==0,dropPosition:$+Number(z[z.length-1])};g||L==null||L(ae),j=null},Le=(d,u)=>{const{expanded:g,key:w}=u,I=H.value.filter(S=>S.key===w)[0],$=Ve(O(O({},ze(w,E.value)),{data:I.data}));he(g?ge(x.value,w):xe(x.value,w)),we(d,$)},et=(d,u)=>{const{onClick:g,expandAction:w}=e;w==="click"&&Le(d,u),g&&g(d,u)},tt=(d,u)=>{const{onDblclick:g,expandAction:w}=e;(w==="doubleclick"||w==="dblclick")&&Le(d,u),g&&g(d,u)},je=(d,u)=>{let g=h.value;const{onSelect:w,multiple:I}=e,{selected:$}=u,S=u[T.value.key],M=!$;M?I?g=xe(g,S):g=[S]:g=ge(g,S);const V=K.value,L=g.map(B=>{const z=V[B];return z?z.node:null}).filter(B=>B);e.selectedKeys===void 0&&(h.value=g),w&&w(g,{event:"select",selected:M,node:u,selectedNodes:L,nativeEvent:d})},Fe=(d,u,g)=>{const{checkStrictly:w,onCheck:I}=e,$=u[T.value.key];let S;const M={event:"check",node:u,checked:g,nativeEvent:d},V=K.value;if(w){const L=g?xe(v.value,$):ge(v.value,$),B=ge(b.value,$);S={checked:L,halfChecked:B},M.checkedNodes=L.map(z=>V[z]).filter(z=>z).map(z=>z.node),e.checkedKeys===void 0&&(v.value=L)}else{let{checkedKeys:L,halfCheckedKeys:B}=dt([...v.value,$],!0,V,ye.value,X.value);if(!g){const z=new Set(L);z.delete($),{checkedKeys:L,halfCheckedKeys:B}=dt(Array.from(z),{halfCheckedKeys:B},V,ye.value,X.value)}S=L,M.checkedNodes=[],M.checkedNodesPositions=[],M.halfCheckedKeys=B,L.forEach(z=>{const ae=V[z];if(!ae)return;const{node:pe,pos:de}=ae;M.checkedNodes.push(pe),M.checkedNodesPositions.push({node:pe,pos:de})}),e.checkedKeys===void 0&&(v.value=L,b.value=B)}I&&I(S,M)},Me=d=>{const u=d[T.value.key],g=new Promise((w,I)=>{const{loadData:$,onLoad:S}=e;if(!$||q.value.has(u)||oe.value.has(u))return null;$(d).then(()=>{const V=xe(f.value,u),L=ge(k.value,u);S&&S(V,{event:"load",node:d}),e.loadedKeys===void 0&&(f.value=V),k.value=L,w()}).catch(V=>{const L=ge(k.value,u);if(k.value=L,C[u]=(C[u]||0)+1,C[u]>=mo){const B=xe(f.value,u);e.loadedKeys===void 0&&(f.value=B),w()}I(V)}),k.value=xe(k.value,u)});return g.catch(()=>{}),g},nt=(d,u)=>{const{onMouseenter:g}=e;g&&g({event:d,node:u})},Be=(d,u)=>{const{onMouseleave:g}=e;g&&g({event:d,node:u})},ot=(d,u)=>{const{onRightClick:g}=e;g&&(d.preventDefault(),g({event:d,node:u}))},at=d=>{const{onFocus:u}=e;A.value=!0,u&&u(d)},He=d=>{const{onBlur:u}=e;A.value=!1,p(null),u&&u(d)},we=(d,u)=>{let g=x.value;const{onExpand:w,loadData:I}=e,{expanded:$}=u,S=u[T.value.key];if(D.value)return;g.indexOf(S);const M=!$;if(M?g=xe(g,S):g=ge(g,S),he(g),w&&w(g,{node:u,expanded:M,nativeEvent:d}),M&&I){const V=Me(u);V&&V.then(()=>{}).catch(L=>{const B=ge(x.value,S);he(B),Promise.reject(L)})}},lt=()=>{D.value=!0},i=()=>{setTimeout(()=>{D.value=!1})},p=d=>{const{onActiveChange:u}=e;_.value!==d&&(e.activeKey!==void 0&&(_.value=d),d!==null&&ie({key:d}),u&&u(d))},R=N(()=>_.value===null?null:H.value.find(d=>{let{key:u}=d;return u===_.value})||null),Y=d=>{let u=H.value.findIndex(w=>{let{key:I}=w;return I===_.value});u===-1&&d<0&&(u=H.value.length),u=(u+d+H.value.length)%H.value.length;const g=H.value[u];if(g){const{key:w}=g;p(w)}else p(null)},Z=N(()=>Ve(O(O({},ze(_.value,E.value)),{data:R.value.data,active:!0}))),ee=d=>{const{onKeydown:u,checkable:g,selectable:w}=e;switch(d.which){case $e.UP:{Y(-1),d.preventDefault();break}case $e.DOWN:{Y(1),d.preventDefault();break}}const I=R.value;if(I&&I.data){const $=I.data.isLeaf===!1||!!(I.data.children||[]).length,S=Z.value;switch(d.which){case $e.LEFT:{$&&F.value.has(_.value)?we({},S):I.parent&&p(I.parent.key),d.preventDefault();break}case $e.RIGHT:{$&&!F.value.has(_.value)?we({},S):I.children&&I.children.length&&p(I.children[0].key),d.preventDefault();break}case $e.ENTER:case $e.SPACE:{g&&!S.disabled&&S.checkable!==!1&&!S.disableCheckbox?Fe({},S,!te.value.has(_.value)):!g&&w&&!S.disabled&&S.selectable!==!1&&je({},S);break}}}u&&u(d)};return a({onNodeExpand:we,scrollTo:ie,onKeydown:ee,selectedKeys:N(()=>h.value),checkedKeys:N(()=>v.value),halfCheckedKeys:N(()=>b.value),loadedKeys:N(()=>f.value),loadingKeys:N(()=>k.value),expandedKeys:N(()=>x.value)}),_n(()=>{window.removeEventListener("dragend",Te),r.value=!0}),Yn({expandedKeys:x,selectedKeys:h,loadedKeys:f,loadingKeys:k,checkedKeys:v,halfCheckedKeys:b,expandedKeysSet:F,selectedKeysSet:J,loadedKeysSet:q,loadingKeysSet:oe,checkedKeysSet:te,halfCheckedKeysSet:Ke,flattenNodes:H}),()=>{const{draggingNodeKey:d,dropLevelOffset:u,dropContainerKey:g,dropTargetKey:w,dropPosition:I,dragOverNodeKey:$}=m,{prefixCls:S,showLine:M,focusable:V,tabindex:L=0,selectable:B,showIcon:z,icon:ae=o.icon,switcherIcon:pe,draggable:de,checkable:le,checkStrictly:ke,disabled:Ie,motion:rt,loadData:vn,filterTreeNode:yn,height:hn,itemHeight:pn,virtual:gn,dropIndicatorRender:bn,onContextmenu:mn,onScroll:Kn,direction:kn,rootClassName:xn,rootStyle:Sn}=e,{class:wn,style:On}=n,Cn=tn(O(O({},e),n),{aria:!0,data:!0});let _e;return de?typeof de=="object"?_e=de:typeof de=="function"?_e={nodeDraggable:de}:_e={}:_e=!1,P(Xn,{value:{prefixCls:S,selectable:B,showIcon:z,icon:ae,switcherIcon:pe,draggable:_e,draggingNodeKey:d,checkable:le,customCheckable:o.checkable,checkStrictly:ke,disabled:Ie,keyEntities:K.value,dropLevelOffset:u,dropContainerKey:g,dropTargetKey:w,dropPosition:I,dragOverNodeKey:$,dragging:d!==null,indent:s.value,direction:kn,dropIndicatorRender:bn,loadData:vn,filterTreeNode:yn,onNodeClick:et,onNodeDoubleClick:tt,onNodeExpand:we,onNodeSelect:je,onNodeCheck:Fe,onNodeLoad:Me,onNodeMouseEnter:nt,onNodeMouseLeave:Be,onNodeContextMenu:ot,onNodeDragStart:Xe,onNodeDragEnter:Ye,onNodeDragOver:Je,onNodeDragLeave:Qe,onNodeDragEnd:Ne,onNodeDrop:Ze,slots:o}},{default:()=>[P("div",{role:"tree",class:fe(S,wn,xn,{[`${S}-show-line`]:M,[`${S}-focused`]:A.value,[`${S}-active-focused`]:_.value!==null}),style:Sn},[P(go,Q({ref:W,prefixCls:S,style:On,disabled:Ie,selectable:B,checkable:!!le,motion:rt,height:hn,itemHeight:pn,virtual:gn,focusable:V,focused:A.value,tabindex:L,activeItem:R.value,onFocus:at,onBlur:He,onKeydown:ee,onActiveChange:p,onListChangeStart:lt,onListChangeEnd:i,onContextmenu:mn,onScroll:Kn},Cn),null)])]})}}});var ko={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"};function Ht(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){xo(e,a,n[a])})}return e}function xo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var We=function(t,n){var o=Ht({},t,n.attrs);return P(De,Ht({},o,{icon:ko}),null)};We.displayName="FileOutlined";We.inheritAttrs=!1;var So={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"};function Rt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){wo(e,a,n[a])})}return e}function wo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var xt=function(t,n){var o=Rt({},t,n.attrs);return P(De,Rt({},o,{icon:So}),null)};xt.displayName="MinusSquareOutlined";xt.inheritAttrs=!1;var Oo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"};function zt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){Co(e,a,n[a])})}return e}function Co(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var St=function(t,n){var o=zt({},t,n.attrs);return P(De,zt({},o,{icon:Oo}),null)};St.displayName="PlusSquareOutlined";St.inheritAttrs=!1;var Eo={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"};function Vt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){No(e,a,n[a])})}return e}function No(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var wt=function(t,n){var o=Vt({},t,n.attrs);return P(De,Vt({},o,{icon:Eo}),null)};wt.displayName="CaretDownFilled";wt.inheritAttrs=!1;function Po(e,t,n,o,a){const{isLeaf:r,expanded:l,loading:s}=n;let h=t;if(s)return P(An,{class:`${e}-switcher-loading-icon`},null);let v;a&&typeof a=="object"&&(v=a.showLeafIcon);let b=null;const f=`${e}-switcher-icon`;return r?a?v&&o?o(n):(typeof a=="object"&&!v?b=P("span",{class:`${e}-switcher-leaf-line`},null):b=P(We,{class:`${e}-switcher-line-icon`},null),b):null:(b=P(wt,{class:f},null),a&&(b=l?P(xt,{class:`${e}-switcher-line-icon`},null):P(St,{class:`${e}-switcher-line-icon`},null)),typeof t=="function"?h=t(O(O({},n),{defaultIcon:b,switcherCls:f})):Ln(h)&&(h=jn(h,{class:f})),h||b)}const Gt=4;function $o(e){const{dropPosition:t,dropLevelOffset:n,prefixCls:o,indent:a,direction:r="ltr"}=e,l=r==="ltr"?"left":"right",s=r==="ltr"?"right":"left",h={[l]:`${-n*a+Gt}px`,[s]:0};switch(t){case-1:h.top="-3px";break;case 1:h.bottom="-3px";break;default:h.bottom="-3px",h[l]=`${a+Gt}px`;break}return P("div",{style:h,class:`${o}-drop-indicator`},null)}const Do=new Hn("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),To=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Io=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${t.lineWidthBold}px solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),_o=(e,t)=>{const{treeCls:n,treeNodeCls:o,treeNodePadding:a,treeTitleHeight:r}=t,l=(r-t.fontSizeLG)/2,s=t.paddingXS;return{[n]:O(O({},Bn(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,[`&${n}-rtl`]:{[`${n}-switcher`]:{"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(90deg)"}}}}},[`&-focused:not(:hover):not(${n}-active-focused)`]:O({},Pt(t)),[`${n}-list-holder-inner`]:{alignItems:"flex-start"},[`&${n}-block-node`]:{[`${n}-list-holder-inner`]:{alignItems:"stretch",[`${n}-node-content-wrapper`]:{flex:"auto"},[`${o}.dragging`]:{position:"relative","&:after":{position:"absolute",top:0,insetInlineEnd:0,bottom:a,insetInlineStart:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:Do,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none"}}}},[`${o}`]:{display:"flex",alignItems:"flex-start",padding:`0 0 ${a}px 0`,outline:"none","&-rtl":{direction:"rtl"},"&-disabled":{[`${n}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}}},[`&-active ${n}-node-content-wrapper`]:O({},Pt(t)),[`&:not(${o}-disabled).filter-node ${n}-title`]:{color:"inherit",fontWeight:500},"&-draggable":{[`${n}-draggable-icon`]:{width:r,lineHeight:`${r}px`,textAlign:"center",visibility:"visible",opacity:.2,transition:`opacity ${t.motionDurationSlow}`,[`${o}:hover &`]:{opacity:.45}},[`&${o}-disabled`]:{[`${n}-draggable-icon`]:{visibility:"hidden"}}}},[`${n}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:r}},[`${n}-draggable-icon`]:{visibility:"hidden"},[`${n}-switcher`]:O(O({},To(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:r,margin:0,lineHeight:`${r}px`,textAlign:"center",cursor:"pointer",userSelect:"none","&-noop":{cursor:"default"},"&_close":{[`${n}-switcher-icon`]:{svg:{transform:"rotate(-90deg)"}}},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:r/2,bottom:-a,marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:r/2*.8,height:r/2,borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${n}-checkbox`]:{top:"initial",marginInlineEnd:s,marginBlockStart:l},[`${n}-node-content-wrapper, ${n}-checkbox + span`]:{position:"relative",zIndex:"auto",minHeight:r,margin:0,padding:`0 ${t.paddingXS/2}px`,color:"inherit",lineHeight:`${r}px`,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`,"&:hover":{backgroundColor:t.controlItemBgHover},[`&${n}-node-selected`]:{backgroundColor:t.controlItemBgActive},[`${n}-iconEle`]:{display:"inline-block",width:r,height:r,lineHeight:`${r}px`,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}},[`${n}-unselectable ${n}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}-node-content-wrapper`]:O({lineHeight:`${r}px`,userSelect:"none"},Io(e,t)),[`${o}.drop-container`]:{"> [draggable]":{boxShadow:`0 0 0 2px ${t.colorPrimary}`}},"&-show-line":{[`${n}-indent`]:{"&-unit":{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:r/2,bottom:-a,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end":{"&:before":{display:"none"}}}},[`${n}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${o}-leaf-last`]:{[`${n}-switcher`]:{"&-leaf-line":{"&:before":{top:"auto !important",bottom:"auto !important",height:`${r/2}px !important`}}}}})}},Ao=e=>{const{treeCls:t,treeNodeCls:n,treeNodePadding:o}=e;return{[`${t}${t}-directory`]:{[n]:{position:"relative","&:before":{position:"absolute",top:0,insetInlineEnd:0,bottom:o,insetInlineStart:0,transition:`background-color ${e.motionDurationMid}`,content:'""',pointerEvents:"none"},"&:hover":{"&:before":{background:e.controlItemBgHover}},"> *":{zIndex:1},[`${t}-switcher`]:{transition:`color ${e.motionDurationMid}`},[`${t}-node-content-wrapper`]:{borderRadius:0,userSelect:"none","&:hover":{background:"transparent"},[`&${t}-node-selected`]:{color:e.colorTextLightSolid,background:"transparent"}},"&-selected":{"\n            &:hover::before,\n            &::before\n          ":{background:e.colorPrimary},[`${t}-switcher`]:{color:e.colorTextLightSolid},[`${t}-node-content-wrapper`]:{color:e.colorTextLightSolid,background:"transparent"}}}}}},Lo=(e,t)=>{const n=`.${e}`,o=`${n}-treenode`,a=t.paddingXS/2,r=t.controlHeightSM,l=Mn(t,{treeCls:n,treeNodeCls:o,treeNodePadding:a,treeTitleHeight:r});return[_o(e,l),Ao(l)]},jo=Fn("Tree",(e,t)=>{let{prefixCls:n}=t;return[{[e.componentCls]:qn(`${n}-checkbox`,e)},Lo(n,e),Vn(e)]}),fn=()=>{const e=rn();return O(O({},e),{showLine:ft([Boolean,Object]),multiple:se(),autoExpandParent:se(),checkStrictly:se(),checkable:se(),disabled:se(),defaultExpandAll:se(),defaultExpandParent:se(),defaultExpandedKeys:Pe(),expandedKeys:Pe(),checkedKeys:ft([Array,Object]),defaultCheckedKeys:Pe(),selectedKeys:Pe(),defaultSelectedKeys:Pe(),selectable:se(),loadedKeys:Pe(),draggable:se(),showIcon:se(),icon:Re(),switcherIcon:me.any,prefixCls:String,replaceFields:Rn(),blockNode:se(),openAnimation:me.any,onDoubleclick:e.onDblclick,"onUpdate:selectedKeys":Re(),"onUpdate:checkedKeys":Re(),"onUpdate:expandedKeys":Re()})},Ge=Ee({compatConfig:{MODE:3},name:"ATree",inheritAttrs:!1,props:mt(fn(),{checkable:!1,selectable:!0,showIcon:!1,blockNode:!1}),slots:Object,setup(e,t){let{attrs:n,expose:o,emit:a,slots:r}=t;en(!(e.treeData===void 0&&r.default));const{prefixCls:l,direction:s,virtual:h}=Zt("tree",e),[v,b]=jo(l),f=ce();o({treeRef:f,onNodeExpand:function(){var c;(c=f.value)===null||c===void 0||c.onNodeExpand(...arguments)},scrollTo:c=>{var K;(K=f.value)===null||K===void 0||K.scrollTo(c)},selectedKeys:N(()=>{var c;return(c=f.value)===null||c===void 0?void 0:c.selectedKeys}),checkedKeys:N(()=>{var c;return(c=f.value)===null||c===void 0?void 0:c.checkedKeys}),halfCheckedKeys:N(()=>{var c;return(c=f.value)===null||c===void 0?void 0:c.halfCheckedKeys}),loadedKeys:N(()=>{var c;return(c=f.value)===null||c===void 0?void 0:c.loadedKeys}),loadingKeys:N(()=>{var c;return(c=f.value)===null||c===void 0?void 0:c.loadingKeys}),expandedKeys:N(()=>{var c;return(c=f.value)===null||c===void 0?void 0:c.expandedKeys})}),Oe(()=>{Un(e.replaceFields===void 0,"Tree","`replaceFields` is deprecated, please use fieldNames instead")});const x=(c,K)=>{a("update:checkedKeys",c),a("check",c,K)},C=(c,K)=>{a("update:expandedKeys",c),a("expand",c,K)},m=(c,K)=>{a("update:selectedKeys",c),a("select",c,K)};return()=>{const{showIcon:c,showLine:K,switcherIcon:A=r.switcherIcon,icon:_=r.icon,blockNode:D,checkable:T,selectable:W,fieldNames:U=e.replaceFields,motion:j=e.openAnimation,itemHeight:y=28,onDoubleclick:E,onDblclick:F}=e,J=O(O(O({},n),bt(e,["onUpdate:checkedKeys","onUpdate:expandedKeys","onUpdate:selectedKeys","onDoubleclick"])),{showLine:!!K,dropIndicatorRender:$o,fieldNames:U,icon:_,itemHeight:y}),q=r.default?qe(r.default()):void 0;return v(P(Ko,Q(Q({},J),{},{virtual:h.value,motion:j,ref:f,prefixCls:l.value,class:fe({[`${l.value}-icon-hide`]:!c,[`${l.value}-block-node`]:D,[`${l.value}-unselectable`]:!W,[`${l.value}-rtl`]:s.value==="rtl"},n.class,b.value),direction:s.value,checkable:T,selectable:W,switcherIcon:oe=>Po(l.value,A,oe,r.leafIcon,K),onCheck:x,onExpand:C,onSelect:m,onDblclick:F||E,children:q}),O(O({},r),{checkable:()=>P("span",{class:`${l.value}-checkbox-inner`},null)})))}}});var Fo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"};function qt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){Mo(e,a,n[a])})}return e}function Mo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ot=function(t,n){var o=qt({},t,n.attrs);return P(De,qt({},o,{icon:Fo}),null)};Ot.displayName="FolderOpenOutlined";Ot.inheritAttrs=!1;var Bo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"};function Ut(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?Object(arguments[t]):{},o=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(a){return Object.getOwnPropertyDescriptor(n,a).enumerable}))),o.forEach(function(a){Ho(e,a,n[a])})}return e}function Ho(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ct=function(t,n){var o=Ut({},t,n.attrs);return P(De,Ut({},o,{icon:Bo}),null)};Ct.displayName="FolderOutlined";Ct.inheritAttrs=!1;var be;(function(e){e[e.None=0]="None",e[e.Start=1]="Start",e[e.End=2]="End"})(be||(be={}));function Et(e,t,n){function o(a){const r=a[t.key],l=a[t.children];n(r,a)!==!1&&Et(l||[],t,n)}e.forEach(o)}function Ro(e){let{treeData:t,expandedKeys:n,startKey:o,endKey:a,fieldNames:r={title:"title",key:"key",children:"children"}}=e;const l=[];let s=be.None;if(o&&o===a)return[o];if(!o||!a)return[];function h(v){return v===o||v===a}return Et(t,r,v=>{if(s===be.End)return!1;if(h(v)){if(l.push(v),s===be.None)s=be.Start;else if(s===be.Start)return s=be.End,!1}else s===be.Start&&l.push(v);return n.includes(v)}),l}function st(e,t,n){const o=[...t],a=[];return Et(e,n,(r,l)=>{const s=o.indexOf(r);return s!==-1&&(a.push(l),o.splice(s,1)),!!o.length}),a}var zo=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)t.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};const Vo=()=>O(O({},fn()),{expandAction:ft([Boolean,String])});function Go(e){const{isLeaf:t,expanded:n}=e;return t?P(We,null,null):n?P(Ot,null,null):P(Ct,null,null)}const ct=Ee({compatConfig:{MODE:3},name:"ADirectoryTree",inheritAttrs:!1,props:mt(Vo(),{showIcon:!0,expandAction:"click"}),slots:Object,setup(e,t){let{attrs:n,slots:o,emit:a,expose:r}=t;var l;const s=ce(e.treeData||ht(qe((l=o.default)===null||l===void 0?void 0:l.call(o))));ve(()=>e.treeData,()=>{s.value=e.treeData}),Qt(()=>{gt(()=>{var y;e.treeData===void 0&&o.default&&(s.value=ht(qe((y=o.default)===null||y===void 0?void 0:y.call(o))))})});const h=ce(),v=ce(),b=N(()=>Ue(e.fieldNames)),f=ce();r({scrollTo:y=>{var E;(E=f.value)===null||E===void 0||E.scrollTo(y)},selectedKeys:N(()=>{var y;return(y=f.value)===null||y===void 0?void 0:y.selectedKeys}),checkedKeys:N(()=>{var y;return(y=f.value)===null||y===void 0?void 0:y.checkedKeys}),halfCheckedKeys:N(()=>{var y;return(y=f.value)===null||y===void 0?void 0:y.halfCheckedKeys}),loadedKeys:N(()=>{var y;return(y=f.value)===null||y===void 0?void 0:y.loadedKeys}),loadingKeys:N(()=>{var y;return(y=f.value)===null||y===void 0?void 0:y.loadingKeys}),expandedKeys:N(()=>{var y;return(y=f.value)===null||y===void 0?void 0:y.expandedKeys})});const x=()=>{const{keyEntities:y}=sn(s.value,{fieldNames:b.value});let E;return e.defaultExpandAll?E=Object.keys(y):e.defaultExpandParent?E=yt(e.expandedKeys||e.defaultExpandedKeys||[],y):E=e.expandedKeys||e.defaultExpandedKeys,E},C=ce(e.selectedKeys||e.defaultSelectedKeys||[]),m=ce(x());ve(()=>e.selectedKeys,()=>{e.selectedKeys!==void 0&&(C.value=e.selectedKeys)},{immediate:!0}),ve(()=>e.expandedKeys,()=>{e.expandedKeys!==void 0&&(m.value=e.expandedKeys)},{immediate:!0});const K=Wn((y,E)=>{const{isLeaf:F}=E;F||y.shiftKey||y.metaKey||y.ctrlKey||f.value.onNodeExpand(y,E)},200,{leading:!0}),A=(y,E)=>{e.expandedKeys===void 0&&(m.value=y),a("update:expandedKeys",y),a("expand",y,E)},_=(y,E)=>{const{expandAction:F}=e;F==="click"&&K(y,E),a("click",y,E)},D=(y,E)=>{const{expandAction:F}=e;(F==="dblclick"||F==="doubleclick")&&K(y,E),a("doubleclick",y,E),a("dblclick",y,E)},T=(y,E)=>{const{multiple:F}=e,{node:J,nativeEvent:q}=E,oe=J[b.value.key],te=O(O({},E),{selected:!0}),Ke=(q==null?void 0:q.ctrlKey)||(q==null?void 0:q.metaKey),re=q==null?void 0:q.shiftKey;let H;F&&Ke?(H=y,h.value=oe,v.value=H,te.selectedNodes=st(s.value,H,b.value)):F&&re?(H=Array.from(new Set([...v.value||[],...Ro({treeData:s.value,expandedKeys:m.value,startKey:oe,endKey:h.value,fieldNames:b.value})])),te.selectedNodes=st(s.value,H,b.value)):(H=[oe],h.value=oe,v.value=H,te.selectedNodes=st(s.value,H,b.value)),a("update:selectedKeys",H),a("select",H,te),e.selectedKeys===void 0&&(C.value=H)},W=(y,E)=>{a("update:checkedKeys",y),a("check",y,E)},{prefixCls:U,direction:j}=Zt("tree",e);return()=>{const y=fe(`${U.value}-directory`,{[`${U.value}-directory-rtl`]:j.value==="rtl"},n.class),{icon:E=o.icon,blockNode:F=!0}=e,J=zo(e,["icon","blockNode"]);return P(Ge,Q(Q(Q({},n),{},{icon:E||Go,ref:f,blockNode:F},J),{},{prefixCls:U.value,class:y,expandedKeys:m.value,selectedKeys:C.value,onSelect:T,onClick:_,onDblclick:D,onExpand:A,onCheck:W}),o)}}}),ut=vt,ea=O(Ge,{DirectoryTree:ct,TreeNode:ut,install:e=>(e.component(Ge.name,Ge),e.component(ut.name,ut),e.component(ct.name,ct),e)});export{ea as _,dt as a,ge as b,sn as c,xe as d,fo as u};
