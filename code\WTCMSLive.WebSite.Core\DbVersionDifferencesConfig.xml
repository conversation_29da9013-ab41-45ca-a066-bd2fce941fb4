<?xml version="1.0" encoding="utf-8"?>
<DbVersionDifferencesConfig>
    <!--数据库版本号, 若数据库版本号和实际版本号不一致则-->
    <!--<CurrentDbVersion></CurrentDbVersion>-->
    <!-- 版本差异配置 -->
    <DifferencesConfigs>
        <DifferencesConfig>
            <DbVersion>1.5.3.8</DbVersion>
            <TableConfigs>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.Function</FullName>
                    <IgnoreColumns>
                        <ColumnName>SysFunction</ColumnName>
                    </IgnoreColumns>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>FunctionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                            <HighGoDataType>Int</HighGoDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>ModuleID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                            <HighGoDataType>Int</HighGoDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RoleFunctionMapping</FullName>
                    <IgnoreColumns>
                        <ColumnName>sysrolefunctionmapping</ColumnName>
                    </IgnoreColumns>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>FunctionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                            <HighGoDataType>Int</HighGoDataType>《
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
            </TableConfigs>
        </DifferencesConfig>


        <DifferencesConfig>

            <DbVersion>1.5.3.5</DbVersion>

            <IgnoreTables>
                <TableName>OilMonitorConfigViscosity</TableName>
                <TableName>dynamicthreshold</TableName>
                <TableName>learnconfig</TableName>
                <TableName>learncorrectcondition</TableName>
                <TableName>learnworkconditiontrend</TableName>
                <TableName>learnevdatatrend</TableName>
                <TableName>learnevdatatrendmeasevent</TableName>
                <TableName>SoftwareFunction</TableName>
                <TableName>ultrasonicboltparam</TableName>
            </IgnoreTables>

            <TableConfigs>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.UltrasonicChannelConfig</FullName>
                    <TableNames>
                        <TableName>ultrasonicchannelconfig</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>BoltModel</ColumnName>
                    </IgnoreColumns>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>PreloadCalCoeffs</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>String</MySqlDataType>
                            <SqliteDataType>String</SqliteDataType>
                            <HighGoDataType>String</HighGoDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>StandardFilePath</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>String</MySqlDataType>
                            <SqliteDataType>String</SqliteDataType>
                            <HighGoDataType>String</HighGoDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindTurbineModel</FullName>
                    <TableNames>
                        <TableName>wtmodel</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>StructureType</ColumnName>
                            <EntityDataType>EnumTurModelStructureType</EntityDataType>
                            <MySqlDataType>String</MySqlDataType>
                            <SqliteDataType>String</SqliteDataType>
                            <HighGoDataType>String</HighGoDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.Role</FullName>
                    <TableNames>
                        <TableName>sysrole</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>RoleID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>IsSystemRole</ColumnName>
                            <EntityDataType>Bool</EntityDataType>
                            <HighGoDataType>Int</HighGoDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.UserRoleMapping</FullName>
                    <TableNames>
                        <TableName>sysuserrolemapping</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>roleid</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RoleFunctionMapping</FullName>
                    <TableNames>
                        <TableName>sysrolefunctionmapping</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>roleid</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RoleModuleMapping</FullName>
                    <TableNames>
                        <TableName>sysrolemodulemapping</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>roleid</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RotSpeedWaveData</FullName>
                    <TableNames>
                        <TableName>rotspeedwavedataalarm</TableName>
                        <TableName>rotspeedvaluehour</TableName>
                        <TableName>rotspeedvalueday</TableName>
                        <TableName>rotspeedwavedatart</TableName>
                        <TableName>rotspeedwavedatahour</TableName>
                        <TableName>rotspeedwavedataday</TableName>
                        <TableName>rotspeedwavedatahis</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.SVMWaveFormData</FullName>
                    <TableNames>
                        <TableName>svmwfdataalarm</TableName>
                        <TableName>svmwfdatart</TableName>
                        <TableName>svmwfdatahour</TableName>
                        <TableName>svmwfdataday</TableName>
                        <TableName>svmwfdatahis</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasDefinition</FullName>
                    <TableNames>
                        <TableName>measdefinition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>IsAvailable</ColumnName>
                            <EntityDataType>Bool</EntityDataType>
                            <HighGoDataType>Int</HighGoDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasActiveRule</FullName>
                    <TableNames>
                        <TableName>mdfmeasactiverule</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDefinition</FullName>
                    <TableNames>
                        <TableName>mdfwavedefinition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefParamID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDef_SVM</FullName>
                    <TableNames>
                        <TableName>svmwavedefinition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>SampleLength</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Short</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDefParam_Time</FullName>
                    <TableNames>
                        <TableName>wdfparamtime</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefParamID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>LowerLimitFreqency</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>UpperLimitFreqency</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasDefinition_Ex</FullName>
                    <TableNames>
                        <TableName>measdefinition_ex</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.ModbusDef</FullName>
                    <TableNames>
                        <TableName>mdfmodbus</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>SampleTime</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.SVMUnit</FullName>
                    <TableNames>
                        <TableName>svmonitor</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>ModbusAddress</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.SVMRegister</FullName>
                    <TableNames>
                        <TableName>svmregister</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>SVMRegisterAdr</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasTriggerRuleDef</FullName>
                    <TableNames>
                        <TableName>mdftriggerrule</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasTriggeredExecuteMdf</FullName>
                    <TableNames>
                        <TableName>mdftriggeredexecutemdf</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasSolution</FullName>
                    <TableNames>
                        <TableName>meassolution</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasDef_Process</FullName>
                    <TableNames>
                        <TableName>mdfworkcondition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDef_RotSpd</FullName>
                    <TableNames>
                        <TableName>mdfwavedefrotspd</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.EigenValueData_Vib</FullName>
                    <TableNames>
                        <TableName>evdatart</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasEvent_Wave</FullName>
                    <TableNames>
                        <TableName>wfdatameaseventalarm</TableName>
                        <TableName>wfdatameaseventhour</TableName>
                        <TableName>wfdatameaseventday</TableName>
                        <TableName>wfdatameaseventhis</TableName>
                        <TableName>wfdatartmeasevent</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>OutPowerBandCode</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RTWFDataTableUpdateRecord</FullName>
                    <TableNames>
                        <TableName>bufferdataupdaterecordhis</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasEvent_EigenValue</FullName>
                    <TableNames>
                        <TableName>evdatartmeasevent</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>OutPowerBandCode</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDef_VoltageCurrent</FullName>
                    <TableNames>
                        <TableName>mdfvoltagecurrentdefinition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefParamID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.EigenValueData_SVM</FullName>
                    <TableNames>
                        <TableName>svmevdatart</TableName>
                        <TableName>svmwfdatahis</TableName>
                        <TableName>svmevdatatrend</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDefParam_Envlope</FullName>
                    <TableNames>
                        <TableName>wdfparamenvlope</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefParamID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>EnvBandWidth</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>EnvFiterFreq</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.VibWaveFormData</FullName>
                    <TableNames>
                        <TableName>wfdataalarm</TableName>
                        <TableName>wfdatart</TableName>
                        <TableName>wfdatahour</TableName>
                        <TableName>wfdataday</TableName>
                        <TableName>wfdatahis</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>WaveDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WorkingConditionData</FullName>
                    <TableNames>
                        <TableName>workconditionevdatart</TableName>
                        <TableName>workconditionevdatahour</TableName>
                        <TableName>workconditionevdataday</TableName>
                        <TableName>workconditiontrend</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WorkConditionWaveFormData</FullName>
                    <TableNames>
                        <TableName>workconditionwfdataalarm</TableName>
                        <TableName>workconditionwfdatart</TableName>
                        <TableName>workconditionwfdatahour</TableName>
                        <TableName>workconditionwfdataday</TableName>
                        <TableName>workconditionwfdatahis</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindDAU</FullName>
                    <TableNames>
                        <TableName>DAUnit</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>IsAvailable</ColumnName>
                            <EntityDataType>Bool</EntityDataType>
                            <HighGoDataType>Int</HighGoDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
            </TableConfigs>
        </DifferencesConfig>


        <DifferencesConfig>
            <DbVersion>1.5.3.4</DbVersion>
            <IgnoreTables>
                <TableName>alarmstatusrtmodoussensor</TableName>
            </IgnoreTables>
            <TableConfigs>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDef_SVM</FullName>
                    <TableNames>
                        <TableName>svmwavedefinition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>SampleLength</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Float</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.ModbusDef</FullName>
                    <TableNames>
                        <TableName>mdfmodbus</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>MeasDefinitionID</ColumnName>
                            <EntityDataType>String</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                        <ValueConverterConfig>
                            <ColumnName>SampleTime</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Float</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
            </TableConfigs>
        </DifferencesConfig>

        <DifferencesConfig>
            <DbVersion>1.5.3.3</DbVersion>
            <IgnoreTables>
                <TableName>devmeaslocvoltagecurrent</TableName>
                <TableName>dauvoltagecurrentchannel</TableName>
                <TableName>mdfvoltagecurrentdefinition</TableName>
            </IgnoreTables>
            <TableConfigs>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAUChannel_Process</FullName>
                    <TableNames>
                        <TableName>dauprocesschannel</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>Coeff_Senser</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDefinition</FullName>
                    <TableNames>
                        <TableName>mdfwavedefinition</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>SignalType</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
            </TableConfigs>
        </DifferencesConfig>

        <DifferencesConfig>
            <DbVersion>1.5.2.5</DbVersion>
            <TableConfigs>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindPark</FullName>
                    <TableNames>
                        <TableName>DEVWINDPARK</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>Area</ColumnName>
                        <ColumnName>location</ColumnName>
                        <ColumnName>Country</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindTurbineModel</FullName>
                    <TableNames>
                        <TableName>WTMODEL</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>RatedGeneratorSpeed</ColumnName>
                        <ColumnName>GridConnectedGeneratorSpeed</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindTurbine</FullName>
                    <TableNames>
                        <TableName>DEVWINDTURBINE</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>Manufacturer</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDefinition</FullName>
                    <TableNames>
                        <TableName>MDFWaveDefinition</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>SignalType</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasDefinition_Ex</FullName>
                    <TableNames>
                        <TableName>MeasDefinition_Ex</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>ModelType</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindDAU</FullName>
                    <TableNames>
                        <TableName>DAUnit</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>Port</ColumnName>
                        <ColumnName>DeviceID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAUChannelV2</FullName>
                    <TableNames>
                        <TableName>DAUVibChannel</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>PhysicalQuantityType</ColumnName>
                        <ColumnName>RegisterAddress</ColumnName>
                        <ColumnName>S2S_Coeff_a</ColumnName>
                        <ColumnName>S2S_Coeff_b</ColumnName>
                        <ColumnName>Coeff_L0</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAUChannel_Process</FullName>
                    <TableNames>
                        <TableName>DAUProcessChannel</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>Coeff_Senser</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WaveDef_SVM</FullName>
                    <TableNames>
                        <TableName>SVMWaveDefinition</TableName>
                    </TableNames>
                    <ValueConverterConfigs>
                        <ValueConverterConfig>
                            <ColumnName>SampleLength</ColumnName>
                            <EntityDataType>Float</EntityDataType>
                            <MySqlDataType>Int</MySqlDataType>
                        </ValueConverterConfig>
                    </ValueConverterConfigs>
                </TableConfig>
            </TableConfigs>
        </DifferencesConfig>

        <DifferencesConfig>
            <DbVersion>1.5.0.6</DbVersion>
            <TableConfigs>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.MeasLoc_SVM</FullName>
                    <TableNames>
                        <TableName>SVMMEASLOCATION</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>SectionName</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.WindDAU</FullName>
                    <TableNames>
                        <TableName>DAUnit</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                        <ColumnName>DAUType</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAUChannelV2</FullName>
                    <TableNames>
                        <TableName>DAUVibChannel</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAUChannel_RotSpeed</FullName>
                    <TableNames>
                        <TableName>DAURotSpdChannel</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAUChannel_Process</FullName>
                    <TableNames>
                        <TableName>DAUProcessChannel</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.DAURunLog</FullName>
                    <TableNames>
                        <TableName>DAURunLog</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RTAlarmStatus_DAU</FullName>
                    <TableNames>
                        <TableName>AlarmStatusRTDAU</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RTAlarmStatus_Channel</FullName>
                    <TableNames>
                        <TableName>AlarmStatusRTSensor</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.RTAlarmStatus_RSChannel</FullName>
                    <TableNames>
                        <TableName>AlarmStatusRTRSSensor</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.SensorDCData</FullName>
                    <TableNames>
                        <TableName>SensorDCData</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>DauID</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
                <TableConfig>
                    <FullName>CMSFramework.BusinessEntity.AlarmDefThreshold</FullName>
                    <TableNames>
                        <TableName>MDFAlarmDefThresholdGroup</TableName>
                    </TableNames>
                    <IgnoreColumns>
                        <ColumnName>ThresholdValueType</ColumnName>
                    </IgnoreColumns>
                </TableConfig>
            </TableConfigs>
        </DifferencesConfig>
    </DifferencesConfigs>
</DbVersionDifferencesConfig>