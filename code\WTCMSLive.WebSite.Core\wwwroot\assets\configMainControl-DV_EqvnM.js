import{C as o,bz as a,bA as c,bB as n,bC as i,bD as h,bE as y,bF as S,bG as l,bH as C,bI as u,bJ as M,bK as p}from"./index-D9CxWmlM.js";import{a as s}from"./tools-DZBuE28U.js";const g=o("configMainControl",{state:()=>({mCSInfoList:[],mCSDataList:[],workFromMcsOptions:[],stateRegisterTypeOptions:[],registerStorageOptions:[],byteArrayTypeOptions:[]}),actions:{reset(){this.$reset()},async fetchMCSGetMCSInfoList(t){try{const e=await p(t);return this.mCSInfoList=e,e}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddMCS(t){try{return await M(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchEditMCS(t){try{return await u(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchDeleteMCS(t){try{return await C(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchGetMCSGetMCSData(t){try{const e=await l(t);return this.mCSDataList=e,e}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddMCSRegister(t){try{return await S(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchAddEditMCSRegister(t){try{return await y(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchBatchDeleteMCSRegister(t){try{return await h(t)}catch(e){throw console.error("操作失败！",e),e}},async fetchGetWorkFromMcs(t){try{const e=await i(t);let r=s(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.workFromMcsOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetStateRegisterTypes(t){try{const e=await n(t);let r=s(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.stateRegisterTypeOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetRegisterStorages(t){try{const e=await c(t);let r=s(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.registerStorageOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},async fetchGetByteArrayTypes(t){try{const e=await a(t);let r=s(e,{label:"key",value:"value",text:"key"},{nother:!0});return this.byteArrayTypeOptions=r,e}catch(e){throw console.error("操作失败！",e),e}},reset(){this.$reset()}}});export{g as u};
