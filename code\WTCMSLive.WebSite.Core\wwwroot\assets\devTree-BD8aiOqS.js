import{C as a,dl as o}from"./index-D9CxWmlM.js";import{a as d}from"./tools-DZBuE28U.js";const f=a("devTree",{state:()=>({treeDatas:[],devTreeCurrentNode:{},templateDeviceList:[],templateDevicoptions:[]}),actions:{async getDevTreeDatas(e){let i=!1;e?i=e==="template":i=window.localStorage.getItem("templateManagement")==="true";let r=[{key:"0",title:"设备树",type:"root",children:[]}],t=[];i?(r[0].title="设备模板树",t=await o({parkID:"HN999"})):t=await o(),t&&t.length>0&&(r[0].children=t),this.devTreeCurrentNode=r[0],this.treeDatas=r},async getTemplateParkList(){let e=await o({parkID:"HN999"});if(this.templateDeviceList=e,e&&e.length>0&&e[0].children&&e[0].children.length>0){let i=d(e[0].children,{label:"title",value:"id"},{nother:!0});this.templateDevicoptions=i}},setDevTreeCurrentNode(e){this.devTreeCurrentNode=e},getDevTreeCurrentNode(){return this.devTreeCurrentNode},getNodeById(e,i=this.treeDatas){for(const r of i){if(r.key===e)return r;if(r.children&&r.children.length){const t=this.getNodeById(e,r.children);if(t)return t}}},findAncestorsWithNodes(e,i=this.treeDatas){function r(t,s){if(s.push(t),t.key===e)return[...s];if(t.children)for(const l of t.children){const n=r(l,s);if(n)return n}return s.pop(),null}for(const t of i){const s=r(t,[]);if(s)return s}return[]}}});export{f as u};
