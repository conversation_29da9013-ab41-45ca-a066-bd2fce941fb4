import{u as D}from"./devTree-BD8aiOqS.js";import{dg as y,r as u,j as x,a as T,w as R,f as S,d as p,u as C,n as V,o as b,b as r,i as l,g as m,t as d,x as N}from"./index-D9CxWmlM.js";import{_ as M,H as B,a as H}from"./index-C1TGOOwI.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./tools-DZBuE28U.js";import"./initDefaultProps-C2vKchlZ.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./shallowequal-vFdacwF3.js";import"./index-DhZUqjZm.js";import"./ChangeLanguage-7vrykP9f.js";const k={class:"footer"},A={class:"copyright"},I={__name:"MainLayout",setup($){const a=D();y();const s=T(),c=C();u([]);const _=u([]);u([]);const f=u(window.localStorage.getItem("version")||""),h=new Date().getFullYear(),g=x(()=>s.getRoutes().filter(e=>{var t;return(t=e.meta)==null?void 0:t.parent})),v=async e=>{e=="enter"?(await a.getDevTreeDatas("template"),await s.push({name:"model",params:{id:"0"}})):(await a.getDevTreeDatas(),await s.push({path:"/"}))},w=async e=>{var i;const t=g.value.find(n=>n.path===e);if(t&&((i=t.children)!=null&&i.length)){let n=t.children.map(o=>({...o,path:`${e}/${o.path}`}));if((!a.treeDatas||!a.treeDatas.length)&&await a.getDevTreeDatas(),a.treeDatas&&a.treeDatas.length>0){const o=a.treeDatas[0];a.setDevTreeCurrentNode(o),s.push({name:n[0].name,params:{id:o.key}})}}else s.push(e)};return R(()=>c.path,()=>{_.value=c.matched.map(e=>({path:e.path,name:e.meta.title||e.name}))},{immediate:!0}),(e,t)=>{const i=V("router-view"),n=H,o=M;return b(),S(o,null,{default:p(()=>[r(B,{onMenuSelect:w,onChangeView:v}),r(o,{class:"centerContainer"},{default:p(()=>[r(o,null,{default:p(()=>[r(n,{style:{background:"#fff",padding:"24px",margin:0,minHeight:"280px"}},{default:p(()=>[r(i)]),_:1})]),_:1})]),_:1}),l("div",k,[l("div",A,[m(" © "+d(N(h))+" V"+d(f.value)+" ",1),t[0]||(t[0]=l("span",null,"配置网站",-1)),t[1]||(t[1]=m(" All Rights Reserved ",-1))])])]),_:1})}}},U=L(I,[["__scopeId","data-v-ff7b2f1c"]]);export{U as default};
