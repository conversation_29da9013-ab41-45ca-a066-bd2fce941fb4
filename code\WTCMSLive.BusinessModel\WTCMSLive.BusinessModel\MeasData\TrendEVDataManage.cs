﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace WTCMSLive.BusinessModel
{
    /// <summary>
    /// 数据库交互层，趋势数据管理
    /// </summary>
    public class TrendEVDataManage
    {
        //public static List<WorkingConditionData> GetWorkingConditionByEigenId(string _turID, DateTime startDate, DateTime endTime, short ParamType, double MinAverageValue, double MaxAverageValue)
        //{
        //    List<WorkingConditionData> list = new List<WorkingConditionData>();
        //    using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
        //    {
        //        list = ctx.WorkingConditionDatas.Where(item=>item.WindTurbineID==_turID && item.Param_Type_Code==(EnumWorkCondition_ParamType)ParamType
        //            && item.Param_Value>=MinAverageValue && item.Param_Value<=MaxAverageValue).ToList();
        //    }
        //    return list;
        //}

        public static List<WorkingConditionData> GetWorkingConditionByTurId(string _turID, DateTime startDate, DateTime endTime)
        {
            List<WorkingConditionData> list = new List<WorkingConditionData>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.WorkingConditionDatas.Where(item => item.WindTurbineID == _turID 
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime < endTime).ToList();
            }
            return list;
        }

        #region 趋势分析，振动特征值
        public static List<EigenValueData_Vib> GetVibEigenValueTrendByTurId(string _turID, DateTime startDate, DateTime endTime)
        {
            List<EigenValueData_Vib> list = new List<EigenValueData_Vib>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.EVData_Vibs.Where(item => item.WindTurbineID == _turID 
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime <= endTime).ToList();
            }
            return list;
        }

        public static List<EigenValueData_Vib> GetVibEigenValueTrendByMeasLocId(string _turID,string measLocId, DateTime startDate, DateTime endTime)
        {
            List<EigenValueData_Vib> list = new List<EigenValueData_Vib>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.EVData_Vibs.Where(item => item.WindTurbineID==_turID && item.MeasLocationID == measLocId 
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime <= endTime).ToList();
            }
            return list;
        }

        public static List<EigenValueData_Vib> GetVibEigenValueTrendByEigenId(string _turID,string measLocId, string EigenId, DateTime startDate, DateTime endTime)
        {
            List<EigenValueData_Vib> list = new List<EigenValueData_Vib>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.EVData_Vibs.Where(item => item.WindTurbineID==_turID && item.MeasLocationID == measLocId && item.EigenValueID == EigenId
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime <= endTime).ToList();
            }
            return list;
        }
        #endregion

        #region 趋势分析，晃动特征值
        public static List<EigenValueData_SVM> GetSVMEigenValueTrendByTurId(string _turID, DateTime startDate, DateTime endTime)
        {
            List<EigenValueData_SVM> list = new List<EigenValueData_SVM>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.EVData_SVMs.Where(item => item.WindTurbineID == _turID
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime <= endTime).ToList();
            }
            return list;
        }

        public static List<EigenValueData_SVM> GetSVMEigenValueTrendByMeasLocId(string _turID, string measLoc, DateTime startDate, DateTime endTime)
        {
            List<EigenValueData_SVM> list = new List<EigenValueData_SVM>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.EVData_SVMs.Where(item => item.WindTurbineID == _turID && item.MeasLocationID == measLoc
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime <= endTime).ToList();
            }
            return list;
        }

        public static List<EigenValueData_SVM> GetSVMEigenValueTrendByEigenId(string _turID, string measLocId, string EigenId, DateTime startDate, DateTime endTime)
        {
            List<EigenValueData_SVM> list = new List<EigenValueData_SVM>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.EVData_SVMs.Where(item => item.WindTurbineID==_turID && item.MeasLocationID == measLocId && item.EigenValueID == EigenId 
                    && item.AcquisitionTime >= startDate && item.AcquisitionTime <= endTime).ToList();
            }
            return list;
        }

        #endregion
        /// <summary>
        /// 获取传感器直流分量趋势
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="channel">通道号</param>
        /// <param name="_beginTime">开始时间</param>
        /// <param name="_endTime">结束时间</param>
        /// <returns></returns>
        public static List<SensorDCData> GetSensorDCListByChannel(string turbineID, int channel, DateTime _beginTime, DateTime _endTime)
        {
            List<SensorDCData> list = new List<SensorDCData>();
            using (CMSFramework.EF.ValueDataContext_His ctx = new CMSFramework.EF.ValueDataContext_His(ConfigInfo.DBConnName))
            {
                list = ctx.SensorDCDatas.Where(item => item.WindTurbineID == turbineID && item.ChannelNumber == channel
                    && item.DCAcquisitionTime >= _beginTime && item.DCAcquisitionTime <= _endTime).ToList();
            }
            return list;
        }
    }
}
