# 数据中心服务 - 简单版本使用说明

## 概述

这是一个简化的数据中心客户端服务，用于从数据中心获取数据。服务直接返回JSON字符串，结构简单，易于使用。

## 核心特点

- ✅ **结构简单**：只有接口和实现类，无复杂DTO
- ✅ **返回JSON**：所有方法直接返回JSON字符串
- ✅ **通用性强**：支持GET和POST请求
- ✅ **易于集成**：依赖注入即可使用

## 文件结构

```
Services/
├── IDataCenterService.cs          # 服务接口
├── DataCenterService.cs           # 服务实现
└── DataCenterServiceExample.cs    # 使用示例
```

## 配置

### appsettings.json
```json
{
  "DataCenterUrl": "http://192.168.90.208:8010/"
}
```

### 服务注册 (Program.cs)
```csharp
// 注册数据中心服务
builder.Services.AddHttpClient<IDataCenterService, DataCenterService>();
```

## 服务接口

```csharp
public interface IDataCenterService
{
    // 获取集团公司代码数据
    Task<string> GetGroupCompanyCodeAsync();
    
    // 通用GET请求
    Task<string> GetAsync(string endpoint, Dictionary<string, string>? parameters = null);
    
    // 通用POST请求
    Task<string> PostAsync(string endpoint, string? jsonData = null);
}
```

## 使用示例

### 1. 在控制器中使用

```csharp
[ApiController]
[Route("api/[controller]")]
public class MyController : ControllerBase
{
    private readonly IDataCenterService _dataCenterService;

    public MyController(IDataCenterService dataCenterService)
    {
        _dataCenterService = dataCenterService;
    }

    [HttpGet("companies")]
    public async Task<IActionResult> GetCompanies()
    {
        var jsonResult = await _dataCenterService.GetGroupCompanyCodeAsync();
        
        // 直接返回JSON字符串
        return Content(jsonResult, "application/json");
    }
}
```

### 2. 在服务中使用

```csharp
public class MyBusinessService
{
    private readonly IDataCenterService _dataCenterService;

    public MyBusinessService(IDataCenterService dataCenterService)
    {
        _dataCenterService = dataCenterService;
    }

    public async Task<string> GetCompanyData()
    {
        // 获取集团公司代码数据
        var jsonResult = await _dataCenterService.GetGroupCompanyCodeAsync();
        
        // 可以直接返回JSON
        return jsonResult;
        
        // 或者解析后处理
        // var companies = JsonConvert.DeserializeObject<List<Company>>(jsonResult);
        // // 处理业务逻辑...
        // return JsonConvert.SerializeObject(processedData);
    }
}
```

### 3. 调用其他API

```csharp
// GET请求示例
var parameters = new Dictionary<string, string>
{
    { "pageSize", "10" },
    { "pageIndex", "1" }
};
var result = await _dataCenterService.GetAsync("OtherEndpoint", parameters);

// POST请求示例
var postData = JsonConvert.SerializeObject(new { Name = "Test", Value = 123 });
var result = await _dataCenterService.PostAsync("OtherEndpoint", postData);
```

## 数据中心API端点

- `GET /GroupCompanyCode` - 获取集团公司代码列表

## JSON数据处理

### 解析JSON为对象
```csharp
// 定义数据模型
public class GroupCompanyInfo
{
    public string Code { get; set; }
    public string Name { get; set; }
    public string ShortName { get; set; }
    public string Type { get; set; }
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime? UpdateTime { get; set; }
    public string? Remark { get; set; }
}

// 解析JSON
var jsonResult = await _dataCenterService.GetGroupCompanyCodeAsync();
var companies = JsonConvert.DeserializeObject<List<GroupCompanyInfo>>(jsonResult);
```

### 生成JSON数据
```csharp
var data = new { Name = "测试", Code = "TEST001" };
var jsonData = JsonConvert.SerializeObject(data);
var result = await _dataCenterService.PostAsync("endpoint", jsonData);
```

## 错误处理

服务内置了错误处理，失败时返回错误JSON：
```json
{
  "error": "错误描述信息"
}
```

可以通过检查返回的JSON来判断是否有错误：
```csharp
var result = await _dataCenterService.GetGroupCompanyCodeAsync();
if (result.Contains("\"error\""))
{
    // 处理错误情况
    var errorObj = JsonConvert.DeserializeObject<dynamic>(result);
    var errorMessage = errorObj.error;
}
```

## 日志记录

服务会自动记录以下日志：
- 请求开始和结束
- 成功和失败的详细信息
- 异常信息

## 扩展使用

### 添加新的专用方法
在接口和实现中添加新方法：
```csharp
// 接口中添加
Task<string> GetCustomDataAsync(string parameter);

// 实现中添加
public async Task<string> GetCustomDataAsync(string parameter)
{
    var parameters = new Dictionary<string, string> { { "param", parameter } };
    return await GetAsync("CustomEndpoint", parameters);
}
```

### 添加缓存
可以在业务层添加缓存机制：
```csharp
public class CachedDataService
{
    private readonly IDataCenterService _dataCenterService;
    private readonly IMemoryCache _cache;

    public async Task<string> GetCachedCompanyData()
    {
        const string cacheKey = "company_data";
        
        if (!_cache.TryGetValue(cacheKey, out string cachedData))
        {
            cachedData = await _dataCenterService.GetGroupCompanyCodeAsync();
            _cache.Set(cacheKey, cachedData, TimeSpan.FromMinutes(10));
        }
        
        return cachedData;
    }
}
```

## 总结

这个简化版本的数据中心服务具有以下优势：

1. **结构简单**：只有必要的接口和实现
2. **易于使用**：直接返回JSON，无需复杂的类型转换
3. **灵活性高**：可以调用任意数据中心API
4. **易于扩展**：可以根据需要添加新方法
5. **错误处理**：内置基本的错误处理机制

您可以根据实际需求在业务层进行JSON解析和数据处理，保持服务层的简洁性。
