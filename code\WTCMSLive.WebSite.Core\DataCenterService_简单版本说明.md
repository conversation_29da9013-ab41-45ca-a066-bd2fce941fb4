# 数据中心服务 - 简单版本使用说明

## 概述

这是一个简化的数据中心客户端服务，用于从数据中心获取数据。服务直接返回JSON字符串，结构简单，易于使用。

## 核心特点

- ✅ **结构简单**：只有接口和实现类，无复杂DTO
- ✅ **返回JSON**：所有方法直接返回JSON字符串
- ✅ **通用性强**：支持GET和POST请求
- ✅ **易于集成**：依赖注入即可使用

## 文件结构

```
Services/
├── IDataCenterService.cs          # 服务接口
├── DataCenterService.cs           # 服务实现
└── DataCenterServiceExample.cs    # 使用示例
```

## 配置

### appsettings.json
```json
{
  "DataCenter": {
    "Url": "http://192.168.90.208:8010/",
    "TimeoutSeconds": 30
  }
}
```

**配置说明：**
- `Url`: 数据中心的基础URL
- `TimeoutSeconds`: 默认超时时间（秒），可以在方法调用时覆盖

### 服务注册 (Program.cs)
```csharp
// 注册数据中心服务
builder.Services.AddHttpClient<IDataCenterService, DataCenterService>();
```

## 服务接口

```csharp
public interface IDataCenterService
{
    // 获取集团公司代码数据（返回字典：集团名称 -> 代码）
    Task<Dictionary<string, string>> GetGroupCompanyCodeAsync(int? timeoutSeconds = null);

    // 通用GET请求（返回JSON字符串）
    Task<string> GetAsync(string endpoint, Dictionary<string, string>? parameters = null, int? timeoutSeconds = null);

    // 通用POST请求（返回JSON字符串）
    Task<string> PostAsync(string endpoint, string? jsonData = null, int? timeoutSeconds = null);
}
```

**超时参数说明：**
- `timeoutSeconds`: 可选参数，指定此次请求的超时时间（秒）
- 如果不传递此参数，将使用配置文件中的默认超时时间
- 超时时会返回包含错误信息的JSON字符串

## 使用示例

### 1. 在控制器中使用

```csharp
[ApiController]
[Route("api/[controller]")]
public class MyController : ControllerBase
{
    private readonly IDataCenterService _dataCenterService;

    public MyController(IDataCenterService dataCenterService)
    {
        _dataCenterService = dataCenterService;
    }

    [HttpGet("companies")]
    public async Task<IActionResult> GetCompanies()
    {
        // 使用默认超时时间，返回字典
        var companies = await _dataCenterService.GetGroupCompanyCodeAsync();

        // 返回字典数据
        return Ok(companies);
    }

    [HttpGet("companies-fast")]
    public async Task<IActionResult> GetCompaniesFast()
    {
        // 使用自定义超时时间（10秒）
        var companies = await _dataCenterService.GetGroupCompanyCodeAsync(10);

        return Ok(companies);
    }

    [HttpGet("company-names")]
    public async Task<IActionResult> GetCompanyNames()
    {
        // 获取所有集团公司名称
        var companies = await _dataCenterService.GetGroupCompanyCodeAsync();
        var names = companies.Keys.ToList();

        return Ok(names);
    }
}
```

### 2. 在服务中使用

```csharp
public class MyBusinessService
{
    private readonly IDataCenterService _dataCenterService;

    public MyBusinessService(IDataCenterService dataCenterService)
    {
        _dataCenterService = dataCenterService;
    }

    public async Task<Dictionary<string, string>> GetCompanyData()
    {
        // 获取集团公司代码数据（返回字典）
        var companies = await _dataCenterService.GetGroupCompanyCodeAsync();

        // 可以直接返回字典
        return companies;
    }

    public async Task<string?> GetCompanyCodeByName(string companyName)
    {
        // 根据集团名称获取代码
        var companies = await _dataCenterService.GetGroupCompanyCodeAsync();

        return companies.TryGetValue(companyName, out string? code) ? code : null;
    }
}
```

### 3. 调用其他API

```csharp
// GET请求示例（使用默认超时）
var parameters = new Dictionary<string, string>
{
    { "pageSize", "10" },
    { "pageIndex", "1" }
};
var result = await _dataCenterService.GetAsync("OtherEndpoint", parameters);

// GET请求示例（自定义超时15秒）
var result = await _dataCenterService.GetAsync("OtherEndpoint", parameters, 15);

// POST请求示例（使用默认超时）
var postData = JsonConvert.SerializeObject(new { Name = "Test", Value = 123 });
var result = await _dataCenterService.PostAsync("OtherEndpoint", postData);

// POST请求示例（自定义超时45秒）
var result = await _dataCenterService.PostAsync("OtherEndpoint", postData, 45);
```

## 数据中心API端点

- `GET /GroupCompanyCode` - 获取集团公司代码列表

### API响应格式

数据中心返回的JSON格式：
```json
{
  "code": "00000",
  "message": "获取集团公司信息成功",
  "data": [
    {"name": "国华", "code": "GH"},
    {"name": "华电", "code": "HD"}
  ]
}
```

服务会自动解析此格式并返回 `Dictionary<string, string>`：
```csharp
{
  "国华": "GH",
  "华电": "HD"
}
```

## 数据处理示例

### 使用字典数据
```csharp
// 获取集团公司数据
var companies = await _dataCenterService.GetGroupCompanyCodeAsync();

// 遍历所有集团公司
foreach (var company in companies)
{
    Console.WriteLine($"集团名称: {company.Key}, 代码: {company.Value}");
}

// 根据名称查找代码
if (companies.TryGetValue("国华", out string? code))
{
    Console.WriteLine($"国华的代码是: {code}");
}

// 获取所有集团名称
var companyNames = companies.Keys.ToList();

// 获取所有代码
var companyCodes = companies.Values.ToList();

// 检查是否包含某个集团
bool hasGuohua = companies.ContainsKey("国华");
```

### 生成JSON数据
```csharp
var data = new { Name = "测试", Code = "TEST001" };
var jsonData = JsonConvert.SerializeObject(data);
var result = await _dataCenterService.PostAsync("endpoint", jsonData);
```

## 错误处理

服务内置了错误处理，失败时返回错误JSON：
```json
{
  "error": "错误描述信息"
}
```

可以通过检查返回的JSON来判断是否有错误：
```csharp
var result = await _dataCenterService.GetGroupCompanyCodeAsync();
if (result.Contains("\"error\""))
{
    // 处理错误情况
    var errorObj = JsonConvert.DeserializeObject<dynamic>(result);
    var errorMessage = errorObj.error;
}
```

## 日志记录

服务会自动记录以下日志：
- 请求开始和结束
- 成功和失败的详细信息
- 异常信息

## 扩展使用

### 添加新的专用方法
在接口和实现中添加新方法：
```csharp
// 接口中添加
Task<string> GetCustomDataAsync(string parameter);

// 实现中添加
public async Task<string> GetCustomDataAsync(string parameter)
{
    var parameters = new Dictionary<string, string> { { "param", parameter } };
    return await GetAsync("CustomEndpoint", parameters);
}
```

### 添加缓存
可以在业务层添加缓存机制：
```csharp
public class CachedDataService
{
    private readonly IDataCenterService _dataCenterService;
    private readonly IMemoryCache _cache;

    public async Task<string> GetCachedCompanyData()
    {
        const string cacheKey = "company_data";
        
        if (!_cache.TryGetValue(cacheKey, out string cachedData))
        {
            cachedData = await _dataCenterService.GetGroupCompanyCodeAsync();
            _cache.Set(cacheKey, cachedData, TimeSpan.FromMinutes(10));
        }
        
        return cachedData;
    }
}
```

## 总结

这个简化版本的数据中心服务具有以下优势：

1. **结构简单**：只有必要的接口和实现
2. **易于使用**：直接返回JSON，无需复杂的类型转换
3. **灵活性高**：可以调用任意数据中心API
4. **易于扩展**：可以根据需要添加新方法
5. **错误处理**：内置基本的错误处理机制

您可以根据实际需求在业务层进行JSON解析和数据处理，保持服务层的简洁性。
