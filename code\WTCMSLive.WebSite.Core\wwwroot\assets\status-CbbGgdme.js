import{u as U,c as V,W as Y}from"./table-C_s53ALS.js";import{N as A}from"./noPermission-BBS3T4wn.js";import{r as c,u as F,y as O,w as R,f as v,x as S,d as l,z as N,o as n,i as e,b as _,c as f,F as h,e as C,g as o,t as u,s as W,A as j}from"./index-D9CxWmlM.js";import{S as G,g as i}from"./tools-DZBuE28U.js";import{u as H}from"./statusMonitor-CqkPK8Q-.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as q,a as K}from"./index-pF9HjW4G.js";import"./index-DyCH3qIX.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./index-DMbgFMzZ.js";import"./shallowequal-vFdacwF3.js";import"./index-BS38043a.js";import"./ActionButton-BRQ4acFZ.js";import"./index-RBozNpIx.js";const Q={class:"border"},X={class:"clearfix totalList"},Z={class:"totalNumber pullLeft"},tt={class:"pullLeft clearfix"},et={class:"parkNumber pullLeft redBg"},at={class:"parkNumber pullLeft yellowBg"},st={class:"parkNumber pullLeft greenBg"},lt=["src","title","alt"],L="YYYY-MM-DD",nt={__name:"status",setup(ot){const P=U(),T=H();let g=(window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[]).includes("21");const y=F(),d=c(!1),x=(t={})=>[{label:"名称",value:t.windParkName},{label:"编号",value:t.windParkCode},{label:"投运日期",value:t.operationalDate?N(t.operationalDate).format(L):""},{label:"设备总数",value:t.windTurbineList?t.windTurbineList.length:0},{label:"联系人",value:t.contactMan},{label:"联系人电话",value:t.contactTel},{label:"区域",value:`${t.country} - ${t.area}`},{label:"地址",value:t.address},{label:"经纬度",value:t.location},{label:"邮编",value:t.postCode},{label:"场站概况",value:t.description}],w=c([x({})]),a=c({}),m=c(y.params.id),D=O({tableData:[]}),k=async()=>{if(m.value){d.value=!0;const t=await P.fetchParkInfo({windParkID:m.value});w.value=x(t),d.value=!1}},B=t=>`/componentStatus/${t.componentName}_${i(t.status).text}.png`,$=[{title:"设备名称",dataIndex:"windTurbineName",canEdit:!0,align:"center"},{title:"设备状态",dataIndex:"turbineStatus",align:"center",headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:t})=>j("span",{style:{color:i(t.turbineStatus).color}},i(t.turbineStatus).text)},{title:"部件状态",dataIndex:"partStatus",canEdit:!0,align:"center",otherColumn:!0},{title:"设备状态更新时间",dataIndex:"turbineStatusUpdateTime",canEdit:!0,align:"center",headerOperations:{sorter:!0,date:!0},customRender:({record:t})=>t.turbineStatusUpdateTime?N(t.turbineStatusUpdateTime).format(L):""}],z=async t=>{a.value=await T.fetchGetDevStatusCount({WindParkID:m.value}),D.tableData=a.value.turbineDetailList||[]};return R(()=>y.params.id,t=>{t&&g&&(m.value=t,z(),k())},{immediate:!0}),(t,s)=>{const M=K,E=q,I=G;return S(g)?(n(),v(I,{key:0,spinning:d.value,size:"large"},{default:l(()=>[e("div",null,[_(V,{tableTitle:"厂站信息",defaultCollapse:!0,batchApply:!1},{content:l(()=>[e("div",Q,[_(E,{column:5,size:"small"},{default:l(()=>[(n(!0),f(h,null,C(w.value,p=>(n(),v(M,{label:p.label},{default:l(()=>[o(u(p.value),1)]),_:2},1032,["label"]))),256))]),_:1})])]),_:1}),e("div",X,[e("p",Z,[s[0]||(s[0]=o(" 设备总数(台) ",-1)),e("span",null,u(a.value.turbineStatusSummary?a.value.turbineStatusSummary.totalCount:0),1)]),e("ul",tt,[e("li",et,[s[1]||(s[1]=o("危险",-1)),e("span",null,u(a.value.turbineStatusSummary?a.value.turbineStatusSummary.dangerCount:0),1)]),e("li",at,[s[2]||(s[2]=o("注意",-1)),e("span",null,u(a.value.turbineStatusSummary?a.value.turbineStatusSummary.warningCount:0),1)]),e("li",st,[s[3]||(s[3]=o("正常",-1)),e("span",null,u(a.value.turbineStatusSummary?a.value.turbineStatusSummary.normalCount:0),1)])])]),e("div",null,[_(Y,{ref:"table",size:"default","table-key":"0","table-title":"设备状态监测",noheader:!0,"table-columns":$,"table-operate":[],"record-key":"name","table-datas":D.tableData,noBatchApply:!0,defaultPageSize:50},{otherColumn:l(({column:p,record:b,text:ut})=>[b.componentList&&b.componentList.length?(n(!0),f(h,{key:0},C(b.componentList,r=>(n(),f("img",{class:"componentImg",src:B(r),title:`${r.componentName}${S(i)(r.status).text}`,alt:`${r.componentName}${S(i)(r.status).text}`},null,8,lt))),256)):W("",!0)]),_:1},8,["table-datas"])])])]),_:1},8,["spinning"])):(n(),v(A,{key:1},{default:l(()=>s[4]||(s[4]=[o(" 无权限 ",-1)])),_:1,__:[4]}))}}},Lt=J(nt,[["__scopeId","data-v-eeccf277"]]);export{Lt as default};
