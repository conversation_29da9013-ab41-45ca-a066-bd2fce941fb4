{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Information"
    }
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://*:803"
      }
    }
  },
  "AllowedHosts": "*",
  "ProxyTo": "",
  "JwtSettings": {
    "SecretKey": "aB3x9Lq2mN7vP4zR8tY1uK5wQ6eJ0cF!@#XyZ%tH8*gD2$sF7&vL9qW",
    "Issuer": "WTCMSLive",
    "Audience": "WTCMSLiveUsers",
    "ExpirationInMinutes": 60
  },
  "DbType": {
    "checked": "mysql",
    "example": "[sqlite, mysql, highgo, seabox]"
  },
  "ConnectionStrings": {
    //"Context": "Server=**************;User Id=WTMANA;Password=*******$#@!;Database=wtlivedb;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",
    //"ContextRt": "Server=**************;User Id=WTMANA;Password=*******$#@!;Database=wtlivedb;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",
    //"ContextTrend": "Server=**************;User Id=WTMANA;Password=*******$#@!;Database=wtlivedbtrend;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",

    "Context": "Server=*************;User Id=root;Password=root;Database=wtlivedb20;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",
    "ContextRt": "Server=*************;User Id=root;Password=root;Database=wtlivedb20;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",
    "ContextTrend": "Server=*************;User Id=root;Password=root;Database=wtlivedbtrend20;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0"


    //"Context": "Server=192.168.20.85;User Id=root;Password=**********;Database=wtlivedb;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",
    //"ContextRt": "Server=192.168.20.85;User Id=root;Password=**********;Database=wtlivedb;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0",
    //"ContextTrend": "Server=192.168.20.85;User Id=root;Password=**********;Database=wtlivedbtrend;Persist Security Info=False;SslMode=None;charset=utf8;default command timeout=0"
  },
  "LogoUrl": "/Images/logo01.svg",
  "ViewModel": "CMS",
  "showComplexEigenValue": "False",
  "DaqServerType": "DAU",
  "serverXMConfigPath": "D:\\windCMS",
  "MetricsCollection": {
    "Enable": true,
    "DataStoragePath": "MetricsData",
    "RetentionDays": 30,
    "FileFormat": "csv"
  },
  "DataCenter": {
    "Url": "http://192.168.90.208:8011/",
    "TimeoutSeconds": 30
  }
}