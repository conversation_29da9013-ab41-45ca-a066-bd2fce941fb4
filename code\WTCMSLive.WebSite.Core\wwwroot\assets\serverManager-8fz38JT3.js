import{C as s,dL as a,dM as n,dN as c,dO as h,dP as i,dQ as l,dR as p,dS as u,dT as y,dU as S,dV as w,dW as f}from"./index-D9CxWmlM.js";import{a as o}from"./tools-DZBuE28U.js";const g=s("serverManager",{state:()=>({logTypes:[],socketDatas:{}}),actions:{reset(){this.$reset()},async fetchGetServerPerformance(r){try{return await f(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchServerPerformanceDownload(r){try{return await w(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchHomeSystemLog(r){try{return await S(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchGetSystemLogType(r){try{const e=await y(r);let t=o(e,{label:"value",value:"key"},{nother:!0});return this.logTypes=t,t}catch(e){throw console.error("获取失败:",e),e}},async fetchGetServiceStatuses(r){try{return await u(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchStartService(r){try{return await p(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchStopService(r){try{return await l(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchRestartService(r){try{return await i(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchGetLogFiles(r){try{const e=await h(r);return e&&e.code==0?[]:o(e,{label:"fileName",value:"fileName"},{nother:!0})}catch(e){throw console.error("获取失败:",e),e}},async fetchGetLogContent(r){try{return await c(r)}catch(e){throw console.error("获取失败:",e),e}},async fetchStartRealTimeLog(r,e){try{return await n(r,e)}catch(t){throw console.error("获取失败:",t),t}},async fetchStopRealTimeLog(r){try{return await a(r)}catch(e){throw console.error("获取失败:",e),e}}}});export{g as u};
