namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// 数据中心响应数据传输对象
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class DataCenterResponseDTO<T>
    {
        /// <summary>
        /// 响应代码（1：成功，0：失败）
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 响应数据
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public long Timestamp { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess => Code == 1;
    }
}
