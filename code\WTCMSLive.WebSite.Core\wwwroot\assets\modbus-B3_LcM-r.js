import{u as ae,W as P}from"./table-C_s53ALS.js";import{O as se}from"./index-DyCH3qIX.js";/* empty css                                                              */import{S as oe,c as g}from"./tools-DZBuE28U.js";import{r as p,j as le,u as ie,y as ue,w as ne,f as B,d as E,o as w,c as G,b as M,m as n,aP as U}from"./index-D9CxWmlM.js";import{u as re}from"./configModbus--Xx2TLry.js";import{u as ce}from"./collectionUnitConfig-tdEU9a2d.js";import{u as de}from"./devTree-BD8aiOqS.js";import{M as be}from"./index-DhZUqjZm.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";const pe={key:1},qe={__name:"modbus",setup(me){const u=re(),C=ce(),$=de(),j=ae();let k=[{label:"串口服务器",value:"0",text:"串口服务器"},{label:"串口直连",value:"1",text:"串口直连"}];const L=(e={isForm:!1})=>[{title:"ModbusID",dataIndex:"modbusID",columnWidth:100,validateRules:g({type:"number",title:"设备ID",required:!0})},{title:"Modbus名称",dataIndex:"modbusDeviceName",isrequired:!0},{title:"Modbus类型",dataIndex:"modbusType",columnWidth:150,isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const o=u.modbusDevTypes.find(l=>l.value==t.modbusType);return o?o.label:a}}},{title:"采集单元",dataIndex:"dauID",isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const o=C.dAUOptionList.find(l=>l.value==t.dauID);return o?o.label:a}}},{title:"连接方式",dataIndex:"comType",isrequired:!0,inputType:"radio",hasChangeEvent:!0,selectOptions:k,headerOperations:{filters:k},...e&&e.isForm?{}:{customRender:({text:a,record:t})=>{const o=k.find(l=>l.value==t.comType);return o?o.label:a}}}],O=(e={isForm:!1})=>[{title:"设备名称",dataIndex:"modbusDeviceID",isrequired:!0,inputType:"select",selectOptions:[],hasChangeEvent:!0,...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.modbusDeviceName||""}},{title:"通道编号",dataIndex:"channelNumber",validateRules:g({type:"number",title:"通道编号",required:!0})},{title:"测量位置",dataIndex:"measLocationID",isrequired:!0,inputType:"select",selectOptions:[],...e&&e.isForm?{}:{customRender:({text:a,record:t})=>t.measLocationName}}],c=()=>[{title:"串口服务器IP",dataIndex:"comIP",validateRules:g({type:"ip",title:"串口服务器IP",required:!0})},{title:"串口服务器端口",dataIndex:"comPort",isrequired:!0,columnWidth:100},{title:"串口名称",dataIndex:"portName",isrequired:!0},{title:"波特率",dataIndex:"baudRate",columnWidth:100,validateRules:g({type:"number",title:"波特率",required:!0})},{title:"数据位",dataIndex:"dataBit",isrequired:!0,columnWidth:100},{title:"校验位",dataIndex:"parity",isrequired:!0,columnWidth:100,inputType:"radio",selectOptions:[{label:"无",value:0},{label:"奇",value:1},{label:"偶",value:2}]},{title:"停止位",dataIndex:"stopBit",isrequired:!0,columnWidth:100}],f=p(!1),v=p(""),m=p(""),z=le(()=>m.value==="batchAdd"?"1200px":"600px"),d=p(""),x=p(!1),_=ie(),I=p({}),r=p([]),i=p(_.params.id),A=p(),s=ue({tableColumns:L(),tableColumns2:O(),tableData1:[],tableData2:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{}}),V=async e=>{A.value&&await j.fetchDevTreedDevicelist({windParkID:A.value,useTobath:!0})},H=()=>{let e=$.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(A.value=e[e.length-2].id)},J=async()=>{await u.fetchGetModbusDevType()},Q=async()=>{await C.fetchGetDAUList({WindTurbineID:i.value,WindParkId:A.value})},X=async e=>{i.value&&await u.fetchGetModbusMeasLocByDeviceID({turbineID:i.value,modbusDeviceID:e})},T=async()=>{f.value=!0;let e=await u.fetchGetModbusDeviceList({turbineID:i.value});e&&(f.value=!1,s.tableData1=e)},R=async()=>{f.value=!0;let e=await u.fetchGetModbusChannelList({turbineID:i.value});e&&(f.value=!1,s.tableData2=e)};ne(()=>_.params.id,async e=>{i.value=e,e&&(H(),await V(),J(),Q(),T(),R())},{immediate:!0});const F=e=>{const{title:a,operateType:t,tableKey:o}=e;switch(m.value=t,d.value=o,o){case"1":v.value="增加Modbus设备";break;case"2":v.value="增加Modbus通道";break}S(),W()},S=()=>{switch(d.value){case"1":let e=[...L({isForm:!0}),c()[0],c()[1]];e[2].selectOptions=u.modbusDevTypes,e[3].selectOptions=[{label:"无",value:-1},...C.dAUOptionList],m.value==="edit"&&(e[3].disabled=!0),I.value={comType:"0"},r.value=e;break;case"2":let a=[...O({isForm:!0})];a[0].selectOptions=u.modbusDeviceOptions,r.value=a;break}},q=e=>{const{tableKey:a,rowData:t,operateType:o}=e;switch(m.value=o,d.value=a,S(),a){case"1":v.value="编辑Modbus设备",N({value:t.comType,dataIndex:"comType"});break;case"2":v.value="编辑Modbus通道";break}I.value={...t,dauID:t.dauID&&t.dauID!==""?t.dauID:-1},W()},Y=async e=>{switch(m.value){case"add":await Z(e);break;case"edit":await ee(e);break}},Z=async e=>{if(d.value==="1"){let a={...e,turbineID:i.value,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await u.fetchAddModbusDevice({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code==1?(T(),s.bathApplyResponse1=t.batchResults||{},D(),n.success("提交成功")):n.error("提交失败:"+t.msg)}else if(d.value==="2"){let a=[{windTurbineID:i.value,channelNumber:e.channelNumber,measLocationID:e.measLocationID,modbusDeviceID:e.modbusDeviceID,description:""}];const t=await u.fetchAddModbusChannel({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code==1?(n.success("提交成功"),R(),s.bathApplyResponse2=t.batchResults||{},D()):n.error("提交失败:"+t.msg)}},ee=async e=>{if(d.value==="1"){let a={...I.value,...e,modbusID:e.modbusID,dauID:e.dauID&&e.dauID!==-1?e.dauID:""};const t=await u.fetchEditModbusDevice({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code===1?(T(),s.bathApplyResponse1=t.batchResults||{},D(),n.success("提交成功")):n.error("提交失败:"+t.msg)}else if(d.value==="2"){let a={windTurbineID:i.value,description:"",...e};const t=await u.fetchEditModbusChannel({sourceData:a,targetTurbineIds:s.batchApplyData});t&&t.code===1?(R(),s.bathApplyResponse2=t.batchResults||{},D(),n.success("提交成功")):n.error("提交失败:"+t.msg)}},K=async e=>{const{tableKey:a,selectedkeys:t,record:o}=e;if(d.value=a,a==="1"){let l=[];if(o)l.push({modbusDeviceID:o.modbusDeviceID,turbineID:i.value,modbusID:o.modbusID});else for(let h=0;h<t.length;h++){let y=t[h].split("&&");l.push({turbineID:i.value,modbusDeviceID:y[0],modbusID:y[1]})}const b=await u.fetchBatchDeleteModbusDevice({sourceData:l,targetTurbineIds:s.batchApplyData});b&&b.code==1?(T(),s.bathApplyResponse1=b.batchResults||{},D(),n.success("删除成功")):n.error("删除失败:"+b.msg)}else if(a==="2"){let l=[];if(o)l.push({windTurbineID:i.value,modbusDeviceID:o.modbusDeviceID,channelNumber:o.channelNumber,measLocationID:o.measLocationID});else for(let h=0;h<t.length;h++){let y=t[h].split("&&");l.push({windTurbineID:i.value,modbusDeviceID:y[1],channelNumber:y[0],measLocationID:y[2]})}const b=await u.fetchBatchDeleteModbusChannel({sourceData:l,targetTurbineIds:s.batchApplyData});b&&b.code==1?(R(),s.bathApplyResponse2=b.batchResults||{},n.success("删除成功"),D()):n.error("删除失败:"+b.msg)}},N=async e=>{if(e.dataIndex&&e.value&&e.dataIndex==="comType"&&(e.value=="0"?r.value=[...r.value.slice(0,5),c()[0],c()[1]]:e.value=="1"&&(r.value=[...r.value.slice(0,5),c()[2],c()[3],c()[4],c()[5],c()[6]])),e.dataIndex&&e.value&&e.dataIndex==="modbusDeviceID"){await X(e.value);let a=[...r.value];a[2].selectOptions=u.modbusMeasLocoptions,r.value=a}},W=()=>{x.value=!0},D=e=>{x.value=!1,r.value=[],I.value={},m.value="",v.value="",d.value=""},te=async e=>{e.type&&e.type=="close"?(s.batchApplyData=[],s.batchApplyKey="",s[`bathApplyResponse${e.key}`]={}):(s.batchApplyData=e.turbines,s.batchApplyKey=e.key)};return U("deviceId",i),U("bathApplySubmit",te),(e,a)=>{const t=be,o=oe;return w(),B(o,{spinning:f.value,size:"large"},{default:E(()=>[(w(),G("div",{key:i.value},[M(P,{ref:"table",size:"default","table-key":"1","table-title":"Modbus设备列表","table-columns":s.tableColumns,borderLight:s.batchApplyKey=="1",bathApplyResponse:s.bathApplyResponse1,"table-operate":["edit","delete","add","batchDelete"],recordKey:l=>`${l.modbusDeviceID}&&${l.modbusID}`,"table-datas":s.tableData1,onAddRow:F,onDeleteRow:K,onEditRow:q},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"]),M(P,{ref:"table",size:"default","table-key":"2","table-title":"通道列表","table-columns":s.tableColumns2,borderLight:s.batchApplyKey=="2",bathApplyResponse:s.bathApplyResponse2,"table-operate":["delete","add","batchDelete"],recordKey:l=>`${l.channelNumber}&&${l.modbusDeviceID}&&${l.measLocationID}`,"table-datas":s.tableData2,onAddRow:F,onDeleteRow:K,onEditRow:q},null,8,["table-columns","borderLight","bathApplyResponse","recordKey","table-datas"])])),M(t,{maskClosable:!1,width:z.value,open:x.value,title:v.value,footer:"",destroyOnClose:!0,onCancel:D},{default:E(()=>[m.value==="add"||m.value==="edit"?(w(),B(se,{key:0,titleCol:r.value,initFormData:I.value,onChange:N,onSubmit:Y},null,8,["titleCol","initFormData"])):(w(),G("div",pe))]),_:1},8,["width","open","title"])]),_:1},8,["spinning"])}}};export{qe as default};
