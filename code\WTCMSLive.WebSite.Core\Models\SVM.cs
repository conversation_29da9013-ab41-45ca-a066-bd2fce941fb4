﻿
using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class SVM
    {
        public BaseTableModel GetSVMList(bool edit, string windParkId, List<SVMUnit> svmUnitList)
        {
            BaseTableModel tableModel = new BaseTableModel();
            List<WindTurbine> trubineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkId);
            tableModel.tableName = "svmList";
            List<Rows> rows = new List<Rows>();
            trubineList.ForEach(item => {
                SVMUnit svm = svmUnitList.Find(svmunit => svmunit.AssocWindTurbineID == item.WindTurbineID);
                if (svm != null)
                {
                    Rows cells = new Rows();
                    cells.cells = CreateWindTurbineListTableCell(svm, edit);
                    rows.Add(cells);
                }
            });
            tableModel.rows = rows.ToArray();
            return tableModel;
        }
        private Cell[] CreateWindTurbineListTableCell(SVMUnit svmUnitList, bool edit)
        {
            List<Cell> cellList = new List<Cell>();
            //机组编号
            Cell cell0 = new Cell();
            cell0.displayValue = svmUnitList.AssocWindTurbineID;
            //机组名称
            Cell cell1 = new Cell();
            cell1.displayValue = svmUnitList.SVMName;
            //ModBus地址  
            Cell cell2 = new Cell();
            cell2.displayValue = svmUnitList.ModbusAddress;
            //采集方式     wangy 2015年11月26日 09:59:28 采集方式不显示
            //Cell cell3 = new Cell();
            //cell3.displayValue = EnumHelper.GetDescription((EnumSVMAcquisitionType)svmUnitList.SVMAcqType);
            //编辑
            Cell cell6 = new Cell();
            cell6.type = "btn";
            cell6.displayValue = "编辑";
            cell6.onclick = "editSVM('" + svmUnitList.AssocWindTurbineID + "')";
            cell6.style = "btn btn-primary btn-sm btnEdit";
            //删除
            Cell cell7 = new Cell();
            cell7.type = "btn";
            cell7.displayValue = " 删除";
            cell7.onclick = "deleteSVM('" + svmUnitList.AssocWindTurbineID + "','" + svmUnitList.ComponentID + "',this)";
            cell7.style = "btn btn-danger btn-sm btnDelete";

            cellList.Add(cell0);
            cellList.Add(cell1);
            cellList.Add(cell2);
           // cellList.Add(cell3);
            cellList.Add(cell6);
            cellList.Add(cell7);
            return cellList.ToArray();
        }
        #region 晃度仪寄存器列表
        public BaseTableModel GetSVMRegisterList(string _turId)
        {
            SVMUnit svmUnit = SVMManagement.GetSVMById(_turId);
            if (svmUnit != null)
            {
                List<SVMRegister> SVMRegisterList = SVMManagement.GetSVMRegisterListBySVMId(_turId);
                BaseTableModel tableModel = new BaseTableModel();
                if (SVMRegisterList.Count != 0 && SVMRegisterList != null)
                {
                    tableModel.tableName = "SVMRegister";
                    List<Rows> rows = new List<Rows>();
                    foreach (EnumSVMParamType svmdata in Enum.GetValues( typeof(EnumSVMParamType)))
                    {
                        SVMRegister register = SVMRegisterList.Find(item => item.DataType == svmdata);
                        if (register != null)
                        {
                            Rows cells = new Rows();
                            cells.cells = CreateSVMRegisterTableCell(register);
                            rows.Add(cells);
                        }
                    }
                    tableModel.rows = rows.ToArray();
                }
                else
                {
                    tableModel.tableName = "SVMRegister";   
                    List<Rows> rowsList = new List<Rows>();
                    Rows rows = new Rows();
                    List<Cell> cells = new List<Cell>();
                    for (int i = 0; i < 2; i++)
                    {
                        Cell cell = new Cell();
                        if (i == 0)
                        {
                            cell.displayValue = "列表无数据";
                        }
                        else
                        {
                            cell.displayValue = "";
                        }
                        cells.Add(cell);
                        cells.ToArray();
                    }
                    rows.cells = cells.ToArray();
                    rowsList.Add(rows);
                    tableModel.rows = rowsList.ToArray();
                }
                return tableModel;
            }
            else 
            {
                BaseTableModel tableModel = new BaseTableModel();
                tableModel.tableName = "SVMRegister";
                List<Rows> rowsList = new List<Rows>();
                Rows rows = new Rows();
                List<Cell> cells = new List<Cell>();
                for (int i = 0; i < 2; i++)
                {
                    Cell cell = new Cell();
                    if (i == 0)
                    {
                        cell.displayValue = "未添加晃度仪，无相关晃度仪数据";
                    }
                    else
                    {
                        cell.displayValue = "";
                    }
                    cells.Add(cell);
                    cells.ToArray();
                }
                rows.cells = cells.ToArray();
                rowsList.Add(rows);
                tableModel.rows = rowsList.ToArray();
                return tableModel; }
        }
        private Cell[] CreateSVMRegisterTableCell(SVMRegister svmUnitList)
        {
            List<Cell> cellList = new List<Cell>();
            //寄存器地址
            Cell cell0 = new Cell();
            cell0.displayValue = svmUnitList.SVMRegisterAdr;
            //测量位置名称
            Cell cell1 = new Cell();
            cell1.displayValue = AppFramework.Utility.EnumHelper.GetDescription((EnumSVMParamType)svmUnitList.RegisterType);
            cellList.Add(cell0);
            cellList.Add(cell1);
            return cellList.ToArray();
        }
        #endregion
        public List<WindTurbine> GetTurbineDropDown(string windParkID)
        {
            List<WindTurbine> list = new List<WindTurbine>();
            List<WindTurbine> wind = WTCMSLive.BusinessModel.DevTreeManagement.GetNotSVMTurListByParkId(windParkID);
            foreach (WindTurbine w in wind)
            {
                WindTurbine winds = new WindTurbine();
                winds.WindTurbineID = w.WindTurbineID;
                winds.WindTurbineName = w.WindTurbineName;
                list.Add(winds);
            }
            return list;
        }
    }
    public class SVMonitorEntity
    {
        public string WindTurbineName { get; set; }
        public string windparkid { get; set; }
        public string WindTurbineId { get; set; }
        public string Modbus { get; set; }
        public string AlarmState { get; set; }
        public string AlarmDate { get; set; }
    }

    public class SVMonitorMeasLocEntity : MeasLoc_SVM
    {
        public double alarmValue { get; set; }
        public double warnValue { get; set; }
        public double eigenValue { get; set; }
        public string EigenName { get; set; }
        public string UnitName { get; set; }

        public double minvalue { get; set; }
        public double maxvalue { get; set; }

        public List<List<string>> EigenValueList { get; set; }
        public string AlarmState { get; set; }
        public string AlarmDate { get; set; }
    }
}