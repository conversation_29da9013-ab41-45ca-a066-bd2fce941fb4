﻿using CMSFramework.BusinessEntity;
using Newtonsoft.Json;
using System.Text.Json;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public static class Utility
    {

        public static string CreateTurbineListNodePath(string windParkID, string turbineID,string url,string Page)
        {
            List<WindTurbine> turbineList = DevTreeManagement.GetTurbinesListByWindParkId(windParkID);
            List<WindCMSMenu> menuList = new List<WindCMSMenu>();
            for (int i = 0; i < turbineList.Count; i++)
            {
                WindCMSMenu menu = new WindCMSMenu();
                menu.Name = Page +":"+ turbineList[i].WindTurbineName;
                menu.Value = url+windParkID+"/"+ turbineList[i].WindTurbineID;
                menu.parentID = turbineList[i].WindParkID;
                if (turbineList[i].WindTurbineID == turbineID)
                {
                    menu.Selected = "selected";
                }
                menuList.Add(menu);
            }
            return menuList.ToJson();
        }

        /// <summary>
        /// 创建URL
        /// </summary>
        /// <param name="action">连接响应的action</param>
        /// <param name="displayname">连接显示内容</param>
        /// <param name="id">连接点击后，后台接收内容</param>
        /// <returns>html 字符串</returns>
        public static string CreateLinkUrl(string Page, string id, string DisplayName)
        {
            //string url = "<a href='javascript:RefreshDivByAjax("+'"'+ action + "?=" + id +'"'+");>" + displayname + "</a>";
            string url = "<a href='javascript:openNode(" + '"' + Page + '"' + ',' + '"' + id + '"' + ")'>" + DisplayName + "</a>";
            return url;
        }

        /// <summary>
        /// 将指定对象转换为Json对象
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static string ToJson(this object obj)
        {
            //JavaScriptSerializer serializer = new JavaScriptSerializer();
            //serializer.MaxJsonLength = Int32.MaxValue;
            //return serializer.Serialize(obj);
            //var options = new JsonSerializerOptions
            //{
            //    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            //};
            
            return System.Text.Json.JsonSerializer.Serialize(obj);
        }
        //public static string ToJson(object obj, int recursionDepth)
        //{
        //    JavaScriptSerializer serializer = new JavaScriptSerializer();
        //    serializer.RecursionLimit = recursionDepth;
        //    return serializer.Serialize(obj);
        //}
        /// <summary>
        /// 根据alramType 取得对应的样式
        /// </summary>
        /// <param name="alarmType"></param>
        /// <returns></returns>
        /*
         * .active	鼠标悬停在行或单元格上时所设置的颜色
           .success	标识成功或积极的动作
           .info	标识普通的提示信息或动作
           .warning	标识警告或需要用户注意
           .danger	标识危险或潜在的带来负面影响的动作
         */
        // EnumAlarmType  未知 2， 正常：3， 通讯异常4 ，报警5，危险6，
        /*
        public static string GetStyleFromAlarmType(AlarmType alarmType)
        {
            string style = string.Empty;
            switch (alarmType.AlarmDegree)
            {
                case 0:
                    style = "";
                    break;
                case 1:
                    style = "";
                    break;
                case 2:
                    style = "info";
                    break;
                case 3:
                    style = "success";
                    break;
                case 4:
                    style = "info";
                    break;
                case 5:
                    style = "warning";
                    break;
                case 6:
                    style = "danger";
                    break;
                default:
                    style = "";
                    break;
            }
            return style;
        }
        */
        // EnumAlarmType  未知 2， 正常：3， 通讯异常4 ，报警5，危险6，结冰7
        public static string GetColorFromAlarmType(EnumAlarmDegree alarmType)
        {
            string color = string.Empty;
            switch ((int)alarmType)
            {
                case 2:
                    color = "#A0A0A0";
                    break;
                case 3:
                    color = "#00FF00";
                    break;
                case 4:
                    color = "#4169E1";
                    break;
                case 5:
                    color = "#FFFF00";
                    break;
                case 6:
                    color = "#FF0000";
                    break;
                case 7:
                    color = "#22DDDD";
                    break;
                default:
                    color = "";
                    break;
            }
            return color;
        }
        /// <summary>
        /// 获取传感器（振动）
        /// </summary>
        /// <param name="dauStatus"></param>
        /// <returns></returns>
        public static string GetColorFromAlarmType(EnumICPSensorStatus dauStatus)
        {
            string color = string.Empty;
            switch (dauStatus)
            {
                case EnumICPSensorStatus.Normal:
                    color = "#00FF00";
                    break;
                case EnumICPSensorStatus.DC_EXCEPTION:
                    color = "#007aff";
                    break;
                case EnumICPSensorStatus.SHORT_OUT:
                    color = "#62BDEE";
                    break;
                case EnumICPSensorStatus.DISCONNECTION:
                    color = "#34aadc";
                    break;
              /*  case EnumICPSensorStatus.JUMPING:
                    color = "#34aadc";
                    break;
                case EnumICPSensorStatus.CollectValue_SMALL:
                    color = "#34aadc";
                    break;*/
                default:
                    color = "";
                    break;
            }
            return color;
        }
        /// <summary>
        /// 获取DAU状态
        /// </summary>
        /// <param name="dauStatus"></param>
        /// <returns></returns>
        public static string GetColorFromAlarmType(EnumDAUStatus dauStatus)
        {
            string color = string.Empty;
            switch (dauStatus)
            {
                case EnumDAUStatus.Unknown:
                    color = "#A0A0A0";
                    break;
                case EnumDAUStatus.Normal:
                    color = "#00FF00";
                    break;
                case EnumDAUStatus.CommunicationError:
                    color = "#007aff";
                    break;
                case EnumDAUStatus.NoDataArrive:
                    color = "#62BDEE";
                    break;
                case EnumDAUStatus.SensorFault:
                    color = "#34aadc";
                    break;
                case EnumDAUStatus.RotSpdFault:
                    color = "#ea9b29";
                    break;
                default:
                    color = "";
                    break;
            }
            return color;
        }
        /// <summary>
        /// 获取传感器（转速）
        /// </summary>
        /// <param name="dauStatus"></param>
        /// <returns></returns>
        public static string GetColorFromAlarmType(EnumRotSpeedChannelStatus dauStatus)
        {
            string color = string.Empty;
            switch (dauStatus)
            {
                case EnumRotSpeedChannelStatus.Unknown:
                    color = "#A0A0A0";
                    break;
                case EnumRotSpeedChannelStatus.Normal:
                    color = "#00FF00";
                    break;
                case EnumRotSpeedChannelStatus.Fault:
                    color = "#007aff";
                    break;
                case EnumRotSpeedChannelStatus.NotAcquisitionData:
                    color = "#007aff";
                    break;
                default:
                    color = "";
                    break;
            }
            return color;
        }

        public class WindCMSMenu
        {
            public string Name { get; set; }
            public string Value { get; set; }
            public string Selected { get; set; }
            public string parentID { get; set; }
        }

        public static string ConvertStringToF2(string str,int count)
        {
            int i = 0;
            count = count + 1;
            i = str.IndexOf('.');
            if (i == -1)
            {
                return str;
            }
            if (str.Length > i + count)
            {
                return str.Substring(0, i + count);
            }
            return str;
        }
    }
}