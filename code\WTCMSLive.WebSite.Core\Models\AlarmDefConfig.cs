﻿using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;

namespace WTCMSLive.WebSite.Models
{
    public class AlarmDefConfig
    {
        #region 振动报警定义
        public static List<AlarmDefinition> GetAlarmListByTurID(string turID)
        {
            List<AlarmDefinition> alarmList = new List<AlarmDefinition>();
            // 得到VDI3834 特征值的ID
            List<MeasLoc_Vib> List = DevTreeManagement.GetVibMeasLocationByTurId(turID);
            List<WindTurbineComponent> ComponentList = DevTreeManagement.GetComListByTurbineId(turID);
            foreach (MeasLoc_Vib MeasLoc in List)
            {
                string MeasLocId = MeasLoc.MeasLocationID;
                string WindTurbineID = MeasLoc.WindTurbineID;
                //MeasLoc.DevTurComponent.ComponentName;
                string _compName = ComponentList.Find(item=>item.ComponentID == MeasLoc.ComponentID).ComponentName;
                switch (_compName)
                {
                    case "主轴承":
                        alarmList.AddRange(GetMBRAlarmDef(WindTurbineID, MeasLocId));
                        break;
                    case "齿轮箱":
                        alarmList.AddRange(GetGBAlarmDef(WindTurbineID, MeasLocId));
                        break;
                    case "发电机":
                        alarmList.AddRange(GetGENAlarmDef(WindTurbineID, MeasLocId));
                        break;
                    case "机舱":
                        alarmList.AddRange(GetOtherAlarmDef(WindTurbineID, MeasLocId));
                        break;
                    case "塔筒":
                        alarmList.AddRange(GetOtherAlarmDef(WindTurbineID, MeasLocId));
                        break;
                }
            }
            return alarmList;
        }

        /// <summary>
        /// 齿轮箱
        /// </summary>
        /// <param name="loc"></param>
        /// <returns></returns>
        private static List<AlarmDefinition> GetGBAlarmDef(string WindTurbineID, string loc)
        {
            List<AlarmDefinition> alarmList = new List<AlarmDefinition>();
            // 0.1-10Hz RMS
            alarmList.AddRange(GetAlarmDefByCode(WindTurbineID, loc, "0.1-10_BRMS"));
            // 10-2kHz RMS
            alarmList.AddRange(GetAlarmDefByCode(WindTurbineID, loc, "10-2000_BRMS"));
            // 10-1K VRMS
            alarmList.AddRange(SetAlarmDef("10-1000_VRMS", WindTurbineID, loc, 3.5M, 5.6M, EnumMeasLocType.VibAlarmDef));

            return alarmList;
        }

        /// <summary>
        /// 发电机
        /// </summary>
        /// <param name="loc"></param>
        /// <returns></returns>
        private static List<AlarmDefinition> GetGENAlarmDef(string WindTurbineID, string measLocId)
        {
            List<AlarmDefinition> alarmList = new List<AlarmDefinition>();

            // 10-5kHz RMS
            alarmList.AddRange(GetAlarmDefByCode(WindTurbineID, measLocId, "10-5000_BRMS"));
            // 10-1K VRMS
            alarmList.AddRange(SetAlarmDef("10-1000_VRMS", WindTurbineID, measLocId, 6.0M, 10M, EnumMeasLocType.VibAlarmDef));
            return alarmList;
        }
        /// <summary>
        /// 主轴承
        /// </summary>
        /// <param name="loc"></param>
        /// <returns></returns>
        private static List<AlarmDefinition> GetMBRAlarmDef(string WindTurbineID, string measLocId)
        {
            List<AlarmDefinition> alarmList = new List<AlarmDefinition>();
            // 0-10Hz RMS
            alarmList.AddRange(GetAlarmDefByCode(WindTurbineID, measLocId, "0.1-10_BRMS"));
            // 10-1K VRMS
            alarmList.AddRange(SetAlarmDef("10-1000_VRMS", WindTurbineID, measLocId, 2.0M, 3.2M, EnumMeasLocType.VibAlarmDef));
            return alarmList;
        }
        /// <summary>
        /// 机舱塔筒
        /// </summary>
        /// <param name="loc"></param>
        /// <returns></returns>
        private static List<AlarmDefinition> GetOtherAlarmDef(string WindTurbineID, string loc)
        {
            List<AlarmDefinition> alarmList = new List<AlarmDefinition>();

            // 0-10Hz RMS
            alarmList.AddRange(GetAlarmDefByCode(WindTurbineID, loc, "0.1-10_BRMS"));
            //  0-10Hz VRMS
            alarmList.AddRange(GetAlarmDefByCode(WindTurbineID, loc, "0.1-10_VRMS"));

            return alarmList;
        }


        private static List<AlarmDefinition> GetAlarmDefByCode(string WindTurbineID, string loc, string eigenValueCode)
        {
            List<AlarmDefinition> alarmList = new List<AlarmDefinition>();
            switch (eigenValueCode)
            {
                case "0.1-10_VRMS":
                    alarmList.AddRange(SetAlarmDef("0.1-10_VRMS", WindTurbineID, loc, 60, 100, EnumMeasLocType.VibAlarmDef));
                    break;
                //case "10-1000_VRMS": 10-1K Hz转速报警值因为部件不同而不同。
                //    alarmList.Add(SetAlarmDef("10-1000_VRMS", "10-1KHz RMS", loc, WorkCondition_ParamType.NOWORKCONDTION, "5", 2.0));
                //    alarmList.Add(SetAlarmDef("10-1000_VRMS", "10-1KHz RMS", loc, WorkCondition_ParamType.NOWORKCONDTION, "6", 3.2));
                //    break;
                case "10-2000_BRMS":
                    alarmList.AddRange(SetAlarmDef("10-2000_BRMS", WindTurbineID, loc, 7.5M, 12, EnumMeasLocType.VibAlarmDef));
                    break;
                case "0.1-10_BRMS":
                    alarmList.AddRange(SetAlarmDef("0.1-10_BRMS", WindTurbineID, loc, 0.3M, 0.5M, EnumMeasLocType.VibAlarmDef));
                    break;
                case "10-5000_BRMS":
                    alarmList.AddRange(SetAlarmDef("10-5000_BRMS", WindTurbineID, loc, 10, 16, EnumMeasLocType.VibAlarmDef));
                    break;
                default:
                    break;
            }
            return alarmList;
        }
        /// <summary>
        /// 获取报警定义的实体
        /// </summary>
        /// <param name="eigenValueCode">特征值ID（code）</param>
        /// <param name="loc">测量位置实体</param>
        /// <param name="WarnValue">注意值</param>
        /// <param name="AlarmValue">报警值</param>
        /// <returns></returns>
        private static List<AlarmDefinition> SetAlarmDef
            (string eigenValueCode, string WindTurbineID, string MeasLocationID, decimal WarnValue, decimal AlarmValue, EnumMeasLocType measLocType)
        {
            List<AlarmDefinition> alarmDefList = new List<AlarmDefinition>();
            //无工况
            AlarmDefinition alarmNoWorkCondent = new AlarmDefinition();
            alarmNoWorkCondent.WindTurbineID = WindTurbineID;
            alarmNoWorkCondent.MeasLocationID = MeasLocationID;
            alarmNoWorkCondent.EigenValueID = MeasLocationID + "&&" + eigenValueCode;
            string GuID = System.Guid.NewGuid().ToString();
            alarmNoWorkCondent.ThresholdGroup = GuID;
            alarmNoWorkCondent.AlarmDefThresholdGroup = new List<AlarmDefThreshold>();
            alarmNoWorkCondent.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
            {
                AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                ThresholdGroup = GuID,
                ThresholdValue = Convert.ToDouble(WarnValue),
                WindTurbineID = WindTurbineID
            });
            alarmNoWorkCondent.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
            {
                AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                ThresholdGroup = GuID,
                ThresholdValue = Convert.ToDouble(AlarmValue),
                WindTurbineID = WindTurbineID
            });
            alarmDefList.Add(alarmNoWorkCondent);
            return alarmDefList;
        }
        #endregion

        #region 晃动报警定义
        /// <summary>
        /// 晃度特征值[无波形相关]
        /// </summary>
        /// <returns></returns>
        public static List<AlarmDefinition> GetSVMAlarmListByTurID(string turID)
        {
            return new List<AlarmDefinition>();
            //晃度报警定义没有给出具体的规则，所以之前的规则作废 @wangy 2016年12月31日
            List<AlarmDefinition> alarmDefList = new List<AlarmDefinition>();
            List<MeasLoc_SVM> List = SVMManagement.GetMeasLoc_SVMListByTurID(turID);
            foreach (MeasLoc_SVM measLoc in List)
            {
                //根据测量位置类型，返回对应列表。
                switch (measLoc.ParamType)
                {
                    case EnumSVMParamType.Vertical://垂直加速度
                        alarmDefList.AddRange(SetAlarmDef("VA" + '_' + measLoc.ParamType.ToString(), measLoc.WindTurbineID, measLoc.MeasLocationID, 0.03M, 0.05M, EnumMeasLocType.SVMAlarmDef));
                        break;
                    case EnumSVMParamType.Horizontal://水平加速度
                        alarmDefList.AddRange(SetAlarmDef("LA" + '_' + measLoc.ParamType.ToString(), measLoc.WindTurbineID, measLoc.MeasLocationID, 0.03M, 0.05M, EnumMeasLocType.SVMAlarmDef));
                        alarmDefList.AddRange(SetAlarmDef("TNF" + '_' + measLoc.ParamType.ToString(), measLoc.WindTurbineID, measLoc.MeasLocationID, 0.03M, 0.05M, EnumMeasLocType.SVMAlarmDef));
                        alarmDefList.AddRange(SetAlarmDef("TNFMA" + '_' + measLoc.ParamType.ToString(), measLoc.WindTurbineID, measLoc.MeasLocationID, 0.03M, 0.05M, EnumMeasLocType.SVMAlarmDef));
                        break;
                    case EnumSVMParamType.Axisl://轴向加速度
                        alarmDefList.AddRange(SetAlarmDef("AA" + '_' + measLoc.ParamType.ToString(), measLoc.WindTurbineID, measLoc.MeasLocationID, 0.03M, 0.05M, EnumMeasLocType.SVMAlarmDef));
                        break;
                    default:
                        break;
                }
            }
            return alarmDefList;
        }
        #endregion
    }
}
