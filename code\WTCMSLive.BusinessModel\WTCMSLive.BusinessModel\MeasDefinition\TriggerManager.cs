﻿using CMSFramework.BusinessEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WTCMSLive.BusinessModel
{
    public class TriggerManager
    {

        public static List<MeasTriggerRuleDef> GetMeasTriggerRuleDefs(string turbineID, string MeasDefinitionID){
            List<MeasTriggerRuleDef> res = new List<MeasTriggerRuleDef>();
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                res = ctx.TriggerRuleDefs.Where(t => t.WindTurbineID == turbineID && t.MeasDefinitionID == MeasDefinitionID).ToList();
                if(res.Count > 0)
                {
                    foreach(var k in res)
                    {
                        k.SupervisedVariables = ctx.TriggerProcess.Where(t => t.RuleID == k.RuleID).ToList();
                        k.TriggerTime = ctx.TriggerTimes.FirstOrDefault(t => t.RuleID == k.RuleID);
                        k.ExecuteMdfs = ctx.ExecuteMdfs.Where(t => t.RuleID == k.RuleID).ToList();
                    }
                }
            }
            return res;
        }
    }
}
