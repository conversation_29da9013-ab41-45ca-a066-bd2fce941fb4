﻿using CMSFramework.BusinessEntity;
using CMSFramework.DevTreeEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WTCMSLive.BusinessModel
{
    public static class ModbusManage
    {
        public static List<string> GetUnConifgModbus(string turbineID, string mdfID)
        {
            List<ModbusUnit> modbus = new List<ModbusUnit>();
            // 机组可用Modbus
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                modbus = ctx.ModbusUnits.Where(obj => obj.WindTurbineID == turbineID).ToList();

            }
            //一个modbus地址可配置多个测量定义

            //List<ModbusDef> modbusmdf = new List<ModbusDef>();
            //// 已配置测量定义的modbus
            //using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            //{
            //    modbusmdf = ctx.ModbusDefs.Where(obj => obj.WindTurbineID == turbineID && obj.MeasDefinitionID == mdfID).ToList();
            //}

            // 过滤掉当前已经配置测量定义的 modbus采集单元
            List<string> modbusList = new List<string>();
            //foreach (var item in modbus)
            //{
            //    //未配置设备
            //    if (modbusmdf.FirstOrDefault(obj => obj.ModbusUnitID == item.ModbusUnitID) == null)
            //    {
            //        modbusList.Add(item.ModbusUnitID);
            //    }
            //}

            foreach (var item in modbus)
            {
                 modbusList.Add(item.ModbusUnitID);
            }

            return modbusList;
        }

        //public static bool DeleteMDFModbus(string turbineID, string mdfID, string modbusID)
        //{
        //    int result = 0;
        //    using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
        //    {

        //        result = ctx.Database.ExecuteSqlCommand($"DELETE FROM mdfmodbus WHERE MeasDefinitionID = '{mdfID}' AND WindTurbineID = '{turbineID}' AND  ModbusUnitID = '{modbusID}'");
        //    }
        //    return result > 0;
        //}

        /// <summary>
        /// 获取Modbus列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="mdfID"></param>
        /// <returns></returns>
        public static List<ModbusDef> GetModbusByMeas(string turbineID, string mdfID)
        {
            List<ModbusDef> modbusmdf = new List<ModbusDef>();

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                modbusmdf = ctx.ModbusDefs.Where(obj => obj.WindTurbineID == turbineID && obj.MeasDefinitionID == mdfID).ToList();
            }
            return modbusmdf;
        }
        /// <summary>
        /// 获取Modbus列表
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="mdfID"></param>
        /// <returns></returns>
        public static ModbusDef GetModbusBymodbusID(string turbineID, string modbusID )
        {
            ModbusDef modbusmdf = new ModbusDef();

            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
                modbusmdf = ctx.ModbusDefs.FirstOrDefault(n => n.WindTurbineID == turbineID && n.ModbusUnitID == modbusID);
            }
            return modbusmdf;
        }


        public static List<MeasLoc_Modbus> GetModbusBymodbusID(string turbineID)
        {
            List<MeasLoc_Modbus> modbusmdf = new();

            using (CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName))
            {
                modbusmdf = ctx.MeasLoc_Modbus.Where(n => n.WindTurbineID == turbineID).ToList();
            }
            return modbusmdf;
        }

        public static bool SetModbusMDF(string turbineID, string mdfID, string modbusID, string sampleTime, string sampleFreq)
        {
            int result = 0;
            ModbusDef modbusDef = new ModbusDef()
            {
                WindTurbineID = turbineID,
                MeasDefinitionID = mdfID,
                ModbusUnitID = modbusID,
                SampleTime = Convert.ToInt32(sampleTime),
                SampleFrequency = Convert.ToSingle(sampleFreq),

            };
            // 添加测量定义
            using (CMSFramework.EF.MeasDef.MDFContext ctx = new CMSFramework.EF.MeasDef.MDFContext(ConfigInfo.DBConnName))
            {
               var _modbusmdf = ctx.ModbusDefs.FirstOrDefault(obj => obj.WindTurbineID == turbineID && obj.MeasDefinitionID == mdfID && obj.ModbusUnitID == modbusID);
                if (_modbusmdf == null)
                {
                    ctx.ModbusDefs.Add(modbusDef);
                }
                else
                {
                    _modbusmdf.SampleTime = modbusDef.SampleTime;
                    _modbusmdf.SampleFrequency = modbusDef.SampleFrequency;
                }
             result = ctx.SaveChanges();
            }
            return result > 0;
        }
    }
}
