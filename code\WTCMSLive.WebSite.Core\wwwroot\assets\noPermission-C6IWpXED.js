import{_ as e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{c as n,o as r,i as t}from"./index-ClUwy-U2.js";const i="/noPermission.png",a={name:"Exception403",methods:{toHome(){this.$router.push({path:"/"})}}},c={class:"exception"},p=["alt"];function l(o,s,m,d,_,u){return r(),n("div",c,[t("img",{class:"componentImg",src:i,alt:o.无页面权限},null,8,p),s[0]||(s[0]=t("div",{class:"ant-result-title"},"无权限页面",-1)),s[1]||(s[1]=t("div",{class:"ant-result-subtitle"},"对不起，您没有该页面的访问权限，请联系管理员分配权限",-1))])}const v=e(a,[["render",l],["__scopeId","data-v-f5a54beb"]]);export{v as N};
