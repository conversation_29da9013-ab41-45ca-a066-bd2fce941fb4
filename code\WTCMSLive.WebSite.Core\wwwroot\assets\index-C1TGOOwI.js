import{X as ke,Y as we,_ as $,$ as Ie,ae as ze,aP as ue,a2 as E,a3 as L,ad as B,a5 as G,j as P,w as D,h as de,a6 as oe,aa as Le,b as c,a8 as R,aC as Ee,aO as je,r as W,am as Fe,ac as De,aN as he,a7 as ve,dg as ge,f as O,o as k,d as C,i as w,t as J,g as ie,c as A,q as ye,dh as We,u as Ge,a as Ve,F as ae,e as re,s as U,di as Xe,dj as Se,dk as xe}from"./index-D9CxWmlM.js";import{_ as He}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{b as Ue,e as Ke,a as Ze,S as Te,L as be,R as _e,c as qe,M as Oe,_ as Be,d as Re}from"./ActionButton-BRQ4acFZ.js";import{r as Je,R as Ye,a as Qe}from"./styleChecker-z-opWSaj.js";import{S as et}from"./tools-DZBuE28U.js";import{M as tt}from"./index-DhZUqjZm.js";import{c as nt,e as ot,C as at}from"./ChangeLanguage-7vrykP9f.js";import{i as rt}from"./initDefaultProps-C2vKchlZ.js";const lt=e=>{const{antCls:n,componentCls:t,iconCls:a,avatarBg:r,avatarColor:g,containerSize:l,containerSizeLG:s,containerSizeSM:u,textFontSize:y,textFontSizeLG:S,textFontSizeSM:i,borderRadius:f,borderRadiusLG:x,borderRadiusSM:h,lineWidth:o,lineType:p}=e,d=(b,v,m)=>({width:b,height:b,lineHeight:`${b-o*2}px`,borderRadius:"50%",[`&${t}-square`]:{borderRadius:m},[`${t}-string`]:{position:"absolute",left:{_skip_check_:!0,value:"50%"},transformOrigin:"0 center"},[`&${t}-icon`]:{fontSize:v,[`> ${a}`]:{margin:0}}});return{[t]:$($($($({},Ie(e)),{position:"relative",display:"inline-block",overflow:"hidden",color:g,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:`${o}px ${p} transparent`,"&-image":{background:"transparent"},[`${n}-image-img`]:{display:"block"}}),d(l,y,f)),{"&-lg":$({},d(s,S,x)),"&-sm":$({},d(u,i,h)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},st=e=>{const{componentCls:n,groupBorderColor:t,groupOverlapping:a,groupSpace:r}=e;return{[`${n}-group`]:{display:"inline-flex",[`${n}`]:{borderColor:t},"> *:not(:first-child)":{marginInlineStart:a}},[`${n}-group-popover`]:{[`${n} + ${n}`]:{marginInlineStart:r}}}},Me=ke("Avatar",e=>{const{colorTextLightSolid:n,colorTextPlaceholder:t}=e,a=we(e,{avatarBg:t,avatarColor:n});return[lt(a),st(a)]},e=>{const{controlHeight:n,controlHeightLG:t,controlHeightSM:a,fontSize:r,fontSizeLG:g,fontSizeXL:l,fontSizeHeading3:s,marginXS:u,marginXXS:y,colorBorderBg:S}=e;return{containerSize:n,containerSizeLG:t,containerSizeSM:a,textFontSize:Math.round((g+l)/2),textFontSizeLG:s,textFontSizeSM:r,groupSpace:y,groupOverlapping:-u,groupBorderColor:S}}),Ne=Symbol("AvatarContextKey"),it=()=>ze(Ne,{}),ct=e=>ue(Ne,e),ut=()=>({prefixCls:String,shape:{type:String,default:"circle"},size:{type:[Number,String,Object],default:()=>"default"},src:String,srcset:String,icon:L.any,alt:String,gap:Number,draggable:{type:Boolean,default:void 0},crossOrigin:String,loadError:{type:Function}}),I=E({compatConfig:{MODE:3},name:"AAvatar",inheritAttrs:!1,props:ut(),slots:Object,setup(e,n){let{slots:t,attrs:a}=n;const r=B(!0),g=B(!1),l=B(1),s=B(null),u=B(null),{prefixCls:y}=G("avatar",e),[S,i]=Me(y),f=it(),x=P(()=>e.size==="default"?f.size:e.size),h=Ue(),o=Ke(()=>{if(typeof e.size!="object")return;const v=Je.find(_=>h.value[_]);return e.size[v]}),p=v=>o.value?{width:`${o.value}px`,height:`${o.value}px`,lineHeight:`${o.value}px`,fontSize:`${v?o.value/2:18}px`}:{},d=()=>{if(!s.value||!u.value)return;const v=s.value.offsetWidth,m=u.value.offsetWidth;if(v!==0&&m!==0){const{gap:_=4}=e;_*2<m&&(l.value=m-_*2<v?(m-_*2)/v:1)}},b=()=>{const{loadError:v}=e;(v==null?void 0:v())!==!1&&(r.value=!1)};return D(()=>e.src,()=>{oe(()=>{r.value=!0,l.value=1})}),D(()=>e.gap,()=>{oe(()=>{d()})}),de(()=>{oe(()=>{d(),g.value=!0})}),()=>{var v,m;const{shape:_,src:H,alt:V,srcset:Q,draggable:j,crossOrigin:T}=e,F=(v=f.shape)!==null&&v!==void 0?v:_,M=Le(t,e,"icon"),z=y.value,ee={[`${a.class}`]:!!a.class,[z]:!0,[`${z}-lg`]:x.value==="large",[`${z}-sm`]:x.value==="small",[`${z}-${F}`]:!0,[`${z}-image`]:H&&r.value,[`${z}-icon`]:M,[i.value]:!0},te=typeof x.value=="number"?{width:`${x.value}px`,height:`${x.value}px`,lineHeight:`${x.value}px`,fontSize:M?`${x.value/2}px`:"18px"}:{},X=(m=t.default)===null||m===void 0?void 0:m.call(t);let N;if(H&&r.value)N=c("img",{draggable:j,src:H,srcset:Q,onError:b,alt:V,crossorigin:T},null);else if(M)N=M;else if(g.value||l.value!==1){const ne=`scale(${l.value}) translateX(-50%)`,Pe={msTransform:ne,WebkitTransform:ne,transform:ne},Ae=typeof x.value=="number"?{lineHeight:`${x.value}px`}:{};N=c(Ye,{onResize:d},{default:()=>[c("span",{class:`${z}-string`,ref:s,style:$($({},Ae),Pe)},[X])]})}else N=c("span",{class:`${z}-string`,ref:s,style:{opacity:0}},[X]);return S(c("span",R(R({},a),{},{ref:u,class:ee,style:[te,p(!!M),a.style]}),[N]))}}}),dt=()=>({prefixCls:String,maxCount:Number,maxStyle:{type:Object,default:void 0},maxPopoverPlacement:{type:String,default:"top"},maxPopoverTrigger:String,size:{type:[Number,String,Object],default:"default"},shape:{type:String,default:"circle"}}),ce=E({compatConfig:{MODE:3},name:"AAvatarGroup",inheritAttrs:!1,props:dt(),setup(e,n){let{slots:t,attrs:a}=n;const{prefixCls:r,direction:g}=G("avatar",e),l=P(()=>`${r.value}-group`),[s,u]=Me(r);return Ee(()=>{const y={size:e.size,shape:e.shape};ct(y)}),()=>{const{maxPopoverPlacement:y="top",maxCount:S,maxStyle:i,maxPopoverTrigger:f="hover",shape:x}=e,h={[l.value]:!0,[`${l.value}-rtl`]:g.value==="rtl",[`${a.class}`]:!!a.class,[u.value]:!0},o=Le(t,e),p=je(o).map((b,v)=>Qe(b,{key:`avatar-key-${v}`})),d=p.length;if(S&&S<d){const b=p.slice(0,S),v=p.slice(S,d);return b.push(c(Ze,{key:"avatar-popover-key",content:v,trigger:f,placement:y,overlayClassName:`${l.value}-popover`},{default:()=>[c(I,{style:i,shape:x},{default:()=>[`+${d-S}`]})]})),s(c("div",R(R({},a),{},{class:h,style:a.style}),[b]))}return s(c("div",R(R({},a),{},{class:h,style:a.style}),[p]))}}});I.Group=ce;I.install=function(e){return e.component(I.name,I),e.component(ce.name,ce),e};const gt=e=>!isNaN(parseFloat(e))&&isFinite(e),pt=e=>{const{componentCls:n,colorBgContainer:t,colorBgBody:a,colorText:r}=e;return{[`${n}-sider-light`]:{background:t,[`${n}-sider-trigger`]:{color:r,background:t},[`${n}-sider-zero-width-trigger`]:{color:r,background:t,border:`1px solid ${a}`,borderInlineStart:0}}}},mt=e=>{const{antCls:n,componentCls:t,colorText:a,colorTextLightSolid:r,colorBgHeader:g,colorBgBody:l,colorBgTrigger:s,layoutHeaderHeight:u,layoutHeaderPaddingInline:y,layoutHeaderColor:S,layoutFooterPadding:i,layoutTriggerHeight:f,layoutZeroTriggerSize:x,motionDurationMid:h,motionDurationSlow:o,fontSize:p,borderRadius:d}=e;return{[t]:$($({display:"flex",flex:"auto",flexDirection:"column",color:a,minHeight:0,background:l,"&, *":{boxSizing:"border-box"},[`&${t}-has-sider`]:{flexDirection:"row",[`> ${t}, > ${t}-content`]:{width:0}},[`${t}-header, &${t}-footer`]:{flex:"0 0 auto"},[`${t}-header`]:{height:u,paddingInline:y,color:S,lineHeight:`${u}px`,background:g,[`${n}-menu`]:{lineHeight:"inherit"}},[`${t}-footer`]:{padding:i,color:a,fontSize:p,background:l},[`${t}-content`]:{flex:"auto",minHeight:0},[`${t}-sider`]:{position:"relative",minWidth:0,background:g,transition:`all ${h}, background 0s`,"&-children":{height:"100%",marginTop:-.1,paddingTop:.1,[`${n}-menu${n}-menu-inline-collapsed`]:{width:"auto"}},"&-has-trigger":{paddingBottom:f},"&-right":{order:1},"&-trigger":{position:"fixed",bottom:0,zIndex:1,height:f,color:r,lineHeight:`${f}px`,textAlign:"center",background:s,cursor:"pointer",transition:`all ${h}`},"&-zero-width":{"> *":{overflow:"hidden"},"&-trigger":{position:"absolute",top:u,insetInlineEnd:-x,zIndex:1,width:x,height:x,color:r,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:g,borderStartStartRadius:0,borderStartEndRadius:d,borderEndEndRadius:d,borderEndStartRadius:0,cursor:"pointer",transition:`background ${o} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${o}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:-x,borderStartStartRadius:d,borderStartEndRadius:0,borderEndEndRadius:0,borderEndStartRadius:d}}}}},pt(e)),{"&-rtl":{direction:"rtl"}})}},ft=ke("Layout",e=>{const{colorText:n,controlHeightSM:t,controlHeight:a,controlHeightLG:r,marginXXS:g}=e,l=r*1.25,s=we(e,{layoutHeaderHeight:a*2,layoutHeaderPaddingInline:l,layoutHeaderColor:n,layoutFooterPadding:`${t}px ${l}px`,layoutTriggerHeight:r+g*2,layoutZeroTriggerSize:r});return[mt(s)]},e=>{const{colorBgLayout:n}=e;return{colorBgHeader:"#001529",colorBgBody:n,colorBgTrigger:"#002140"}}),pe=()=>({prefixCls:String,hasSider:{type:Boolean,default:void 0},tagName:String});function Y(e){let{suffixCls:n,tagName:t,name:a}=e;return r=>E({compatConfig:{MODE:3},name:a,props:pe(),setup(l,s){let{slots:u}=s;const{prefixCls:y}=G(n,l);return()=>{const S=$($({},l),{prefixCls:y.value,tagName:t});return c(r,S,u)}}})}const me=E({compatConfig:{MODE:3},props:pe(),setup(e,n){let{slots:t}=n;return()=>c(e.tagName,{class:e.prefixCls},t)}}),ht=E({compatConfig:{MODE:3},inheritAttrs:!1,props:pe(),setup(e,n){let{slots:t,attrs:a}=n;const{prefixCls:r,direction:g}=G("",e),[l,s]=ft(r),u=W([]);ue(Te,{addSider:i=>{u.value=[...u.value,i]},removeSider:i=>{u.value=u.value.filter(f=>f!==i)}});const S=P(()=>{const{prefixCls:i,hasSider:f}=e;return{[s.value]:!0,[`${i}`]:!0,[`${i}-has-sider`]:typeof f=="boolean"?f:u.value.length>0,[`${i}-rtl`]:g.value==="rtl"}});return()=>{const{tagName:i}=e;return l(c(i,$($({},a),{class:[S.value,a.class]}),t))}}}),le=Y({suffixCls:"layout",tagName:"section",name:"ALayout"})(ht),K=Y({suffixCls:"layout-header",tagName:"header",name:"ALayoutHeader"})(me),se=Y({suffixCls:"layout-footer",tagName:"footer",name:"ALayoutFooter"})(me),Z=Y({suffixCls:"layout-content",tagName:"main",name:"ALayoutContent"})(me);var vt={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};function Ce(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},a=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(a=a.concat(Object.getOwnPropertySymbols(t).filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable}))),a.forEach(function(r){yt(e,r,t[r])})}return e}function yt(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var fe=function(n,t){var a=Ce({},n,t.attrs);return c(Fe,Ce({},a,{icon:vt}),null)};fe.displayName="BarsOutlined";fe.inheritAttrs=!1;const $e={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px",xxxl:"1999.98px"},St=()=>({prefixCls:String,collapsible:{type:Boolean,default:void 0},collapsed:{type:Boolean,default:void 0},defaultCollapsed:{type:Boolean,default:void 0},reverseArrow:{type:Boolean,default:void 0},zeroWidthTriggerStyle:{type:Object,default:void 0},trigger:L.any,width:L.oneOfType([L.number,L.string]),collapsedWidth:L.oneOfType([L.number,L.string]),breakpoint:L.oneOf(ve("xs","sm","md","lg","xl","xxl","xxxl")),theme:L.oneOf(ve("light","dark")).def("dark"),onBreakpoint:Function,onCollapse:Function}),xt=(()=>{let e=0;return function(){let n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return e+=1,`${n}${e}`}})(),q=E({compatConfig:{MODE:3},name:"ALayoutSider",inheritAttrs:!1,props:rt(St(),{collapsible:!1,defaultCollapsed:!1,reverseArrow:!1,width:200,collapsedWidth:80}),emits:["breakpoint","update:collapsed","collapse"],setup(e,n){let{emit:t,attrs:a,slots:r}=n;const{prefixCls:g}=G("layout-sider",e),l=ze(Te,void 0),s=B(!!(e.collapsed!==void 0?e.collapsed:e.defaultCollapsed)),u=B(!1);D(()=>e.collapsed,()=>{s.value=!!e.collapsed}),ue(qe,s);const y=(o,p)=>{e.collapsed===void 0&&(s.value=o),t("update:collapsed",o),t("collapse",o,p)},S=B(o=>{u.value=o.matches,t("breakpoint",o.matches),s.value!==o.matches&&y(o.matches,"responsive")});let i;function f(o){return S.value(o)}const x=xt("ant-sider-");l&&l.addSider(x),de(()=>{D(()=>e.breakpoint,()=>{try{i==null||i.removeEventListener("change",f)}catch{i==null||i.removeListener(f)}if(typeof window<"u"){const{matchMedia:o}=window;if(o&&e.breakpoint&&e.breakpoint in $e){i=o(`(max-width: ${$e[e.breakpoint]})`);try{i.addEventListener("change",f)}catch{i.addListener(f)}f(i)}}},{immediate:!0})}),De(()=>{try{i==null||i.removeEventListener("change",f)}catch{i==null||i.removeListener(f)}l&&l.removeSider(x)});const h=()=>{y(!s.value,"clickTrigger")};return()=>{var o,p;const d=g.value,{collapsedWidth:b,width:v,reverseArrow:m,zeroWidthTriggerStyle:_,trigger:H=(o=r.trigger)===null||o===void 0?void 0:o.call(r),collapsible:V,theme:Q}=e,j=s.value?b:v,T=gt(j)?`${j}px`:String(j),F=parseFloat(String(b||0))===0?c("span",{onClick:h,class:he(`${d}-zero-width-trigger`,`${d}-zero-width-trigger-${m?"right":"left"}`),style:_},[H||c(fe,null,null)]):null,M={expanded:m?c(_e,null,null):c(be,null,null),collapsed:m?c(be,null,null):c(_e,null,null)},z=s.value?"collapsed":"expanded",ee=M[z],te=H!==null?F||c("div",{class:`${d}-trigger`,onClick:h,style:{width:T}},[H||ee]):null,X=[a.style,{flex:`0 0 ${T}`,maxWidth:T,minWidth:T,width:T}],N=he(d,`${d}-${Q}`,{[`${d}-collapsed`]:!!s.value,[`${d}-has-trigger`]:V&&H!==null&&!F,[`${d}-below`]:!!u.value,[`${d}-zero-width`]:parseFloat(T)===0},a.class);return c("aside",R(R({},a),{},{class:N,style:X}),[c("div",{class:`${d}-children`},[(p=r.default)===null||p===void 0?void 0:p.call(r)]),V||u.value&&F?te:null])}}}),bt=K,Gt=q,Vt=Z,Xt=$(le,{Header:K,Footer:se,Content:Z,Sider:q,install:e=>(e.component(le.name,le),e.component(K.name,K),e.component(se.name,se),e.component(q.name,q),e.component(Z.name,Z),e)}),_t="/logo.png",Ct=ge(),$t={name:"AvatarDropdown",props:{currentUser:{type:Object,default:()=>null},menu:{type:Boolean,default:!0}},methods:{handleToCenter(){this.$router.push({path:"/account/center"})},handleToSettings(){this.$router.push({path:"/account/settings"})},handleLogout(e){tt.confirm({title:this.$t("layouts.usermenu.dialog.title"),content:this.$t("layouts.usermenu.dialog.content"),okText:this.$t("button.confirm"),cancelText:this.$t("button.cancel"),onOk:()=>{Ct.logout()},onCancel(){}})}}},kt={class:"ant-pro-account-avatar"};function wt(e,n,t,a,r,g){const l=I,s=Be,u=Oe,y=Re,S=et;return t.currentUser&&t.currentUser.name?(k(),O(y,{key:0,placement:"bottomRight",overlayClassName:"logoutClass"},{overlay:C(()=>[c(u,{class:"ant-pro-drop-down menu","selected-keys":[]},{default:C(()=>[c(s,{key:"logout",onClick:g.handleLogout},{default:C(()=>[ie(J(e.$t("menu.logout")),1)]),_:1},8,["onClick"])]),_:1})]),default:C(()=>[w("span",kt,[c(l,{size:"small",src:"/userIcon.png",class:"antd-pro-global-header-index-avatar"}),w("span",null,J(t.currentUser.name),1)])]),_:1})):(k(),O(y,{key:1,overlayClassName:"logoutClass",placement:"bottomRight"},{overlay:C(()=>[c(u,{class:"ant-pro-drop-down menu","selected-keys":[]},{default:C(()=>[c(s,{key:"logout",onClick:g.handleLogout},{default:C(()=>n[1]||(n[1]=[ie(" 返回登录 ",-1)])),_:1,__:[1]},8,["onClick"])]),_:1})]),default:C(()=>[w("span",null,[c(S,{size:"small",style:{marginLeft:8,marginRight:8}}),n[0]||(n[0]=w("span",{class:"noLogin"},"未登录",-1))])]),_:1}))}const zt=He($t,[["render",wt],["__scopeId","data-v-7c843132"]]),Lt={__name:"RightContent",props:{prefixCls:{type:String,default:"ant-pro-global-header-index-action"},isMobile:{type:Boolean,default:()=>!1}},setup(e){const n=ge(),t=W(!0),a=W({}),r=P(()=>({"ant-pro-global-header-index-right":!0}));return de(()=>{let g=localStorage.getItem("user");if(g){let l=JSON.parse(g);a.value={name:l.username}}else n.logout()}),(g,l)=>(k(),A("div",{class:ye(r.value)},[c(zt,{menu:t.value,"current-user":a.value,class:ye(e.prefixCls)},null,8,["menu","current-user","class"])],2))}},Ht={class:"topMenus"},Tt={class:"topRight clearfix"},Ot={key:0,class:"pullLeft"},Bt={key:1,class:"pullLeft"},Rt=["onClick"],Mt={class:"pullLeft"},Nt={__name:"index",props:{currentTreeNode:{type:Object,default:()=>({})}},emits:["menu-select","changeView"],setup(e,{emit:n}){const t=n,a=e,{locale:r}=We(),g=Ge(),l=Ve();ge();const s=W(window.localStorage.getItem("templateManagement")==="true"),u=W([]);P(()=>r.value==="en"?nt:ot);const y=P(()=>{const h=l.getRoutes();return s.value?h.filter(o=>{var p;return((p=o.meta)==null?void 0:p.locationLeft)&&o.name==="config"}):a.currentTreeNode&&a.currentTreeNode.type?h.filter(o=>{var p;return((p=o.meta)==null?void 0:p.locationLeft)&&o.children.some(d=>{var b;return((b=d.meta)==null?void 0:b.selectNodeType)===a.currentTreeNode.type})}):h.filter(o=>{var p;return(p=o.meta)==null?void 0:p.locationLeft})}),S=P(()=>l.getRoutes().filter(h=>{var o;return(o=h.meta)==null?void 0:o.locationRight})),i=({item:h,key:o,keyPath:p})=>{t("menu-select",o)},f=h=>{try{h.name=="templateManagement"?(window.localStorage.setItem("templateManagement",!0),xe({templateView:!0}),s.value=!0,t("changeView","enter")):l.push({name:h.name})}catch(o){console.error("Error navigating to route:",o)}},x=()=>{window.localStorage.setItem("templateManagement",!1),xe({}),s.value=!1,t("changeView","exit")};return D(()=>g.path,()=>{g.matched&&g.matched.length>0&&(u.value=[g.matched[0].path])},{immediate:!0}),(h,o)=>{const p=Be,d=Oe,b=Re,v=bt;return k(),O(v,{class:"header clearfix"},{default:C(()=>[o[2]||(o[2]=w("div",{class:"logo pullLeft"},[w("img",{src:_t,alt:"logo"})],-1)),w("div",Ht,[c(d,{selectedKeys:u.value,"onUpdate:selectedKeys":o[0]||(o[0]=m=>u.value=m),theme:"dark",mode:"horizontal",onSelect:i},{default:C(()=>[(k(!0),A(ae,null,re(y.value,m=>(k(),O(p,{key:m.path},{default:C(()=>[ie(J(h.$t(m.meta.title)),1)]),_:2},1024))),128))]),_:1},8,["selectedKeys"])]),w("div",Tt,[s.value?(k(),A("div",Ot,[w("span",{class:"exitTemp",onClick:x},"返回")])):U("",!0),c(at),s.value?U("",!0):(k(),A("div",Bt,[(k(!0),A(ae,null,re(S.value,m=>(k(),O(b,{key:m.path,placement:"bottom",overlayClassName:"manageClass",arrow:""},{overlay:C(()=>[c(d,null,{default:C(()=>[(k(!0),A(ae,null,re(m.children,_=>(k(),O(p,{key:_.path},{default:C(()=>[_.icon?(k(),O(Se(_.icon),{key:0})):U("",!0),w("a",{href:"javascript:;",onClick:H=>f(_),class:"dropMenuItem"},J(h.$t(_.meta.title)),9,Rt)]),_:2},1024))),128))]),_:2},1024)]),default:C(()=>[w("a",{class:"dropdown-link",onClick:o[1]||(o[1]=Xe(()=>{},["prevent"]))},[m.meta&&m.meta.icon?(k(),O(Se(m.meta.icon),{key:0})):U("",!0)])]),_:2},1024))),128))])),w("div",Mt,[c(Lt)])])]),_:1,__:[2]})}}},Ut=He(Nt,[["__scopeId","data-v-7b1740f4"]]);export{Ut as H,Gt as L,Xt as _,Vt as a};
