import{u as Me,c as Ne,W as F}from"./table-C_s53ALS.js";import{O as We}from"./index-DyCH3qIX.js";import{W as _e}from"./index-R80zldKD.js";import{r as v,u as xe,y as Fe,j as oe,w as Se,f as se,d as I,z as de,o as k,c as R,b as w,i as me,F as Ve,e as Pe,g as B,t as qe,s as be,m as c,aP as he}from"./index-D9CxWmlM.js";import{S as Ge,f as ne}from"./tools-DZBuE28U.js";import{u as Ke}from"./configDevice-ce3VhjpI.js";import{u as Ee}from"./model-CfkjPzDn.js";import{u as $e}from"./devTree-BD8aiOqS.js";import{_ as Be}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ze,a as je}from"./index-pF9HjW4G.js";import{B as Ye}from"./index-BGEB0Rhf.js";import{M as Ue}from"./index-DhZUqjZm.js";import"./ActionButton-BRQ4acFZ.js";import"./styleChecker-z-opWSaj.js";import"./initDefaultProps-C2vKchlZ.js";import"./shallowequal-vFdacwF3.js";import"./index-DMbgFMzZ.js";import"./index-RBozNpIx.js";import"./index-BS38043a.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";/* empty css                                                              */const He={class:"border"},Je={key:0,class:"editForm"},Qe={key:0,class:"getMeasLocName"},Xe={key:0,class:"setNameBtn"},Ze={key:2},De="YYYY-MM-DD",z=320,et={__name:"device",setup(tt){const a=Ke();Ee();const j=$e(),fe=Me(),Y=[{label:"晃度倾角",value:"svm",text:"晃度倾角"},{label:"modbus",value:"modbus",text:"modbus"}],S=()=>[{title:"振动测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,isrequired:!0,afterContent:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,columnWidth:200,isrequired:!0,headerOperations:{filters:[],filterDataIndex:["devTurComponent","componentName"]},inputType:"select",selectOptions:[],hasChangeEvent:!0,customRender:({record:e})=>e.devTurComponent&&e.devTurComponent.componentName?e.devTurComponent.componentName:""},{title:"截面",dataIndex:"sectionName",inputType:"select",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}}],V=e=>[{title:"电流电压测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,afterContent:!0,isrequired:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,isrequired:!0,columnWidth:200,inputType:"select",selectOptions:[],hasChangeEvent:!0,headerOperations:{filters:[],filterDataIndex:["devTurComponent","componentName"]},customRender:({record:t})=>t.devTurComponent&&t.devTurComponent.componentName?t.devTurComponent.componentName:""},{title:"截面",dataIndex:"sectionName",inputType:"select",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",columnWidth:200,isrequired:!0,selectOptions:[],headerOperations:{filters:[]}}],U=e=>[{title:"转速测量位置名称",dataIndex:"measLocName",columnWidth:80,isrequired:!0},{title:"变速比",dataIndex:"gearRatio",columnWidth:100,isrequired:!0},{title:"编码器线数",dataIndex:"lineCounts",columnWidth:70,isrequired:!0}],P=e=>[{title:"工况测量位置名称",dataIndex:"measLocName",width:300,columnWidth:220,afterContent:!0,isrequired:!0},{title:"工况参数",dataIndex:"mDFWorkData",labelInValue:!0,columnWidth:220,inputType:"select",selectOptions:[],isrequired:!0,...e?{}:{customRender:({record:t})=>{if(!a.workCondMeasLocDicOptions||!a.workCondMeasLocDicOptions.length)return t.param_Type_Code||"";const s=a.workCondMeasLocDicOptions.find(n=>n.value==t.param_Type_Code);return s?s.label:t.param_Type_Code}}},{title:"数据来源",dataIndex:"datafrom",labelInValue:!0,inputType:"select",columnWidth:220,selectOptions:[],isrequired:!0,headerOperations:{filters:a.enumWorkConDataSourceOptions},...e?{}:{customRender:({text:t,record:s})=>{if(!a.enumWorkConDataSourceOptions||!a.enumWorkConDataSourceOptions.length)return t;const n=a.enumWorkConDataSourceOptions.find(l=>l.value==s.fieldBusType);return n?n.label:t}}}],q=()=>[{title:"测量位置名称",dataIndex:"measLocName",width:260,columnWidth:180,isrequired:!0,afterContent:!0,align:"left"},{title:"部件",dataIndex:"componentID",labelInValue:!0,isrequired:!0,headerOperations:{filters:[],filterDataIndex:["componentName"]},columnWidth:160,inputType:"select",selectOptions:[],hasChangeEvent:!0,customRender:({record:e})=>e.componentName||""},{title:"截面",dataIndex:"sectionName",columnWidth:160,inputType:"selectinput",isrequired:!0,selectOptions:[],headerOperations:{filters:[]}},{title:"方向",dataIndex:"orientation",inputType:"selectinput",isrequired:!0,selectOptions:[],headerOperations:{filters:[]},columnWidth:160},{title:"测量位置类型",dataIndex:"measType",inputType:"select",isrequired:!0,selectOptions:Y,headerOperations:{filters:Y},customRender:({text:e,record:t})=>{const s=Y.find(n=>n.value==t.measType);return s?s.label:e}},{title:"物理量",dataIndex:"physicalType",inputType:"select",selectOptions:[],headerOperations:{filters:[]}}],le=xe(),H=v(""),A=v(null),O=v(""),f=v(""),G=v(),h=v({}),i=v(le.params.id),p=v(""),J=v(!1),D=v([]),Q=v(!1),o=Fe({table1Columns:S(),table2Columns:V(),table3Columns:U(),table4Columns:P(),table5Columns:q(),tableDatas1:[],tableDatas2:[],tableDatas3:[],tableDatas4:[],tableDatas5:[],batchApplyData:[],batchApplyKey:"",bathApplyResponse1:{},bathApplyResponse2:{},bathApplyResponse3:{},bathApplyResponse4:{},bathApplyResponse5:{}}),ye=oe(()=>{switch(p.value){case"1":return["measLocName","componentID"];case"2":return["measLocName","componentID"];case"3":return["measLocName"];case"4":return["measLocName","mDFWorkData"];case"5":return["measLocName","componentID"];default:return[]}}),ve=oe(()=>{switch(p.value){case"1":return["measLocName"];case"2":return["measLocName"];case"3":return["measLocName"];case"4":return["measLocName"];case"5":return["measLocName"];default:return[]}}),we=()=>{let e=j.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(H.value=e[e.length-2].id)},ie=(e={})=>[{label:"设备名称",value:e.windTurbineName},{label:"设备编号",value:e.windTurbineCode},{label:"设备型号",value:e.windTurbineModel},{label:"投运日期",value:e.operationalDate?de(e.operationalDate).format(De):""},{label:"额定功率(KW)",value:e.ratedPower},{label:"设备厂商",value:e.wTurbineModel&&e.wTurbineModel.manufactory?e.wTurbineModel.manufactory:""}],re=v([ie({})]),ce=async e=>{if(J.value=!0,i.value){const t=await a.fetchDeviceInfo({turbineID:i.value});re.value=ie(t)}J.value=!1},Ie=async e=>{H.value&&await fe.fetchDevTreedDevicelist({windParkID:H.value,useTobath:!0})},Te=oe(()=>f.value==="batchAdd"?"1200px":"600px"),K=async e=>{i.value&&(o.tableDatas1=await a.fetchGetVibMeaslocation({turbineID:i.value}),o.table1Columns=S())},E=async e=>{i.value&&(o.tableDatas2=await a.fetchGetProcessMeaslocation({turbineID:i.value}),o.table2Columns=V())},$=async e=>{i.value&&(o.tableDatas4=await a.fetchGetWorkCondMeasLocs({turbineID:i.value}),o.table4Columns=P())},X=async e=>{i.value&&(o.tableDatas3=await a.fetchGetRotSpdMeasLocList({turbineID:i.value}))},Z=async e=>{i.value&&(o.tableDatas5=await a.fetchGetModbusMeasLocList({turbineID:i.value}),o.table5Columns=q())},M=async()=>{(!a.componentList||a.componentList.length<1)&&await a.fetchGetComponentList({turbineID:i.value})},N=async()=>{(!a.orientatioList||a.orientatioList.length<1)&&await a.fetchGetOrientationList()},ue=async()=>{(!a.sVMParamType||a.sVMParamType.length<1)&&await a.fetchGetSVMParamType()},ee=async()=>{(!a.workCondMeasLocDicOptions||a.workCondMeasLocDicOptions.length<1)&&await a.fetchGetWorkCondMeasLocDic()},te=async()=>{(!a.enumWorkConDataSourceOptions||a.enumWorkConDataSourceOptions.length<1)&&await a.fetchGetEnumWorkConDataSource()};Se(()=>le.params.id,async e=>{e&&(a.reset(),i.value=e,we(),await Ie(),ce(),K(),E(),await te(),await ee(),$(),X(),Z())},{immediate:!0});const Le=async()=>{f.value="editDevice",O.value="编辑设备信息";const e=a.deviceInfo;D.value=[...Ae];let t=e.operationalDate?de(e.operationalDate,De):"";h.value={...e,operationalDate:t,windTurbineCode:e.windTurbineCode,windTurbineModel:e.windTurbineModel,windTurbineName:e.windTurbineName},ae()},W=async e=>{const{tableKey:t,title:s,operateType:n}=e;switch(f.value=n,p.value=t,O.value="添加"+s.split("列表")[0],t){case"1":await M(),await N();let l=S();l[1].selectOptions=[...a.componentList],l[3].selectOptions=[...a.orientatioList],D.value=[...l];break;case"2":await M(),await N();let d=V();d[1].selectOptions=[...a.componentList],d[3].selectOptions=[...a.orientatioList],D.value=[...d];break;case"3":let m=U();D.value=[...m],h.value={measLocName:"发电机转速"};break;case"4":await ee(),await te();let u=P(!0);u[1].selectOptions=[...a.workCondMeasLocDicOptions],u[2].selectOptions=[...a.enumWorkConDataSourceOptions],D.value=[...u];break;case"5":await M(),await N(),await ue();let r=q();r[1].selectOptions=[...a.componentList],r[3].selectOptions=[...a.orientatioList],r[5].selectOptions=[...a.sVMParamType],D.value=[...r];break}ae()},_=async e=>{const{tableKey:t,selectedkeys:s}=e;if(!(!e||!s||!s.length))switch(t){case"1":const n=await a.fetchDeleteVibMeasLocs({sourceData:s,targetTurbineIds:o.batchApplyData});n&&n.code===1?(K(),o.bathApplyResponse1=n.batchResults||{},c.success("删除成功")):c.error("删除失败:"+n.msg);break;case"2":const l=await a.fetchDeleteProcessMeasLocs({sourceData:s,targetTurbineIds:o.batchApplyData});l&&l.code===1?(E(),o.bathApplyResponse2=l.batchResults||{},c.success("删除成功")):c.error("删除失败:"+l.msg);break;case"3":let m={sourceData:[{WindTurbineID:i.value,MeasLocationID:s[0]}],targetTurbineIds:o.batchApplyData};const u=await a.fetchDeleteRotSpdLoc(m);u&&u.code===1?(X(),o.bathApplyResponse3=u.batchResults||{},c.success("删除成功")):c.error("删除失败:"+u.msg);break;case"4":let r=s.map(L=>({measLocationID:L,windTurbineID:i.value}));const y=await a.fetchDeleteWorkingConditionMeasBatch({sourceData:r,targetTurbineIds:o.batchApplyData});y&&y.code===1?(o.bathApplyResponse4=y.batchResults||{},$(),c.success("删除成功")):c.error("删除失败:"+y.msg);break;case"5":const b=await a.fetchBatchDeleteMeasLoc({sourceData:s,targetTurbineIds:o.batchApplyData});b&&b.code===1?(o.bathApplyResponse5=b.batchResults||{},Z(),c.success("删除成功")):c.error("删除失败:"+b.msg);break}},x=async e=>{var d,m,u,r,y;const{rowData:t,tableKey:s,title:n,operateType:l}=e;switch(f.value=l,p.value=s,O.value="编辑"+n.split("列表")[0],s){case"1":case"2":let b=[];s==1?b=S():b=V(),await M(),await N();let L=await a.fetchGetSectionList({turbineID:i.value,componentName:(d=t.devTurComponent)==null?void 0:d.componentName});b[1].selectOptions=[...a.componentList],b[3].selectOptions=[...a.orientatioList],b[2].selectOptions=L,D.value=[...b],h.value={...t,componentID:{label:(m=t.devTurComponent)==null?void 0:m.componentName,value:(u=t.devTurComponent)==null?void 0:u.componentID}};break;case"3":h.value={...t},D.value=U();break;case"4":await ee(),await te();let g=P(!0);g[1].selectOptions=[...a.workCondMeasLocDicOptions],g[2].selectOptions=[...a.enumWorkConDataSourceOptions],D.value=[...g],h.value={...t,datafrom:{label:"",value:t.fieldBusType}};break;case"5":await M(),await N(),await ue();let C=q();C[1].selectOptions=[...a.componentList],C[3].selectOptions=[...a.orientatioList],C[5].selectOptions=[...a.sVMParamType],D.value=[...C],h.value={...t,componentID:{label:(r=t.devTurComponent)==null?void 0:r.componentName,value:(y=t.devTurComponent)==null?void 0:y.componentID}};break}ae()},pe=async e=>{const{value:t,dataIndex:s,index:n}=e;if(s&&s.indexOf("componentID")>-1&&t.label){let l=[...D.value],d=await a.fetchGetSectionList({turbineID:i.value,componentName:t.label});l[2].selectOptions=d,D.value=[...l],d&&d.length>0&&(f.value=="batchAdd"?A.value.setTableFieldValue({formDataIndex:`${l[2].dataIndex}[${n}]`,tableDataIndex:l[2].dataIndex,index:n,value:d[0].value}):G.value.setFieldValue("sectionList",d[0].value))}},Ce=async e=>{var t,s,n,l;if(f.value=="editDevice"){let d={...e,componentIds:e.componentIds&&e.componentIds.length?e.componentIds.join(","):"",windTurbineID:h.value.windTurbineID,windParkId:h.value.windParkID};const m=await a.fetcheditOneDevice(d);m&&m.code===1?(ce(),e.windTurbineName!==h.value.windTurbineName&&await j.getDevTreeDatas(),await j.getDevTreeDatas(),T(),c.success("提交成功")):c.error("提交失败:"+m.msg)}else switch(p.value){case"1":case"2":let d={...e,windTurbineID:i.value,measLocationID:h.value.measLocationID,componentName:(t=e.componentID)==null?void 0:t.value,componentID:(s=e.componentID)==null?void 0:s.value},m={};p.value==1?m=await a.fetchEditVibMeasLoc(d):m=await a.fetchEditProcessMeasLoc(d),m&&m.code===1?(p.value==1?K():E(),T(),c.success("提交成功")):c.error("提交失败:"+m.msg);break;case"3":let u={...e,WindTurbineID:i.value},r={};f.value=="edit"?(u.measLocationID=h.value.measLocationID,r=await a.fetchEditRotSpdLoc({sourceData:u,targetTurbineIds:o.batchApplyData})):r=await a.fetchAddRotSpdMeaslocs({sourceData:u,targetTurbineIds:o.batchApplyData}),r&&r.code===1?(X(),o.bathApplyResponse3=r.batchResults||{},T(),c.success("提交成功")):c.error("提交失败:"+r.msg);break;case"4":let y={windTurbineID:i.value,measLocationID:h.value.measLocationID,measLocName:h.value.measLocName,datafrom:(n=e.datafrom)==null?void 0:n.label,mDFWorkData:(l=e.mDFWorkData)==null?void 0:l.label};const b=await a.fetchEditWorkingConditionMeas(y);b&&b.code===1?($(),T(),c.success("提交成功")):c.error("提交失败:"+b.msg);break}},ke=async e=>{switch(p.value){case"1":case"2":let t={windTurbineID:i.value,measLocationID:""},n=ne(e,t,{componentID:{label:"componentName",value:"componentID"}}),l={};p.value==1?l=await a.fetchAddVibMeasLocs({sourceData:n,targetTurbineIds:o.batchApplyData}):l=await a.fetchAddProcessMeasLocs({sourceData:n,targetTurbineIds:o.batchApplyData}),l&&l.code===1?(p.value==1?(K(),o.bathApplyResponse1=l.batchResults||{}):(E(),o.bathApplyResponse2=l.batchResults||{}),T(),c.success("提交成功")):c.error("提交失败:"+l.msg);break;case"4":let d={windTurbineID:i.value,measLocationID:""},u=ne(e,d,{mDFWorkData:{label:"MDFWorkData"},datafrom:{label:"datafrom"}}),r=await a.fetchAddWorkingConditionMeaslocs({sourceData:u,targetTurbineIds:o.batchApplyData});r&&r.code===1?($(),o.bathApplyResponse4=r.batchResults||{},T(),c.success("提交成功")):c.error("提交失败:"+r.msg);break;case"5":let y={windTurbineID:i.value,measLocationID:""},L=ne(e,y,{componentID:{label:"componentName",value:"componentID"}});for(let C=0;C<L.length;C++)L[C].physicalType=L[C].physicalType||"";let g=await a.fetchAddModbusMeasloc({sourceData:L,targetTurbineIds:o.batchApplyData});g&&g.code===1?(Z(),o.bathApplyResponse5=g.batchResults||{},T(),c.success("提交成功")):c.error("提交失败:"+g.msg);break}},ge=e=>{var l;const{index:t}=e,s=(l=A.value)==null?void 0:l.getTableFieldsValue();if(!s||!Object.keys(s).length||t==null)return;let n="";p.value==1||p.value==2||p.value==5?(n+=s[`componentID[${t}]`]&&s[`componentID[${t}]`].label?s[`componentID[${t}]`].label:"",n+=s[`sectionName[${t}]`]||"",n+=s[`orientation[${t}]`]||""):p.value==4?n=s[`mDFWorkData[${t}]`]&&s[`mDFWorkData[${t}]`].label?s[`mDFWorkData[${t}]`].label:"":p.value==5&&(n="发电机转速"),A.value.setTableFieldValue({formDataIndex:`measLocName[${t}]`,tableDataIndex:"measLocName",index:t,value:n}),n&&n!==""&&A.value.clearValidate(`measLocName[${t}]`)},Re=()=>{var s;const e=(s=G.value)==null?void 0:s.getFieldsValue();let t="";p.value==1||p.value==2?(t+=e.componentID&&e.componentID.label?e.componentID.label:"",t+=e.sectionName||"",t+=e.orientation||""):p.value==4&&(t=e.mDFWorkData&&e.mDFWorkData.label?e.mDFWorkData.label:""),G.value.setFieldValue("measLocName",t)},ae=()=>{Q.value=!0},T=e=>{Q.value=!1,h.value={},D.value=[],f.value="",O.value="",p.value=""},Ae=[{title:"设备编号",dataIndex:"windTurbineCode",formItemWidth:z,disabled:!0},{title:"设备名称",dataIndex:"windTurbineName",formItemWidth:z},{title:"设备型号",dataIndex:"windTurbineModel",formItemWidth:z,inputType:"select",selectOptions:[],disabled:!0},{title:"投递日期",dataIndex:"operationalDate",formItemWidth:z,inputType:"datepicker",timeFormat:"YYYY-MM-DD"}],Oe=async e=>{e.type&&e.type=="close"?(o.batchApplyData=[],o.batchApplyKey="",o[`bathApplyResponse${e.key}`]={}):(o.batchApplyData=e.turbines,o.batchApplyKey=e.key)};return he("deviceId",i),he("bathApplySubmit",Oe),(e,t)=>{const s=Ye,n=je,l=ze,d=Ue,m=Ge;return k(),se(m,{spinning:J.value,size:"large"},{default:I(()=>[(k(),R("div",{key:i.value},[w(Ne,{tableTitle:"设备信息",defaultCollapse:!0,batchApply:!1},{rightButtons:I(()=>[w(s,{type:"primary",onClick:Le},{default:I(()=>t[1]||(t[1]=[B(" 编辑 ",-1)])),_:1,__:[1]})]),content:I(()=>[me("div",He,[w(l,{column:4,size:"small"},{default:I(()=>[(k(!0),R(Ve,null,Pe(re.value,u=>(k(),se(n,{label:u.label},{default:I(()=>[B(qe(u.value),1)]),_:2},1032,["label"]))),256))]),_:1})])]),_:1}),me("div",null,[w(F,{ref:"table",size:"default","table-key":"1","table-title":"振动测量位置列表","table-columns":o.table1Columns,"table-operate":["delete","add","batchDelete","batchAdd"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="1",bathApplyResponse:o.bathApplyResponse1,"table-datas":o.tableDatas1,onAddRow:W,onDeleteRow:_,onEditRow:x,noPagination:!0},null,8,["table-columns","borderLight","bathApplyResponse","table-datas"]),w(F,{ref:"table",size:"default","table-key":"2","table-title":"电流电压测量位置列表","table-columns":o.table2Columns,bathApplyResponse:o.bathApplyResponse2,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="2","table-datas":o.tableDatas2,onAddRow:W,onDeleteRow:_,onEditRow:x,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"]),w(F,{ref:"table",size:"default","table-key":"3","table-title":"转速测量位置列表","table-columns":o.table3Columns,bathApplyResponse:o.bathApplyResponse3,"table-operate":["batchDelete",o.tableDatas3.length?"":"add","delete","edit"],borderLight:o.batchApplyKey=="3","record-key":"measLocationID","table-datas":o.tableDatas3,onAddRow:W,onDeleteRow:_,onEditRow:x,noPagination:!0},null,8,["table-columns","bathApplyResponse","table-operate","borderLight","table-datas"]),w(F,{ref:"table",size:"default","table-key":"4","table-title":"工况测量位置列表","table-columns":o.table4Columns,bathApplyResponse:o.bathApplyResponse4,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="4","table-datas":o.tableDatas4,onAddRow:W,onDeleteRow:_,onEditRow:x,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"]),w(F,{ref:"table",size:"default","table-key":"5","table-title":"modbus设备测量位置列表","table-columns":o.table5Columns,bathApplyResponse:o.bathApplyResponse5,"table-operate":["batchDelete","batchAdd","delete"],"record-key":"measLocationID",borderLight:o.batchApplyKey=="5","table-datas":o.tableDatas5,onAddRow:W,onDeleteRow:_,onEditRow:x,noPagination:!0},null,8,["table-columns","bathApplyResponse","borderLight","table-datas"])]),w(d,{maskClosable:!1,width:Te.value,open:Q.value,title:O.value,footer:"",destroyOnClose:!0,onCancel:T},{default:I(()=>[f.value==="add"||f.value==="edit"||f.value==="editDevice"?(k(),R("div",Je,[w(We,{ref_key:"formModalRef",ref:G,titleCol:D.value,initFormData:h.value,onChange:pe,onSubmit:Ce},{otherInfo:I(({formModel:u})=>[p.value=="1"||p.value=="2"||p.value=="4"?(k(),R("div",Qe,[w(s,{onClick:t[0]||(t[0]=r=>Re())},{default:I(()=>t[2]||(t[2]=[B("自动",-1)])),_:1,__:[2]})])):be("",!0)]),_:1},8,["titleCol","initFormData"])])):f.value==="batchAdd"?(k(),se(_e,{key:1,ref_key:"modalTableFormRef",ref:A,size:"default","table-key":"0","table-columns":D.value,"table-operate":["copyUp","delete"],"table-datas":[],"noCopyUp-keys":ye.value,"noRepeat-Keys":ve.value,onSubmit:ke,onHangeTableFormChange:pe,onCancel:T},{afterContent:I(({column:u,record:r,text:y,index:b})=>[u.dataIndex==="measLocName"?(k(),R("div",Xe,[w(s,{onClick:L=>ge({record:r,index:b})},{default:I(()=>t[3]||(t[3]=[B("自动",-1)])),_:2,__:[3]},1032,["onClick"])])):be("",!0)]),_:1},8,["table-columns","noCopyUp-keys","noRepeat-Keys"])):(k(),R("div",Ze))]),_:1},8,["width","open","title"])]))]),_:1},8,["spinning"])}}},Ct=Be(et,[["__scopeId","data-v-8831547d"]]);export{Ct as default};
