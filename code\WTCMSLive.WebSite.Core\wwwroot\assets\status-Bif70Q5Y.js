import{W as C}from"./table-C_s53ALS.js";import{r as b,u as D,y,w as v,c as r,b as f,d as N,o as l,i as a,g as s,t as n,s as i,A as d,z as S}from"./index-D9CxWmlM.js";import{S as x,b as A}from"./tools-DZBuE28U.js";import{u as U}from"./collectionUnitMonitor-DMECnQY9.js";import{_ as L}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DyCH3qIX.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./index-DMbgFMzZ.js";import"./shallowequal-vFdacwF3.js";import"./index-BS38043a.js";import"./ActionButton-BRQ4acFZ.js";import"./index-RBozNpIx.js";const g={class:"border padding totalInfo"},h={class:"totalNumber pullLeft"},w={class:"totalList"},M={class:"clearfix"},T={class:"parkNumber pullLeft dauNormal"},B={key:0,class:"parkNumber pullLeft dauAlarm"},z={key:1,class:"parkNumber pullLeft dauAlarm"},F={key:2,class:"parkNumber pullLeft dauAlarm"},O={key:3,class:"parkNumber pullLeft dauAlarm"},P={key:4,class:"parkNumber pullLeft dauUnknown"},R={__name:"status",setup(V){const c=U(),m=D(),p=b(m.params.id),e=y({baseInfo:{},tableData:[]}),u=b(!1),I=[{title:"设备名",dataIndex:"windTurbineName",headerOperations:{filters:[]},customRender:({text:o,record:t})=>d("a",{href:`/CollectionUnitMonitor/device/collectionUnitMonitor/${t.windTurbineID}`,style:{cursor:"pointer",textDecoration:"underline"}},o)},{title:"采集单元名称",dataIndex:"dauName",headerOperations:{filters:[]}},{title:"IP地址",dataIndex:"ipAddress"},{title:"采集单元运行状态",dataIndex:"alarmStateDescription",headerOperations:{filters:[]},customRender:({text:o,record:t})=>t.alarmState?d("span",{style:{color:A(t.alarmState).color}},o):""},{title:"更新时间",dataIndex:"statusUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({record:o})=>o.statusUpdateTime?d("span",{},S(o.statusUpdateTime).format("YYYY-MM-DD HH:mm:ss")):""}],_=async o=>{u.value=!0;let t=await c.fetchGetDAUListByParkID({WindParkID:p.value});e.baseInfo=t.statusSummary||{},e.tableData=t.dauList||[],u.value=!1};return v(()=>m.params.id,o=>{o&&(p.value=o,_())},{immediate:!0}),(o,t)=>{const k=x;return l(),r("div",null,[f(k,{spinning:u.value,size:"large"},{default:N(()=>[a("div",g,[a("p",h,[t[0]||(t[0]=s("采集单元总数(台) ",-1)),a("b",null,n(e.baseInfo.totalCount||""),1)]),a("div",w,[a("ul",M,[a("li",T,[t[1]||(t[1]=s("正常",-1)),a("b",null,n(e.baseInfo.normalCount||""),1)]),e.baseInfo.sensorFaultCount?(l(),r("li",B,[t[2]||(t[2]=s("传感器故障",-1)),a("b",null,n(e.baseInfo.sensorFaultCount||""),1)])):i("",!0),e.baseInfo.communicationErrorCount?(l(),r("li",z,[t[3]||(t[3]=s("通讯异常",-1)),a("b",null,n(e.baseInfo.communicationErrorCount||""),1)])):i("",!0),e.baseInfo.rotSpdFaultCount?(l(),r("li",F,[t[4]||(t[4]=s("转速异常",-1)),a("b",null,n(e.baseInfo.rotSpdFaultCount||""),1)])):i("",!0),e.baseInfo.noDataArriveCount?(l(),r("li",O,[t[5]||(t[5]=s("无数据到达",-1)),a("b",null,n(e.baseInfo.noDataArriveCount||""),1)])):i("",!0),e.baseInfo.unknownCount?(l(),r("li",P,[t[6]||(t[6]=s("未知",-1)),a("b",null,n(e.baseInfo.unknownCount||""),1)])):i("",!0)])])]),a("div",null,[f(C,{ref:"table",size:"default","table-key":"0","table-title":"采集单元状态列表",noheader:!0,"table-columns":I,"record-key":"ModbusUnitID","table-datas":e.tableData,noBatchApply:!0,defaultPageSize:50},null,8,["table-columns","table-datas"])])]),_:1},8,["spinning"])])}}},ot=L(R,[["__scopeId","data-v-46dabd70"]]);export{ot as default};
