﻿using WTCMSLive.WebSite.Models;
using CMSFramework.BusinessEntity;
using WTCMSLive.BusinessModel;
using Microsoft.AspNetCore.Mvc;

namespace WTCMSLive.WebSite.Controllers
{
    public class EigenValueController : Controller
    {
        //
        // GET: /EigenValue/

        public ActionResult SupervisoryEV(string windParkID, string turbineID)
        {
            ViewBag.info = windParkID + "/" + turbineID;
            WindTurbine turbine = DevTreeManagement.GetWindTurbine(turbineID);
            ViewData["NodePath"] = Utility.ToJson(new object[]
            {
                new []{Resources.Message.CompanyName,"/OverViewPage/Index"},
                new []{turbine.DevWindPark.WindParkName,"/WindPark/Index/"+windParkID},
                new []{turbine.WindTurbineName,""}
            });
            ViewData["TurbineID"] = turbineID;
            ViewData["WindParkID"] = windParkID;
            TreeManager treeManager = new TreeManager();
            ViewData["leftTree"] = treeManager.GetWindTurbineTreeModel().ToJson();
            WindDAU dau = DauManagement.GetDAUById(turbineID);
            ViewData["DAUState"] = false;
            if (dau != null)
            {
                ViewData["DAUState"] = dau.IsAvailable;
            }
            return View();
        }


        public string GetMeasLocListByTurbineId(string turbineID)
        {
            List<MeasLoc_Vib> list = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
            return list.OrderBy(item => item.OrderSeq).ToJson();
        }

        /// <summary>
        /// AJAX方法调用获取，IE不支持EventSource方法获取后台数据
        /// </summary>
        /// <param name="turbineID"></param>
        /// <returns></returns>
        //public string GetSupervisoryAJAX(string turbineID)
        //{
        //    return GetSupervisoryEVData(turbineID);
        //}

        //public string GetSupervisoryEVData(string turbineID)
        //{
        //    List<EigenValueData_Vib> dataList = null;
        //    try
        //    {
        //        using var client = WindCMS.SupervisoryEV.SupervosiryEVDataClient.GetClient();
        //        dataList = client.GetSupervosiryEV(turbineID);
        //    }
        //    catch (Exception ex)
        //    {
        //        //如果监视特征值获取失败，记录日志，并返回空列表
        //        CMSFramework.Logger.Logger.LogErrorMessage("[GetSupervisoryEVData][client]获取数据失败", ex);
        //        dataList = new List<EigenValueData_Vib>();
        //        return dataList.ToJson();
        //    }
        //    List<SupervisoryEVModel> SupervisoryEVList = new List<SupervisoryEVModel>();
        //    if (dataList != null && dataList.Count > 0)
        //    {
        //        List<MeasLoc_Vib> list = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
        //        dataList.ForEach(item =>
        //        {
        //            string eigenUnit = "";
        //            if (item.EigenValueCode.IndexOf("VRMS") > -1)
        //            {
        //                eigenUnit = "( mm/s )";
        //            }
        //            else if (item.EigenValueCode.IndexOf("RMS") > -1 || item.EigenValueCode.IndexOf("PK") > -1 || item.EigenValueCode.IndexOf("PPK") > -1)
        //            {
        //                eigenUnit = "( m/s^2 )";
        //            }
        //            double eigenValue = Math.Round(item.Eigen_Value, 3);
        //            SupervisoryEVList.Add(new SupervisoryEVModel()
        //            {
        //                measLocationID = item.MeasLocationID,
        //                measLocName = list.Find(meas => meas.MeasLocationID == item.MeasLocationID).MeasLocName + eigenUnit,
        //                iOrderSeq = list.Find(meas => meas.MeasLocationID == item.MeasLocationID).OrderSeq,
        //                ChartData = new ChartOptionData()
        //                {
        //                    evNameList = new List<string>() { CMSFramework.EigenValueDef.EVNameUtility.GetEVNameByEVCode(item.EigenValueCode) + "(" + eigenValue + ")" },
        //                    evValueList = new List<double>() { eigenValue }
        //                }
        //            });
        //        });
        //    }
        //    //获取到的数据，先按测量位置排序
        //    string SupervisortEvData = SupervisoryEVList.OrderBy(item => item.iOrderSeq).ToJson();
        //    return SupervisortEvData;
        //}

        /// <summary>
        /// 获取通用特征值列表
        /// </summary>
        /// <returns></returns>
        public string GetEvCodeList()
        {
            var res = EigenValueManager.GetGeneralEigenValueCodeList();
            return res.ToJson();
        }

        public string GetSVMEvCodeList()
        {
            var res = EigenValueManager.GetSVMEigenValueCodeList();
            return res.ToJson();
        }

    }
}
