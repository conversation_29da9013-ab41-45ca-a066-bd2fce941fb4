﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace WTCMSLive.BusinessEntityConvert
{
    [DataContract(Namespace = "http://WTCMSLive.BusinessEntity/")]
    public class EVDataTrendMeasEvent_bussiness
    {
        public string WindTurbineID { get; set; }
        public int MeasDefinitionID { get; set; }
        public System.DateTime AcquisitionTime { get; set; }
        public short WkConDataNum { get; set; }
        public Nullable<short> EigenValueNum { get; set; }
        public Nullable<short> EigenValueNumSVM { get; set; }
        public Nullable<short> WkConLevelCode { get; set; }
        public Nullable<short> AlarmDegree { get; set; }
        public Nullable<short> DataQualType { get; set; }
        public List<EVDataTrend_bussiness> EVDataTrends { get; set; }
        public List<SVMEVDataTrend_bussiness> SVMEVDataTrends { get; set; }
    }
    /// <summary>
    /// 特征值趋势数据
    /// </summary>
    [DataContract(Namespace = "http://WTCMSLive.BusinessEntity/")]
    public class EVDataTrend_bussiness
    {
        public DateTime AcquisitionTime { get; set; }
        public short? AlarmDegree { get; set; }
        public short DataQualType { get; set; }
        public decimal? EigenValue { get; set; }
        public string EigenValueCode { get; set; }
        public string EigenValueID { get; set; }
        public int MeasDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public int WaveDefinitionID { get; set; }
        public string WindTurbineID { get; set; }
        public short WkConLevelCode { get; set; }
    }

    /// <summary>
    /// 晃度仪趋势数据
    /// </summary>
    [DataContract(Namespace = "http://WTCMSLive.BusinessEntity/")]
    public class SVMEVDataTrend_bussiness
    {
        public DateTime AcquisitionTime { get; set; }
        public short? AlarmDegree { get; set; }
        public short DataQualType { get; set; }
        public decimal? EigenValue { get; set; }
        public string EigenValueID { get; set; }
        public int MeasDefinitionID { get; set; }
        public string MeasLocationID { get; set; }
        public short? ParamType { get; set; }
        public int? SVMRegister { get; set; }
        public int WaveDefinitionID { get; set; }
        public string WindTurbineID { get; set; }
        public short? WkConLevelCode { get; set; }
    }
}
