﻿namespace WTCMSLive.WebSite.Models
{
    public class WaveChartModel
    {
        public int OrderSeq { get; set; }
        //x轴
        public string TreadName { get; set; }
        public string[] XAxis { get; set; }
        public List<YAxis> YAxisList { get; set; }
        public string[] DataName { get; set; }
        /// <summary>
        /// 数据格式化 时域'{value} g' 频域 {value}(Hz)
        /// </summary>
        public string Formart { get; set; }
        /// <summary>
        /// 频域{value}幅值(g）
        /// </summary>
        public string Yformart { get; set; }
    }
    public class YAxis
    {
        public string Name { get; set; }
        public string Type { get; set; }
        public double[] Data { get; set; }
    }
}