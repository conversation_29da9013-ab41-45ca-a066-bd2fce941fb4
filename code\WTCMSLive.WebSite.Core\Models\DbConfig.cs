﻿using System.Security.Cryptography;
using System.Text;

namespace WTCMSLive.WebSite.Models
{
    class DbConfig
    {
        //主数据库
        public static string DbConnName
        {
            get { return GetStr(); }
        }
        private static string _DbConnName;
        /// <summary>
        /// 趋势分表
        /// </summary>
        public static string DBConnNameTrend
        {
            get { return GetStrTrend(); }
        }
        private static string _DbConnNameTrend;

        public static string DbConnNameRt
        {
            get { return GetStrRt(); }
        }
        private static string _DbConnNameRt;
        private static string GetStrRt()
        {
            if (string.IsNullOrEmpty(_DbConnNameRt))
            {
                var connect = System.Configuration.ConfigurationManager.ConnectionStrings["ContextRt"];
                if (connect != null)
                {
                    string connetStr = connect.ConnectionString;
                    //明文字符串，不做解码
                    if (connetStr.StartsWith("server"))
                    {
                        _DbConnNameRt = connetStr;
                    }
                    else
                    {
                        _DbConnNameRt = DecryptDES(connetStr);
                    }
                }
            }
            return _DbConnNameRt;
        }

        private static string GetStrTrend()
        {
            if (string.IsNullOrEmpty(_DbConnNameTrend))
            {
                var connect = System.Configuration.ConfigurationManager.ConnectionStrings["ContextTrend"];
                if (connect != null)
                {
                    string connetStr = connect.ConnectionString;
                    //明文字符串，不做解码
                    if (connetStr.StartsWith("server"))
                    {
                        _DbConnNameTrend = connetStr;
                    }
                    else
                    {
                        _DbConnNameTrend = DecryptDES(connetStr);
                    }
                }
            }
            return _DbConnNameTrend;
        }

        private static string GetStr()
        {
            if (string.IsNullOrEmpty(_DbConnName))
            {
                var connect = System.Configuration.ConfigurationManager.ConnectionStrings["Context"];
                if (connect != null)
                {
                    string connetStr = connect.ConnectionString;
                    //明文字符串，不做解码
                    if (connetStr.StartsWith("server"))
                    {
                        _DbConnName = connetStr;
                    }
                    else
                    {
                        _DbConnName = DecryptDES(connetStr);
                    }
                }
            }
            return _DbConnName;
        }
        /// <summary>
        /// 解密链接字符串
        /// </summary>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        private static string DecodeConnectString(string connectionString)
        {
            return connectionString;
        }

        private static byte[] Keys = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };

        private static string encryptKey = "201708241327";

        /// <summary>
        /// DES解密字符串
        /// </summary>
        /// <param name="decryptString">待解密的字符串</param>
        /// <param name="decryptKey">解密密钥,要求为8位,和加密密钥相同</param>
        /// <returns>解密成功返回解密后的字符串，失败返源串</returns>
        public static string DecryptDES(string decryptString)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(encryptKey.Substring(0, 8));
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Convert.FromBase64String(decryptString);
                DESCryptoServiceProvider DCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, DCSP.CreateDecryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Encoding.UTF8.GetString(mStream.ToArray());
            }
            catch
            {
                return decryptString;
            }
        }


        public static string EncryptDES(string encryptString)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(encryptKey.Substring(0, 8));
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Encoding.UTF8.GetBytes(encryptString);
                DESCryptoServiceProvider DCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, DCSP.CreateEncryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Convert.ToBase64String(mStream.ToArray());
            }
            catch
            {
                return encryptString;
            }
        }


    }
}
