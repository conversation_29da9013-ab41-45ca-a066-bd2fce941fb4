<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务ID查询工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #005a9e;
        }
        .service-list {
            margin-top: 20px;
        }
        .service-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            background: #fafafa;
        }
        .service-id {
            font-family: monospace;
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            color: #495057;
        }
        .service-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .service-path {
            color: #666;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .service-description {
            color: #777;
            font-size: 13px;
            font-style: italic;
        }
        .copy-btn {
            background: #28a745;
            font-size: 12px;
            padding: 4px 8px;
            margin-left: 10px;
        }
        .copy-btn:hover {
            background: #218838;
        }
        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 15px;
            font-size: 16px;
        }
        .status-info {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .error-info {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>服务ID查询工具</h1>
        
        <div>
            <button onclick="loadServices()">刷新服务列表</button>
            <button onclick="exportIds()">导出所有ID</button>
            <button onclick="generateNewId()">生成新ID</button>
        </div>

        <div id="statusInfo" style="display: none;"></div>
        
        <input type="text" id="searchBox" class="search-box" placeholder="搜索服务名称或ID..." onkeyup="filterServices()">
        
        <div id="serviceList" class="service-list"></div>
    </div>

    <script>
        let allServices = [];

        // 页面加载时自动获取服务列表
        window.addEventListener('load', function() {
            loadServices();
        });

        // 加载服务列表
        async function loadServices() {
            try {
                showStatus('正在加载服务列表...', 'info');
                
                const response = await fetch('/api/ServerConf/GetServiceConfigs');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                allServices = await response.json();
                displayServices(allServices);
                
                showStatus(`成功加载 ${allServices.length} 个服务配置`, 'success');
                setTimeout(() => hideStatus(), 3000);
                
            } catch (error) {
                console.error('加载服务列表失败:', error);
                showStatus(`加载失败: ${error.message}`, 'error');
                allServices = [];
                displayServices([]);
            }
        }

        // 显示服务列表
        function displayServices(services) {
            const container = document.getElementById('serviceList');
            
            if (services.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">没有找到服务配置</p>';
                return;
            }

            container.innerHTML = '';
            
            services.forEach(service => {
                const serviceEl = document.createElement('div');
                serviceEl.className = 'service-item';
                
                serviceEl.innerHTML = `
                    <div class="service-name">${service.serviceName}</div>
                    <div>
                        <strong>Service ID:</strong> 
                        <span class="service-id">${service.serviceId}</span>
                        <button class="copy-btn" onclick="copyToClipboard('${service.serviceId}')">复制</button>
                    </div>
                    <div class="service-path"><strong>路径:</strong> ${service.servicePath}</div>
                    ${service.description ? `<div class="service-description">${service.description}</div>` : ''}
                    <div style="margin-top: 8px; font-size: 12px; color: #999;">
                        启用: ${service.isEnabled ? '是' : '否'} | 
                        自动启动: ${service.autoStart ? '是' : '否'} | 
                        创建时间: ${new Date(service.createdTime).toLocaleString()}
                    </div>
                `;
                
                container.appendChild(serviceEl);
            });
        }

        // 过滤服务
        function filterServices() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            
            if (!searchTerm) {
                displayServices(allServices);
                return;
            }

            const filteredServices = allServices.filter(service => 
                service.serviceName.toLowerCase().includes(searchTerm) ||
                service.serviceId.toLowerCase().includes(searchTerm) ||
                (service.description && service.description.toLowerCase().includes(searchTerm)) ||
                service.servicePath.toLowerCase().includes(searchTerm)
            );

            displayServices(filteredServices);
        }

        // 复制到剪贴板
        async function copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                showStatus(`已复制到剪贴板: ${text}`, 'success');
                setTimeout(() => hideStatus(), 2000);
            } catch (err) {
                console.error('复制失败:', err);
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus(`已复制到剪贴板: ${text}`, 'success');
                setTimeout(() => hideStatus(), 2000);
            }
        }

        // 导出所有ID
        function exportIds() {
            if (allServices.length === 0) {
                showStatus('没有服务可导出', 'error');
                return;
            }

            const exportData = allServices.map(service => ({
                serviceId: service.serviceId,
                serviceName: service.serviceName,
                servicePath: service.servicePath,
                isEnabled: service.isEnabled,
                autoStart: service.autoStart
            }));

            const jsonString = JSON.stringify(exportData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `service-ids-${new Date().toISOString().slice(0, 10)}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('服务ID列表已导出', 'success');
            setTimeout(() => hideStatus(), 2000);
        }

        // 生成新的UUID
        function generateNewId() {
            const uuid = generateUUID();
            const timestamp = new Date().toISOString().slice(0, 19).replace('T', '_').replace(/:/g, '-');
            const readableId = `service-${timestamp}`;
            
            const message = `
                <strong>生成的新ID选项：</strong><br>
                <div style="margin: 10px 0;">
                    <strong>UUID格式:</strong> 
                    <span class="service-id">${uuid}</span>
                    <button class="copy-btn" onclick="copyToClipboard('${uuid}')">复制</button>
                </div>
                <div style="margin: 10px 0;">
                    <strong>可读格式:</strong> 
                    <span class="service-id">${readableId}</span>
                    <button class="copy-btn" onclick="copyToClipboard('${readableId}')">复制</button>
                </div>
                <small>建议使用可读格式，如: your-project-service-001</small>
            `;
            
            showStatus(message, 'info');
        }

        // 生成UUID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // 显示状态信息
        function showStatus(message, type) {
            const statusEl = document.getElementById('statusInfo');
            statusEl.innerHTML = message;
            statusEl.className = type === 'error' ? 'error-info' : 'status-info';
            statusEl.style.display = 'block';
        }

        // 隐藏状态信息
        function hideStatus() {
            const statusEl = document.getElementById('statusInfo');
            statusEl.style.display = 'none';
        }
    </script>
</body>
</html>
