﻿using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace CMSFramework.BusinessEntity
{
    public class ConfigInfo
    {

        #region 旧Mysql配置
        ////public static string DbConnName = "name=Context";
        ////主数据库
        //public static string DBConnName
        //{
        //    get { return GetStr(); }
        //}
        //private static string _DbConnName;
        ///// <summary>
        ///// 趋势分表
        ///// </summary>
        //public static string DBConnNameTrend
        //{
        //    get { return GetStrTrend(); }
        //}
        //private static string _DbConnNameTrend;

        //public static string DbConnNameRt
        //{
        //    get { return GetStrRt(); }
        //}
        //private static string _DbConnNameRt;
        //private static string GetStrRt()
        //{
        //    if (string.IsNullOrEmpty(_DbConnNameRt))
        //    {
        //        var connect = System.Configuration.ConfigurationManager.ConnectionStrings["ContextRt"];
        //        if (connect != null)
        //        {
        //            string connetStr = connect.ConnectionString;
        //            //明文字符串，不做解码
        //            if (connetStr.StartsWith("server"))
        //            {
        //                _DbConnNameRt = connetStr;
        //            }
        //            else
        //            {
        //                _DbConnNameRt = DecryptDES(connetStr);
        //            }
        //        }
        //    }
        //    return _DbConnNameRt;
        //}

        //private static string GetStrTrend()
        //{
        //    if (string.IsNullOrEmpty(_DbConnNameTrend))
        //    {
        //        var connect = System.Configuration.ConfigurationManager.ConnectionStrings["ContextTrend"];
        //        if (connect != null)
        //        {
        //            string connetStr = connect.ConnectionString;
        //            //明文字符串，不做解码
        //            if (connetStr.StartsWith("server"))
        //            {
        //                _DbConnNameTrend = connetStr;
        //            }
        //            else
        //            {
        //                _DbConnNameTrend = DecryptDES(connetStr);
        //            }
        //        }
        //    }
        //    return _DbConnNameTrend;
        //}

        //private static string GetStr()
        //{
        //    if (string.IsNullOrEmpty(_DbConnName))
        //    {
        //        var connect = System.Configuration.ConfigurationManager.ConnectionStrings["Context"];
        //        if (connect != null)
        //        {
        //            string connetStr = connect.ConnectionString;
        //            //明文字符串，不做解码
        //            if (connetStr.StartsWith("server"))
        //            {
        //                _DbConnName = connetStr;
        //            }
        //            else
        //            {
        //                _DbConnName = DecryptDES(connetStr);
        //            }
        //        }
        //    }
        //    return _DbConnName;
        //}
        ///// <summary>
        ///// 解密链接字符串
        ///// </summary>
        ///// <param name="connectionString"></param>
        ///// <returns></returns>
        //private static string DecodeConnectString(string connectionString)
        //{
        //    return connectionString;
        //}

        //private static byte[] Keys = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };

        //private static string encryptKey = "201708241327";

        ///// <summary>
        ///// DES解密字符串
        ///// </summary>
        ///// <param name="decryptString">待解密的字符串</param>
        ///// <param name="decryptKey">解密密钥,要求为8位,和加密密钥相同</param>
        ///// <returns>解密成功返回解密后的字符串，失败返源串</returns>
        //private static string DecryptDES(string decryptString)
        //{
        //    try
        //    {
        //        byte[] rgbKey = Encoding.UTF8.GetBytes(encryptKey.Substring(0, 8));
        //        byte[] rgbIV = Keys;
        //        byte[] inputByteArray = Convert.FromBase64String(decryptString);
        //        DESCryptoServiceProvider DCSP = new DESCryptoServiceProvider();
        //        MemoryStream mStream = new MemoryStream();
        //        CryptoStream cStream = new CryptoStream(mStream, DCSP.CreateDecryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
        //        cStream.Write(inputByteArray, 0, inputByteArray.Length);
        //        cStream.FlushFinalBlock();
        //        return Encoding.UTF8.GetString(mStream.ToArray());
        //    }
        //    catch
        //    {
        //        return decryptString;
        //    }
        //}

        #endregion

        private static DbConfigProvider dbConfigProvider = null;
        private static DbConfigProvider dbTrendConfigProvider = null;
        private static DbConfigProvider dbRtConfigProvider = null;

        private static IConfiguration _configuration;

        //static ConfigInfo()
        //{
        //    dbConfigProvider = new DbConfigProvider("Context");
        //    dbTrendConfigProvider = new DbConfigProvider("ContextTrend");
        //}

        public static void SetConfiguration(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// 一般库
        /// </summary>
        public static string DBConnName
        {
            get
            {
                //return dbConfigProvider.GetDbConnectionString();
                return DbConfigProvider.GetDbConnectionDecryptString(_configuration.GetConnectionString("Context"));
            }
        }

        public static string DBConnNameStr
        {
            get
            {
                return dbConfigProvider.GetDbConnectionOriginalString();
            }
        }

        public static string DBConnProviderName
        {
            get
            {
                return dbConfigProvider.GetDBProviderName();
            }
        }

        /// <summary>
        /// 趋势库
        /// </summary>
        public static string DBConnNameTrend
        {
            get
            {
                return DbConfigProvider.GetDbConnectionDecryptString(_configuration.GetConnectionString("ContextTrend"));
                //return dbTrendConfigProvider.GetDbConnectionString();
            }
        }

        /// <summary>
        /// 实时库
        /// </summary>
        public static string DbConnNameRt
        {
            get
            {
                return dbRtConfigProvider.GetDbConnectionString();
            }
        }

    }

    public class DbConfigProvider
    {
        private string _contextName;
        private string _connectionString;
        private string _providerName;
        private readonly IConfiguration _configuration;


        public DbConfigProvider(string dbContextName)
        {
            this._contextName = dbContextName;
            if (string.IsNullOrWhiteSpace(_contextName) == false)
            {
                string connectionStringSettings = _configuration.GetConnectionString(dbContextName);
                if (connectionStringSettings != null)
                {
                    this._connectionString = connectionStringSettings;
                    this._providerName = connectionStringSettings;
                }
            }
        }

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        /// <returns></returns>
        public string GetDbConnectionString()
        {
            // sqlite数据库将Context配置节传入 只传_connectionString EF 无法使用
            //if (IsSqliteProviderName(_providerName))
            //    return _contextName;
            return IsConnectionStringEncrypt(_connectionString) ? DbConfigEncrypt.Decrypt(_connectionString) : _connectionString;
        }
        public static string GetDbConnectionDecryptString(string str)
        {
            // sqlite数据库将Context配置节传入 只传_connectionString EF 无法使用
            //if (IsSqliteProviderName(_providerName))
            //    return _contextName;
            return IsConnectionStringEncrypt(str) ? DbConfigEncrypt.Decrypt(str) : str;
        }
        public string GetDbConnectionOriginalString()
        {
            return _connectionString;
        }

        public string GetDBProviderName()
        {
            return _providerName;
        }

        /// <summary>
        /// 是否加密
        /// </summary>
        /// <returns></returns>
        private static bool IsConnectionStringEncrypt(string connectString)
        {
            return string.IsNullOrWhiteSpace(connectString) ? false : !connectString.ToLower().StartsWith("server");
        }

        /// <summary>
        /// 是否是sqlite驱动 sqlite驱动没办法将连接字符串传入
        /// </summary>
        /// <param name="providerName"></param>
        /// <returns></returns>
        private static bool IsSqliteProviderName(string providerName)
        {
            return providerName.ToLower().Contains("sqlite");
        }

    }


    /// <summary>
    /// 数据库连接字符串加解密
    /// </summary>
    public class DbConfigEncrypt
    {
        private static string EncryptKey = "201708241327";
        private static byte[] Keys = { 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF };

        /// <summary>
        /// 解密连接字符串 
        /// </summary>
        /// <param name="dbConnectString"></param>
        /// <returns>解密后的字符串 解密失败时会返回原字符串</returns>
        public static string Decrypt(string dbConnectString)
        {
            try
            {
                byte[] rgbKey = Encoding.UTF8.GetBytes(EncryptKey.Substring(0, 8));
                byte[] rgbIV = Keys;
                byte[] inputByteArray = Convert.FromBase64String(dbConnectString);
                DESCryptoServiceProvider DCSP = new DESCryptoServiceProvider();
                MemoryStream mStream = new MemoryStream();
                CryptoStream cStream = new CryptoStream(mStream, DCSP.CreateDecryptor(rgbKey, rgbIV), CryptoStreamMode.Write);
                cStream.Write(inputByteArray, 0, inputByteArray.Length);
                cStream.FlushFinalBlock();
                return Encoding.UTF8.GetString(mStream.ToArray());
            }
            catch
            {
                return dbConnectString;
            }
        }
    }

}
