namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// 集团公司代码数据传输对象
    /// </summary>
    public class GroupCompanyCodeDTO
    {
        /// <summary>
        /// 公司代码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 公司名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 公司简称
        /// </summary>
        public string ShortName { get; set; } = string.Empty;

        /// <summary>
        /// 公司类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 状态（1：启用，0：禁用）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remark { get; set; }
    }
}
