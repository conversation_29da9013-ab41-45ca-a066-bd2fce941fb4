import{C as _}from"./ChangeLanguage-7vrykP9f.js";import{dg as u,r as d,h as p,c as v,i as e,b as a,n as m,g as r,t as n,x as h,o as f,q as g}from"./index-D9CxWmlM.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";const w={id:"userLayout",class:g(["user-layout-wrapper"])},x={class:"container"},V={class:"changelang"},C={class:"user-layout-content"},L={class:"footer"},B={class:"copyright"},N={__name:"UserLayout",setup(S){const c=u(),t=d(""),i=new Date().getFullYear();return p(async()=>{let o=await c.fetchGetVersion();t.value=o}),(o,s)=>{const l=m("router-view");return f(),v("div",w,[e("div",x,[e("div",V,[a(_)]),e("div",C,[a(l)]),e("div",L,[e("div",B,[r(" © "+n(h(i))+" V"+n(t.value)+" ",1),s[0]||(s[0]=e("span",null,"配置网站",-1)),s[1]||(s[1]=r(" All Rights Reserved ",-1))])])])])}}},D=y(N,[["__scopeId","data-v-e13a30b4"]]);export{D as default};
