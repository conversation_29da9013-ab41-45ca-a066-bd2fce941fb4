﻿using CMSFramework.BusinessEntity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Principal;
using System.Text;
using System.Threading.Tasks;
using WindCMS.EF.WTParameter;

namespace WTCMSLive.BusinessModel.WTParm
{
    public class WTParmManagement
    {
        // 新增 WtTowerParameter
        public static async Task<WtTowerParameter> AddWtTowerParameterAsync(WtTowerParameter wtTowerParameter)
        {
            if (wtTowerParameter == null)
            {
                throw new ArgumentNullException(nameof(wtTowerParameter));
            }
            using (wtparamContext _context = new(ConfigInfo.DBConnName))
            {
                await _context.WtTowerParameters.AddAsync(wtTowerParameter);
                await _context.SaveChangesAsync();
            }

            return wtTowerParameter;
        }

        // 修改 WtTowerParameter
        public static async Task<WtTowerParameter> UpdateWtTowerParameterAsync(WtTowerParameter wtTowerParameter)
        {
            if (wtTowerParameter == null)
            {
                throw new ArgumentNullException(nameof(wtTowerParameter));
            }
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            var existingParameter = await _context.WtTowerParameters.FirstOrDefaultAsync(p => p.TowerId == wtTowerParameter.TowerId);
            if (existingParameter == null)
            {
                throw new InvalidOperationException("WtTowerParameter not found.");
            }

            existingParameter.ModelId = wtTowerParameter.ModelId;
            existingParameter.TowerType = wtTowerParameter.TowerType;
            existingParameter.TowerHeight = wtTowerParameter.TowerHeight;
            existingParameter.ConcreteTowerHeight = wtTowerParameter.ConcreteTowerHeight;
            existingParameter.SteelTowerHeight = wtTowerParameter.SteelTowerHeight;
            existingParameter.BaseDiameter = wtTowerParameter.BaseDiameter;
            existingParameter.TowerNaturalFrequency = wtTowerParameter.TowerNaturalFrequency;
            existingParameter.FlangeCount = wtTowerParameter.FlangeCount;
            existingParameter.FlangeDetails = wtTowerParameter.FlangeDetails;
            existingParameter.CableClusterCount = wtTowerParameter.CableClusterCount;
            existingParameter.CablePerCluster = wtTowerParameter.CablePerCluster;
            existingParameter.WirePerCable = wtTowerParameter.WirePerCable;
            existingParameter.WireDiameter = wtTowerParameter.WireDiameter;
            existingParameter.CableLength = wtTowerParameter.CableLength;
            existingParameter.HasSwitch = wtTowerParameter.HasSwitch;
            existingParameter.SwitchHeight = wtTowerParameter.SwitchHeight;
            existingParameter.TopDiameter = wtTowerParameter.TopDiameter;
            existingParameter.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return existingParameter;
        }


        public static async Task<WtTowerParameter> GetWtTowerParameterByIdAsync(string id)
        {
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            return await _context.WtTowerParameters.Include(p => p.WtParametersFlanges).FirstOrDefaultAsync(p => p.ModelId == id);

        }

        public static async Task<WtTowerParameter> AddOrUpdateWtTowerParameterAsync(WtTowerParameter wtTowerParameter)
        {

            if (wtTowerParameter == null)
            {
                throw new ArgumentNullException(nameof(wtTowerParameter));
            }
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            
            
            if (wtTowerParameter.TowerId == 0) // 新增
            {
                var existingParameter = await _context.WtTowerParameters.FirstOrDefaultAsync(p => p.ModelId == wtTowerParameter.ModelId);
                if (existingParameter != null)
                {
                    throw new InvalidOperationException("参数已存在，请勿重复添加！");
                }
                wtTowerParameter.UpdatedAt = DateTime.Now;
                wtTowerParameter.CreatedAt = DateTime.Now;
                await _context.WtTowerParameters.AddAsync(wtTowerParameter);
            }
            else // 修改
            {
                var existingParameter = await _context.WtTowerParameters.FirstOrDefaultAsync(p => p.TowerId == wtTowerParameter.TowerId);

                if (existingParameter == null)
                {
                    throw new InvalidOperationException("WtTowerParameter not found.");
                }

                existingParameter.ModelId = wtTowerParameter.ModelId;
                existingParameter.TowerType = wtTowerParameter.TowerType;
                existingParameter.TowerHeight = wtTowerParameter.TowerHeight;
                existingParameter.ConcreteTowerHeight = wtTowerParameter.ConcreteTowerHeight;
                existingParameter.SteelTowerHeight = wtTowerParameter.SteelTowerHeight;
                existingParameter.BaseDiameter = wtTowerParameter.BaseDiameter;
                existingParameter.TowerNaturalFrequency = wtTowerParameter.TowerNaturalFrequency;
                existingParameter.FlangeCount = wtTowerParameter.FlangeCount;
                existingParameter.FlangeDetails = wtTowerParameter.FlangeDetails;
                existingParameter.CableClusterCount = wtTowerParameter.CableClusterCount;
                existingParameter.CablePerCluster = wtTowerParameter.CablePerCluster;
                existingParameter.WirePerCable = wtTowerParameter.WirePerCable;
                existingParameter.WireDiameter = wtTowerParameter.WireDiameter;
                existingParameter.CableLength = wtTowerParameter.CableLength;
                existingParameter.HasSwitch = wtTowerParameter.HasSwitch;
                existingParameter.SwitchHeight = wtTowerParameter.SwitchHeight;
                existingParameter.TopDiameter = wtTowerParameter.TopDiameter;

                existingParameter.TowerNaturalFrequencyUpper = wtTowerParameter.TowerNaturalFrequencyUpper;
                existingParameter.TowerNaturalFrequencyLower = wtTowerParameter.TowerNaturalFrequencyLower;
                existingParameter.SecondOrderNaturalFrequency = wtTowerParameter.SecondOrderNaturalFrequency;
                existingParameter.GridFrequency = wtTowerParameter.GridFrequency;
                existingParameter.MaxGridFrequency = wtTowerParameter.MaxGridFrequency;
                existingParameter.TowerSectionCount = wtTowerParameter.TowerSectionCount;
                existingParameter.DeflectionCoefficient = wtTowerParameter.DeflectionCoefficient;
                existingParameter.UpdatedAt = DateTime.Now;

                // 先删除所有现有的WtParametersFlanges
                existingParameter.WtParametersFlanges = _context.WtParametersFlanges.Where(t => t.CompId == existingParameter.TowerId).ToList();
                _context.WtParametersFlanges.RemoveRange(existingParameter.WtParametersFlanges);

                // 如果有新的WtParametersFlanges数据，则重新添加
                if (wtTowerParameter.WtParametersFlanges != null)
                {
                    foreach (var incomingFlange in wtTowerParameter.WtParametersFlanges)
                    {
                        incomingFlange.CompId = existingParameter.TowerId; // 设置外键关联
                    }
                    //existingParameter.WtParametersFlanges = wtTowerParameter.WtParametersFlanges;
                    _context.WtParametersFlanges.AddRange(wtTowerParameter.WtParametersFlanges);
                }
                else
                {
                    existingParameter.WtParametersFlanges = new List<WtParametersFlange>();
                }
            }

            await _context.SaveChangesAsync();
            return wtTowerParameter;
        }



        // 根据ID查询单个叶片参数
        public static async Task<WtBladeParameter> GetWtBladeParameterByIdAsync(string modelId)
        {
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            return await _context.WtBladeParameters.Include(p => p.WtParametersFlanges).FirstOrDefaultAsync(p => p.ModelId == modelId);
        }


        // 新增或修改叶片参数
        public static async Task<WtBladeParameter> AddOrUpdateWtBladeParameterAsync(WtBladeParameter wtBladeParameter)
        {
            if (wtBladeParameter == null)
            {
                throw new ArgumentNullException(nameof(wtBladeParameter));
            }
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            if (wtBladeParameter.RotorId == 0) // 新增
            {
                var existingParameter = await _context.WtBladeParameters.FirstOrDefaultAsync(p => p.ModelId == wtBladeParameter.ModelId);
                if (existingParameter != null)
                {
                    throw new InvalidOperationException("参数已存在，请勿重复添加！");
                }
                wtBladeParameter.CreatedAt = DateTime.Now;
                wtBladeParameter.UpdatedAt = DateTime.Now;
                await _context.WtBladeParameters.AddAsync(wtBladeParameter);
            }
            else // 修改
            {
                var existingParameter = await _context.WtBladeParameters.FirstOrDefaultAsync(p => p.RotorId == wtBladeParameter.RotorId);
                if (existingParameter == null)
                {
                    throw new InvalidOperationException("WtBladeParameter not found.");
                }

                existingParameter.ModelId = wtBladeParameter.ModelId;
                existingParameter.BladeManufacturer = wtBladeParameter.BladeManufacturer;
                existingParameter.BladeModel = wtBladeParameter.BladeModel;
                existingParameter.BladeLength = wtBladeParameter.BladeLength;
                existingParameter.BladeMaterial = wtBladeParameter.BladeMaterial;
                existingParameter.BladeMass = wtBladeParameter.BladeMass;
                existingParameter.SweptArea = wtBladeParameter.SweptArea;
                existingParameter.BladeCount = wtBladeParameter.BladeCount;
                existingParameter.BladeFlapFrequency1 = wtBladeParameter.BladeFlapFrequency1;
                existingParameter.BladeFlapFrequency2 = wtBladeParameter.BladeFlapFrequency2;
                existingParameter.BladeFlapFrequency3 = wtBladeParameter.BladeFlapFrequency3;
                existingParameter.BladeEdgeFrequency1 = wtBladeParameter.BladeEdgeFrequency1;
                existingParameter.BladeEdgeFrequency2 = wtBladeParameter.BladeEdgeFrequency2;
                existingParameter.BladeEdgeFrequency3 = wtBladeParameter.BladeEdgeFrequency3;
                existingParameter.BladeVibrationSensorCount = wtBladeParameter.BladeVibrationSensorCount;
                existingParameter.BladeVibrationSensorType = wtBladeParameter.BladeVibrationSensorType;
                existingParameter.BladeVibrationDataRate = wtBladeParameter.BladeVibrationDataRate;

                existingParameter.PitchBearingManufacturer1 = wtBladeParameter.PitchBearingManufacturer1;
                existingParameter.PitchBearingModel1 = wtBladeParameter.PitchBearingModel1;
                existingParameter.PitchBearingManufacturer2 = wtBladeParameter.PitchBearingManufacturer2;
                existingParameter.PitchBearingModel2 = wtBladeParameter.PitchBearingModel2;
                existingParameter.PitchBearingManufacturer3 = wtBladeParameter.PitchBearingManufacturer3;
                existingParameter.PitchBearingModel3 = wtBladeParameter.PitchBearingModel3;
                existingParameter.PitchBearingOuterDiameter = wtBladeParameter.PitchBearingOuterDiameter;
                existingParameter.PitchBearingInnerDiameter = wtBladeParameter.PitchBearingInnerDiameter;
                existingParameter.PitchBearingOuterHeight = wtBladeParameter.PitchBearingOuterHeight;
                existingParameter.PitchBearingInnerHeight = wtBladeParameter.PitchBearingInnerHeight;
                existingParameter.PitchBearingOuterRollwayDiameter = wtBladeParameter.PitchBearingOuterRollwayDiameter;
                existingParameter.PitchBearingInnerRollwayDiameter = wtBladeParameter.PitchBearingInnerRollwayDiameter;
                existingParameter.PitchBearingRollNumber = wtBladeParameter.PitchBearingRollNumber;
                existingParameter.PitchBearingRollType = wtBladeParameter.PitchBearingRollType;
                existingParameter.PitchBearingRollDiameter = wtBladeParameter.PitchBearingRollDiameter;
                existingParameter.PitchBearingRollBackDiameter = wtBladeParameter.PitchBearingRollBackDiameter;
                existingParameter.PitchBearingRollCount = wtBladeParameter.PitchBearingRollCount;
                existingParameter.PitchMethod = wtBladeParameter.PitchMethod;
                existingParameter.PitchBearingVibrationSensorCount = wtBladeParameter.PitchBearingVibrationSensorCount;
                existingParameter.PitchBearingVibrationSensorType = wtBladeParameter.PitchBearingVibrationSensorType;
                existingParameter.PitchBearingVibrationDataRate = wtBladeParameter.PitchBearingVibrationDataRate;

                existingParameter.RootBoltMonitoringSystem = wtBladeParameter.RootBoltMonitoringSystem;
                existingParameter.RootBoltSensorCount = wtBladeParameter.RootBoltSensorCount;
                existingParameter.RootBoltSensorType = wtBladeParameter.RootBoltSensorType;
                existingParameter.RootBoltDataRate = wtBladeParameter.RootBoltDataRate;

                existingParameter.GearCirclePitch = wtBladeParameter.GearCirclePitch;
                existingParameter.GearCircleToothNumber = wtBladeParameter.GearCircleToothNumber;
                existingParameter.GearCircleProfileShiftFactor = wtBladeParameter.GearCircleProfileShiftFactor;
                existingParameter.GearCircleToothTipDiameter = wtBladeParameter.GearCircleToothTipDiameter;
                existingParameter.GearCircleGearLength = wtBladeParameter.GearCircleGearLength;


                existingParameter.UpdatedAt = DateTime.Now;

                // 先删除所有现有的WtParametersFlanges
                existingParameter.WtParametersFlanges = _context.WtParametersFlanges.Where(t => t.CompId == existingParameter.RotorId).ToList();
                _context.WtParametersFlanges.RemoveRange(existingParameter.WtParametersFlanges);

                // 如果有新的WtParametersFlanges数据，则重新添加
                if (wtBladeParameter.WtParametersFlanges != null)
                {
                    foreach (var incomingFlange in wtBladeParameter.WtParametersFlanges)
                    {
                        incomingFlange.CompId = existingParameter.RotorId; // 设置外键关联
                    }
                    //existingParameter.WtParametersFlanges = wtBladeParameter.WtParametersFlanges;
                    _context.WtParametersFlanges.AddRange(wtBladeParameter.WtParametersFlanges);
                }
                else
                {
                    existingParameter.WtParametersFlanges = new List<WtParametersFlange>();
                }
            }

            await _context.SaveChangesAsync();
            return wtBladeParameter;
        }


        // 根据ID查询单个机舱参数
        public static async Task<WtNacelleParameter> GetWtNacelleParameterByIdAsync(string modelId)
        {
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            //return await _context.WtNacelleParameters
            //    .Include(p => p.WtParametersGearboxes)
            //    .FirstOrDefaultAsync(p => p.ModelId == modelId);

            var nac = await _context.WtNacelleParameters
                .Include(p => p.WtParametersGearboxes)
                .FirstOrDefaultAsync(p => p.ModelId == modelId);

            if (nac != null)
            {
                // 主轴承
                nac.MainBearingList = _context.WtParametersBearings.Where(t => t.StageId == nac.MainBearingId).ToList();
                // 发电机轴承
                nac.GeneratorBearingList = _context.WtParametersBearings.Where(t => t.StageId == nac.GeneratorBearingId).ToList();

                // 齿轮箱轴承
                foreach (var item in nac.WtParametersGearboxes)
                {
                    item.BearingList = _context.WtParametersBearings.Where(t => t.StageId == item.BearingId).ToList();
                }
            }

            return nac;

        }

        // 新增或修改机舱参数
        public static async Task<WtNacelleParameter> AddOrUpdateWtNacelleParameterAsync(WtNacelleParameter wtNacelleParameter)
        {
            if (wtNacelleParameter == null)
            {
                throw new ArgumentNullException(nameof(wtNacelleParameter));
            }
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            if (wtNacelleParameter.NacelleId == 0) // 新增
            {
                var existingParameter = await _context.WtNacelleParameters.FirstOrDefaultAsync(p => p.ModelId == wtNacelleParameter.ModelId);
                if (existingParameter != null)
                {
                    throw new InvalidOperationException("参数已存在，请勿重复添加！");
                }
                // 轴承参数
                List<WtParametersBearing> bearlist = getBearingList(wtNacelleParameter);

                wtNacelleParameter.UpdatedAt = DateTime.Now;
                wtNacelleParameter.CreatedAt = DateTime.Now;
                await _context.WtNacelleParameters.AddAsync(wtNacelleParameter);
                await _context.WtParametersBearings.AddRangeAsync(bearlist);

                // 齿轮箱型号
                //await AddWtGearboxModelAsync(new WtGearboxModel()
                //{
                //    Manufacturer = wtNacelleParameter.GearboxManufacturer,
                //    Model = wtNacelleParameter.GearboxModel,
                //    GearRatio = wtNacelleParameter.GearboxGearRatio,
                //    CreatedAt = DateTime.Now,
                //    UpdatedAt = DateTime.Now,
                //});
            }
            else // 修改
            {
                var existingParameter = await _context.WtNacelleParameters
                    .Include(p => p.WtParametersGearboxes)
                    .FirstOrDefaultAsync(p => p.NacelleId == wtNacelleParameter.NacelleId);
                if (existingParameter == null)
                {
                    throw new InvalidOperationException("WtNacelleParameter not found.");
                }
                HashSet<string> bearing = new()
                {
                    existingParameter.MainBearingId,
                    existingParameter.GeneratorBearingId
                };

                existingParameter.ModelId = wtNacelleParameter.ModelId;
                existingParameter.RatedPower = wtNacelleParameter.RatedPower;
                existingParameter.GeneratorRatedSpeed = wtNacelleParameter.GeneratorRatedSpeed;
                existingParameter.GeneratorGridSpeed = wtNacelleParameter.GeneratorGridSpeed;
                existingParameter.GearboxManufacturer = wtNacelleParameter.GearboxManufacturer;
                existingParameter.GearboxStructureType = wtNacelleParameter.GearboxStructureType;
                existingParameter.LubricantManufacturer = wtNacelleParameter.LubricantManufacturer;
                existingParameter.LubricantModel = wtNacelleParameter.LubricantModel;
                existingParameter.GearRatio = wtNacelleParameter.GearRatio;
                existingParameter.GeneratorType = wtNacelleParameter.GeneratorType;
                existingParameter.GeneratorRatedVoltage = wtNacelleParameter.GeneratorRatedVoltage;

                existingParameter.LubricantViscosity100C = wtNacelleParameter.LubricantViscosity100C;
                existingParameter.LubricantViscosity40C = wtNacelleParameter.LubricantViscosity40C;
                existingParameter.LubricantDensity25C = wtNacelleParameter.LubricantDensity25C;

                existingParameter.GearboxGearRatio = wtNacelleParameter.GearboxGearRatio;
                existingParameter.GearboxModel = wtNacelleParameter.GearboxModel;
                existingParameter.GeneratorStatorSlotCount = wtNacelleParameter.GeneratorStatorSlotCount;
                existingParameter.GeneratorRotorBarCount = wtNacelleParameter.GeneratorRotorBarCount;
                existingParameter.GeneratorPolePairs = wtNacelleParameter.GeneratorPolePairs;
                existingParameter.GeneratorModel = wtNacelleParameter.GeneratorModel;
                existingParameter.GeneratorManufacturer = wtNacelleParameter.GeneratorManufacturer;

                existingParameter.UpdatedAt = DateTime.UtcNow;

                // 轴承参数
                List<WtParametersBearing> bearlist = new();

                // 主轴承
                existingParameter.MainBearingId = Guid.NewGuid().ToString();
                foreach (var item in wtNacelleParameter.MainBearingList)
                {
                    item.StageId = existingParameter.MainBearingId;
                    item.BearingId = 0;
                    bearlist.Add(item);
                }

                // 发电机
                existingParameter.GeneratorBearingId = Guid.NewGuid().ToString();
                foreach (var item in wtNacelleParameter.GeneratorBearingList)
                {
                    item.StageId = existingParameter.GeneratorBearingId;
                    item.BearingId = 0;
                    bearlist.Add(item);
                }

                // 齿轮箱
                if (wtNacelleParameter.WtParametersGearboxes != null && wtNacelleParameter.WtParametersGearboxes.Count != 0)
                {
                    foreach (var item in wtNacelleParameter.WtParametersGearboxes)
                    {
                        bearing.Add(item.BearingId);
                        item.BearingId = Guid.NewGuid().ToString();
                        item.NacelleId = existingParameter.NacelleId;
                        foreach (var bear in item.BearingList)
                        {
                            bear.StageId = item.BearingId;
                            bear.BearingId = 0;
                            bearlist.Add(bear);
                        }
                    }
                }

                // 更新齿轮箱参数
                //foreach (var gearbox in wtNacelleParameter.WtParametersGearboxes)
                //{
                //    var existingGearbox = existingParameter.WtParametersGearboxes.FirstOrDefault(g => g.StageId == gearbox.StageId);
                //    if (existingGearbox != null)
                //    {
                //        existingGearbox.StageNumber = gearbox.StageNumber;
                //        existingGearbox.StageType = gearbox.StageType;
                //        existingGearbox.PlanetCount = gearbox.PlanetCount;
                //        existingGearbox.SunTeethCount = gearbox.SunTeethCount;
                //        existingGearbox.PlanetTeethCount = gearbox.PlanetTeethCount;
                //        existingGearbox.RingTeethCount = gearbox.RingTeethCount;
                //        existingGearbox.LargeTeethCount = gearbox.LargeTeethCount;
                //        existingGearbox.SmallTeethCount = gearbox.SmallTeethCount;
                //        existingGearbox.BearingId = gearbox.BearingId;

                //        bearing.Add(existingGearbox.BearingId);
                //    }
                //    else
                //    {
                //        existingParameter.WtParametersGearboxes.Add(gearbox);
                //    }
                //}

                // 删除齿轮箱
                _context.WtParametersGearboxes.RemoveRange(existingParameter.WtParametersGearboxes);

                // 查询需要删除的记录
                var toBeDeleted = _context.WtParametersBearings
                                          .Where(w => bearing.Contains(w.StageId))
                                          .ToList();

                // 删除这些记录
                _context.WtParametersBearings.RemoveRange(toBeDeleted);

                // 提交更改到数据库
                //_context.SaveChanges();

                //添加齿轮箱
                await _context.WtParametersGearboxes.AddRangeAsync(wtNacelleParameter.WtParametersGearboxes);
                //List<WtParametersBearing> bearlist = getBearingList(wtNacelleParameter);
                await _context.WtParametersBearings.AddRangeAsync(bearlist);

            }

            // 齿轮箱型号
            await AddWtGearboxModelAsync(new WtGearboxModel()
            {
                Manufacturer = wtNacelleParameter.GearboxManufacturer,
                Model = wtNacelleParameter.GearboxModel,
                GearRatio = wtNacelleParameter.GearboxGearRatio,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
            });
            await _context.SaveChangesAsync();
            return wtNacelleParameter;
        }

        private static List<WtParametersBearing> getBearingList(WtNacelleParameter wtNacelleParameter)
        {
            // 轴承参数
            List<WtParametersBearing> bearlist = new();

            // 主轴承
            wtNacelleParameter.MainBearingId = Guid.NewGuid().ToString();
            foreach (var item in wtNacelleParameter.MainBearingList)
            {
                item.StageId = wtNacelleParameter.MainBearingId;
                item.BearingId = 0;
                item.CreatedAt = DateTime.Now;
                item.UpdatedAt = DateTime.Now;
                bearlist.Add(item);
            }

            // 发电机
            wtNacelleParameter.GeneratorBearingId = Guid.NewGuid().ToString();
            foreach (var item in wtNacelleParameter.GeneratorBearingList)
            {
                item.StageId = wtNacelleParameter.GeneratorBearingId;
                item.BearingId = 0;
                item.CreatedAt = DateTime.Now;
                item.UpdatedAt = DateTime.Now;
                bearlist.Add(item);
            }

            // 齿轮箱
            if (wtNacelleParameter.WtParametersGearboxes != null && wtNacelleParameter.WtParametersGearboxes.Count != 0)
            {
                foreach (var item in wtNacelleParameter.WtParametersGearboxes)
                {
                    item.BearingId = Guid.NewGuid().ToString();
                    foreach (var bear in item.BearingList)
                    {
                        bear.StageId = item.BearingId;
                        bear.BearingId = 0;
                        bear.CreatedAt = DateTime.Now;
                        bear.UpdatedAt = DateTime.Now;
                        bearlist.Add(bear);
                    }
                }
            }

            return bearlist;
        }

        public static async Task<bool> DeleteTurbineModel(string modelId)
        {
            try
            {
                using CMSFramework.EF.DevContext ctx = new CMSFramework.EF.DevContext(ConfigInfo.DBConnName);
                using wtparamContext _context = new wtparamContext(ConfigInfo.DBConnName);

                // 删除机型表
                var turModel = ctx.DevWTurbineModels.Find(modelId);
                if (turModel != null)
                {
                    ctx.DevWTurbineModels.Remove(turModel);
                }

                // 删除塔筒及其关联的法兰参数
                var tow = await _context.WtTowerParameters
                                         .Include(p => p.WtParametersFlanges)
                                         .FirstOrDefaultAsync(p => p.ModelId == modelId);
                if (tow != null)
                {
                    _context.WtParametersFlanges.RemoveRange(tow.WtParametersFlanges);
                    _context.WtTowerParameters.Remove(tow);
                }

                // 删除叶片及其关联的法兰参数
                var bld = await _context.WtBladeParameters
                                         .Include(p => p.WtParametersFlanges)
                                         .FirstOrDefaultAsync(p => p.ModelId == modelId);
                if (bld != null)
                {
                    _context.WtParametersFlanges.RemoveRange(bld.WtParametersFlanges);
                    _context.WtBladeParameters.Remove(bld);
                }

                // 删除机舱及其关联的轴承和齿轮箱
                var wtNacelleParameter = await GetWtNacelleParameterByIdAsync(modelId);
                if (wtNacelleParameter != null)
                {
                    // 收集需要删除的轴承ID
                    HashSet<string> bearingIds = new()
                    {
                    wtNacelleParameter.MainBearingId,
                    wtNacelleParameter.GeneratorBearingId
                    };

                    if (wtNacelleParameter.WtParametersGearboxes != null && wtNacelleParameter.WtParametersGearboxes.Count > 0)
                    {
                        foreach (var gearbox in wtNacelleParameter.WtParametersGearboxes)
                        {
                            bearingIds.Add(gearbox.BearingId);
                        }
                    }

                    // 删除关联的轴承
                    var toBeDeletedBearings = _context.WtParametersBearings
                                                       .Where(w => bearingIds.Contains(w.StageId))
                                                       .ToList();
                    _context.WtParametersBearings.RemoveRange(toBeDeletedBearings);

                    // 删除关联的齿轮箱
                    if (wtNacelleParameter.WtParametersGearboxes != null)
                    {
                        _context.WtParametersGearboxes.RemoveRange(wtNacelleParameter.WtParametersGearboxes);
                    }

                    // 删除机舱
                    _context.WtNacelleParameters.Remove(wtNacelleParameter);
                }

                // 统一提交更改
                await ctx.SaveChangesAsync();
                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception ex)
            {
                // 可以在这里记录日志或进行其他异常处理
                Console.WriteLine($"Error occurred while deleting turbine model: {ex.Message}");
                return false;
            }
        }


        /// <summary>
        /// 查询所有的齿轮箱厂家型号
        /// </summary>
        /// <returns></returns>

        public static async Task<List<WtGearboxModel>> GetGearboxModelsAsync()
        {
            using wtparamContext _context = new(ConfigInfo.DBConnName);
            return await _context.WtGearboxModels.ToListAsync();
        }

        /// <summary>
        /// 新增齿轮箱厂家及型号
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public static async Task<bool> AddWtGearboxModelAsync(WtGearboxModel model)
        {
            using wtparamContext _context = new(ConfigInfo.DBConnName);

            var mod = _context.WtGearboxModels.FirstOrDefault(t=>t.Manufacturer == model.Manufacturer 
                                            && t.Model == model.Model && t.GearRatio == model.GearRatio );

            if (mod == null)
            {
                await _context.WtGearboxModels.AddAsync(model);
                await _context.SaveChangesAsync();
            }

            return true;
        }

    }
}
