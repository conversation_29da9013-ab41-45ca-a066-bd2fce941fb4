import{F as Z,S as ee}from"./index-DyCH3qIX.js";/* empty css                                                              */import{_ as te}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{B as ae}from"./index-BGEB0Rhf.js";import{F as le}from"./index-CgGbkMmC.js";import{y as P,r as B,w as U,af as se,c as _,o as y,b as R,d as w,i as $,s as h,f as E,g as N,F as j,cd as V,t as q,q as z,m as ne}from"./index-D9CxWmlM.js";import{_ as oe}from"./table-C_s53ALS.js";import{a as ie}from"./ActionButton-BRQ4acFZ.js";const re="/icons/copy-up.png",de=(c,g)=>{if(g!==3&&g!==4)throw new Error("递增位必须是 3 或 4");if(!c||c=="")return;const p=c.split(".").map(o=>parseInt(o,10));if(p.length!==4||p.some(o=>isNaN(o)||p.some(i=>i<0||i>255)))throw new Error("无效的 IP 地址");const k=g-1;p[k]+=1;for(let o=k;o>=0;o--)p[o]>255&&(p[o]=0,o>0?p[o-1]+=1:p[o]=255);return p.join(".")},ue=(c=0,g=1)=>{if(g<=0||!Number.isInteger(g))throw new Error("步长必须是正整数");if(typeof c=="number")return c+g;if(typeof c=="string"){const p=c.match(/(\d+)$/);if(p){const k=c.slice(0,p.index),o=p[1],I=parseInt(o,10)+g,O=String(I).padStart(o.length,"0");return O.length>o.length,k+O}else return c+g}throw new Error("起始值必须是数字或包含数字的字符串")},ce={class:"tableBox"},fe={class:"tableBox"},pe={key:0,class:"requiredStyle"},ve={key:1,class:"copy-up-icon",src:re,title:"默认同上"},be={key:2,class:"autoIncrementorBox"},ye=["title","onClick"],me={class:"autoIncrementorBoxContentBox"},he={key:0,class:"actions"},ge={key:0,class:"addRow"},Ie={key:0,class:"footer"},_e={class:z(["footer-btn"])},Ce={key:0,class:"btnitem"},we={key:1,class:"btnitem"},ke={__name:"index",props:{tableColumns:Array,tableDatas:Array,recordKey:String,tableTitle:String,tableOperate:{type:Array,default:()=>[]},noCopyUpKeys:{type:Array,default:()=>[]},removeDuplicateKeys:{type:Array,default:()=>[]},noRepeatKeys:{type:Array,default:()=>[]},noCopyUpAll:{type:Boolean,default:()=>!1},addBtnDisabled:Boolean,size:String,noPagination:Boolean,noForm:Boolean},emits:["addRow","deleteRow","editRow","submit","hangeTableFormChange","update:modelValue"],setup(c,{expose:g,emit:p}){const k=[{value:3,label:"第3位"},{value:4,label:"第4位"}],o=P({}),i=c,I=P({autoIncrementorNames:{},autoIncrementorRules:{}}),O=e=>{I.autoIncrementorNames[e]=!I.autoIncrementorNames[e]},W=(e,a)=>{I.autoIncrementorRules[a]={value:e}},M=e=>e.isrequired||e.validateRules&&e.validateRules.length&&e.validateRules[0].required,A=e=>{var s,r;let a=e.filter(n=>!n.columnHidden);return(s=i.removeDuplicateKeys)!=null&&s.length&&a.forEach(n=>{i.removeDuplicateKeys.includes(n.dataIndex)&&(n.hasChangeEvent=!0)}),(r=i.tableOperate)!=null&&r.length&&a.push({title:"操作",key:"action",dataIndex:"action"}),a},F=B(null),L=B(A(i.tableColumns)),D=p,m=B(i.tableDatas.length?i.tableDatas:[{key:Date.now()}]),C=B({});U(()=>i.tableDatas,e=>{e.forEach((a,s)=>{i.tableColumns.forEach(r=>{if(!r.noEdit){const n=`${r.dataIndex}[${s}]`;o[n]=a[r.dataIndex]}})})},{immediate:!0});const G=()=>{let e=m.value.length;m.value.push({key:Date.now()}),K(e)};U(()=>i.tableColumns,e=>{L.value=A(e)},{immediate:!0});const H=(e,a,s,r)=>{const n=`${e}[${a}]`;o[n]=r,m.value[a][e]=r,F.value&&e&&s&&s.validate&&setTimeout(()=>{F.value.validateFields([n])},100)},J=(e,a,s)=>{var r,n;if((r=i.removeDuplicateKeys)!=null&&r.length&&i.removeDuplicateKeys.includes(s)&&(e&&e.value&&typeof e.value=="object"&&e.value!==null?C.value[s]=[...C.value[s]||[],e.value.value]:C.value[s]=[...C.value[s]||[],e.value],C.value[s]&&C.value[s].length>1))for(let f=0;f<L.value.length;f++){let t=L.value[f];if(t.dataIndex==s)for(let d=0;d<t.tableList.length;d++){if(d==a)continue;let l=o[`${t.dataIndex}[${d}]`],u=(n=t.selectOptions)==null?void 0:n.filter(v=>l&&l==v.value||!C.value[t.dataIndex].includes(v.value));t.tableList[d]={...t.tableList[d],selectOptions:u}}}e&&e.value&&typeof e.value=="object"&&e.value!==null?D("hangeTableFormChange",{value:{...e.value,dataIndex:e.dataIndex},dataIndex:s,index:a}):D("hangeTableFormChange",{value:e.value,dataIndex:s,index:a})},Q=e=>{D("deleteRow",e);let a=m.value.filter(s=>s.key!==e.key);a.forEach((s,r)=>{Object.keys(s).forEach(n=>{const f=`${n}[${r}]`;o[f]=s[n]})}),m.value=a},K=e=>{const a={};let s=L.value;s.forEach(t=>{var d,l,u;if(!t.noEdit){const v=`${t.dataIndex}[${e-1}]`;if(t.columnOperate&&I.autoIncrementorNames[t.dataIndex])switch(t.columnOperate.type){case"number":a[t.dataIndex]=ue(o[v],1);break;case"ip":let b=((d=I.autoIncrementorRules[t.dataIndex])==null?void 0:d.value)||4;a[t.dataIndex]=de(o[v],b);break}else a[t.dataIndex]=o[v]}if((l=i.removeDuplicateKeys)!=null&&l.length){if(i.removeDuplicateKeys.includes(t.dataIndex)&&(t.tableList||(t.tableList=[]),t.tableList&&t.tableList.length&&e>=t.tableList.length&&!t.tableList[e]&&t.tableList.push({}),C.value[t.dataIndex]&&C.value[t.dataIndex].length)){let v=(u=t.selectOptions)==null?void 0:u.filter(b=>!C.value[t.dataIndex].includes(b.value));t.tableList[e]={...t.tableList[e-1],selectOptions:v}}}else t.tableList&&t.tableList.length&&(e>=t.tableList.length&&!t.tableList[e]&&t.tableList.push({}),t.tableList[e]={...t.tableList[e-1]})});const n={...m.value[e]};L.value=[...s],Object.keys(a).forEach(t=>{if(!i.noCopyUpAll&&!i.noCopyUpKeys.includes(t)&&t!=="key"){n[t]=a[t];const d=`${t}[${e}]`;o[d]=a[t]}});const f=[...m.value];f[e]=n,m.value=[...f]},X=()=>{D("cancel")},Y=e=>{var s,r;let a=!0;try{if((s=i.noRepeatKeys)!=null&&s.length){let n="",f={};for(let t=0;t<Object.keys(e).length;t++){const d=Object.keys(e)[t];let l=d.split("["),u=d;if(l.length>1&&(u=l[0]),i.noRepeatKeys.includes(u))if(!f[u])f[u]=[],f[u].push(e[d]);else{if(f[u].includes(e[d])){let v=(r=i.tableColumns.find(S=>S.dataIndex==u))==null?void 0:r.title;n=`第${l[1].split("]")[0]*1+1}行: ${v} 不能重复!`,ne.error(n),a=!1;break}f[u].push(e[d])}}}}catch(n){console.log(n)}a&&D("submit",e)};return g({getTableFieldsValue:()=>se(o),setTableFieldValue:e=>{const{formDataIndex:a,tableDataIndex:s,index:r,value:n}=e;o[a]=n;let f={...m.value[r]},t=[...m.value];f[s]=n,t[r]=f,m.value=[...t]},clearValidate:e=>{F.value.clearValidate(e)}}),(e,a)=>{const s=ee,r=ie,n=ae,f=oe,t=le;return y(),_("div",ce,[R(t,{ref_key:"formRef",ref:F,model:o,onFinish:Y},{default:w(()=>{var d;return[$("div",fe,[R(f,{bordered:"",columns:L.value,"data-source":m.value,pagination:!c.noPagination&&m.value.length>10,size:c.size||"middle"},{headerCell:w(({column:l})=>[M(l)?(y(),_("span",pe,"*")):h("",!0),!i.noCopyUpKeys.includes(l.dataIndex)&&l.dataIndex!=="action"?(y(),_("img",ve)):h("",!0),N(" "+q(l.title)+" ",1),l.columnOperate?(y(),_("div",be,[$("span",{class:z(["title",I.autoIncrementorNames[l.dataIndex]?"activeTitle":"noActiveTitle"]),title:`${I.autoIncrementorNames[l.dataIndex]?"关闭":"开启"}自增`,onClick:u=>O(l.dataIndex)},null,10,ye),l.columnOperate&&l.columnOperate.type=="ip"&&I.autoIncrementorNames[l.dataIndex]?(y(),E(r,{key:0,placement:"bottom",overlayClassName:"myPopover",trigger:"click"},{content:w(()=>{var u;return[$("div",me,[a[1]||(a[1]=$("span",null,"递增位：",-1)),R(s,{ref:"select",options:k,style:{width:"100px"},value:((u=I.autoIncrementorRules[l.dataIndex])==null?void 0:u.value)||4,onChange:v=>W(v,l.dataIndex)},null,8,["value","onChange"])])]}),default:w(()=>[a[2]||(a[2]=$("span",{class:"operate"},"IP递增位",-1))]),_:2,__:[2]},1024)):h("",!0)])):h("",!0),l.headerSlotName?V(e.$slots,l.headerSlotName,{key:3},void 0,!0):h("",!0)]),bodyCell:w(({column:l,record:u,text:v,index:b})=>{var S,T;return[l&&l.dataIndex&&l.dataIndex==="action"?(y(),_("div",he,[(S=c.tableOperate)!=null&&S.includes("copyUp")&&b>0&&!c.noCopyUpAll?(y(),E(n,{key:0,onClick:x=>K(b)},{default:w(()=>a[3]||(a[3]=[N("同上",-1)])),_:2,__:[3]},1032,["onClick"])):h("",!0),(T=c.tableOperate)!=null&&T.includes("delete")?(y(),E(n,{key:1,onClick:x=>Q(u)},{default:w(()=>a[4]||(a[4]=[N("删除",-1)])),_:2,__:[4]},1032,["onClick"])):h("",!0)])):l&&!l.noEdit?(y(),_(j,{key:1},[$("div",null,[R(Z,{class:"formItem",notshowLabels:!0,itemProps:{...l,formItemWidth:l.columnWidth,dataIndex:`${l.dataIndex}[${b}]`,...l.tableList&&l.tableList[b]?l.tableList[b]:{}},modelValue:o[`${l.dataIndex}[${b}]`],"onUpdate:modelValue":x=>H(l.dataIndex,b,{validate:l.inputType=="selectinput"},x),onOnchangeSelect:x=>J(x,b,l.dataIndex)},null,8,["itemProps","modelValue","onUpdate:modelValue","onOnchangeSelect"])]),l.afterContent?V(e.$slots,l.afterContentName||"afterContent",{key:0,column:l,record:u,index:b,text:v},void 0,!0):h("",!0)],64)):(y(),_(j,{key:2},[N(q(v),1)],64))]}),_:3},8,["columns","data-source","pagination","size"]),(d=c.tableOperate)!=null&&d.includes("noAdd")?h("",!0):(y(),_("div",ge,[R(n,{onClick:a[0]||(a[0]=l=>G())},{default:w(()=>a[5]||(a[5]=[N("添加一行",-1)])),_:1,__:[5]})]))]),c.noForm?h("",!0):(y(),_("div",Ie,[V(e.$slots,"footer",{},void 0,!0),$("div",_e,[i.noCancle?h("",!0):(y(),_("div",Ce,[R(n,{onClick:X},{default:w(()=>a[6]||(a[6]=[N("取消",-1)])),_:1,__:[6]})])),i.noSubmit?h("",!0):(y(),_("div",we,[R(n,{type:"primary","html-type":"submit"},{default:w(()=>a[7]||(a[7]=[N("确定",-1)])),_:1,__:[7]})]))])]))]}),_:3},8,["model"])])}}},Se=te(ke,[["__scopeId","data-v-6afd4d5d"]]);export{Se as W};
