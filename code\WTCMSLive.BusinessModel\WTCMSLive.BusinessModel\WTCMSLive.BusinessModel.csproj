﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AssemblyTitle>WTCMSLive.DALFacade</AssemblyTitle>
    <Product>WTCMSLive.DALFacade</Product>
    <Copyright>Copyright ©  2013</Copyright>
    <AssemblyVersion>1.7.0.6</AssemblyVersion>
    <FileVersion>1.7.0.6</FileVersion>
  </PropertyGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\WindCMS.EF.WTParameter\WindCMS.EF.WTParameter.csproj" />
    <ProjectReference Include="..\WTCMSLive.UserRoleManagement\WTCMSLive.UserRoleManagement.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.36" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.3" />
    <PackageReference Include="Microsoft.Web.Infrastructure" Version="2.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.3" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.2" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="App_Start\EntityFrameworkProfilerBootstrapper.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="AppFramework.Analysis">
      <HintPath>..\..\AppNetCore\AppFramework.Analysis.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.AnalysisBVM">
      <HintPath>..\..\AppNetCore\AppFramework.AnalysisBVM.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.DataQueue">
      <HintPath>..\..\AppNetCore\AppFramework.DataQueue.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.IDUtility">
      <HintPath>..\..\AppNetCore\AppFramework.IDUtility.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.License">
      <HintPath>..\..\AppNetCore\AppFramework.License.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.NetworkGap">
      <HintPath>..\..\AppNetCore\AppFramework.NetworkGap.dll</HintPath>
    </Reference>
    <Reference Include="AppFrameWork.Utility">
      <HintPath>..\..\AppNetCore\AppFramework.Utility.dll</HintPath>
    </Reference>
    <Reference Include="AppFramework.WPFControl">
      <HintPath>..\..\AppNetCore\AppFramework.WPFControl.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper">
      <HintPath>..\..\AppNetCore\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DataStorageLogic">
      <HintPath>..\..\AppNetCore\CMSFramework.DataStorageLogic.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DAUEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DAUMonitoringEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DAUMonitoringEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DevTreeEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DevTreeEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DiagnosisEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DiagnosisEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.DomainEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.DomainEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Base">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.Base.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.DAU">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.DAU.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.DAUMonitoring">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.DAUMonitoring.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.DevTree">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.DevTree.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Diagnosis">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.Diagnosis.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.MeasData">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.MeasData.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.MeasDef">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.MeasDef.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Monitoring">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.Monitoring.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.Overview">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.Overview.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.POCO">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.POCO.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EF.System">
      <HintPath>..\..\AppNetCore\CMSFramework.EF.System.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EigenValue.Storage">
      <HintPath>..\..\AppNetCore\CMSFramework.EigenValue.Storage.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.EigenValueDef">
      <HintPath>..\..\AppNetCore\CMSFramework.EigenValueDef.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.FSDB">
      <HintPath>..\..\AppNetCore\CMSFramework.FSDB.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Logger">
      <HintPath>..\..\AppNetCore\CMSFramework.Logger.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDataEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.MeasDataEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDataStorage">
      <HintPath>..\..\AppNetCore\CMSFramework.MeasDataStorage.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MeasDefEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.MeasDefEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.MonitoringEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.MonitoringEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.OverviewEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.OverviewEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.SystemEntities">
      <HintPath>..\..\AppNetCore\CMSFramework.SystemEntities.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Trigger">
      <HintPath>..\..\AppNetCore\CMSFramework.Trigger.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.TypeDef">
      <HintPath>..\..\AppNetCore\CMSFramework.TypeDef.dll</HintPath>
    </Reference>
    <Reference Include="CMSFramework.Utility">
      <HintPath>..\..\AppNetCore\CMSFramework.Utility.dll</HintPath>
    </Reference>
    <Reference Include="CMSFrameworkEX.Permission">
      <HintPath>..\..\AppNetCore\CMSFrameworkEX.Permission.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>