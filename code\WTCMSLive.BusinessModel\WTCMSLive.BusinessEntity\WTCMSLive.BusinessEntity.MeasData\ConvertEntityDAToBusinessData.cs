﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using AppFramework.ServiceBus;
using WTCMSLive.BusinessEntity;
using WTCMSLive.BusinessEntity.SVM;
using WTCMSLive.Entity.Enum;
using WTCMSLive.IDALService;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: GuoKaile
    // Create: 2013-09-12
    /// <summary>
    /// 数据实体转换， 从DA层实体转换为业务层实体
    /// </summary>
    public static class ConvertEntityDAToBusinessData
    {
        //波形定义参数服务 接口
        private static IWDFParamService wdfParamSvc =
             ServiceLocator.GetService<IWDFParamService>();

        #region 测量事件转换

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static MeasEvent_Wave ConvertMeasEventWF<T>(
            T _entity) where T : new()
        {
            if (_entity == null) return null;

            Type type = typeof(T);

            MeasEvent_Wave ev = new MeasEvent_Wave();
            if(type.GetProperty("SVMWaveFormNum").GetValue(_entity, null)!=null)
            {
                ev.SVMWaveFormNum = Convert.ToInt32(type.GetProperty("SVMWaveFormNum").GetValue(_entity, null));
            }
            ev.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            if (type.GetProperty("AlarmDegree").GetValue(_entity, null) != null)
            {
            ev.AlarmDegree = Convert.ToInt32(type.GetProperty("AlarmDegree").GetValue(_entity, null));
            }
            if (type.GetProperty("DataQualType").GetValue(_entity, null) != null)
            {
                ev.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));
            }
            ev.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            if (type.GetProperty("WkConLevelCode").GetValue(_entity, null) != null)
            {
                ev.OutPowerBandCode = type.GetProperty("WkConLevelCode").GetValue(_entity, null).ToString();
            }
            ev.RotSpdWaveFromNum = Convert.ToInt32(type.GetProperty("RotSpdWaveFromNum").GetValue(_entity, null));
            ev.WaveFormNum = Convert.ToInt32(type.GetProperty("WaveFormNum").GetValue(_entity, null));
            ev.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            ev.WkConDataNum = Convert.ToInt32(type.GetProperty("WkConDataNum").GetValue(_entity, null));

            return ev;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换波形测量事件
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static MeasEvent_EigenValue ConvertMeasEventEV<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            MeasEvent_EigenValue ev = new MeasEvent_EigenValue();

            ev.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            ev.AlarmDegree = Convert.ToInt32(type.GetProperty("AlarmDegree").GetValue(_entity, null));
            ev.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));
            ev.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            ev.OutPowerBandCode = type.GetProperty("WkConLevelCode").GetValue(_entity, null).ToString();
            ev.EigenValueNum = Convert.ToInt32(type.GetProperty("EigenValueNum").GetValue(_entity, null));
            ev.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            ev.WkConDataNum = Convert.ToInt32(type.GetProperty("WkConDataNum").GetValue(_entity, null));

            return ev;
        }

        #endregion 测量事件转换

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换工况数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WorkingConditionData ConvertWorkConData<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            WorkingConditionData data = new WorkingConditionData();

            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            data.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));
            if (type.GetProperty("MaximumValue") != null)
            {
                data.MaxValue = Convert.ToDouble(type.GetProperty("MaximumValue").GetValue(_entity, null));
            }
            if (type.GetProperty("MinimumValue") != null)
            {
                data.MinValue = Convert.ToDouble(type.GetProperty("MinimumValue").GetValue(_entity, null));
            }
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.Param_Type_Code = Convert.ToInt32(type.GetProperty("ParamTypeCode").GetValue(_entity, null));
            data.Param_Value = Convert.ToDouble(type.GetProperty("AverageValue").GetValue(_entity, null));
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();

            return data;
        }

        /// <summary>
        /// 转速单值转换为工况
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WorkingConditionData ConvertRotSpeedValueToWorkConData<T>(
    T _entity) where T : new()
        {
            Type type = typeof(T);

            WorkingConditionData data = new WorkingConditionData();

            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            data.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));
            if (type.GetProperty("MaximumValue") != null)
            {
                data.MaxValue = Convert.ToDouble(type.GetProperty("MaximumValue").GetValue(_entity, null));
            }
            if (type.GetProperty("MinimumValue") != null)
            {
                data.MinValue = Convert.ToDouble(type.GetProperty("MinimumValue").GetValue(_entity, null));
            }
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.Param_Type_Code = short.Parse(WTCMSLive.BusinessEntity.WorkCondition_ParamType.ROTSPEED.Param_Type_Code);
            data.Param_Value = Convert.ToDouble(type.GetProperty("AvgValue").GetValue(_entity, null));
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();

            return data;
        }
        /// <summary>
        /// 转换转速单值数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static RotSpeedValue ConvertRotSpdValue<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);
            RotSpeedValue rsValue = new RotSpeedValue();
            rsValue.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
           rsValue.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
           rsValue.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
           rsValue.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
           if (type.GetProperty("MaximumValue") != null && type.GetProperty("MaximumValue").GetValue(_entity, null)!=null)
           {
               rsValue.MaxRotSpeed = float.Parse(type.GetProperty("MaximumValue").GetValue(_entity, null).ToString());
           }
           if (type.GetProperty("MinimumValue") != null && type.GetProperty("MinimumValue").GetValue(_entity, null)!=null)
           {
               rsValue.MinRotSpeed = float.Parse(type.GetProperty("MinimumValue").GetValue(_entity, null).ToString());
           }
           if (type.GetProperty("AvgValue") != null && type.GetProperty("AvgValue").GetValue(_entity, null) != null)
           {
               rsValue.AveRotSpeed = float.Parse(type.GetProperty("AvgValue").GetValue(_entity, null).ToString());
           }
           rsValue.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));

           return rsValue;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换输出功率
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static OutPowerData ConvertOutPower<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);
            OutPowerData data = new OutPowerData();
            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            data.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));
            if (type.GetProperty("MaximumValue") != null && type.GetProperty("MaximumValue").GetValue(_entity, null) != null)
            {
                data.MaxValue = Convert.ToDouble(type.GetProperty("MaximumValue").GetValue(_entity, null));
            }
            if (type.GetProperty("MinimumValue") != null && type.GetProperty("MinimumValue").GetValue(_entity, null) != null)
            {
                data.MinValue = Convert.ToDouble(type.GetProperty("MinimumValue").GetValue(_entity, null));
            }
            if (type.GetProperty("AverageValue") != null && type.GetProperty("AverageValue").GetValue(_entity, null) != null)
            {
                data.Param_Value = Convert.ToDouble(type.GetProperty("AverageValue").GetValue(_entity, null));
            }
            if (type.GetProperty("MaximumValue") != null && type.GetProperty("MaximumValue").GetValue(_entity, null) != null)
            {
                data.MaxPowerValue = float.Parse(type.GetProperty("MaximumValue").GetValue(_entity, null).ToString());
            }
            if (type.GetProperty("MinimumValue") != null && type.GetProperty("MinimumValue").GetValue(_entity, null) != null)
            {
                data.MinPowerValue = float.Parse(type.GetProperty("MinimumValue").GetValue(_entity, null).ToString());
            }
            if (type.GetProperty("AverageValue") != null && type.GetProperty("AverageValue").GetValue(_entity, null) != null)
            {
                data.AvePowerValue = float.Parse(type.GetProperty("AverageValue").GetValue(_entity, null).ToString());
            }
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.Param_Type_Code = Convert.ToInt32(type.GetProperty("ParamTypeCode").GetValue(_entity, null));
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();

            return data;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转速波形数据转换
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="_entity"></param>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static RotSpeedWaveData ConvertRotSpdWF<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            RotSpeedWaveData data = new RotSpeedWaveData();

            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            data.Data_Qual_Type = (EnumDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.WaveLength = Convert.ToInt32(type.GetProperty("WaveLength").GetValue(_entity, null));
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            data.WaveLength =Convert.ToInt32( type.GetProperty("WaveLength").GetValue(_entity,null));
            try
            {
                data.GearRatio = Convert.ToSingle(type.GetProperty("GearRatio").GetValue(_entity, null).ToString());
            }
            catch{
                data.GearRatio = 1;
            }
            data.LineCounts = Convert.ToInt32(type.GetProperty("LineCounts").GetValue(_entity, null));
            try
            {
                data.waveDataPath = Encoding.Default.GetString((byte[])type.GetProperty("WaveDataPath").GetValue(_entity, null));
            }
            catch {
                data.waveDataPath = string.Empty;
            }
            return data;

        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 转换振动波形数据
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WaveFormData ConvertWFData<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            WaveFormData data = null;

            EnumMeasDefType WFType = (EnumMeasDefType)Convert.ToInt32(
                type.GetProperty("WaveType").GetValue(_entity, null));

            switch (WFType)
            {
                case EnumMeasDefType.MDF_Time:
                    data = new WaveFormData_Time();
                    break;
                case EnumMeasDefType.MDF_Envelope:
                    data = new WaveFormData_Envelope();
                    break;
                case EnumMeasDefType.MDF_Order:
                    data = new WaveFormData_Order();
                    break;
                case EnumMeasDefType.MDF_OrderEnvelope:
                    data = new WaveFormData_OrderEnv();
                    break;
                default:
                    break;
            }

            data.ComponentName = type.GetProperty("ComponentName").GetValue(_entity, null).ToString();
            data.Data_Qual_Type = (EnumDataQualityType)Convert.ToInt32(
                type.GetProperty("DataQualType").GetValue(_entity, null));
            data.DAUChannelID = Convert.ToInt32(type.GetProperty("DAUChannelID").GetValue(_entity, null));
            data.SampleRate = Convert.ToDouble(type.GetProperty("SampleRate").GetValue(_entity, null));
            data.LocationOrientation = type.GetProperty("LocationOrientation").GetValue(_entity, null).ToString();
            data.LocationSection = type.GetProperty("LocationSection").GetValue(_entity, null).ToString();
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.SignalType = type.GetProperty("SignalType").GetValue(_entity, null).ToString();
            data.WaveDefinitionID = type.GetProperty("WaveDefinitionID").GetValue(_entity, null).ToString();
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            data.WindParkID = type.GetProperty("WindParkID").GetValue(_entity, null).ToString();
            data.Data_Qual_Type = (EnumDataQualityType)Convert.ToInt32(
                type.GetProperty("DataQualType").GetValue(_entity, null));
            if (type.GetProperty("WaveDataPath").GetValue(_entity, null) != null)
            {
                data.WaveDataPath = Encoding.Default.GetString((byte[])type.GetProperty("WaveDataPath").GetValue(_entity, null));
            }
            data.ConvertCoefficient = Convert.ToDouble(type.GetProperty("ConvertCoefficient").GetValue(_entity, null));
            data.WaveLength = Convert.ToInt32(type.GetProperty("WaveLength").GetValue(_entity, null));
            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            //Maxy@2015-08-20 波形定义参数字符串存储
            //data.WaveDefParamID = type.GetProperty("WaveDefParamID").GetValue(_entity, null).ToString();
            string WaveDefParam = string.Empty;
            if (type.GetProperty("WaveDefParam").GetValue(_entity, null) != null)
            {
                WaveDefParam = type.GetProperty("WaveDefParam").GetValue(_entity, null).ToString();
            }
            //波形参数赋值
            WaveDefParamConvert.DecodeWaveDefParam(data, WFType, WaveDefParam);
            return data;
        }



        public static SVMWaveFormData ConvertSVMWFData<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            SVMWaveFormData data = new SVMWaveFormData();

            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.WaveDefinitionID = type.GetProperty("WaveDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            data.ParamType = (EnumSVMParamType)(Convert.ToInt32(type.GetProperty("ParamType").GetValue(_entity, null)));
            data.SVMRegister = (Convert.ToInt32(type.GetProperty("SVMRegister").GetValue(_entity, null)));
            data.WindParkID = type.GetProperty("WindParkID").GetValue(_entity, null).ToString();
            data.SampleRate =Convert.ToDouble(type.GetProperty("SampleRate").GetValue(_entity, null));
            data.WaveLength = Convert.ToInt32(type.GetProperty("WaveLength").GetValue(_entity, null));
            data.ConvertCoefficient = Convert.ToDouble(type.GetProperty("ConvertCoefficient").GetValue(_entity, null));
            if (type.GetProperty("WaveDataPath").GetValue(_entity, null) != null)
            {
                data.WavePath = Encoding.Default.GetString((byte[])type.GetProperty("WaveDataPath").GetValue(_entity, null));
            }
            data.Data_Qual_Type = (EnumSVMDataQualityType)(Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null)));

            return data;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 特征值数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static EigenValueData ConvertEVData<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            EigenValueData data = new EigenValueData();
            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            data.AlarmDegree = Convert.ToInt32(type.GetProperty("AlarmDegree").GetValue(_entity, null));
            data.Data_Qual_Type = (EnumDataQualityType)Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null));
            data.Eigen_Value = Convert.ToDouble(type.GetProperty("EigenValue").GetValue(_entity, null));
            data.EigenValueID = type.GetProperty("EigenValueID").GetValue(_entity, null).ToString();
            data.EigenValueCode = type.GetProperty("EigenValueCode").GetValue(_entity, null).ToString();
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            data.WaveDefinitionID = type.GetProperty("WaveDefinitionID").GetValue(_entity, null).ToString();
            data.OutPowerBandCode = type.GetProperty("WkConLevelCode").GetValue(_entity, null).ToString();
            data.SamplingTime = Convert.ToInt32(type.GetProperty("SamplingTime").GetValue(_entity,null));
            data.EigenValueCategory = EnumEigenValueCategory.EigenValueCategory_FreqBand;
            return data;
        }

        public static List<EigenValueData> ConvertEVDataTrendList<T>(
            List<T> _list) where T : new()
        {
            Type type = typeof(T);
            List<EigenValueData> eigenValueTrendList = new List<EigenValueData>();
            foreach (T _entity in _list)
            {
                EigenValueData data = new EigenValueData();
                data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
                data.AlarmDegree = Convert.ToInt32(type.GetProperty("AlarmDegree").GetValue(_entity, null));
                data.Data_Qual_Type = (EnumDataQualityType)Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null));
                data.Eigen_Value = Convert.ToDouble(type.GetProperty("EigenValue").GetValue(_entity, null));
                data.EigenValueID = type.GetProperty("EigenValueID").GetValue(_entity, null).ToString();
                data.EigenValueCode = type.GetProperty("EigenValueCode").GetValue(_entity, null).ToString();
                data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
                data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
                data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
                data.OutPowerBandCode = type.GetProperty("WkConLevelCode").GetValue(_entity, null).ToString();
                data.SamplingTime = Convert.ToInt32(type.GetProperty("SamplingTime").GetValue(_entity,null));
                data.EigenValueCategory = EnumEigenValueCategory.EigenValueCategory_FreqBand;
                eigenValueTrendList.Add(data);
            }
            return eigenValueTrendList;
        }

        public static List<EigenValueData> ConvertSVMEVDataTrendList<T>(
    List<T> _list) where T : new()
        {
            Type type = typeof(T);
            List<EigenValueData> eigenValueTrendList = new List<EigenValueData>();
            foreach (T _entity in _list)
            {
                EigenValueData data = new EigenValueData();
                data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
                data.AlarmDegree = Convert.ToInt32(type.GetProperty("AlarmDegree").GetValue(_entity, null));
                data.Data_Qual_Type = (EnumDataQualityType)Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null));
                data.Eigen_Value = Convert.ToDouble(type.GetProperty("EigenValue").GetValue(_entity, null));
                data.EigenValueID = type.GetProperty("EigenValueID").GetValue(_entity, null).ToString();
                //data.EigenValueCode = type.GetProperty("EigenValueCode").GetValue(_entity, null).ToString();
                data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
                data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
                data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
                data.OutPowerBandCode = type.GetProperty("WkConLevelCode").GetValue(_entity, null).ToString();
                //data.SamplingTime = Convert.ToInt32(type.GetProperty("SamplingTime").GetValue(_entity, null));
                eigenValueTrendList.Add(data);
            }
            return eigenValueTrendList;
        }

        /// <summary>
        /// 历史工况数据转换
        /// </summary>
        /// <param name="WorkConditionList"></param>
        /// <returns></returns>
        public static List<WorkingConditionData> GetWorkingConditionByIDAndEigenId(List<WTCMSLive.Entity.Models.WorkConditionTrend> WorkConditionList)
        {
            List<WorkingConditionData> list = new List<WorkingConditionData>();
            foreach (WTCMSLive.Entity.Models.WorkConditionTrend data in WorkConditionList)
            {
                WorkingConditionData myWork = new WorkingConditionData();
                myWork.MeasDefinitionID = data.MeasDefinitionID.ToString();
                myWork.MeasLocationID = data.MeasLocationID.ToString();
                myWork.Param_Type_Code = data.ParamTypeCode;
                myWork.AcquisitionTime = data.AcquisitionTime;
                list.Add(myWork);
            }
            return list;
        }

        public static List<SVMEigenValueData> ConvertSVMEVDataList(List<WTCMSLive.Entity.Models.SVMEVDataTrend> SVMDataList)
        {
            List<SVMEigenValueData> dataList = new List<SVMEigenValueData> ();
            for (int i = 0; i < SVMDataList.Count; i++)
            {
                dataList.Add(ConvertSVMEVData(SVMDataList[i]));
            }
            return dataList;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 特征值数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public static SVMEigenValueData ConvertSVMEVData<T>(
            T _entity) where T : new()
        {
            Type type = typeof(T);

            SVMEigenValueData data = new SVMEigenValueData();
            data.AcquisitionTime = Convert.ToDateTime(type.GetProperty("AcquisitionTime").GetValue(_entity, null));
            if (type.GetProperty("AlarmDegree").GetValue(_entity, null) != null)
            {
                data.AlarmDegree = Convert.ToInt32(type.GetProperty("AlarmDegree").GetValue(_entity, null));
            }
            data.Data_Qual_Type = (EnumSVMDataQualityType)Convert.ToInt32(type.GetProperty("DataQualType").GetValue(_entity, null));
            if (type.GetProperty("EigenValue").GetValue(_entity, null) != null)
            {
                data.EigenValue = float.Parse(type.GetProperty("EigenValue").GetValue(_entity, null).ToString());
            }
            data.EigenValueID = type.GetProperty("EigenValueID").GetValue(_entity, null).ToString();
            data.EigenValueType = (EnumSVMEigenValueType)Convert.ToInt32(type.GetProperty("EigenValueType").GetValue(_entity, null));
            data.MeasDefinitionID = type.GetProperty("MeasDefinitionID").GetValue(_entity, null).ToString();
            data.MeasLocationID = type.GetProperty("MeasLocationID").GetValue(_entity, null).ToString();
            data.WindTurbineID = type.GetProperty("WindTurbineID").GetValue(_entity, null).ToString();
            if (type.GetProperty("WkConLevelCode").GetValue(_entity, null) != null)
            {
                data.LevelCode = type.GetProperty("WkConLevelCode").GetValue(_entity, null).ToString();
            }
            data.SVMRegister = type.GetProperty("SVMRegister").GetValue(_entity, null).ToString();
            data.WaveDefinitionID = type.GetProperty("WaveDefinitionID").GetValue(_entity, null).ToString();
            return data;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 直流分量数据转换
        /// </summary>
        /// <param name="_data"></param>
        /// <returns></returns>
        public static WTCMSLive.BusinessEntity.SensorDCData ConvertSensorDCData(
             WTCMSLive.Entity.Models.SensorDCData _data)
        {
            WTCMSLive.BusinessEntity.SensorDCData data = new WTCMSLive.BusinessEntity.SensorDCData();

            data.ChannelNumber = _data.ChannelNumber;
            data.DCAcquisitionTime = _data.DCAcquisitionTime;
            data.DCDataValue = (double)_data.DCDataValue;
            data.Ds_asset_id = _data.WindTurbineID.ToString();

            return data;
        }

        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 缓存数据更新记录表
        /// </summary>
        /// <param name="_record"></param>
        /// <returns></returns>
        public static RTWaveFormDataTableUpdateRecord ConvertBufferDataUpDateRecord(
            WTCMSLive.Entity.Models.BufferDataUpdateRecordHi _record)
        {
            RTWaveFormDataTableUpdateRecord record = new RTWaveFormDataTableUpdateRecord();

            record.WindTurbineID = _record.WindTurbineID.ToString();
            record.AlamState = (int)_record.AlarmState;            
            record.MeasDefinitionID = _record.MeasDefinitionID.ToString();
            if (_record.UpdateTimeAlarm != null)
            {
                record.UpdateTimeAlarm = (DateTime)_record.UpdateTimeAlarm;
            }
            if (_record.UpdateTimeDay != null)
            {
                record.UpdateTimeDay = (DateTime)_record.UpdateTimeDay;
            }
            if (_record.UpdateTimeEigenValue != null)
            {
                record.UpdateTimeEig = (DateTime)_record.UpdateTimeEigenValue;
            }
            if (_record.UpdateTimeHis != null)
            {
                record.UpdateTimeHis = (DateTime)_record.UpdateTimeHis;
            }
            if (_record.UpdateTimeHour != null)
            {
                record.UpdateTimeHour = (DateTime)_record.UpdateTimeHour;
            }
            record.WindTurbineID = _record.WindTurbineID.ToString();
            record.WorkCondDegree = (short)_record.WkConLevelCode;

            return record;
        }

        /// <summary>
        /// 转换波形挑选记录
        /// </summary>
        /// <param name="_record"></param>
        /// <returns></returns>
        public static WaveDataSelectedRecord ConvertWaveDataSelectRecord(
            WTCMSLive.Entity.Models.WaveDataSelectedRecord _record)
        {
            WaveDataSelectedRecord rec = new WaveDataSelectedRecord();
            rec.WindTurbineID = _record.WindTurbineID.ToString();
            rec.MeasDefinitionID = _record.MeasDefinitionID.ToString();
            rec.RangeID = _record.RangeID.ToString();
            rec.SelectTime =(DateTime) _record.SelectTime;
            return rec;
        }
        /// <summary>
        /// 转换波形数据传输任务
        /// </summary>
        /// <param name="_assignment"></param>
        /// <returns></returns>
        public static SynchroDataTask ConvertSynchroDataTask(
            WTCMSLive.Entity.Models.SynchroDataTask _assignment)
        {
            SynchroDataTask ass = new SynchroDataTask();
            ass.WindTurbineID = _assignment.WindTurbineID.ToString();
            ass.ServerAddress = _assignment.ServerAddress;
            ass.MeasDefinitionID = _assignment.MeasDefinitionID.ToString();
            ass.AcquisitionTime = _assignment.AcquisitionTime;
            ass.SynchroDataFilePath = _assignment.SynchroDataFilePath;
            ass.PriorityLevel = (int)_assignment.PriorityLevel;
            ass.NetType = (EnumSynchroDataNetType)_assignment.NetType;
            return ass;
        }

        #region 特征值趋势数据
        public static List<EVDataTrendMeasEvent_bussiness> ConvertEVDataTrendMeasEventList(List<WTCMSLive.Entity.Models.EVDataTrendMeasEvent> _EVDataList)
        {
            List<EVDataTrendMeasEvent_bussiness> dataList = new List<EVDataTrendMeasEvent_bussiness>();
            foreach (WTCMSLive.Entity.Models.EVDataTrendMeasEvent _data in _EVDataList)
            {
                dataList.Add(ConvertEVDataTrendMeasEvent(_data));
            }
            return dataList;
        }

        /// <summary>
        /// 特征值趋势数据
        /// </summary>
        /// <param name="_EVData"></param>
        /// <returns></returns>
        public static EVDataTrendMeasEvent_bussiness ConvertEVDataTrendMeasEvent( WTCMSLive.Entity.Models.EVDataTrendMeasEvent _EVData)
        {
            EVDataTrendMeasEvent_bussiness data = new EVDataTrendMeasEvent_bussiness();
            data.AcquisitionTime = _EVData.AcquisitionTime;
            data.AlarmDegree = _EVData.AlarmDegree;
            data.DataQualType = _EVData.DataQualType;
            data.EigenValueNum = _EVData.EigenValueNum;
            data.EigenValueNumSVM = _EVData.EigenValueNumSVM;
            data.MeasDefinitionID = _EVData.MeasDefinitionID;
            data.WindTurbineID = _EVData.WindTurbineID;
            data.WkConDataNum = _EVData.WkConDataNum;
            data.WkConLevelCode = _EVData.WkConLevelCode;
            return data;
        }

        private static List<SVMEVDataTrend_bussiness> ConvertSVMEVDataTrends_bussinessList(ICollection<Entity.Models.SVMEVDataTrend> _dataList)
        {
            List<SVMEVDataTrend_bussiness> dataList = new List<SVMEVDataTrend_bussiness>();
            foreach (Entity.Models.SVMEVDataTrend _data in _dataList)
            {
                SVMEVDataTrend_bussiness data = new SVMEVDataTrend_bussiness();
                data.AcquisitionTime = _data.AcquisitionTime;
                data.AlarmDegree = _data.AlarmDegree;
                data.DataQualType = _data.DataQualType;
                data.ParamType = _data.EigenValueType;
                data.EigenValue = _data.EigenValue;
                data.EigenValueID = _data.EigenValueID;
                data.MeasDefinitionID = _data.MeasDefinitionID;
                data.MeasLocationID = _data.MeasLocationID;
                data.WaveDefinitionID = _data.WaveDefinitionID;
                data.WindTurbineID = _data.WindTurbineID;
                data.WkConLevelCode = _data.WkConLevelCode;
                dataList.Add(data);
            }
            return dataList;
        }

        private static List<EVDataTrend_bussiness> ConvertEVDataTrend_bussinessList(ICollection<Entity.Models.EVDataTrend> _dataList)
        {
            List<EVDataTrend_bussiness> dataList = new List<EVDataTrend_bussiness>();
            foreach(Entity.Models.EVDataTrend _data in _dataList)
            {
                EVDataTrend_bussiness data = new EVDataTrend_bussiness();
                data.AcquisitionTime = _data.AcquisitionTime;
                data.AlarmDegree = _data.AlarmDegree;
                data.DataQualType = _data.DataQualType;
                data.EigenValue = _data.EigenValue;
                data.EigenValueCode = _data.EigenValueCode;
                data.EigenValueID = _data.EigenValueID;
                data.MeasDefinitionID = _data.MeasDefinitionID;
                data.MeasLocationID = _data.MeasLocationID;
                data.WaveDefinitionID = _data.WaveDefinitionID;
                data.WindTurbineID = _data.WindTurbineID;
                data.WkConLevelCode = _data.WkConLevelCode;
                dataList.Add(data);
            }
            return dataList;
        }

        
        #endregion
    }
}
