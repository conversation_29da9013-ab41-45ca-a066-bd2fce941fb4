﻿using CMSFramework.BusinessEntity.SVM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CMSFramework.EigenValueDef
{
    public static class EVAMSNameUtility
    {
        /// <summary>
        /// 特征值Name
        /// </summary>
        /// <param name="eigenValueCode"></param>
        /// <returns>下限带宽-上限带宽</returns>
        public static string GetEVNameByEVCode(string eigenValueCode)
        {
            string code = eigenValueCode;
            if (eigenValueCode.Contains('_')) {
                code = eigenValueCode.Split('_')[1];
            }
            return GetAMSEigenValue().FirstOrDefault(item => item.Key == code).Value;
        }


        /// <summary>
        /// 特征值Name
        /// </summary>
        /// <param name="eigenValueType"></param>
        /// <returns></returns>
        public static string GetSVMEVNameByEVCode(EnumSVMEigenValueType eigenValueType)
        {
            // 原来的系统均为枚举值
            //EnumSVMEigenValueType evt = (EnumSVMEigenValueType)int.Parse(eigenValueCode);

            return AppFramework.Utility.EnumHelper.GetDescription(eigenValueType);            
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        private static string GetEigenValueNameByType(string type)
        {
            if (AMSEigenValue.Keys.Count == 0)
            {
                GetAMSEigenValue();
            }
            if (AMSEigenValue.Keys.Contains(type))
            {
                return AMSEigenValue[type];
            }
            else {
                return "";
            }
        }
        private static string ConvertFreBand(string p)
        {
            if (p.Contains("K") || p.Contains("k"))
            {
                //本身转换完成
                return p;
            }
            double freBand = Convert.ToDouble(p);
            string dataBand = string.Empty;
            if (freBand >= 1000)
            {
                double data = freBand / 1000;
                dataBand = data.ToString() + "K";
            }
            else
            {
                dataBand = p;
            }
            return dataBand;
        }
        private static Dictionary<string, string> AMSEigenValue = new Dictionary<string, string>();
        public static Dictionary<string, string> GetAMSEigenValue()
        {
            if (AMSEigenValue.Keys.Count == 0)
            {
                AMSEigenValue = new Dictionary<string, string>
                {
                    { "RMS", "有效值" },
                    { "RMSE", "均方根值" },
                    { "VCE", "方差" },
                    { "KT", "峭度" },
                    { "SK", "偏斜度" },
                    { "PK", "峰值" },
                    { "PPK", "峰峰值" },
                    { "SM", "谱均值" },
                    { "SC", "谱重心" },
                    { "LF", "裕度" },
                    { "KTS", "峭度指标" },
                    { "SKF", "偏斜度指标" },

                    { "RMSE-OS", "均方根值_位移" },
                    { "VCE-OS", "方差_位移" },
                    { "PPK-OS", "峰峰值_位移" },
                    { "SK-OS", "歪度_位移" },
                    { "SF-OS", "波形指标_位移" },
                    { "KTS-OS", "峭度指标_位移" },

                };
            }
            return AMSEigenValue;
        }
    }
}
