﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.Entity.Models;

namespace WTCMSLive.BusinessEntityConvert
{
    public static class ConvertEntityDAToBusinessCommon
    {
        /// <summary>
        /// 系统配置表 
        /// </summary>
        /// <param name="_entity">SysConfiguration</param>
        /// <returns></returns>
        public static Global ConvertGlobal(SysConfiguration _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            Global _Global = new Global();

            _Global.DatabaseVersion = _entity.DatabaseVersion;
            _Global.SystemState = _entity.SystemState;

            return _Global;
        }

        /// <summary>
        /// 系统运行日志 
        /// </summary>
        /// <param name="_entity">SysRunningLog</param>
        /// <returns></returns>
        public static LogEntity ConvertLogEntity(SysRunningLog _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            LogEntity _LogEntity = new LogEntity();
            _LogEntity.LogTime = _entity.EventTime;
            _LogEntity.OperationDescription = _entity.LogContent;
            _LogEntity.OperationDescription = _entity.LogTitle;
            _LogEntity.LogDB = Convert.ToInt32(_entity.LogType);
            _LogEntity.UserName = _entity.Operator;

            return _LogEntity;
        }

        /// <summary>
        /// 查询条件实体
        /// </summary>
        /// <param name="_entity">SysLogQueryCondition</param>
        /// <returns></returns>
        public static LogQueryCondition ConvertLogQueryCondition(WTCMSLive.Entity.Models.SysLogQueryCondition _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            LogQueryCondition _LogQueryCondition = new LogQueryCondition();

            //_SysRunningLog.devwindpark;
            _LogQueryCondition.EndTime = _entity.EndTime;
            _LogQueryCondition.KeyWords = _entity.KeyWords;
            _LogQueryCondition.UserName = _entity.UserName;
            _LogQueryCondition.StartTime = _entity.StartTime;
            _LogQueryCondition.LogDB = (int)_entity.LogType;
            //_SysRunningLog.WindParkID;

            return _LogQueryCondition;
        }

        /// <summary>
        /// 用户表 
        /// </summary>
        /// <param name="_entity">User</param>
        /// <returns></returns>
        public static User ConvertSysUser(SysUser _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            User _User = new User();

            _User.PassWord = _entity.PassWord;
            _User.UserID = _entity.UserID;
            _User.UserName = _entity.UserName;
            _User.UserState = _entity.UserState;
            _User.UserRole = ConvertEntityDAToBusinessCommon.ConvertSysRole(_entity.SysRoles.ToList()[0]);
            _User.FunctionIDs = new List<string>();
            _User.Email = _entity.Email;
            _User.Phone = _entity.Phone;
            foreach (Function item in _User.UserRole.Functions)
            {
                _User.FunctionIDs.Add(item.FunctionID);
            }

            _User.ModuleIDs = new List<string>();

            foreach (Module item in _User.UserRole.Modules)
            {
                _User.ModuleIDs.Add(item.ModuleID);
            }

            return _User;
        }

        /// <summary>
        /// 角色表 
        /// </summary>
        /// <param name="_entity">SysRole</param>
        /// <returns></returns>
        public static Role ConvertSysRole(SysRole _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            Role _SysRole = new Role();

            _SysRole.RoleID = _entity.RoleID.ToString();
            _SysRole.RoleName = _entity.RoleName;

            _SysRole.Functions = new List<Function>();

            foreach (SysFunction item in _entity.SysFunctions)
            {
                _SysRole.Functions.Add(ConvertEntityDAToBusinessCommon.ConvertSysFunction(item));
            }

            _SysRole.Modules = new List<Module>();

            foreach (SysModule item in _entity.SysModules)
            {
                _SysRole.Modules.Add(ConvertEntityDAToBusinessCommon.ConvertSysModule(item));
            }

            _SysRole.IsSystemRole = _entity.IsSystemRole == 1;

            return _SysRole;
        }

        /// <summary>
        /// 权限表 
        /// </summary>
        /// <param name="_entity">SysFunction</param>
        /// <returns></returns>
        public static Function ConvertSysFunction(SysFunction _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            Function _SysFunction = new Function();

            _SysFunction.FunctionID = _entity.FunctionID.ToString();
            _SysFunction.FunctionName = _entity.FunctionName;
            _SysFunction.ModuleID = _entity.ModuleID.ToString();
            //_SysFunction.sysmodule;
            //_SysFunction.sysroles;

            return _SysFunction;
        }

        /// <summary>
        /// 模块表 SysModule
        /// </summary>
        /// <param name="_entity">SysModule</param>
        /// <returns></returns>
        public static Module ConvertSysModule(SysModule _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            Module _SysModule = new Module();

            _SysModule.ModuleID = _entity.ModuleID.ToString();
            _SysModule.ModuleName = _entity.ModuleName;

            _SysModule.ModuleFunctions = new List<Function>();

            foreach (SysFunction item in _entity.SysFunctions)
            {
                _SysModule.ModuleFunctions.Add(ConvertEntityDAToBusinessCommon.ConvertSysFunction(item));
            }

            return _SysModule;
        }


        /// <summary>
        /// 转换用户列表 
        /// </summary>
        /// <param name="_list">Template</param>
        /// <returns></returns>
        public static List<User> ConvertUserList(List<WTCMSLive.Entity.Models.SysUser> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<User> userList = new List<User>();

            foreach (WTCMSLive.Entity.Models.SysUser item in _list)
            {
                userList.Add(ConvertEntityDAToBusinessCommon.ConvertSysUser(item));
            }

            return userList;
        }

        /// <summary>
        /// 转换角色列表 
        /// </summary>
        /// <param name="_list">Template</param>
        /// <returns></returns>
        public static List<Role> ConvertRoleList(List<WTCMSLive.Entity.Models.SysRole> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<Role> roleList = new List<Role>();

            foreach (WTCMSLive.Entity.Models.SysRole item in _list)
            {
                roleList.Add(ConvertEntityDAToBusinessCommon.ConvertSysRole(item));
            }

            return roleList;
        }

        /// <summary>
        /// 转换角色列表 
        /// </summary>
        /// <param name="_list">Template</param>
        /// <returns></returns>
        public static List<Function> ConvertSysFunctionList(List<WTCMSLive.Entity.Models.SysFunction> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<Function> functionList = new List<Function>();

            foreach (WTCMSLive.Entity.Models.SysFunction item in _list)
            {
                functionList.Add(ConvertEntityDAToBusinessCommon.ConvertSysFunction(item));
            }

            return functionList;
        }

        /// <summary>
        /// 系统运行日志
        /// </summary>
        /// <param name="_entity">SysRunningLog</param>
        /// <returns></returns>
        public static List<SystemRunningLog> ConvertSystemRunningLogList(List<WTCMSLive.Entity.Models.SysRunningLog> _list)
        {
            if (_list == null)
            {
                return null;
            }

            List<SystemRunningLog> sysList = new List<SystemRunningLog>();

            foreach (SysRunningLog _entity in _list)
            {
                SystemRunningLog _SystemRunningLog = new SystemRunningLog();

                //_SystemRunningLog.devwindpark = _entity.devwindpark;
                _SystemRunningLog.EventTime = _entity.EventTime;
                _SystemRunningLog.LogContent = _entity.LogContent;
                _SystemRunningLog.LogTitle = _entity.LogTitle;
                _SystemRunningLog.LogType = _entity.LogType.ToString();
                _SystemRunningLog.Operator = _entity.Operator;
                //_SystemRunningLog.WindParkID = _entity.WindParkID;

                sysList.Add(_SystemRunningLog);
            }

            return sysList;
        }
    }
}
