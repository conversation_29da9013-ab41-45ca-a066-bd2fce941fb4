﻿using WTCMSLive.BusinessModel;
using WTCMSLive.WebSite.Models;
using WTCMSLive.WebSite.Models.DTOs;
using WTCMSLive.WebSite.Core.Models;
using WTCMSLive.WebSite.Core.Models.DTOs;
using WTCMSLive.WebSite.Core.Attributes;
using CMSFramework.BusinessEntity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace WTCMSLive.WebSite.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class WarnController : ControllerBase
    {
        // 王岩 2015年6月16日 16:44:06
        //报警定义配置相关方法

        #region 风机相关
        /// <summary>
        /// 根据部件名称获取振动测量位置
        /// </summary>
        /// <param name="turbineID"></param>
        /// <param name="ComponentID"></param>
        /// <returns></returns>
        public string GetVibMeasLocationByComId(string _turbineID, string ComponentID)
        {
            //List<MeasLoc_Vib> list = DevTreeManagement.GetMeasLocVibListByTurIds(new List<string>() { _turbineID });
            List<MeasLoc_Vib> list = DevTreeManagement.GetVibMeasLocationByComId(ComponentID);
            return list.OrderBy(item => item.OrderSeq).ToJson();
        }
        /// <summary>
        /// 根据机组ID获取振动测量位置信息
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        public string GetMeasLocVibListByTurIds(string _turbineID)
        {
            List<MeasLoc_Vib> list = DevTreeManagement.GetVibMeasLocationByTurId(_turbineID);
            return list.OrderBy(item => item.OrderSeq).ToJson();
        }

        /// <summary>
        /// 获取机组下部件的名称
        /// </summary>
        /// <param name="turbineid"></param>
        /// <returns></returns>
        public string GetComponetList(string _turbineID)
        {
            List<WindTurbineComponent> list = new List<WindTurbineComponent>();
            List<WindTurbineComponent> myList = DevTreeManagement.GetWindTurbine(_turbineID).TurComponentList;
            myList.ForEach(item =>
            {
                list.Add(new WindTurbineComponent()
                {
                    ComponentID = item.ComponentID,
                    ComponentName = item.ComponentName
                });
            });
            return myList.ToJson();
        }
        /// <summary>
        /// 获取机组下的晃度仪信息
        /// </summary>
        /// <param name="_turbineID"></param>
        /// <returns></returns>
        public string GetSVMByTurId(string _turbineID)
        {
            SVMUnit mySVM = SVMManagement.GetSVMById(_turbineID);
            List<MeasLoc_SVM> SvmList = new List<MeasLoc_SVM>();
            if (mySVM != null)
            {
                SvmList = SVMManagement.GetMeasLoc_SVMListByTurID(_turbineID);
                // 移除不报警的测点
                SvmList = SvmList.Where(t => t.ParamType == EnumSVMParamType.YInclination
                                                    || t.ParamType == EnumSVMParamType.Pitch || t.ParamType == EnumSVMParamType.Horizontal || t.ParamType == EnumSVMParamType.Vertical).ToList();

                List<SVMRegister> RegisterList = SVMManagement.GetSVMRegisterListBySVMId(_turbineID);

                RegisterList.ForEach(item =>
                {
                    MeasLoc_SVM SVM = SvmList.Find(MeasLoc => MeasLoc.MeasLocationID == item.SVMMeasLocId);

                    if (SVM == null)
                    {
                        SvmList.RemoveAll(MeasLoc => MeasLoc.MeasLocationID == item.SVMMeasLocId);
                    }
                });
                //测点名称中添加部件截面信息
                SvmList.ForEach(item =>
                {
                    item.OrderSeq = 0;// 用于区分svm和油液
                                      //item.MeasLocName += item.SectionName == "塔基"?"(塔基)":"(塔顶)";
                });
            }


            // 添加油液测点
            using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
            {
                var oil = ctx.OilUnits.FirstOrDefault(t => t.WindTurbineID == _turbineID);
                if (oil != null)
                {
                    if (oil.ViscositySensorEnabled)
                    {
                        SvmList.Add(new MeasLoc_SVM()
                        {
                            WindTurbineID = _turbineID,
                            MeasLocationID = _turbineID + "ViscositySensor",
                            ParamType = EnumSVMParamType.Axisl,
                            MeasLocName = "油液粘度",
                            OrderSeq = 2,
                        });
                    }
                    if (oil.WaterSensorEnabled)
                    {
                        SvmList.Add(new MeasLoc_SVM()
                        {
                            WindTurbineID = _turbineID,
                            MeasLocationID = _turbineID + "WaterSensor",
                            ParamType = EnumSVMParamType.Axisl,
                            MeasLocName = "油液水分",
                            OrderSeq = 2,
                        });

                    }

                    if (oil.WearParticleSensorEnabled)
                    {
                        SvmList.Add(new MeasLoc_SVM()
                        {
                            WindTurbineID = _turbineID,
                            MeasLocationID = _turbineID + "WearParticleSensor",
                            ParamType = EnumSVMParamType.Axisl,
                            MeasLocName = "油液磨粒",
                            OrderSeq = 2,
                        });
                    }
                }
            }

            //"[[" + mySVM.ToJson() + "]," +  + "]";
            return SvmList.OrderBy(item => item.OrderSeq).ToJson();
        }
        /// <summary>
        /// 根据类别获取特征值列表
        /// </summary>
        /// <param name="dataType">1 振动，0 晃动，2油液</param>
        /// <returns></returns>
        public string GetEigenValueTypeByMeasLocId(string turbineID, string measLocId, string MeasLocType)
        {
            if (string.IsNullOrEmpty(measLocId)) return "";
            if (MeasLocType == "1")
            {
                List<EigenValueData_Vib> list = EigenValueManage.GetPublicEigenValueList(turbineID, measLocId);
                list.ForEach(i =>
                {
                    i.EigenValueID = i.EigenValueCode;
                    //i.EngUnitName= EigenValueManage.GetFreBandByCode(i.EigenValueCode);

                    //if (string.IsNullOrEmpty(i.EngUnitName))
                    //{
                    //    i.EngUnitName = i.EigenValueCode;
                    //}
                });
                return list.ToJson();
            }
            else if (MeasLocType == "0")
            {
                List<EigenValueData_SVM> list = EigenValueManage.GetSVMPublicEigenValueList(turbineID, measLocId);
                list.ForEach(i =>
                {
                    i.EngUnitName = AppFramework.Utility.EnumHelper.GetDescription(i.EigenValueType);
                });
                return list.ToJson();
            }
            else
            {
                // 油液特征值
                List<EigenValueData_SVM> eigenValueList = new List<EigenValueData_SVM>();
                measLocId = measLocId.Replace(turbineID, "");
                switch (measLocId)
                {
                    case "ViscositySensor":
                        EigenValueData_SVM w1 = new EigenValueData_SVM();
                        w1.EigenValueID = "EqualNKinematicViscosity";
                        //p2.EigenValueType = EnumSVMEigenValueType.TDB_Avg;
                        w1.WindTurbineID = turbineID;
                        w1.EngUnitName = "40℃等效粘度";
                        eigenValueList.Add(w1);
                        break;
                    case "WaterSensor":
                        EigenValueData_SVM wp1 = new EigenValueData_SVM();
                        wp1.EigenValueID = "WaterContent";
                        //p2.EigenValueType = EnumSVMEigenValueType.TDB_Avg;
                        wp1.WindTurbineID = turbineID;
                        wp1.EngUnitName = "水含量";
                        eigenValueList.Add(wp1);
                        break;
                    case "WearParticleSensor":
                        EigenValueData_SVM p1 = new EigenValueData_SVM();
                        p1.EigenValueID = "FeConcentration";
                        //p1.EigenValueType = EnumSVMEigenValueType.NaturalFrequency;
                        p1.WindTurbineID = turbineID;
                        p1.EngUnitName = "铁磁磨粒浓度";
                        eigenValueList.Add(p1);

                        // 位移平均值
                        EigenValueData_SVM p2 = new EigenValueData_SVM();
                        p2.EigenValueID = "NonFeConcentration";
                        //p2.EigenValueType = EnumSVMEigenValueType.TDB_Avg;
                        p2.WindTurbineID = turbineID;
                        p2.EngUnitName = "非铁磁磨粒浓度";
                        eigenValueList.Add(p2);
                        break;
                    default:
                        break;
                }
                return eigenValueList.ToJson();
            }
        }
        /// <summary>
        /// 获取显示数据
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="MeasLocType">1 振动，0 晃动</param>
        /// <returns></returns>
        public string GetEigenValueList(string turbineID, string MeasLocType)
        {
            //List<AlarmDefinitonUIModel> list = new List<AlarmDefinitonUIModel>();
            //EnumMeasLocType MeasType = MeasLocType == "0" ? EnumMeasLocType.SVMAlarmDef : EnumMeasLocType.VibAlarmDef;
            //List<AlarmDefinition> alarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(turbineID, MeasType);

            ////List<AlarmDefinition> groupDef = AlarmDefinitionManage.GetAlarmDefListByTurID(turbineID);
            //List<MeasLoc_Vib> myMeasLocList= new List<MeasLoc_Vib>();
            //if (MeasLocType == "1")
            //{
            //    myMeasLocList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID).ToList();
            //}
            //else
            //{
            //    SVMManagement.GetMeasLoc_SVMListByTurID(turbineID).ForEach(item =>
            //        myMeasLocList.Add(new MeasLoc_Vib()
            //        {
            //            MeasLocationID = item.MeasLocationID,
            //            MeasLocName = item.MeasLocName,
            //            OrderSeq = item.OrderSeq,
            //            SectionName = item.SectionName
            //        })) ;
            //}
            //if (myMeasLocList.Count == 0)
            //{
            //    return "[]";
            //}
            //List<KeyValuePair<int, string>> parmeTypelist = EnumWorkCondParamTypeHelper.GetParamTypeList();
            //foreach (AlarmDefinition myDef in alarmDef)
            //{
            //    bool isAcceleratedSpeed = false;
            //    AlarmDefinitonUIModel myModel = new AlarmDefinitonUIModel();
            //    myModel.WindTurbineID = myDef.WindTurbineID.ToString();
            //    myModel.MeasLocationID = myDef.MeasLocationID.ToString();
            //    myModel.EigenValueID = myDef.EigenValueID;
            //    string EigenCode = myDef.EigenValueID.IndexOf("&&") > -1 ? myDef.EigenValueID.Split(new string[] { "&&" }, StringSplitOptions.None)[1] : "";
            //    if (string.IsNullOrEmpty(EigenCode))
            //        myModel.EigenValueName = "";
            //    else
            //    {
            //        if (MeasLocType == "1")
            //        {
            //            string eigenUnit = "";
            //            if (EigenCode.IndexOf("VRMS") > -1)
            //            {
            //                eigenUnit = " (mm/s)";
            //            }
            //            else if (EigenCode.IndexOf("RMS") > -1 || EigenCode.IndexOf("PK") > -1 || EigenCode.IndexOf("PPK") > -1)
            //            {
            //                eigenUnit = " (m/s^2)";
            //            }
            //            myModel.EigenValueName = EigenValueManage.GetFreBandByCode(EigenCode) + eigenUnit;
            //            if (string.IsNullOrEmpty(myModel.EigenValueName))
            //            {
            //                myModel.EigenValueName = EigenCode;

            //            }

            //        }
            //        else
            //        {
            //            List<EigenValueData_SVM> EigenValueSVM = EigenValueManage.GetSVMPublicEigenValueList(turbineID, myModel.MeasLocationID);
            //            if (EigenValueSVM!=null) {
            //                //return "[]";
            //                EigenValueData_SVM myValue = EigenValueSVM.Find(item => (myModel.MeasLocationID + "&&" + item.EigenValueID) == myDef.EigenValueID);
            //                myModel.EigenValueName = AppFramework.Utility.EnumHelper.GetDescription(myValue.EigenValueType);
            //                if (myModel.EigenValueName.IndexOf("加速度") > -1)
            //                {
            //                    myModel.EigenValueName += " (mg)";
            //                }
            //                if (myModel.EigenValueName.IndexOf("角") > -1)
            //                {
            //                    myModel.EigenValueName += " (°)";
            //                }
            //            }

            //        }
            //    }
            //    myModel.ThresholdGroup = myDef.ThresholdGroup;
            //    myModel.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
            //    myModel.UpperLimitValue = myDef.UpperLimitValue;
            //    myModel.LowerLimitValue = myDef.LowerLimitValue;
            //    MeasLoc_Vib vib = myMeasLocList.Find(i => i.MeasLocationID == myModel.MeasLocationID);

            //    if (vib == null)
            //    {
            //        continue;
            //    }

            //    myModel.MeasLocationName = vib.MeasLocName;
            //    if (MeasLocType == "0")
            //    {
            //        //myModel.MeasLocationName += vib.SectionName == "TOP" ? "(塔顶)" : "(基础)";
            //    }

            //    isAcceleratedSpeed = MeasLocType == "0" && myModel.MeasLocationName.IndexOf("加速度") > -1 ? true : false;
            //    myModel.MeasLocationID = myDef.MeasLocationID;
            //    var parmeType = parmeTypelist.Find(con => con.Key.ToString() == myModel.WorkConditionParamTypeCode);
            //    if (!string.IsNullOrEmpty(parmeType.Value))
            //        myModel.WorkConParameterName = parmeType.Value;
            //    double? WarnValue = null;
            //    double? AlarmValue = null;
            //    double? FXWarnValue = null;
            //    double? FXAlarmValue = null;
            //    AlarmDefThreshold alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item=>item.AlarmDegree== EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Forward);
            //    if (alarmThresholdparame != null)
            //    {
            //        WarnValue = (double)alarmThresholdparame.ThresholdValue;
            //        //if (isAcceleratedSpeed)
            //        //{
            //        //    WarnValue *= 1000;
            //        //}
            //    }
            //    alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Forward);
            //    if (alarmThresholdparame != null)
            //    {
            //        AlarmValue = (double)alarmThresholdparame.ThresholdValue;
            //        //if (isAcceleratedSpeed)
            //        //{
            //        //    AlarmValue *= 1000;
            //        //}
            //    }
            //    alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Reverse);
            //    if (alarmThresholdparame != null)
            //    {
            //        FXWarnValue = alarmThresholdparame.ThresholdValue;
            //        //if (isAcceleratedSpeed)
            //        //{
            //        //    FXWarnValue *= 1000;
            //        //}
            //    }
            //    alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Reverse);
            //    if (alarmThresholdparame != null)
            //    {
            //        FXAlarmValue = alarmThresholdparame.ThresholdValue;
            //        //if (isAcceleratedSpeed)
            //        //{
            //        //    FXAlarmValue *= 1000;
            //        //}
            //    }

            //    myModel.WarnValue = WarnValue;
            //    myModel.AlarmValue = AlarmValue;

            //    myModel.ReverseWarnValue = FXWarnValue;
            //    myModel.ReverseAlarmValue = FXAlarmValue;
            //    list.Add(myModel);
            //}

            //// 添加油液报警查询
            //if (MeasLocType.Equals("0"))
            //{
            //    List<AlarmDefinition> oilAlarmDef = AlarmDefinitionManage.GetMDFAlarmDefListByTurbineId(turbineID, EnumMeasLocType.OilAlarmDef);
            //    // 油液测量位置
            //    Dictionary<string, string> oilMeasloc = new Dictionary<string, string>()
            //    {
            //        {"ViscositySensor","油液粘度" },
            //        {"WaterSensor","油液水分" },
            //        {"WearParticleSensor","油液磨粒" },
            //    };
            //    Dictionary<string, string> oilEv = new Dictionary<string, string>()
            //    {
            //        {"EqualNKinematicViscosity","40℃等效粘度" },
            //        {"WaterContent","水含量" },
            //        {"FeConcentration","铁磁磨粒浓度" },
            //        {"NonFeConcentration","非铁磁磨粒浓度" },
            //    };
            //    foreach(var myDef in oilAlarmDef)
            //    {
            //        AlarmDefinitonUIModel myModel = new AlarmDefinitonUIModel();
            //        myModel.WindTurbineID = myDef.WindTurbineID.ToString();
            //        myModel.MeasLocationID = myDef.MeasLocationID.ToString();
            //        myModel.EigenValueID = myDef.EigenValueID;
            //        myModel.ThresholdGroup = myDef.ThresholdGroup;
            //        myModel.WorkConditionParamTypeCode = myDef.WorkConParameter.ToString();
            //        myModel.UpperLimitValue = myDef.UpperLimitValue;
            //        myModel.LowerLimitValue = myDef.LowerLimitValue;
            //        //MeasLoc_Vib vib = myOilMeasLocList.Find(i => i.MeasLocationID == myModel.MeasLocationID);
            //        //myModel.MeasLocationName = vib.MeasLocName;
            //        var _oilmeasloc = myDef.MeasLocationID.Replace(turbineID, "");
            //        if (oilMeasloc.ContainsKey(_oilmeasloc))
            //        {
            //            myModel.MeasLocationName = oilMeasloc[_oilmeasloc];
            //        }

            //        var _oilEv = myDef.EigenValueID.Split(new[] { "&&" }, StringSplitOptions.None)[1];
            //        if (oilEv.ContainsKey(_oilEv))
            //        {
            //            myModel.EigenValueName = oilEv[_oilEv];
            //        }

            //        myModel.MeasLocationID = myDef.MeasLocationID;
            //        var parmeType = parmeTypelist.Find(con => con.Key.ToString() == myModel.WorkConditionParamTypeCode);
            //        if (!string.IsNullOrEmpty(parmeType.Value))
            //            myModel.WorkConParameterName = parmeType.Value;
            //        double? WarnValue = null;
            //        double? AlarmValue = null;
            //        double? FXWarnValue = null;
            //        double? FXAlarmValue = null;
            //        AlarmDefThreshold alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Forward);
            //        if (alarmThresholdparame != null)
            //        {
            //            WarnValue = (double)alarmThresholdparame.ThresholdValue;
            //        }
            //        alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Forward);
            //        if (alarmThresholdparame != null)
            //        {
            //            AlarmValue = (double)alarmThresholdparame.ThresholdValue;
            //        }
            //        alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Reverse);
            //        if (alarmThresholdparame != null)
            //        {
            //            FXWarnValue = alarmThresholdparame.ThresholdValue;
            //        }
            //        alarmThresholdparame = myDef.AlarmDefThresholdGroup.Find(item => item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Reverse);
            //        if (alarmThresholdparame != null)
            //        {
            //            FXAlarmValue = alarmThresholdparame.ThresholdValue;
            //        }

            //        myModel.WarnValue = WarnValue;
            //        myModel.AlarmValue = AlarmValue;

            //        myModel.ReverseWarnValue = FXWarnValue;
            //        myModel.ReverseAlarmValue = FXAlarmValue;
            //        list.Add(myModel);
            //    }
            //}
            //return list.OrderBy(i => i.EigenValueID).ThenBy(i => i.WorkConParameter).ThenBy(i => i.LowerLimitValue).ToJson();

            return WarnManager.alarmDefinitonUIModels(turbineID, MeasLocType).OrderBy(i => i.EigenValueID).ThenBy(i => i.WorkConParameter).ThenBy(i => i.LowerLimitValue).ToJson();
        }
        /// <summary>
        /// 根据类别获取测量位置下的工况上限
        /// </summary>
        /// <param name="type">1 振动，0 晃动</param>
        /// <param name="measLocId"></param>
        /// <returns></returns>
        public string GetWordCondentMinByMeasLoc(string turbineID, string dataType, string measLocId, string EigenValueId)
        {
            EigenValueId = measLocId + "&&" + EigenValueId;
            string returnValue = AlarmDefinitionManage.GetMDFAlarmDefThresholdGroupMaxList(EigenValueId, (short)EnumWorkCondition_ParamType.WCPT_Power) + "$" + AlarmDefinitionManage.GetMDFAlarmDefThresholdGroupMaxList(EigenValueId, (short)EnumWorkCondition_ParamType.WCPT_RotSpeed);
            return returnValue;
        }
        /// <summary>
        /// 报警定义设置
        /// </summary>
        /// <param name="datatype"></param>
        /// <param name="WarnId"></param>
        /// <param name="MeasLId"></param>
        /// <param name="WindTurbineID"></param>
        /// <param name="WindParkID"></param>
        /// <param name="Condentparame"></param>
        /// <param name="AlarmValue"></param>
        /// <param name="WarnValue"></param>
        /// <param name="EigenValueType"></param>
        /// <param name="isAddType"></param>
        /// <returns></returns>
        public string AddWarnRule(string datatype, string WarnId, string MeasLId, string WindTurbineID, string WindParkID, string Condentparame, double? AttentionValue, double? WarnValue, string EigenValue, string EigenValueType, string isAddType, double? reverseAlarm, double? reverseWarn, bool applyAll)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "添加";
            try
            {
                string effectiveId = "";
                //if (datatype == "0")
                //{
                //    //如果晃度测量位置是加速度的，数据库中的值会缩小1000倍（单位mg）
                //    if (EigenValueType == "0")
                //    {
                //        AttentionValue /= 1000;
                //        WarnValue /= 1000;
                //    }
                //}
                List<string> measLocIdList = new List<string>();
                if (isAddType == "1")
                {
                    //Condentparame 的类型如下
                    //功率:100,300$转速:100,200
                    if (!string.IsNullOrEmpty(Condentparame))
                    {
                        effectiveId = Condentparame;
                        measLocIdList = Condentparame.Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries).ToList();
                    }
                    else
                    {
                        measLocIdList = new List<string>() { "无工况:0,0" };
                        effectiveId = MeasLId + "&&" + EigenValue;
                    }
                    List<AlarmDefinition> myList = new List<AlarmDefinition>();
                    foreach (string data in measLocIdList)
                    {
                        string CondentName = data.Split(':')[0]; //工况参数
                        string LowerLimitValue = data.Split(':')[1].Split(',')[0]; // 上线
                        string UpperLimitValue = data.Split(':')[1].Split(',')[1];  // 下线
                        string GuId = System.Guid.NewGuid().ToString();
                        LowerLimitValue = LowerLimitValue.IndexOf(".") > -1 ? LowerLimitValue.Split('.')[0] : LowerLimitValue;
                        UpperLimitValue = UpperLimitValue.IndexOf(".") > -1 ? UpperLimitValue.Split('.')[0] : UpperLimitValue;
                        AlarmDefinition myAlarm = new AlarmDefinition()
                        {
                            WindTurbineID = WindTurbineID,
                            MeasLocationID = MeasLId,
                            EigenValueID = MeasLId + "&&" + EigenValue,
                            LowerLimitValue = double.Parse(LowerLimitValue),
                            UpperLimitValue = double.Parse(UpperLimitValue),
                            WorkConParameter = (short)(CondentName == "功率" ? EnumWorkCondition_ParamType.WCPT_Power : (CondentName == "无工况" ? EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION : EnumWorkCondition_ParamType.WCPT_RotSpeed)),
                            MeasLocType = (EnumMeasLocType)(int.Parse(datatype)),
                            ThresholdGroup = GuId
                        };
                        if (CondentName.Equals("功率"))
                        {
                            myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_Power;
                        }
                        else if (CondentName.Equals("转速"))
                        {
                            myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_RotSpeed;
                        }
                        else if (CondentName.Equals("温度"))
                        {
                            if (MeasLId.Contains("BLD1"))
                            {
                                myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_Blade_01Temp;
                            }
                            else if (MeasLId.Contains("BLD2"))
                            {
                                myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_Blade_02Temp;
                            }
                            else if (MeasLId.Contains("BLD3"))
                            {
                                myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_Blade_03Temp;
                            }
                            else
                            {
                                myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_Blade_01Temp;
                            }

                        }
                        else
                        {
                            myAlarm.WorkConParameter = (short)EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION;
                        }

                        // 判断是否已经添加报警
                        var _alrm = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(myAlarm.WindTurbineID, myAlarm.MeasLocationID, myAlarm.EigenValueID, myAlarm.WorkConParameter, myAlarm.MeasLocType);
                        if (_alrm != null)
                        {
                            message = string.Format(message, 0, "请勿重复添加！");
                            return "{" + message + "}";
                        }

                        myAlarm.AlarmDefThresholdGroup = new List<AlarmDefThreshold>();
                        if (AttentionValue != null)
                        {
                            myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                            {
                                ThresholdGroup = GuId,
                                AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                                ThresholdValue = (double)AttentionValue,
                                WindTurbineID = WindTurbineID,
                                ThresholdValueType = EnumThresholdValueType.Forward,
                            });
                        }
                        if (WarnValue != null)
                        {
                            myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                            {
                                ThresholdGroup = GuId,
                                AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                                ThresholdValue = (double)WarnValue,
                                WindTurbineID = WindTurbineID,
                                ThresholdValueType = EnumThresholdValueType.Forward,
                            });
                        }

                        if (reverseAlarm != null)
                        {
                            myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                            {
                                ThresholdGroup = GuId,
                                AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                                ThresholdValue = (double)reverseAlarm,
                                WindTurbineID = WindTurbineID,
                                ThresholdValueType = EnumThresholdValueType.Reverse,
                            });
                        }

                        if (reverseWarn != null)
                        {
                            myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                            {
                                ThresholdGroup = GuId,
                                AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                                ThresholdValue = (double)reverseWarn,
                                WindTurbineID = WindTurbineID,
                                ThresholdValueType = EnumThresholdValueType.Reverse,
                            });
                        }
                        myList.Add(myAlarm);
                    }
                    if (applyAll)
                    {
                        List<AlarmDefinition> applyall = new List<AlarmDefinition>();
                        var turLIst = DevTreeManagement.GetTurbinesListByWindParkId(WindParkID);
                        turLIst = turLIst.Where(t => t.WindTurbineModel == turLIst.Find(p => p.WindTurbineID == WindTurbineID).WindTurbineModel && t.WindTurbineID != WindTurbineID).ToList();

                        foreach (var tur in turLIst)
                        {
                            foreach (var alarm in myList)
                            {
                                string _id = System.Guid.NewGuid().ToString();
                                AlarmDefinition curTurbineAlarm = new AlarmDefinition();
                                curTurbineAlarm.WindTurbineID = tur.WindTurbineID;
                                curTurbineAlarm.WorkConParameter = alarm.WorkConParameter;
                                curTurbineAlarm.LowerLimitValue = alarm.LowerLimitValue;
                                curTurbineAlarm.UpperLimitValue = alarm.UpperLimitValue;
                                curTurbineAlarm.MeasLocType = alarm.MeasLocType;
                                curTurbineAlarm.ThresholdGroup = _id;
                                curTurbineAlarm.MeasLocationID = alarm.MeasLocationID.Replace(WindTurbineID, tur.WindTurbineID);
                                curTurbineAlarm.EigenValueID = alarm.EigenValueID.Replace(WindTurbineID, tur.WindTurbineID);

                                curTurbineAlarm.AlarmDefThresholdGroup = new List<AlarmDefThreshold>();

                                if (alarm.AlarmDefThresholdGroup != null)
                                {
                                    alarm.AlarmDefThresholdGroup.ForEach(item =>
                                    {
                                        curTurbineAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                        {
                                            WindTurbineID = tur.WindTurbineID,
                                            ThresholdGroup = _id,
                                            ThresholdValueType = item.ThresholdValueType,
                                            AlarmDegree = item.AlarmDegree,
                                            ThresholdValue = item.ThresholdValue,
                                        });
                                    });
                                }

                                applyall.Add(curTurbineAlarm);
                            }
                        }
                        myList.AddRange(applyall);
                    }

                    AlarmDefinitionManage.AddAlarmDefinition(myList);
                    actionName = "添加";
                    message = string.Format(message, 1, "");
                }
                #region 添加日志
                if (!string.IsNullOrEmpty(effectiveId))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = 3;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_报警定义({0})", effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId, actionName);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddWarnRule]测量定义失败", ex);
                message = string.Format(message, 0, actionName + "测量定义失败 :" + ex.Message);
            }
            return "{" + message + "}";
        }
        /// <summary>
        /// 报警定义修改
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="ThresholdGroup"></param>
        /// <param name="AttentionValue"></param>
        /// <param name="WarnValue"></param>
        /// <returns></returns>
        public string EditWarnRule(string WindTurbineID, string ThresholdGroup, double? AttentionValue, double? WarnValue, string AcceleratedSpeed, string datatype, double? reverseAlarm, double? reverseWarn, bool applyAll)
        {
            string message = "state:{0},msg:'{1}'";
            string actionName = "修改";
            try
            {
                //if(datatype=="0")
                //{
                //    //如果晃度测量位置是加速度的，数据库总的值会缩小1000倍（单位mg）
                //    if (AcceleratedSpeed == "0")
                //    {
                //        AttentionValue /= 1000;
                //        WarnValue /= 1000;
                //    }
                //}
                //List<AlarmDefinition> UpdateList = new List<AlarmDefinition>();
                //AlarmDefinition AlarmDef = AlarmDefinitionManage.GetMDFAlarmDefThresholdGroupListbyGroupId(WindTurbineID, ThresholdGroup);
                //AlarmDef.AlarmDefThresholdGroup.ForEach(item => {
                //    if (item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Forward)
                //    {
                //        item.ThresholdValue = WarnValue;
                //    }
                //    if (item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Forward)
                //    {
                //        item.ThresholdValue = AttentionValue;
                //    }

                //    if (item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Alarm && item.ThresholdValueType == EnumThresholdValueType.Reverse)
                //    {
                //        item.ThresholdValue = (double)reverseAlarm;
                //    }
                //    if (item.AlarmDegree == EnumAlarmDegree.AlarmDeg_Warning && item.ThresholdValueType == EnumThresholdValueType.Reverse)
                //    {
                //        item.ThresholdValue = (double)reverseWarn;
                //    }
                //});

                // 重写报警逻辑
                var AlarmDefThresholdGroup = new List<AlarmDefThreshold>();
                if (AttentionValue != null)
                {
                    AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                    {
                        ThresholdGroup = ThresholdGroup,
                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                        ThresholdValue = (double)AttentionValue,
                        WindTurbineID = WindTurbineID,
                        ThresholdValueType = EnumThresholdValueType.Forward,
                    });
                }
                if (WarnValue != null)
                {
                    AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                    {
                        ThresholdGroup = ThresholdGroup,
                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                        ThresholdValue = (double)WarnValue,
                        WindTurbineID = WindTurbineID,
                        ThresholdValueType = EnumThresholdValueType.Forward,
                    });
                }

                if (reverseAlarm != null)
                {
                    AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                    {
                        ThresholdGroup = ThresholdGroup,
                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                        ThresholdValue = (double)reverseAlarm,
                        WindTurbineID = WindTurbineID,
                        ThresholdValueType = EnumThresholdValueType.Reverse,
                    });
                }

                if (reverseWarn != null)
                {
                    AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                    {
                        ThresholdGroup = ThresholdGroup,
                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                        ThresholdValue = (double)reverseWarn,
                        WindTurbineID = WindTurbineID,
                        ThresholdValueType = EnumThresholdValueType.Reverse,
                    });
                }

                AlarmDefinitionManage.updateMDFAlarmDefThresholdGroup(WindTurbineID, ThresholdGroup, AlarmDefThresholdGroup);
                if (applyAll)
                {
                    AlarmDefinition AlarmDef = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(WindTurbineID, ThresholdGroup);

                    var park = DevTreeManagement.GetWindParkByTurID(WindTurbineID);
                    var turList = DevTreeManagement.GetTurbinesListByWindParkId(park.WindParkID);
                    turList = turList.Where(k => k.WindTurbineModel == turList.Find(t => t.WindTurbineID == WindTurbineID).WindTurbineModel && k.WindTurbineID != WindTurbineID).ToList();
                    //turLIst = turLIst.Where(t => t.WindTurbineModel == turLIst.Find(p => p.WindTurbineID == WindTurbineID).WindTurbineModel && t.WindTurbineID != WindTurbineID).ToList();
                    foreach (var tur in turList)
                    {
                        string curMeas = AlarmDef.MeasLocationID.Replace(WindTurbineID, tur.WindTurbineID);
                        string curEV = AlarmDef.EigenValueID.Replace(WindTurbineID, tur.WindTurbineID);

                        AlarmDefinition curAlarm = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(tur.WindTurbineID, curMeas, curEV, AlarmDef.WorkConParameter, AlarmDef.MeasLocType);
                        if (curAlarm != null)
                        {
                            AlarmDefThresholdGroup.ForEach(item =>
                            {
                                item.ThresholdGroup = curAlarm.ThresholdGroup;
                                item.WindTurbineID = curAlarm.WindTurbineID;

                            });
                            AlarmDefinitionManage.updateMDFAlarmDefThresholdGroup(tur.WindTurbineID, curAlarm.ThresholdGroup, AlarmDefThresholdGroup);
                        }
                    }


                }

                message = string.Format(message, 1, "");
                #region 添加日志
                if (!string.IsNullOrEmpty(ThresholdGroup))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = ThresholdGroup;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("{1}_修改报警阈值({0})", ThresholdGroup, actionName);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWarnRule]修改报警阈值出现异常", ex);
                message = string.Format(message, 0, actionName + "修改报警阈值 :" + ex.Message);
            }
            return "{" + message + "}";
        }
        /// <summary>
        /// 删除报警数据
        /// </summary>
        /// <param name="WindTurbineID"></param>
        /// <param name="MeasLocationID"></param>
        /// <param name="EigenValueID"></param>
        /// <param name="WorkConParameter"></param>
        /// <param name="ThresholdGroup"></param>
        /// <returns></returns>
        public string DeleteWarnRule(string WindTurbineID, string MeasLocationID, string EigenValueID, short WorkConParameter, string ThresholdGroup)
        {
            string message = "state:{0},msg:'{1}'";
            try
            {
                AlarmDefinitionManage.DeleteAlarmDefinition(EigenValueID, WorkConParameter, ThresholdGroup);
                message = string.Format(message, 1, "");
                #region 添加日志
                if (!string.IsNullOrEmpty(ThresholdGroup))
                {
                    LogEntity logEntity = new LogEntity();
                    logEntity.LogDB = ConstDefine.UserManagementLog;
                    logEntity.LogTime = DateTime.Now;
                    logEntity.NodeID = ThresholdGroup;
                    logEntity.UserName = Request.Cookies["WindCMSUserName"];
                    logEntity.OperationDescription
                        = string.Format("删除报警阈值({0})", ThresholdGroup);
                    LogManagement.UserlogWrite(logEntity);
                }
                #endregion
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteWarnRule]删除报警数据出现异常", ex);
                message = string.Format(message, 0, "删除报警设置失败 ,刷新页面后重试！");
            }
            return "{" + message + "}";
        }


        public string AutoAddWarnRule(string WindTurbineID)
        {

            return "false";
        }

        #region 新增接口 - 特征值报警定义管理

        /// <summary>
        /// 获取所有类型的特征值报警定义列表（不再通过类型区分）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        [HttpGet("GetAllEigenValueList")]
        public List<AlarmDefinitonUIModel> GetAllEigenValueList(string turbineID)
        {
            try
            {
                List<AlarmDefinitonUIModel> allList = new List<AlarmDefinitonUIModel>();

                // 获取振动报警定义
                var vibList = WarnManager.alarmDefinitonUIModels(turbineID, "1");
                allList.AddRange(vibList);

                // 获取晃动和油液报警定义
                var svmList = WarnManager.alarmDefinitonUIModels(turbineID, "0");
                allList.AddRange(svmList);

                return allList.OrderBy(i => i.EigenValueID)
                             .ThenBy(i => i.WorkConParameter)
                             .ThenBy(i => i.LowerLimitValue)
                             .ToList();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetAllEigenValueList]获取所有特征值报警定义失败", ex);
                return new List<AlarmDefinitonUIModel>();
            }
        }

        /// <summary>
        /// 获取机组的所有测量位置信息（合并振动、晃动、油液测量位置）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <returns></returns>
        [HttpGet("GetAllMeasLocationsByTurId")]
        public List<MeasLocationInfoDTO> GetAllMeasLocationsByTurId(string turbineID)
        {
            try
            {
                List<MeasLocationInfoDTO> allMeasLocs = new List<MeasLocationInfoDTO>();

                // 获取振动测量位置
                List<MeasLoc_Vib> vibList = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
                foreach (var vib in vibList)
                {
                    allMeasLocs.Add(new MeasLocationInfoDTO
                    {
                        MeasLocationID = vib.MeasLocationID,
                        MeasLocName = vib.MeasLocName,
                        WindTurbineID = vib.WindTurbineID,
                        ComponentID = vib.ComponentID,
                        OrderSeq = vib.OrderSeq,
                        MeasLocType = 1 // 振动
                    });
                }

                // 获取晃动测量位置
                SVMUnit mySVM = SVMManagement.GetSVMById(turbineID);
                if (mySVM != null)
                {
                    List<MeasLoc_SVM> svmList = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
                    // 移除不报警的测点
                    svmList = svmList.Where(t => t.ParamType == EnumSVMParamType.YInclination
                                                || t.ParamType == EnumSVMParamType.Pitch
                                                || t.ParamType == EnumSVMParamType.Horizontal
                                                || t.ParamType == EnumSVMParamType.Vertical).ToList();

                    foreach (var svm in svmList)
                    {
                        allMeasLocs.Add(new MeasLocationInfoDTO
                        {
                            MeasLocationID = svm.MeasLocationID,
                            MeasLocName = svm.MeasLocName,
                            WindTurbineID = svm.WindTurbineID,
                            ComponentID = svm.ComponentID,
                            OrderSeq = svm.OrderSeq,
                            MeasLocType = 0, // 晃动
                            SectionName = svm.SectionName,
                            ParamType = (int)svm.ParamType
                        });
                    }
                }

                // 添加油液测点
                using (CMSFramework.EF.DauContext ctx = new CMSFramework.EF.DauContext(ConfigInfo.DBConnName))
                {
                    var oil = ctx.OilUnits.FirstOrDefault(t => t.WindTurbineID == turbineID);
                    if (oil != null)
                    {
                        if (oil.ViscositySensorEnabled)
                        {
                            allMeasLocs.Add(new MeasLocationInfoDTO
                            {
                                MeasLocationID = turbineID + "ViscositySensor",
                                MeasLocName = "油液粘度",
                                WindTurbineID = turbineID,
                                OrderSeq = 2,
                                MeasLocType = 2 // 油液
                            });
                        }
                        if (oil.WaterSensorEnabled)
                        {
                            allMeasLocs.Add(new MeasLocationInfoDTO
                            {
                                MeasLocationID = turbineID + "WaterSensor",
                                MeasLocName = "油液水分",
                                WindTurbineID = turbineID,
                                OrderSeq = 2,
                                MeasLocType = 2 // 油液
                            });
                        }
                        if (oil.WearParticleSensorEnabled)
                        {
                            allMeasLocs.Add(new MeasLocationInfoDTO
                            {
                                MeasLocationID = turbineID + "WearParticleSensor",
                                MeasLocName = "油液磨粒",
                                WindTurbineID = turbineID,
                                OrderSeq = 2,
                                MeasLocType = 2 // 油液
                            });
                        }
                    }
                }

                return allMeasLocs.OrderBy(item => item.OrderSeq).ToList();
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetAllMeasLocationsByTurId]获取所有测量位置失败", ex);
                return new List<MeasLocationInfoDTO>();
            }
        }

        /// <summary>
        /// 获取测量位置下的所有特征值列表（不再通过类型区分）
        /// </summary>
        /// <param name="turbineID">机组ID</param>
        /// <param name="measLocId">测量位置ID</param>
        /// <returns></returns>
        [HttpGet("GetAllEigenValueTypeByMeasLocId")]
        public List<EigenValueInfoDTO> GetAllEigenValueTypeByMeasLocId(string turbineID, string measLocId)
        {
            try
            {
                if (string.IsNullOrEmpty(measLocId)) return new List<EigenValueInfoDTO>();

                List<EigenValueInfoDTO> allEigenValues = new List<EigenValueInfoDTO>();

                // 判断测量位置类型
                bool isVibration = false;
                bool isSVM = false;
                bool isOil = false;

                // 检查是否为振动测量位置
                var vibMeasLocs = DevTreeManagement.GetVibMeasLocationByTurId(turbineID);
                if (vibMeasLocs.Any(v => v.MeasLocationID == measLocId))
                {
                    isVibration = true;
                }

                // 检查是否为晃动测量位置
                var svmMeasLocs = SVMManagement.GetMeasLoc_SVMListByTurID(turbineID);
                if (svmMeasLocs.Any(s => s.MeasLocationID == measLocId))
                {
                    isSVM = true;
                }

                // 检查是否为油液测量位置
                if (measLocId.Contains("ViscositySensor") || measLocId.Contains("WaterSensor") || measLocId.Contains("WearParticleSensor"))
                {
                    isOil = true;
                }

                if (isVibration)
                {
                    // 振动特征值
                    List<EigenValueData_Vib> list = EigenValueManage.GetPublicEigenValueList(turbineID, measLocId);
                    foreach (var item in list)
                    {
                        allEigenValues.Add(new EigenValueInfoDTO
                        {
                            EigenValueID = item.EigenValueCode,
                            EigenValueName = item.EigenValueCode,
                            WindTurbineID = turbineID,
                            EngUnitName = item.EigenValueCode
                        });
                    }
                }
                else if (isSVM)
                {
                    // 晃动特征值
                    List<EigenValueData_SVM> list = EigenValueManage.GetSVMPublicEigenValueList(turbineID, measLocId);
                    foreach (var item in list)
                    {
                        allEigenValues.Add(new EigenValueInfoDTO
                        {
                            EigenValueID = item.EigenValueID,
                            EigenValueName = AppFramework.Utility.EnumHelper.GetDescription(item.EigenValueType),
                            WindTurbineID = turbineID,
                            EngUnitName = AppFramework.Utility.EnumHelper.GetDescription(item.EigenValueType),
                            EigenValueType = (int)item.EigenValueType
                        });
                    }
                }
                else if (isOil)
                {
                    // 油液特征值
                    string sensorType = measLocId.Replace(turbineID, "");
                    switch (sensorType)
                    {
                        case "ViscositySensor":
                            allEigenValues.Add(new EigenValueInfoDTO
                            {
                                EigenValueID = "EqualNKinematicViscosity",
                                EigenValueName = "40℃等效粘度",
                                WindTurbineID = turbineID,
                                EngUnitName = "40℃等效粘度"
                            });
                            break;
                        case "WaterSensor":
                            allEigenValues.Add(new EigenValueInfoDTO
                            {
                                EigenValueID = "WaterContent",
                                EigenValueName = "水含量",
                                WindTurbineID = turbineID,
                                EngUnitName = "水含量"
                            });
                            break;
                        case "WearParticleSensor":
                            allEigenValues.Add(new EigenValueInfoDTO
                            {
                                EigenValueID = "FeConcentration",
                                EigenValueName = "铁磁磨粒浓度",
                                WindTurbineID = turbineID,
                                EngUnitName = "铁磁磨粒浓度"
                            });
                            allEigenValues.Add(new EigenValueInfoDTO
                            {
                                EigenValueID = "NonFeConcentration",
                                EigenValueName = "非铁磁磨粒浓度",
                                WindTurbineID = turbineID,
                                EngUnitName = "非铁磁磨粒浓度"
                            });
                            break;
                    }
                }

                return allEigenValues;
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[GetAllEigenValueTypeByMeasLocId]获取特征值列表失败", ex);
                return new List<EigenValueInfoDTO>();
            }
        }

        /// <summary>
        /// 新增报警规则接口 - 单条修改特征值报警定义
        /// </summary>
        /// <param name="request">批量编辑报警规则请求</param>
        /// <returns></returns>
        [HttpPost("EditWarnRule")]
        [BatchOperation(nameof(BatchEditWarnRuleBatch))]
        public IActionResult EditWarnRuleNew([FromBody] BatchEditWarnRuleRequest request)
        {
            var result = new WarnRuleOperationResultDTO();
            try
            {
                if (request?.SourceData == null)
                {
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                int successCount = 0;
                int failureCount = 0;
                var details = new List<string>();

                // 遍历每个编辑请求
                var editRequest = request.SourceData;
                try
                {

                    // 直接实现业务逻辑，不调用原有controller方法
                    var alarmDefThresholdGroup = new List<AlarmDefThreshold>();

                    // 构建阈值组数据
                    if (editRequest.WarnValue != null)
                    {
                        alarmDefThresholdGroup.Add(new AlarmDefThreshold()
                        {
                            ThresholdGroup = editRequest.ThresholdGroup,
                            AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                            ThresholdValue = (double)editRequest.WarnValue,
                            WindTurbineID = editRequest.WindTurbineID,
                            ThresholdValueType = EnumThresholdValueType.Forward,
                        });
                    }

                    if (editRequest.AlarmValue != null)
                    {
                        alarmDefThresholdGroup.Add(new AlarmDefThreshold()
                        {
                            ThresholdGroup = editRequest.ThresholdGroup,
                            AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                            ThresholdValue = (double)editRequest.AlarmValue,
                            WindTurbineID = editRequest.WindTurbineID,
                            ThresholdValueType = EnumThresholdValueType.Forward,
                        });
                    }

                    if (editRequest.ReverseAlarmValue != null)
                    {
                        alarmDefThresholdGroup.Add(new AlarmDefThreshold()
                        {
                            ThresholdGroup = editRequest.ThresholdGroup,
                            AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                            ThresholdValue = (double)editRequest.ReverseAlarmValue,
                            WindTurbineID = editRequest.WindTurbineID,
                            ThresholdValueType = EnumThresholdValueType.Reverse,
                        });
                    }

                    if (editRequest.ReverseWarnValue != null)
                    {
                        alarmDefThresholdGroup.Add(new AlarmDefThreshold()
                        {
                            ThresholdGroup = editRequest.ThresholdGroup,
                            AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                            ThresholdValue = (double)editRequest.ReverseWarnValue,
                            WindTurbineID = editRequest.WindTurbineID,
                            ThresholdValueType = EnumThresholdValueType.Reverse,
                        });
                    }

                    // 更新阈值组
                    AlarmDefinitionManage.updateMDFAlarmDefThresholdGroup(editRequest.WindTurbineID, editRequest.ThresholdGroup, alarmDefThresholdGroup);

                    // 如果需要应用到所有同型号机组
                    if (editRequest.ApplyToAll)
                    {
                        var alarmDef = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(editRequest.WindTurbineID, editRequest.ThresholdGroup);
                        if (alarmDef != null)
                        {
                            var park = DevTreeManagement.GetWindParkByTurID(editRequest.WindTurbineID);
                            var turList = DevTreeManagement.GetTurbinesListByWindParkId(park.WindParkID);
                            turList = turList.Where(k => k.WindTurbineModel == turList.Find(t => t.WindTurbineID == editRequest.WindTurbineID).WindTurbineModel && k.WindTurbineID != editRequest.WindTurbineID).ToList();

                            foreach (var tur in turList)
                            {
                                string curMeas = alarmDef.MeasLocationID.Replace(editRequest.WindTurbineID, tur.WindTurbineID);
                                string curEV = alarmDef.EigenValueID.Replace(editRequest.WindTurbineID, tur.WindTurbineID);

                                var curAlarm = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(tur.WindTurbineID, curMeas, curEV, alarmDef.WorkConParameter, alarmDef.MeasLocType);
                                if (curAlarm != null)
                                {
                                    var curAlarmDefThresholdGroup = new List<AlarmDefThreshold>();
                                    foreach (var item in alarmDefThresholdGroup)
                                    {
                                        curAlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                        {
                                            ThresholdGroup = curAlarm.ThresholdGroup,
                                            WindTurbineID = curAlarm.WindTurbineID,
                                            AlarmDegree = item.AlarmDegree,
                                            ThresholdValue = item.ThresholdValue,
                                            ThresholdValueType = item.ThresholdValueType
                                        });
                                    }
                                    AlarmDefinitionManage.updateMDFAlarmDefThresholdGroup(tur.WindTurbineID, curAlarm.ThresholdGroup, curAlarmDefThresholdGroup);
                                }
                            }
                        }
                    }

                    // 添加日志
                    if (!string.IsNullOrEmpty(editRequest.ThresholdGroup))
                    {
                        var logEntity = new LogEntity()
                        {
                            LogDB = ConstDefine.UserManagementLog,
                            LogTime = DateTime.Now,
                            NodeID = editRequest.ThresholdGroup,
                            UserName = Request.Cookies["WindCMSUserName"],
                            OperationDescription = $"修改_修改报警阈值({editRequest.ThresholdGroup})"
                        };
                        LogManagement.UserlogWrite(logEntity);
                    }

                    successCount++;
                    details.Add($"修改成功: {editRequest.ThresholdGroup}");
                }
                catch (Exception ex)
                {
                    failureCount++;
                    details.Add($"修改失败: {editRequest.ThresholdGroup} - {ex.Message}");
                    CMSFramework.Logger.Logger.LogErrorMessage($"[EditWarnRuleNew]修改报警规则失败: {editRequest.ThresholdGroup}", ex);
                }


                result.Success = failureCount == 0;
                result.Message = failureCount == 0 ? "批量修改成功" : $"批量修改完成，成功 {successCount} 个，失败 {failureCount} 个";
                result.SuccessCount = successCount;
                result.FailureCount = failureCount;
                result.Details = details;

                return Ok(ApiResponse<string>.Success(result.Message));
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[EditWarnRuleNew]修改报警规则失败", ex);
                result.Success = false;
                result.Message = $"修改失败: {ex.Message}";
                result.SuccessCount = 0;
                result.FailureCount = 1;
                result.Details.Add($"修改失败: {ex.Message}");

                return Ok(ApiResponse<string>.Error("修改失败" + ex.Message));
            }
        }

        /// <summary>
        /// 新增报警规则接口 - 批量删除报警定义
        /// </summary>
        /// <param name="request">批量删除报警规则请求</param>
        /// <returns></returns>
        [HttpPost("DeleteWarnRule")]
        [BatchOperation(nameof(BatchDeleteWarnRuleBatch))]
        public IActionResult DeleteWarnRuleNew([FromBody] BatchDeleteWarnRuleRequest request)
        {
            var result = new WarnRuleOperationResultDTO();

            try
            {
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    result.Success = false;
                    result.Message = "请求参数不能为空";
                    result.FailureCount = 0;
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                int successCount = 0;
                int failureCount = 0;
                var details = new List<string>();

                // 使用事务确保原子性
                using (var transaction = new System.Transactions.TransactionScope())
                {
                    try
                    {
                        foreach (var warnRule in request.SourceData)
                        {
                            try
                            {
                                // 直接调用业务逻辑删除报警定义
                                AlarmDefinitionManage.DeleteAlarmDefinition(
                                    warnRule.EigenValueID,
                                    warnRule.WorkConParameter,
                                    warnRule.ThresholdGroup
                                );

                                // 添加日志
                                if (!string.IsNullOrEmpty(warnRule.ThresholdGroup))
                                {
                                    var logEntity = new LogEntity()
                                    {
                                        LogDB = ConstDefine.UserManagementLog,
                                        LogTime = DateTime.Now,
                                        NodeID = warnRule.ThresholdGroup,
                                        UserName = Request.Cookies["WindCMSUserName"],
                                        OperationDescription = $"删除报警阈值({warnRule.ThresholdGroup})"
                                    };
                                    LogManagement.UserlogWrite(logEntity);
                                }

                                successCount++;
                                details.Add($"删除成功: {warnRule.ThresholdGroup}");
                            }
                            catch (Exception ex)
                            {
                                failureCount++;
                                details.Add($"删除失败: {warnRule.ThresholdGroup} - {ex.Message}");
                                CMSFramework.Logger.Logger.LogErrorMessage($"[DeleteWarnRuleNew]删除报警数据出现异常: {warnRule.ThresholdGroup}", ex);
                            }
                        }

                        // 如果有任何失败，回滚事务
                        if (failureCount > 0)
                        {
                            throw new Exception($"批量删除中有 {failureCount} 条失败，回滚所有操作");
                        }

                        transaction.Complete();

                        result.Success = true;
                        result.Message = $"批量删除成功，共处理 {successCount} 条记录";
                        result.SuccessCount = successCount;
                        result.FailureCount = failureCount;
                        result.Details = details;

                        return Ok(ApiResponse<string>.Success("result.Message"));
                    }
                    catch (Exception ex)
                    {
                        result.Success = false;
                        result.Message = $"批量删除失败: {ex.Message}";
                        result.SuccessCount = 0;
                        result.FailureCount = request?.SourceData?.Count ?? 0;
                        result.Details = details;

                        return Ok(ApiResponse<string>.Error(result.Message));
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[DeleteWarnRuleNew]批量删除报警规则失败", ex);
                result.Success = false;
                result.Message = $"批量删除失败: {ex.Message}";
                result.SuccessCount = 0;
                result.FailureCount = request?.SourceData?.Count ?? 0;

                return Ok(ApiResponse<string>.Error(result.Message));
            }
        }

        /// <summary>
        /// 新增报警规则接口 - 批量新增报警定义
        /// </summary>
        /// <param name="request">批量添加报警规则请求</param>
        /// <returns></returns>
        [HttpPost("AddWarnRule")]
        [BatchOperation(nameof(BatchAddWarnRuleFun))]
        public IActionResult AddWarnRuleNew([FromBody] BatchAddWarnRuleRequest request)
        {
            var result = new WarnRuleOperationResultDTO();

            try
            {
                if (request?.SourceData == null || !request.SourceData.Any())
                {
                    result.Success = false;
                    result.Message = "请求参数不能为空";
                    result.FailureCount = 0;
                    return Ok(ApiResponse<string>.Error("请求参数不能为空"));
                }

                int successCount = 0;
                int failureCount = 0;
                var details = new List<string>();

                // 使用事务确保原子性
                using (var transaction = new System.Transactions.TransactionScope())
                {
                    try
                    {
                        var allAlarmDefinitions = new List<AlarmDefinition>();

                        foreach (var warnRule in request.SourceData)
                        {
                            try
                            {
                                // 解析工况参数
                                var measLocIdList = new List<string>();
                                string effectiveId = "";

                                if (!string.IsNullOrEmpty(warnRule.WorkConditionParams))
                                {
                                    effectiveId = warnRule.WorkConditionParams;
                                    //measLocIdList = warnRule.WorkConditionParams.Split(new char[] { '$' }, StringSplitOptions.RemoveEmptyEntries).ToList();

                                    measLocIdList.Add(warnRule.WorkConditionParams);
                                }
                                else
                                {
                                    //measLocIdList = new List<string>() { "无工况:0,0" };
                                    effectiveId = warnRule.MeasLocationID + "&&" + warnRule.EigenValueID;
                                    measLocIdList.Add("无工况");
                                }

                                var currentAlarmDefinitions = new List<AlarmDefinition>();

                                //string condentName = data.Split(':')[0]; // 工况参数
                                //string lowerLimitValue = data.Split(':')[1].Split(',')[0]; // 下限
                                //string upperLimitValue = data.Split(':')[1].Split(',')[1]; // 上限
                                string guId = System.Guid.NewGuid().ToString();

                                //lowerLimitValue = lowerLimitValue.IndexOf(".") > -1 ? lowerLimitValue.Split('.')[0] : lowerLimitValue;
                                //upperLimitValue = upperLimitValue.IndexOf(".") > -1 ? upperLimitValue.Split('.')[0] : upperLimitValue;

                                var myAlarm = new AlarmDefinition()
                                {
                                    WindTurbineID = warnRule.WindTurbineID,
                                    MeasLocationID = warnRule.MeasLocationID,
                                    EigenValueID = warnRule.MeasLocationID + "&&" + warnRule.EigenValueID,
                                    LowerLimitValue = warnRule.LowerLimitValue ?? 0,
                                    UpperLimitValue = warnRule.UpperLimitValue ?? 0,
                                    WorkConParameter = GetWorkConParameter(warnRule.WorkConditionParams, warnRule.MeasLocationID),
                                    MeasLocType = (EnumMeasLocType)warnRule.MeasLocType,
                                    ThresholdGroup = guId
                                };

                                // 检查是否已经存在相同的报警定义
                                var existingAlarm = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(
                                    myAlarm.WindTurbineID,
                                    myAlarm.MeasLocationID,
                                    myAlarm.EigenValueID,
                                    myAlarm.WorkConParameter,
                                    myAlarm.MeasLocType
                                );

                                if (existingAlarm != null)
                                {
                                    throw new Exception("请勿重复添加！");
                                }

                                // 构建阈值组
                                myAlarm.AlarmDefThresholdGroup = new List<AlarmDefThreshold>();

                                if (warnRule.WarnValue != null)
                                {
                                    myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                    {
                                        ThresholdGroup = guId,
                                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                                        ThresholdValue = (double)warnRule.WarnValue,
                                        WindTurbineID = warnRule.WindTurbineID,
                                        ThresholdValueType = EnumThresholdValueType.Forward,
                                    });
                                }

                                if (warnRule.AlarmValue != null)
                                {
                                    myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                    {
                                        ThresholdGroup = guId,
                                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                                        ThresholdValue = (double)warnRule.AlarmValue,
                                        WindTurbineID = warnRule.WindTurbineID,
                                        ThresholdValueType = EnumThresholdValueType.Forward,
                                    });
                                }

                                if (warnRule.ReverseAlarmValue != null)
                                {
                                    myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                    {
                                        ThresholdGroup = guId,
                                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Alarm,
                                        ThresholdValue = (double)warnRule.ReverseAlarmValue,
                                        WindTurbineID = warnRule.WindTurbineID,
                                        ThresholdValueType = EnumThresholdValueType.Reverse,
                                    });
                                }

                                if (warnRule.ReverseWarnValue != null)
                                {
                                    myAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                    {
                                        ThresholdGroup = guId,
                                        AlarmDegree = EnumAlarmDegree.AlarmDeg_Warning,
                                        ThresholdValue = (double)warnRule.ReverseWarnValue,
                                        WindTurbineID = warnRule.WindTurbineID,
                                        ThresholdValueType = EnumThresholdValueType.Reverse,
                                    });
                                }

                                currentAlarmDefinitions.Add(myAlarm);


                                // 如果需要应用到所有同型号机组
                                if (warnRule.ApplyToAll && !string.IsNullOrEmpty(warnRule.WindParkID))
                                {
                                    var turList = DevTreeManagement.GetTurbinesListByWindParkId(warnRule.WindParkID);
                                    turList = turList.Where(t => t.WindTurbineModel == turList.Find(p => p.WindTurbineID == warnRule.WindTurbineID).WindTurbineModel && t.WindTurbineID != warnRule.WindTurbineID).ToList();

                                    foreach (var tur in turList)
                                    {
                                        foreach (var alarm in currentAlarmDefinitions)
                                        {
                                            string newId = System.Guid.NewGuid().ToString();
                                            var curTurbineAlarm = new AlarmDefinition()
                                            {
                                                WindTurbineID = tur.WindTurbineID,
                                                WorkConParameter = alarm.WorkConParameter,
                                                LowerLimitValue = alarm.LowerLimitValue,
                                                UpperLimitValue = alarm.UpperLimitValue,
                                                MeasLocType = alarm.MeasLocType,
                                                ThresholdGroup = newId,
                                                MeasLocationID = alarm.MeasLocationID.Replace(warnRule.WindTurbineID, tur.WindTurbineID),
                                                EigenValueID = alarm.EigenValueID.Replace(warnRule.WindTurbineID, tur.WindTurbineID),
                                                AlarmDefThresholdGroup = new List<AlarmDefThreshold>()
                                            };

                                            if (alarm.AlarmDefThresholdGroup != null)
                                            {
                                                foreach (var item in alarm.AlarmDefThresholdGroup)
                                                {
                                                    curTurbineAlarm.AlarmDefThresholdGroup.Add(new AlarmDefThreshold()
                                                    {
                                                        WindTurbineID = tur.WindTurbineID,
                                                        ThresholdGroup = newId,
                                                        ThresholdValueType = item.ThresholdValueType,
                                                        AlarmDegree = item.AlarmDegree,
                                                        ThresholdValue = item.ThresholdValue,
                                                    });
                                                }
                                            }

                                            currentAlarmDefinitions.Add(curTurbineAlarm);
                                        }
                                    }
                                }

                                allAlarmDefinitions.AddRange(currentAlarmDefinitions);
                                successCount++;
                                details.Add($"新增成功: {warnRule.MeasLocationID} - {warnRule.EigenValueID}");

                                // 添加日志
                                if (!string.IsNullOrEmpty(effectiveId))
                                {
                                    var logEntity = new LogEntity()
                                    {
                                        LogDB = 3,
                                        LogTime = DateTime.Now,
                                        NodeID = effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId,
                                        UserName = Request.Cookies["WindCMSUserName"],
                                        OperationDescription = $"添加_报警定义({(effectiveId.IndexOf(',') > -1 ? effectiveId.Substring(0, effectiveId.LastIndexOf(',')) : effectiveId)})"
                                    };
                                    LogManagement.UserlogWrite(logEntity);
                                }
                            }
                            catch (Exception ex)
                            {
                                failureCount++;
                                details.Add($"新增失败: {warnRule.MeasLocationID} - {warnRule.EigenValueID} - {ex.Message}");
                                CMSFramework.Logger.Logger.LogErrorMessage($"[AddWarnRuleNew]新增报警规则失败: {warnRule.MeasLocationID} - {warnRule.EigenValueID}", ex);
                            }
                        }

                        // 如果有任何失败，回滚事务
                        if (failureCount > 0)
                        {
                            throw new Exception($"批量新增中有 {failureCount} 条失败，回滚所有操作");
                        }

                        // 批量添加所有报警定义
                        if (allAlarmDefinitions.Any())
                        {
                            AlarmDefinitionManage.AddAlarmDefinition(allAlarmDefinitions);
                        }

                        transaction.Complete();

                        result.Success = true;
                        result.Message = $"批量新增成功，共处理 {successCount} 条记录";
                        result.SuccessCount = successCount;
                        result.FailureCount = failureCount;
                        result.Details = details;

                        return Ok(ApiResponse<string>.Success("result.Message"));
                    }
                    catch (Exception ex)
                    {
                        result.Success = false;
                        result.Message = $"批量新增失败: {ex.Message}";
                        result.SuccessCount = 0;
                        result.FailureCount = request?.SourceData?.Count ?? 0;
                        result.Details = details;

                        return Ok(ApiResponse<string>.Error(result.Message));
                    }
                }
            }
            catch (Exception ex)
            {
                CMSFramework.Logger.Logger.LogErrorMessage("[AddWarnRuleNew]批量新增报警规则失败", ex);
                result.Success = false;
                result.Message = $"批量新增失败: {ex.Message}";
                result.SuccessCount = 0;
                result.FailureCount = request?.SourceData?.Count ?? 0;

                return Ok(ApiResponse<string>.Error(result.Message));
            }
        }

        /// <summary>
        /// 根据工况名称和测量位置ID获取工况参数类型
        /// </summary>
        /// <param name="condentName">工况名称</param>
        /// <param name="measLocationID">测量位置ID</param>
        /// <returns></returns>
        private short GetWorkConParameter(string condentName, string measLocationID)
        {
            if (condentName.Equals("功率"))
            {
                return (short)EnumWorkCondition_ParamType.WCPT_Power;
            }
            else if (condentName.Equals("转速"))
            {
                return (short)EnumWorkCondition_ParamType.WCPT_RotSpeed;
            }
            else if (condentName.Equals("温度"))
            {
                if (measLocationID.Contains("BLD1"))
                {
                    return (short)EnumWorkCondition_ParamType.WCPT_Blade_01Temp;
                }
                else if (measLocationID.Contains("BLD2"))
                {
                    return (short)EnumWorkCondition_ParamType.WCPT_Blade_02Temp;
                }
                else if (measLocationID.Contains("BLD3"))
                {
                    return (short)EnumWorkCondition_ParamType.WCPT_Blade_03Temp;
                }
                else
                {
                    return (short)EnumWorkCondition_ParamType.WCPT_Blade_01Temp;
                }
            }
            else
            {
                return (short)EnumWorkCondition_ParamType.WCPT_NOWORKCONDTION;
            }
        }

        #endregion

        #endregion

       
        #region 批量操作实现方法

        /// <summary>
        /// 批量添加报警规则实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量添加请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchAddWarnRuleFun(string targetTurbineId, BatchAddWarnRuleRequest request)
        {
            var results = new List<string>();
            foreach (var sourceRule in request.SourceData)
            {
                try
                {
                    // 映射测量位置ID,需要判断测量位置是否存在
                    var targetMeasLocationId = sourceRule.MeasLocationID.Replace(sourceRule.WindTurbineID, targetTurbineId);

                    // 映射特征值ID
                    var targetEigenValueId = sourceRule.EigenValueID.Replace(sourceRule.WindTurbineID, targetTurbineId);

                    // 创建目标报警规则
                    var targetRule = new AddWarnRuleDTO
                    {
                        WindTurbineID = targetTurbineId,
                        WindParkID = sourceRule.WindParkID,
                        MeasLocationID = targetMeasLocationId,
                        EigenValueID = targetEigenValueId,
                        WorkConParameter = sourceRule.WorkConParameter,
                        LowerLimitValue = sourceRule.LowerLimitValue,
                        UpperLimitValue = sourceRule.UpperLimitValue,
                        WarnValue = sourceRule.WarnValue,
                        AlarmValue = sourceRule.AlarmValue,
                        ReverseWarnValue = sourceRule.ReverseWarnValue,
                        ReverseAlarmValue = sourceRule.ReverseAlarmValue,
                        MeasLocType = sourceRule.MeasLocType,
                        ApplyToAll = false, // 批量操作时不再应用到所有机组
                        WorkConditionParams = sourceRule.WorkConditionParams
                    };

                    // 判断测量位置是否存在


                    // 调用单个添加方法
                    var singleRequest = new BatchAddWarnRuleRequest { SourceData = new List<AddWarnRuleDTO> { targetRule } };
                    var result = AddWarnRuleNew(singleRequest);

                    if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                    {
                        if (apiResponse.Code == 1)
                        {
                            results.Add($"机组 {targetTurbineId}: 成功添加报警规则 {targetRule.MeasLocationID}");
                        }
                        else
                        {
                            results.Add($"机组 {targetTurbineId}: 添加报警规则失败 - {apiResponse.Msg}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"机组 {targetTurbineId}: 添加报警规则异常 - {ex.Message}");
                }
            }

            return results;
        }

        /// <summary>
        /// 批量编辑报警规则实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量编辑请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchEditWarnRuleBatch(string targetTurbineId, BatchEditWarnRuleRequest request)
        {
            var results = new List<string>();
            var sourceEdit = request.SourceData;
            try
            {
                // 映射到目标机组
                //var targetEdit = await MapEditWarnRuleToTargetTurbine(sourceEdit, targetTurbineId);
                // 检查目标阈值组是否存在
                var targetAlarmDef = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(targetTurbineId, request.SourceData.ThresholdGroup);
                if (targetAlarmDef == null)
                    return null;

                // 创建目标编辑请求
                var targetEdit = new EditWarnRuleDTO
                {
                    WindTurbineID = targetTurbineId,
                    ThresholdGroup = request.SourceData.ThresholdGroup,
                    WarnValue = sourceEdit.WarnValue,
                    AlarmValue = sourceEdit.AlarmValue,
                    ReverseWarnValue = sourceEdit.ReverseWarnValue,
                    ReverseAlarmValue = sourceEdit.ReverseAlarmValue,
                    ApplyToAll = false // 批量操作时不再应用到所有机组
                };

                if (targetEdit == null)
                {
                    results.Add($"机组 {targetTurbineId}: 无法映射报警规则编辑 {sourceEdit.ThresholdGroup}");
                }

                // 调用单个编辑方法
                var singleRequest = request;
                var result = EditWarnRuleNew(singleRequest);

                if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                {
                    if (apiResponse.Code == 1)
                    {
                        results.Add($"机组 {targetTurbineId}: 成功编辑报警规则 {targetEdit.ThresholdGroup}");
                    }
                    else
                    {
                        results.Add($"机组 {targetTurbineId}: 编辑报警规则失败 - {apiResponse.Msg}");
                    }
                }
            }
            catch (Exception ex)
            {
                results.Add($"机组 {targetTurbineId}: 编辑报警规则异常 - {ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 批量删除报警规则实现
        /// </summary>
        /// <param name="targetTurbineId">目标机组ID</param>
        /// <param name="request">批量删除请求</param>
        /// <returns></returns>
        public async Task<List<string>> BatchDeleteWarnRuleBatch(string targetTurbineId, BatchDeleteWarnRuleRequest request)
        {
            var results = new List<string>();
            int successCount = 0;
            int failureCount = 0;

            // 映射删除数据到目标机组
            var targetRules = new List<DeleteWarnRuleItemDTO>();
            foreach (var sourceRule in request.SourceData)
            {
                //var targetRule = await MapDeleteWarnRuleToTargetTurbine(sourceRule, targetTurbineId);
                // 映射测量位置ID和特征值ID
                var targetMeasLocationId = sourceRule.MeasLocationID.Replace(sourceRule.WindTurbineID, targetTurbineId);
                var targetEigenValueId = sourceRule.EigenValueID.Replace(sourceRule.WindTurbineID, targetTurbineId);

                // 检查目标报警定义是否存在
                var targetAlarmDef = AlarmDefinitionManage.GetMDFAlarmDefByTurbineId(targetTurbineId, targetMeasLocationId, targetEigenValueId, sourceRule.WorkConParameter, 0);
                if (targetAlarmDef == null)
                {
                    results.Add($"机组 {targetTurbineId}: 未找到 {targetMeasLocationId} 报警定义");
                    continue;
                }

                // 创建目标删除请求
                var targetRule = new DeleteWarnRuleItemDTO
                {
                    WindTurbineID = targetTurbineId,
                    MeasLocationID = targetMeasLocationId,
                    EigenValueID = targetEigenValueId,
                    WorkConParameter = targetAlarmDef.WorkConParameter,
                    ThresholdGroup = targetAlarmDef.ThresholdGroup,
                };
                if (targetRule != null)
                {
                    targetRules.Add(targetRule);
                }
            }

            if (targetRules.Count > 0)
            {
                try
                {
                    // 调用单个删除方法
                    var singleRequest = new BatchDeleteWarnRuleRequest { SourceData = targetRules };
                    var result = DeleteWarnRuleNew(singleRequest);

                    if (result is OkObjectResult okResult && okResult.Value is ApiResponse<string> apiResponse)
                    {
                        if (apiResponse.Code == 1)
                        {
                            results.Add($"机组 {targetTurbineId}: 成功删除 {targetRules.Count} 个报警规则");
                        }
                        else
                        {
                            results.Add($"机组 {targetTurbineId}: 删除报警规则失败 - {apiResponse.Msg}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    results.Add($"机组 {targetTurbineId}: 删除报警规则异常 - {ex.Message}");
                }
            }
            return results;
        }

        #endregion

    }
}
