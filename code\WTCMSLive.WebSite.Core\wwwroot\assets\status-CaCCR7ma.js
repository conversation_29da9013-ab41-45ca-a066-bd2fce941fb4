import{c as W,W as M}from"./table-C_s53ALS.js";import{N as E}from"./noPermission-BBS3T4wn.js";import{r as p,u as F,z as g,y as G,w as j,B as J,A as f,f as _,x as d,d as c,o as m,i as v,b as D,c as b,F as C,e as K,g as $,t as q,s as U}from"./index-D9CxWmlM.js";import{u as Q}from"./configDevice-ce3VhjpI.js";import{u as X}from"./statusMonitor-CqkPK8Q-.js";import{u as Z}from"./devTree-BD8aiOqS.js";import{S as ee,g as o}from"./tools-DZBuE28U.js";import{_ as te}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as ae,a as ne}from"./index-pF9HjW4G.js";import"./index-DyCH3qIX.js";import"./index-C8OfElRN.js";import"./index-CgGbkMmC.js";import"./styleChecker-z-opWSaj.js";import"./index-BGEB0Rhf.js";import"./initDefaultProps-C2vKchlZ.js";import"./index-DMbgFMzZ.js";import"./shallowequal-vFdacwF3.js";import"./index-BS38043a.js";import"./ActionButton-BRQ4acFZ.js";import"./index-RBozNpIx.js";const le={class:"border"},re={class:"componentStatusBox"},se=["src","title","alt"],oe=["src","title","alt"],me={__name:"status",setup(ie){const B=Q(),h=X(),H=Z();let x=(window.localStorage.getItem("role")?JSON.parse(window.localStorage.getItem("role")).moduleIds:[]).includes("21");const T=(e={})=>[{label:"设备名称",value:e.windTurbineName},{label:"设备编号",value:e.windTurbineCode},{label:"设备型号",value:e.windTurbineModel},{label:"额定功率(KW)",value:e.ratedPower},{label:"设备厂商",value:e.manufacturer}],N=F(),I=p(""),i=p(N.params.id),S=p(!1),y=p([g().subtract(3,"months"),g()]),u=G({filterData:[],tableData:[],tableData1:[],tableData2:[]}),Y=p([T({})]),P=()=>{let e=H.findAncestorsWithNodes(i.value);e&&e.length&&e.length>1&&(I.value=e[e.length-2].id)},R=async e=>{if(i.value){const t=await B.fetchDeviceInfo({turbineID:i.value});Y.value=T(t)}},A=async()=>{const e=await h.fetchGetTurbineStatusCount({TurbineID:i.value,WindParkID:I.value});if(!e||!e.length)return;let t=[],s=[],a="devSegmentName";e.forEach(n=>{s.indexOf(n[a])===-1&&n[a]&&n[a]!==""&&(s.push(n[a]),t.push({text:n[a],value:n[a]}))}),u.filterData=t,w(e)},w=e=>{var s,a,n;if(!e||!e.length)return;const t=[];for(let l=0;l<e.length;l+=2)t.push({...e[l],devSegmentName1:(s=e[l+1])==null?void 0:s.devSegmentName,alarmUpdateTime1:(a=e[l+1])==null?void 0:a.alarmUpdateTime,alarmDegree1:(n=e[l+1])==null?void 0:n.alarmDegree});u.tableData=t},z=async()=>{S.value=!0,u.tableData2=await h.fetchGetSearchMonitorLog({turbineID:i.value,beginTime:y.value[0].format("YYYY-MM-DD"),endTime:y.value[1].format("YYYY-MM-DD")}),S.value=!1};j(()=>N.params.id,e=>{e&&x&&(i.value=e,P(),z(),R(),A())},{immediate:!0});const L=J(()=>[{title:"部件状态",dataIndex:"devSegmentName",filters:u.filterData,otherColumn:!0},{title:"状态更新时间",dataIndex:"alarmUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({text:e,record:t})=>t.alarmUpdateTime?f("span",{},g(e).format("YYYY-MM-DD HH:mm:ss")):""},{title:"部件状态",dataIndex:"devSegmentName1",otherColumn:!0},{title:"状态更新时间",dataIndex:"alarmUpdateTime1",customRender:({text:e,record:t})=>t.alarmUpdateTime1?f("span",{},g(e).format("YYYY-MM-DD HH:mm:ss")):""}]),O=e=>{const{data:t}=e,{filters:s,sorter:a}=t;let n=h.deviceStatusList;s&&s.devSegmentName&&(n=n.filter(l=>s.devSegmentName.includes(l.devSegmentName))),a&&a.field&&a.order&&(n=n.sort((l,r)=>{if(a.order==="ascend")return new Date(l.alarmUpdateTime).getTime()-new Date(r.alarmUpdateTime).getTime();if(a.order==="descend")return new Date(r.alarmUpdateTime).getTime()-new Date(l.alarmUpdateTime).getTime()})),w(n)},V=[{title:"状态",dataIndex:"alarmDegree",width:80,headerOperations:{filters:[{text:"正常",value:3},{text:"注意",value:5},{text:"危险",value:6},{text:"未知",value:2}]},customRender:({record:e})=>e.alarmDegree?f("span",{style:{color:o(e.alarmDegree).color}},o(e.alarmDegree).text):""},{title:"详情",dataIndex:"logTitle",customRender:({text:e,record:t})=>t.logTitle?f("p",{style:{textAlign:"left"}},e):""},{title:"状态更新时间",dataIndex:"eventTime",width:160,headerOperations:{sorter:!0},customRender:({text:e,record:t})=>t.eventTime?f("span",{},g(e).format("YYYY-MM-DD HH:mm:ss")):""}],k=(e,t)=>t?`/componentStatus/${e.devSegmentName1}_${o(e.alarmDegree1).text}.png`:`/componentStatus/${e.devSegmentName}_${o(e.alarmDegree).text}.png`;return(e,t)=>{const s=ne,a=ae,n=ee;return d(x)?(m(),_(n,{key:0,spinning:S.value,size:"large"},{default:c(()=>[v("div",null,[D(W,{tableTitle:"设备信息",defaultCollapse:!0,batchApply:!1},{content:c(()=>[v("div",le,[D(a,{column:5,size:"small"},{default:c(()=>[(m(!0),b(C,null,K(Y.value,l=>(m(),_(s,{label:l.label},{default:c(()=>[$(q(l.value),1)]),_:2},1032,["label"]))),256))]),_:1})])]),_:1}),v("div",null,[v("div",re,[D(M,{ref:"table",size:"default","table-key":"0","table-title":"设备部件状态","table-columns":d(L),"table-operate":[],noBatchApply:!0,"record-key":"name","table-datas":u.tableData,onHandleTableChange:O},{otherColumn:c(({column:l,record:r,text:de})=>[r.devSegmentName?(m(),b(C,{key:0},[l.dataIndex==="devSegmentName"&&r.devSegmentName?(m(),b("img",{key:0,class:"componentImg",src:k(r),title:`${r.devSegmentName}${d(o)(r.alarmDegree).text}`,alt:`${r.devSegmentName}${d(o)(r.alarmDegree).text}`},null,8,se)):l.dataIndex==="devSegmentName1"&&r.devSegmentName1?(m(),b("img",{key:1,class:"componentImg",src:k(r,"2"),title:`${r.devSegmentName1}${d(o)(r.alarmDegree1).text}`,alt:`${r.devSegmentName1}${d(o)(r.alarmDegree1).text}`},null,8,oe)):U("",!0)],64)):U("",!0)]),_:1},8,["table-columns","table-datas"])]),D(M,{ref:"table",size:"default","table-key":"0","table-title":"部件报警日志","table-columns":V,"table-operate":[],noBatchApply:!0,"record-key":"name","table-datas":u.tableData2},null,8,["table-datas"])])])]),_:1},8,["spinning"])):(m(),_(E,{key:1},{default:c(()=>t[0]||(t[0]=[$(" 无权限 ",-1)])),_:1,__:[0]}))}}},$e=te(me,[["__scopeId","data-v-8cdb4c8d"]]);export{$e as default};
