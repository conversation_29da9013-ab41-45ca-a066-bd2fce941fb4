﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;
using WTCMSLive.Entity.Models;

namespace WTCMSLive.BusinessEntityConvert
{
    // Author: whr
    // Create: 2013-09-02
    /// <summary>
    /// 实体转换， 从业务层实体转换为DA层实体
    /// </summary>
    public static class ConvertEntityBusinessToDAMDF
    {
        // --------------------------------------------------------------------------------------------------
        /// <summary>
        /// 测量定义实体转换
        /// </summary>
        /// <param name="_windPark"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.MeasDefinition ConvertMeasDefinition(WTCMSLive.BusinessEntity.MeasDefinition _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.MeasDefinition mdf = new WTCMSLive.Entity.Models.MeasDefinition();

            if (!string.IsNullOrEmpty(_entity.MeasDefinitionID))
            {
                mdf.MeasDefinitionID = Convert.ToInt32(_entity.MeasDefinitionID);
            }

            mdf.WindParkID = _entity.WindParkID;
            mdf.WindTurbineID = _entity.WindTurbineID;
            mdf.MeasurementDefinitionName = _entity.MeasDefinitionName;
            mdf.OrderSeq = _entity.OrderSeq;
            mdf.IsAvailable = (sbyte)(_entity.IsAvailable == true ? 1 : 0);

            return mdf;
        }

        public static List<MDFWorkCondition> ConvertValueDefWorkCondition(List<MeasLoc_Process> _measLocProList)
        {
            if (_measLocProList == null) return null;

            List<MDFWorkCondition> mdfWorkCons = new List<MDFWorkCondition>();

            _measLocProList.ForEach(
                item =>
                {
                    MDFWorkCondition workConDef = new MDFWorkCondition();

                    workConDef.MeasLocationID = item.MeasLocationID;
                    mdfWorkCons.Add(workConDef);
                }
                );
            return mdfWorkCons;
        }

        public static List<WTCMSLive.Entity.Models.MDFWaveDefinition> ConvertVibWaveDefinitionList(List<WaveDefinition> _entityList)
        {
            if (_entityList == null) return null;
            List<MDFWaveDefinition> mdfWaveDefinitionList = new List<MDFWaveDefinition>();

            _entityList.ForEach(
                item =>
                {
                    MDFWaveDefinition mdfWaveDefinition = ConvertVibWaveDefinition(item);
                    mdfWaveDefinitionList.Add(mdfWaveDefinition);
                }
                );
            return mdfWaveDefinitionList;
        }

        /// <summary>
        /// 波形定义实体转换
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.MDFWaveDefinition ConvertVibWaveDefinition(WaveDefinition _entity)
        {
            if (_entity == null) return null;

            WTCMSLive.Entity.Models.MDFWaveDefinition vibWDF = new Entity.Models.MDFWaveDefinition();

            if (!string.IsNullOrEmpty(_entity.WaveDefinitionID))
            {
                vibWDF.WaveDefinitionID = Convert.ToInt32(_entity.WaveDefinitionID);
            }
            vibWDF.WindTurbineID = _entity.WindTurbineID;
            vibWDF.MeasDefinitionID = Convert.ToInt32(_entity.MeasDefinitionID);
            vibWDF.MeasLocationID = _entity.MeasLocationID;
            vibWDF.WaveDefinitionName = _entity.WaveDefinitionName;
            vibWDF.WaveType = short.Parse(_entity.WaveType);

            if (!string.IsNullOrEmpty(_entity.WaveDefParamID))
            {
                vibWDF.WaveParamID = Convert.ToInt32(_entity.WaveDefParamID);
            }

            return vibWDF;
        }
        /// <summary>
        /// 有效性规则实体转换
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.MDFMeasActiveRule ConvertMeasActiveRule(MeasActiveRule _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.MDFMeasActiveRule measActiveRule = new Entity.Models.MDFMeasActiveRule();

            measActiveRule.MeasDefinitionID = Convert.ToInt32(_entity.MeasDefinitionID);
            measActiveRule.MeasLocationID = _entity.MeasLocationId;
            measActiveRule.IsAvailable = sbyte.Parse((_entity.IsAvailable == true ? '1' : '0').ToString());
            measActiveRule.LowerLimitValue = (decimal)_entity.LowerLimiValue;
            measActiveRule.UpperLimitValue = (decimal)_entity.UpperLimitValue;
            measActiveRule.SpecifcValue = _entity.SpecificValue;
            measActiveRule.RuleType = (sbyte)_entity.RuleType;

            return measActiveRule;
        }

        /// <summary>
        /// 时域波形定义转换
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.WDFParamTime ConvertWDFParamTime(WaveDef_Time _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.WDFParamTime wdfParamTime = new Entity.Models.WDFParamTime();

            wdfParamTime.WaveDefParamID = Convert.ToInt32(_entity.WaveDefParamID);
            wdfParamTime.WaveDefParamName = _entity.WaveDefinitionName;
            wdfParamTime.LowerLimitFreqency = (int)_entity.LowerLimitFreqency;
            wdfParamTime.UpperLimitFreqency = (int)_entity.UpperLimitFreqency;
            wdfParamTime.SampleLength = _entity.SampleLength;

            return wdfParamTime;
        }

        public static WDFParamTime ConvertWDFParamTime(WaveDefParam_Time _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            WTCMSLive.Entity.Models.WDFParamTime wdfParamTime = new Entity.Models.WDFParamTime();
            if (!string.IsNullOrEmpty(_entity.WaveDefParamID))
            {
                wdfParamTime.WaveDefParamID = Convert.ToInt32(_entity.WaveDefParamID);
            }
            wdfParamTime.WaveDefParamName = _entity.WaveDefParamName;
            wdfParamTime.LowerLimitFreqency = (int)_entity.LowerLimitFreqency;
            wdfParamTime.UpperLimitFreqency = (int)_entity.UpperLimitFreqency;
            wdfParamTime.SampleLength = _entity.SampleLength;

            return wdfParamTime;
        }

        /// <summary>
        /// 阶次包络波形定义转换
        /// </summary>
        /// <param name="_entity"></param>
        /// <returns></returns>
        public static WTCMSLive.Entity.Models.WDFParamOrderEvp ConvertWaveDef_OrderEvp(WaveDef_OrderEnv _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            Entity.Models.WDFParamOrderEvp wdfParaOrderEvp = new Entity.Models.WDFParamOrderEvp();

            wdfParaOrderEvp.WaveDefParamID = Convert.ToInt32(_entity.WaveDefParamID);
            wdfParaOrderEvp.WaveDefParamName = _entity.WaveDefinitionName;
            wdfParaOrderEvp.EnvBandWidth = (int)_entity.EnvBandWidth;
            wdfParaOrderEvp.EnvFiterFreq = (int)_entity.EnvFiterFreq;
            wdfParaOrderEvp.CycleNumber = _entity.CycleNumber;

            return wdfParaOrderEvp;
        }

        public static WTCMSLive.Entity.Models.WDFParamOrderEvp ConvertWaveDef_OrderEvp(WaveDefParam_OrderEnv _entity)
        {
            if (_entity == null)
            {
                return null;
            }

            Entity.Models.WDFParamOrderEvp wdfParaOrderEvp = new Entity.Models.WDFParamOrderEvp();

            wdfParaOrderEvp.WaveDefParamID = Convert.ToInt32(_entity.WaveDefParamID);
            wdfParaOrderEvp.WaveDefParamName = _entity.WaveDefParamName;
            wdfParaOrderEvp.EnvBandWidth = (int)_entity.EnvBandWidth;
            wdfParaOrderEvp.EnvFiterFreq = (int)_entity.EnvFiterFreq;
            wdfParaOrderEvp.CycleNumber = _entity.CycleNumber;

            return wdfParaOrderEvp;
        }
    }
}
