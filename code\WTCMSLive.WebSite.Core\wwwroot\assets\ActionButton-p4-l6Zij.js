import{y as Mt,S as Zt,z as Jt,A as Qt,u as en,d as qe,C as tn,D as nn,o as _t,T as Bt,j as on,l as ln,E as Tt,a as ve,F as an,G as rn,H as sn,g as un,c as cn}from"./styleChecker-D2z1GQZd.js";import{ad as R,h as Ze,dy as dn,aC as Be,X as Je,Y as Te,_ as y,$ as Qe,a1 as mn,a2 as Y,r as F,dB as pn,a5 as Le,j as b,aN as le,b as $,a8 as M,dC as vn,aq as rt,aH as st,F as Oe,a3 as N,w as Z,ek as fn,ar as oe,ap as me,ao as gn,el as bn,am as et,a0 as ut,ae as fe,aP as Se,aG as Ae,aK as ct,e4 as At,ac as he,aO as Dt,aA as $n,aB as yn,p as hn,v as Cn,em as dt,aa as Et,d$ as wn,aJ as Sn,Z as mt,aI as pt,en as xn,x as Me,eo as In,e3 as On}from"./index-ClUwy-U2.js";import{i as tt}from"./initDefaultProps-CbhiVpW4.js";import{i as De,a as vt,s as Pn,b as Mn,c as _n,d as Bn,O as Ie,K as Tn,e as xe}from"./shallowequal-DorGB6RW.js";import{S as We,h as An,B as Ee,d as Ce,w as Xe,c as Dn}from"./index-ByAZPsB5.js";function En(){}var Kn=1/0,kn=We&&1/Mt(new We([,-0]))[1]==Kn?function(e){return new We(e)}:En,Rn=200;function zn(e,n,t){var o=-1,l=Jt,r=e.length,c=!0,i=[],s=i;if(r>=Rn){var g=kn(e);if(g)return Mt(g);c=!1,l=Qt,s=new Zt}else s=i;e:for(;++o<r;){var d=e[o],a=d;if(d=d!==0?d:0,c&&a===a){for(var m=s.length;m--;)if(s[m]===a)continue e;i.push(d)}else l(s,a,t)||(s!==i&&s.push(a),i.push(d))}return i}function Ge(e){return e&&e.length?zn(e):[]}function ko(){const e=R({});let n=null;const t=en();return Ze(()=>{n=t.value.subscribe(o=>{e.value=o})}),dn(()=>{t.value.unsubscribe(n)}),e}function Ro(e){const n=R();return Be(()=>{n.value=e()},{flush:"sync"}),n}const Nn=e=>{const{componentCls:n,popoverBg:t,popoverColor:o,width:l,fontWeightStrong:r,popoverPadding:c,boxShadowSecondary:i,colorTextHeading:s,borderRadiusLG:g,zIndexPopup:d,marginXS:a,colorBgElevated:m}=e;return[{[n]:y(y({},Qe(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:d,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--antd-arrow-background-color":m,"&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${n}-content`]:{position:"relative"},[`${n}-inner`]:{backgroundColor:t,backgroundClip:"padding-box",borderRadius:g,boxShadow:i,padding:c},[`${n}-title`]:{minWidth:l,marginBottom:a,color:s,fontWeight:r},[`${n}-inner-content`]:{color:o}})},tn(e,{colorBg:"var(--antd-arrow-background-color)"}),{[`${n}-pure`]:{position:"relative",maxWidth:"none",[`${n}-content`]:{display:"inline-block"}}}]},Ln=e=>{const{componentCls:n}=e;return{[n]:nn.map(t=>{const o=e[`${t}-6`];return{[`&${n}-${t}`]:{"--antd-arrow-background-color":o,[`${n}-inner`]:{backgroundColor:o},[`${n}-arrow`]:{background:"transparent"}}}})}},jn=e=>{const{componentCls:n,lineWidth:t,lineType:o,colorSplit:l,paddingSM:r,controlHeight:c,fontSize:i,lineHeight:s,padding:g}=e,d=c-Math.round(i*s),a=d/2,m=d/2-t,u=g;return{[n]:{[`${n}-inner`]:{padding:0},[`${n}-title`]:{margin:0,padding:`${a}px ${u}px ${m}px`,borderBottom:`${t}px ${o} ${l}`},[`${n}-inner-content`]:{padding:`${r}px ${u}px`}}}},Hn=Je("Popover",e=>{const{colorBgElevated:n,colorText:t,wireframe:o}=e,l=Te(e,{popoverBg:n,popoverColor:t,popoverPadding:12});return[Nn(l),Ln(l),o&&jn(l),qe(l,"zoom-big")]},e=>{let{zIndexPopupBase:n}=e;return{zIndexPopup:n+30,width:177}}),Fn=()=>y(y({},ln()),{content:rt(),title:rt()}),Vn=Y({compatConfig:{MODE:3},name:"APopover",inheritAttrs:!1,props:tt(Fn(),y(y({},on()),{trigger:"hover",placement:"top",mouseEnterDelay:.1,mouseLeaveDelay:.1})),setup(e,n){let{expose:t,slots:o,attrs:l}=n;const r=F();pn(e.visible===void 0),t({getPopupDomNode:()=>{var m,u;return(u=(m=r.value)===null||m===void 0?void 0:m.getPopupDomNode)===null||u===void 0?void 0:u.call(m)}});const{prefixCls:c,configProvider:i}=Le("popover",e),[s,g]=Hn(c),d=b(()=>i.getPrefixCls()),a=()=>{var m,u;const{title:v=st((m=o.title)===null||m===void 0?void 0:m.call(o)),content:C=st((u=o.content)===null||u===void 0?void 0:u.call(o))}=e,f=!!(Array.isArray(v)?v.length:v),h=!!(Array.isArray(C)?C.length:v);return!f&&!h?null:$(Oe,null,[f&&$("div",{class:`${c.value}-title`},[v]),$("div",{class:`${c.value}-inner-content`},[C])])};return()=>{const m=le(e.overlayClassName,g.value);return s($(Bt,M(M(M({},_t(e,["title","content"])),l),{},{prefixCls:c.value,ref:r,overlayClassName:m,transitionName:vn(d.value,"zoom-big",e.transitionName),"data-popover-inject":!0}),{title:a,default:o.default}))}}}),zo=mn(Vn),be={adjustX:1,adjustY:1},$e=[0,0],Wn={topLeft:{points:["bl","tl"],overflow:be,offset:[0,-4],targetOffset:$e},topCenter:{points:["bc","tc"],overflow:be,offset:[0,-4],targetOffset:$e},topRight:{points:["br","tr"],overflow:be,offset:[0,-4],targetOffset:$e},bottomLeft:{points:["tl","bl"],overflow:be,offset:[0,4],targetOffset:$e},bottomCenter:{points:["tc","bc"],overflow:be,offset:[0,4],targetOffset:$e},bottomRight:{points:["tr","br"],overflow:be,offset:[0,4],targetOffset:$e}};var Xn=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)n.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(t[o[l]]=e[o[l]]);return t};const Gn=Y({compatConfig:{MODE:3},props:{minOverlayWidthMatchTrigger:{type:Boolean,default:void 0},arrow:{type:Boolean,default:!1},prefixCls:N.string.def("rc-dropdown"),transitionName:String,overlayClassName:N.string.def(""),openClassName:String,animation:N.any,align:N.object,overlayStyle:{type:Object,default:void 0},placement:N.string.def("bottomLeft"),overlay:N.any,trigger:N.oneOfType([N.string,N.arrayOf(N.string)]).def("hover"),alignPoint:{type:Boolean,default:void 0},showAction:N.array,hideAction:N.array,getPopupContainer:Function,visible:{type:Boolean,default:void 0},defaultVisible:{type:Boolean,default:!1},mouseEnterDelay:N.number.def(.15),mouseLeaveDelay:N.number.def(.1)},emits:["visibleChange","overlayClick"],setup(e,n){let{slots:t,emit:o,expose:l}=n;const r=F(!!e.visible);Z(()=>e.visible,u=>{u!==void 0&&(r.value=u)});const c=F();l({triggerRef:c});const i=u=>{e.visible===void 0&&(r.value=!1),o("overlayClick",u)},s=u=>{e.visible===void 0&&(r.value=u),o("visibleChange",u)},g=()=>{var u;const v=(u=t.overlay)===null||u===void 0?void 0:u.call(t),C={prefixCls:`${e.prefixCls}-menu`,onClick:i};return $(Oe,{key:fn},[e.arrow&&$("div",{class:`${e.prefixCls}-arrow`},null),ve(v,C,!1)])},d=b(()=>{const{minOverlayWidthMatchTrigger:u=!e.alignPoint}=e;return u}),a=()=>{var u;const v=(u=t.default)===null||u===void 0?void 0:u.call(t);return r.value&&v?ve(v[0],{class:e.openClassName||`${e.prefixCls}-open`},!1):v},m=b(()=>!e.hideAction&&e.trigger.indexOf("contextmenu")!==-1?["click"]:e.hideAction);return()=>{const{prefixCls:u,arrow:v,showAction:C,overlayStyle:f,trigger:h,placement:S,align:K,getPopupContainer:E,transitionName:w,animation:x,overlayClassName:D}=e,_=Xn(e,["prefixCls","arrow","showAction","overlayStyle","trigger","placement","align","getPopupContainer","transitionName","animation","overlayClassName"]);return $(Tt,M(M({},_),{},{prefixCls:u,ref:c,popupClassName:le(D,{[`${u}-show-arrow`]:v}),popupStyle:f,builtinPlacements:Wn,action:h,showAction:C,hideAction:m.value||[],popupPlacement:S,popupAlign:K,popupTransitionName:w,popupAnimation:x,popupVisible:r.value,stretch:d.value?"minWidth":"",onPopupVisibleChange:s,getPopupContainer:E}),{popup:g,default:a})}}}),Kt=()=>({arrow:gn([Boolean,Object]),trigger:{type:[Array,String]},menu:me(),overlay:N.any,visible:oe(),open:oe(),disabled:oe(),danger:oe(),autofocus:oe(),align:me(),getPopupContainer:Function,prefixCls:String,transitionName:String,placement:String,overlayClassName:String,overlayStyle:me(),forceRender:oe(),mouseEnterDelay:Number,mouseLeaveDelay:Number,openClassName:String,minOverlayWidthMatchTrigger:oe(),destroyPopupOnHide:oe(),onVisibleChange:{type:Function},"onUpdate:visible":{type:Function},onOpenChange:{type:Function},"onUpdate:open":{type:Function}}),Ye=An(),Yn=()=>y(y({},Kt()),{type:Ye.type,size:String,htmlType:Ye.htmlType,href:String,disabled:oe(),prefixCls:String,icon:N.any,title:String,loading:Ye.loading,onClick:bn()});var Un={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};function ft(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(l){return Object.getOwnPropertyDescriptor(t,l).enumerable}))),o.forEach(function(l){qn(e,l,t[l])})}return e}function qn(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var je=function(n,t){var o=ft({},n,t.attrs);return $(et,ft({},o,{icon:Un}),null)};je.displayName="EllipsisOutlined";je.inheritAttrs=!1;const Zn=e=>{const{componentCls:n,antCls:t,paddingXS:o,opacityLoading:l}=e;return{[`${n}-button`]:{whiteSpace:"nowrap",[`&${t}-btn-group > ${t}-btn`]:{[`&-loading, &-loading + ${t}-btn`]:{cursor:"default",pointerEvents:"none",opacity:l},[`&:last-child:not(:first-child):not(${t}-btn-icon-only)`]:{paddingInline:o}}}}},Jn=e=>{const{componentCls:n,menuCls:t,colorError:o,colorTextLightSolid:l}=e,r=`${t}-item`;return{[`${n}, ${n}-menu-submenu`]:{[`${t} ${r}`]:{[`&${r}-danger:not(${r}-disabled)`]:{color:o,"&:hover":{color:l,backgroundColor:o}}}}}},Qn=e=>{const{componentCls:n,menuCls:t,zIndexPopup:o,dropdownArrowDistance:l,dropdownArrowOffset:r,sizePopupArrow:c,antCls:i,iconCls:s,motionDurationMid:g,dropdownPaddingVertical:d,fontSize:a,dropdownEdgeChildPadding:m,colorTextDisabled:u,fontSizeIcon:v,controlPaddingHorizontal:C,colorBgElevated:f,boxShadowPopoverArrow:h}=e;return[{[n]:y(y({},Qe(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:-l+c/2,zIndex:-9999,opacity:1e-4,content:'""'},[`${n}-wrap`]:{position:"relative",[`${i}-btn > ${s}-down`]:{fontSize:v},[`${s}-down::before`]:{transition:`transform ${g}`}},[`${n}-wrap-open`]:{[`${s}-down::before`]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},[`
        &-show-arrow${n}-placement-topLeft,
        &-show-arrow${n}-placement-top,
        &-show-arrow${n}-placement-topRight
      `]:{paddingBottom:l},[`
        &-show-arrow${n}-placement-bottomLeft,
        &-show-arrow${n}-placement-bottom,
        &-show-arrow${n}-placement-bottomRight
      `]:{paddingTop:l},[`${n}-arrow`]:y({position:"absolute",zIndex:1,display:"block"},rn(c,e.borderRadiusXS,e.borderRadiusOuter,f,h)),[`
        &-placement-top > ${n}-arrow,
        &-placement-topLeft > ${n}-arrow,
        &-placement-topRight > ${n}-arrow
      `]:{bottom:l,transform:"translateY(100%) rotate(180deg)"},[`&-placement-top > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},[`&-placement-topLeft > ${n}-arrow`]:{left:{_skip_check_:!0,value:r}},[`&-placement-topRight > ${n}-arrow`]:{right:{_skip_check_:!0,value:r}},[`
          &-placement-bottom > ${n}-arrow,
          &-placement-bottomLeft > ${n}-arrow,
          &-placement-bottomRight > ${n}-arrow
        `]:{top:l,transform:"translateY(-100%)"},[`&-placement-bottom > ${n}-arrow`]:{left:{_skip_check_:!0,value:"50%"},transform:"translateY(-100%) translateX(-50%)"},[`&-placement-bottomLeft > ${n}-arrow`]:{left:{_skip_check_:!0,value:r}},[`&-placement-bottomRight > ${n}-arrow`]:{right:{_skip_check_:!0,value:r}},[`&${i}-slide-down-enter${i}-slide-down-enter-active${n}-placement-bottomLeft,
          &${i}-slide-down-appear${i}-slide-down-appear-active${n}-placement-bottomLeft,
          &${i}-slide-down-enter${i}-slide-down-enter-active${n}-placement-bottom,
          &${i}-slide-down-appear${i}-slide-down-appear-active${n}-placement-bottom,
          &${i}-slide-down-enter${i}-slide-down-enter-active${n}-placement-bottomRight,
          &${i}-slide-down-appear${i}-slide-down-appear-active${n}-placement-bottomRight`]:{animationName:Bn},[`&${i}-slide-up-enter${i}-slide-up-enter-active${n}-placement-topLeft,
          &${i}-slide-up-appear${i}-slide-up-appear-active${n}-placement-topLeft,
          &${i}-slide-up-enter${i}-slide-up-enter-active${n}-placement-top,
          &${i}-slide-up-appear${i}-slide-up-appear-active${n}-placement-top,
          &${i}-slide-up-enter${i}-slide-up-enter-active${n}-placement-topRight,
          &${i}-slide-up-appear${i}-slide-up-appear-active${n}-placement-topRight`]:{animationName:_n},[`&${i}-slide-down-leave${i}-slide-down-leave-active${n}-placement-bottomLeft,
          &${i}-slide-down-leave${i}-slide-down-leave-active${n}-placement-bottom,
          &${i}-slide-down-leave${i}-slide-down-leave-active${n}-placement-bottomRight`]:{animationName:Mn},[`&${i}-slide-up-leave${i}-slide-up-leave-active${n}-placement-topLeft,
          &${i}-slide-up-leave${i}-slide-up-leave-active${n}-placement-top,
          &${i}-slide-up-leave${i}-slide-up-leave-active${n}-placement-topRight`]:{animationName:Pn}})},{[`${n} ${t}`]:{position:"relative",margin:0},[`${t}-submenu-popup`]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul,li":{listStyle:"none"},ul:{marginInline:"0.3em"}},[`${n}, ${n}-menu-submenu`]:{[t]:y(y({padding:m,listStyleType:"none",backgroundColor:f,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},ut(e)),{[`${t}-item-group-title`]:{padding:`${d}px ${C}px`,color:e.colorTextDescription,transition:`all ${g}`},[`${t}-item`]:{position:"relative",display:"flex",alignItems:"center",borderRadius:e.borderRadiusSM},[`${t}-item-icon`]:{minWidth:a,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${t}-title-content`]:{flex:"auto","> a":{color:"inherit",transition:`all ${g}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}}},[`${t}-item, ${t}-submenu-title`]:y(y({clear:"both",margin:0,padding:`${d}px ${C}px`,color:e.colorText,fontWeight:"normal",fontSize:a,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${g}`,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},ut(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:u,cursor:"not-allowed","&:hover":{color:u,backgroundColor:f,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${e.marginXXS}px 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${n}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${n}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorTextDescription,fontSize:v,fontStyle:"normal"}}}),[`${t}-item-group-list`]:{margin:`0 ${e.marginXS}px`,padding:0,listStyle:"none"},[`${t}-submenu-title`]:{paddingInlineEnd:C+e.fontSizeSM},[`${t}-submenu-vertical`]:{position:"relative"},[`${t}-submenu${t}-submenu-disabled ${n}-menu-submenu-title`]:{[`&, ${n}-menu-submenu-arrow-icon`]:{color:u,backgroundColor:f,cursor:"not-allowed"}},[`${t}-submenu-selected ${n}-menu-submenu-title`]:{color:e.colorPrimary}})}},[De(e,"slide-up"),De(e,"slide-down"),vt(e,"move-up"),vt(e,"move-down"),qe(e,"zoom-big")]]},kt=Je("Dropdown",(e,n)=>{let{rootPrefixCls:t}=n;const{marginXXS:o,sizePopupArrow:l,controlHeight:r,fontSize:c,lineHeight:i,paddingXXS:s,componentCls:g,borderRadiusOuter:d,borderRadiusLG:a}=e,m=(r-c*i)/2,{dropdownArrowOffset:u}=an({sizePopupArrow:l,contentRadius:a,borderRadiusOuter:d}),v=Te(e,{menuCls:`${g}-menu`,rootPrefixCls:t,dropdownArrowDistance:l/2+o,dropdownArrowOffset:u,dropdownPaddingVertical:m,dropdownEdgeChildPadding:s});return[Qn(v),Zn(v),Jn(v)]},e=>({zIndexPopup:e.zIndexPopupBase+50}));var eo=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)n.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(t[o[l]]=e[o[l]]);return t};const to=Ee.Group,Ke=Y({compatConfig:{MODE:3},name:"ADropdownButton",inheritAttrs:!1,__ANT_BUTTON:!0,props:tt(Yn(),{trigger:"hover",placement:"bottomRight",type:"default"}),slots:Object,setup(e,n){let{slots:t,attrs:o,emit:l}=n;const r=m=>{l("update:visible",m),l("visibleChange",m),l("update:open",m),l("openChange",m)},{prefixCls:c,direction:i,getPopupContainer:s}=Le("dropdown",e),g=b(()=>`${c.value}-button`),[d,a]=kt(c);return()=>{var m,u;const v=y(y({},e),o),{type:C="default",disabled:f,danger:h,loading:S,htmlType:K,class:E="",overlay:w=(m=t.overlay)===null||m===void 0?void 0:m.call(t),trigger:x,align:D,open:_,visible:L,onVisibleChange:B,placement:V=i.value==="rtl"?"bottomLeft":"bottomRight",href:W,title:U,icon:ae=((u=t.icon)===null||u===void 0?void 0:u.call(t))||$(je,null,null),mouseEnterDelay:re,mouseLeaveDelay:ee,overlayClassName:te,overlayStyle:se,destroyPopupOnHide:G,onClick:de,"onUpdate:open":O}=v,k=eo(v,["type","disabled","danger","loading","htmlType","class","overlay","trigger","align","open","visible","onVisibleChange","placement","href","title","icon","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyPopupOnHide","onClick","onUpdate:open"]),X={align:D,disabled:f,trigger:f?[]:x,placement:V,getPopupContainer:s==null?void 0:s.value,onOpenChange:r,mouseEnterDelay:re,mouseLeaveDelay:ee,open:_??L,overlayClassName:te,overlayStyle:se,destroyPopupOnHide:G},q=$(Ee,{danger:h,type:C,disabled:f,loading:S,onClick:de,htmlType:K,href:W,title:U},{default:t.default}),J=$(Ee,{danger:h,type:C,icon:ae},null);return d($(to,M(M({},k),{},{class:le(g.value,E,a.value)}),{default:()=>[t.leftButton?t.leftButton({button:q}):q,$(ye,X,{default:()=>[t.rightButton?t.rightButton({button:J}):J],overlay:()=>w})]}))}}});var no={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};function gt(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(l){return Object.getOwnPropertyDescriptor(t,l).enumerable}))),o.forEach(function(l){oo(e,l,t[l])})}return e}function oo(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var ke=function(n,t){var o=gt({},n,t.attrs);return $(et,gt({},o,{icon:no}),null)};ke.displayName="RightOutlined";ke.inheritAttrs=!1;const Rt=Symbol("OverrideContextKey"),zt=()=>fe(Rt,void 0),lo=e=>{var n,t,o;const{prefixCls:l,mode:r,selectable:c,validator:i,onClick:s,expandIcon:g}=zt()||{};Se(Rt,{prefixCls:b(()=>{var d,a;return(a=(d=e.prefixCls)===null||d===void 0?void 0:d.value)!==null&&a!==void 0?a:l==null?void 0:l.value}),mode:b(()=>{var d,a;return(a=(d=e.mode)===null||d===void 0?void 0:d.value)!==null&&a!==void 0?a:r==null?void 0:r.value}),selectable:b(()=>{var d,a;return(a=(d=e.selectable)===null||d===void 0?void 0:d.value)!==null&&a!==void 0?a:c==null?void 0:c.value}),validator:(n=e.validator)!==null&&n!==void 0?n:i,onClick:(t=e.onClick)!==null&&t!==void 0?t:s,expandIcon:(o=e.expandIcon)!==null&&o!==void 0?o:g==null?void 0:g.value})},ye=Y({compatConfig:{MODE:3},name:"ADropdown",inheritAttrs:!1,props:tt(Kt(),{mouseEnterDelay:.15,mouseLeaveDelay:.1,placement:"bottomLeft",trigger:"hover"}),slots:Object,setup(e,n){let{slots:t,attrs:o,emit:l}=n;const{prefixCls:r,rootPrefixCls:c,direction:i,getPopupContainer:s}=Le("dropdown",e),[g,d]=kt(r),a=b(()=>{const{placement:f="",transitionName:h}=e;return h!==void 0?h:f.includes("top")?`${c.value}-slide-down`:`${c.value}-slide-up`});lo({prefixCls:b(()=>`${r.value}-menu`),expandIcon:b(()=>$("span",{class:`${r.value}-menu-submenu-arrow`},[$(ke,{class:`${r.value}-menu-submenu-arrow-icon`},null)])),mode:b(()=>"vertical"),selectable:b(()=>!1),onClick:()=>{},validator:f=>{let{mode:h}=f}});const m=()=>{var f,h,S;const K=e.overlay||((f=t.overlay)===null||f===void 0?void 0:f.call(t)),E=Array.isArray(K)?K[0]:K;if(!E)return null;const w=E.props||{};Ce(!w.mode||w.mode==="vertical","Dropdown",`mode="${w.mode}" is not supported for Dropdown's Menu.`);const{selectable:x=!1,expandIcon:D=(S=(h=E.children)===null||h===void 0?void 0:h.expandIcon)===null||S===void 0?void 0:S.call(h)}=w,_=typeof D<"u"&&Ae(D)?D:$("span",{class:`${r.value}-menu-submenu-arrow`},[$(ke,{class:`${r.value}-menu-submenu-arrow-icon`},null)]);return Ae(E)?ve(E,{mode:"vertical",selectable:x,expandIcon:()=>_}):E},u=b(()=>{const f=e.placement;if(!f)return i.value==="rtl"?"bottomRight":"bottomLeft";if(f.includes("Center")){const h=f.slice(0,f.indexOf("Center"));return Ce(!f.includes("Center"),"Dropdown",`You are using '${f}' placement in Dropdown, which is deprecated. Try to use '${h}' instead.`),h}return f}),v=b(()=>typeof e.visible=="boolean"?e.visible:e.open),C=f=>{l("update:visible",f),l("visibleChange",f),l("update:open",f),l("openChange",f)};return()=>{var f,h;const{arrow:S,trigger:K,disabled:E,overlayClassName:w}=e,x=(f=t.default)===null||f===void 0?void 0:f.call(t)[0],D=ve(x,y({class:le((h=x==null?void 0:x.props)===null||h===void 0?void 0:h.class,{[`${r.value}-rtl`]:i.value==="rtl"},`${r.value}-trigger`)},E?{disabled:E}:{})),_=le(w,d.value,{[`${r.value}-rtl`]:i.value==="rtl"}),L=E?[]:K;let B;L&&L.includes("contextmenu")&&(B=!0);const V=sn({arrowPointAtCenter:typeof S=="object"&&S.pointAtCenter,autoAdjustOverflow:!0}),W=_t(y(y(y({},e),o),{visible:v.value,builtinPlacements:V,overlayClassName:_,arrow:!!S,alignPoint:B,prefixCls:r.value,getPopupContainer:s==null?void 0:s.value,transitionName:a.value,trigger:L,onVisibleChange:C,placement:u.value}),["overlay","onUpdate:visible"]);return g($(Gn,W,{default:()=>[D],overlay:m}))}}});ye.Button=Ke;const Nt=Symbol("menuContextKey"),Lt=e=>{Se(Nt,e)},ie=()=>fe(Nt),jt=Symbol("ForceRenderKey"),io=e=>{Se(jt,e)},Ht=()=>fe(jt,!1),Ft=Symbol("menuFirstLevelContextKey"),Vt=e=>{Se(Ft,e)},ao=()=>fe(Ft,!0),Re=Y({compatConfig:{MODE:3},name:"MenuContextProvider",inheritAttrs:!1,props:{mode:{type:String,default:void 0},overflowDisabled:{type:Boolean,default:void 0}},setup(e,n){let{slots:t}=n;const o=ie(),l=y({},o);return e.mode!==void 0&&(l.mode=ct(e,"mode")),e.overflowDisabled!==void 0&&(l.overflowDisabled=ct(e,"overflowDisabled")),Lt(l),()=>{var r;return(r=t.default)===null||r===void 0?void 0:r.call(t)}}}),ro=Symbol("siderCollapsed"),No=Symbol("siderHookProvider"),_e="$$__vc-menu-more__key",Wt=Symbol("KeyPathContext"),nt=()=>fe(Wt,{parentEventKeys:b(()=>[]),parentKeys:b(()=>[]),parentInfo:{}}),so=(e,n,t)=>{const{parentEventKeys:o,parentKeys:l}=nt(),r=b(()=>[...o.value,e]),c=b(()=>[...l.value,n]);return Se(Wt,{parentEventKeys:r,parentKeys:c,parentInfo:t}),c},Xt=Symbol("measure"),bt=Y({compatConfig:{MODE:3},setup(e,n){let{slots:t}=n;return Se(Xt,!0),()=>{var o;return(o=t.default)===null||o===void 0?void 0:o.call(t)}}}),ot=()=>fe(Xt,!1);function Gt(e){const{mode:n,rtl:t,inlineIndent:o}=ie();return b(()=>n.value!=="inline"?null:t.value?{paddingRight:`${e.value*o.value}px`}:{paddingLeft:`${e.value*o.value}px`})}let uo=0;const co=()=>({id:String,role:String,disabled:Boolean,danger:Boolean,title:{type:[String,Boolean],default:void 0},icon:N.any,onMouseenter:Function,onMouseleave:Function,onClick:Function,onKeydown:Function,onFocus:Function,originItemValue:me()}),Pe=Y({compatConfig:{MODE:3},name:"AMenuItem",inheritAttrs:!1,props:co(),slots:Object,setup(e,n){let{slots:t,emit:o,attrs:l}=n;const r=At(),c=ot(),i=typeof r.vnode.key=="symbol"?String(r.vnode.key):r.vnode.key;Ce(typeof r.vnode.key!="symbol","MenuItem",`MenuItem \`:key="${String(i)}"\` not support Symbol type`);const s=`menu_item_${++uo}_$$_${i}`,{parentEventKeys:g,parentKeys:d}=nt(),{prefixCls:a,activeKeys:m,disabled:u,changeActiveKeys:v,rtl:C,inlineCollapsed:f,siderCollapsed:h,onItemClick:S,selectedKeys:K,registerMenuInfo:E,unRegisterMenuInfo:w}=ie(),x=ao(),D=R(!1),_=b(()=>[...d.value,i]);E(s,{eventKey:s,key:i,parentEventKeys:g,parentKeys:d,isLeaf:!0}),he(()=>{w(s)}),Z(m,()=>{D.value=!!m.value.find(O=>O===i)},{immediate:!0});const B=b(()=>u.value||e.disabled),V=b(()=>K.value.includes(i)),W=b(()=>{const O=`${a.value}-item`;return{[`${O}`]:!0,[`${O}-danger`]:e.danger,[`${O}-active`]:D.value,[`${O}-selected`]:V.value,[`${O}-disabled`]:B.value}}),U=O=>({key:i,eventKey:s,keyPath:_.value,eventKeyPath:[...g.value,s],domEvent:O,item:y(y({},e),l)}),ae=O=>{if(B.value)return;const k=U(O);o("click",O),S(k)},re=O=>{B.value||(v(_.value),o("mouseenter",O))},ee=O=>{B.value||(v([]),o("mouseleave",O))},te=O=>{if(o("keydown",O),O.which===Tn.ENTER){const k=U(O);o("click",O),S(k)}},se=O=>{v(_.value),o("focus",O)},G=(O,k)=>{const X=$("span",{class:`${a.value}-title-content`},[k]);return(!O||Ae(k)&&k.type==="span")&&k&&f.value&&x&&typeof k=="string"?$("div",{class:`${a.value}-inline-collapsed-noicon`},[k.charAt(0)]):X},de=Gt(b(()=>_.value.length));return()=>{var O,k,X,q,J;if(c)return null;const ne=(O=e.title)!==null&&O!==void 0?O:(k=t.title)===null||k===void 0?void 0:k.call(t),p=Dt((X=t.default)===null||X===void 0?void 0:X.call(t)),I=p.length;let T=ne;typeof ne>"u"?T=x&&I?p:"":ne===!1&&(T="");const z={title:T};!h.value&&!f.value&&(z.title=null,z.open=!1);const j={};e.role==="option"&&(j["aria-selected"]=V.value);const A=(q=e.icon)!==null&&q!==void 0?q:(J=t.icon)===null||J===void 0?void 0:J.call(t,e);return $(Bt,M(M({},z),{},{placement:C.value?"left":"right",overlayClassName:`${a.value}-inline-collapsed-tooltip`}),{default:()=>[$(Ie.Item,M(M(M({component:"li"},l),{},{id:e.id,style:y(y({},l.style||{}),de.value),class:[W.value,{[`${l.class}`]:!!l.class,[`${a.value}-item-only-child`]:(A?I+1:I)===1}],role:e.role||"menuitem",tabindex:e.disabled?null:-1,"data-menu-id":i,"aria-disabled":e.disabled},j),{},{onMouseenter:re,onMouseleave:ee,onClick:ae,onKeydown:te,onFocus:se,title:typeof ne=="string"?ne:void 0}),{default:()=>[ve(typeof A=="function"?A(e.originItemValue):A,{class:`${a.value}-item-icon`},!1),G(A,p)]})]})}}}),ce={adjustX:1,adjustY:1},mo={topLeft:{points:["bl","tl"],overflow:ce,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:ce,offset:[0,7]},leftTop:{points:["tr","tl"],overflow:ce,offset:[-4,0]},rightTop:{points:["tl","tr"],overflow:ce,offset:[4,0]}},po={topLeft:{points:["bl","tl"],overflow:ce,offset:[0,-7]},bottomLeft:{points:["tl","bl"],overflow:ce,offset:[0,7]},rightTop:{points:["tr","tl"],overflow:ce,offset:[-4,0]},leftTop:{points:["tl","tr"],overflow:ce,offset:[4,0]}},vo={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"},$t=Y({compatConfig:{MODE:3},name:"PopupTrigger",inheritAttrs:!1,props:{prefixCls:String,mode:String,visible:Boolean,popupClassName:String,popupOffset:Array,disabled:Boolean,onVisibleChange:Function},slots:Object,emits:["visibleChange"],setup(e,n){let{slots:t,emit:o}=n;const l=R(!1),{getPopupContainer:r,rtl:c,subMenuOpenDelay:i,subMenuCloseDelay:s,builtinPlacements:g,triggerSubMenuAction:d,forceSubMenuRender:a,motion:m,defaultMotions:u,rootClassName:v}=ie(),C=Ht(),f=b(()=>c.value?y(y({},po),g.value):y(y({},mo),g.value)),h=b(()=>vo[e.mode]),S=R();Z(()=>e.visible,w=>{Xe.cancel(S.value),S.value=Xe(()=>{l.value=w})},{immediate:!0}),he(()=>{Xe.cancel(S.value)});const K=w=>{o("visibleChange",w)},E=b(()=>{var w,x;const D=m.value||((w=u.value)===null||w===void 0?void 0:w[e.mode])||((x=u.value)===null||x===void 0?void 0:x.other),_=typeof D=="function"?D():D;return _?$n(_.name,{css:!0}):void 0});return()=>{const{prefixCls:w,popupClassName:x,mode:D,popupOffset:_,disabled:L}=e;return $(Tt,{prefixCls:w,popupClassName:le(`${w}-popup`,{[`${w}-rtl`]:c.value},x,v.value),stretch:D==="horizontal"?"minWidth":null,getPopupContainer:r.value,builtinPlacements:f.value,popupPlacement:h.value,popupVisible:l.value,popupAlign:_&&{offset:_},action:L?[]:[d.value],mouseEnterDelay:i.value,mouseLeaveDelay:s.value,onPopupVisibleChange:K,forceRender:C||a.value,popupAnimation:E.value},{popup:t.popup,default:t.default})}}}),lt=(e,n)=>{let{slots:t,attrs:o}=n;var l;const{prefixCls:r,mode:c}=ie();return $("ul",M(M({},o),{},{class:le(r.value,`${r.value}-sub`,`${r.value}-${c.value==="inline"?"inline":"vertical"}`),"data-menu-list":!0}),[(l=t.default)===null||l===void 0?void 0:l.call(t)])};lt.displayName="SubMenuList";const fo=Y({compatConfig:{MODE:3},name:"InlineSubMenuList",inheritAttrs:!1,props:{id:String,open:Boolean,keyPath:Array},setup(e,n){let{slots:t}=n;const o=b(()=>"inline"),{motion:l,mode:r,defaultMotions:c}=ie(),i=b(()=>r.value===o.value),s=F(!i.value),g=b(()=>i.value?e.open:!1);Z(r,()=>{i.value&&(s.value=!1)},{flush:"post"});const d=b(()=>{var a,m;const u=l.value||((a=c.value)===null||a===void 0?void 0:a[o.value])||((m=c.value)===null||m===void 0?void 0:m.other),v=typeof u=="function"?u():u;return y(y({},v),{appear:e.keyPath.length<=1})});return()=>{var a;return s.value?null:$(Re,{mode:o.value},{default:()=>[$(yn,d.value,{default:()=>[hn($(lt,{id:e.id},{default:()=>[(a=t.default)===null||a===void 0?void 0:a.call(t)]}),[[Cn,g.value]])]})]})}}});let yt=0;const go=()=>({icon:N.any,title:N.any,disabled:Boolean,level:Number,popupClassName:String,popupOffset:Array,internalPopupClose:Boolean,eventKey:String,expandIcon:Function,theme:String,onMouseenter:Function,onMouseleave:Function,onTitleClick:Function,originItemValue:me()}),we=Y({compatConfig:{MODE:3},name:"ASubMenu",inheritAttrs:!1,props:go(),slots:Object,setup(e,n){let{slots:t,attrs:o,emit:l}=n;var r,c;Vt(!1);const i=ot(),s=At(),g=typeof s.vnode.key=="symbol"?String(s.vnode.key):s.vnode.key;Ce(typeof s.vnode.key!="symbol","SubMenu",`SubMenu \`:key="${String(g)}"\` not support Symbol type`);const d=dt(g)?g:`sub_menu_${++yt}_$$_not_set_key`,a=(r=e.eventKey)!==null&&r!==void 0?r:dt(g)?`sub_menu_${++yt}_$$_${g}`:d,{parentEventKeys:m,parentInfo:u,parentKeys:v}=nt(),C=b(()=>[...v.value,d]),f=R([]),h={eventKey:a,key:d,parentEventKeys:m,childrenEventKeys:f,parentKeys:v};(c=u.childrenEventKeys)===null||c===void 0||c.value.push(a),he(()=>{var P;u.childrenEventKeys&&(u.childrenEventKeys.value=(P=u.childrenEventKeys)===null||P===void 0?void 0:P.value.filter(H=>H!=a))}),so(a,d,h);const{prefixCls:S,activeKeys:K,disabled:E,changeActiveKeys:w,mode:x,inlineCollapsed:D,openKeys:_,overflowDisabled:L,onOpenChange:B,registerMenuInfo:V,unRegisterMenuInfo:W,selectedSubMenuKeys:U,expandIcon:ae,theme:re}=ie(),ee=g!=null,te=!i&&(Ht()||!ee);io(te),(i&&ee||!i&&!ee||te)&&(V(a,h),he(()=>{W(a)}));const se=b(()=>`${S.value}-submenu`),G=b(()=>E.value||e.disabled),de=R(),O=R(),k=b(()=>_.value.includes(d)),X=b(()=>!L.value&&k.value),q=b(()=>U.value.includes(d)),J=R(!1);Z(K,()=>{J.value=!!K.value.find(P=>P===d)},{immediate:!0});const ne=P=>{G.value||(l("titleClick",P,d),x.value==="inline"&&B(d,!k.value))},p=P=>{G.value||(w(C.value),l("mouseenter",P))},I=P=>{G.value||(w([]),l("mouseleave",P))},T=Gt(b(()=>C.value.length)),z=P=>{x.value!=="inline"&&B(d,P)},j=()=>{w(C.value)},A=a&&`${a}-popup`,Q=b(()=>le(S.value,`${S.value}-${e.theme||re.value}`,e.popupClassName)),ge=(P,H)=>{if(!H)return D.value&&!v.value.length&&P&&typeof P=="string"?$("div",{class:`${S.value}-inline-collapsed-noicon`},[P.charAt(0)]):$("span",{class:`${S.value}-title-content`},[P]);const ue=Ae(P)&&P.type==="span";return $(Oe,null,[ve(typeof H=="function"?H(e.originItemValue):H,{class:`${S.value}-item-icon`},!1),ue?P:$("span",{class:`${S.value}-title-content`},[P])])},He=b(()=>x.value!=="inline"&&C.value.length>1?"vertical":x.value),Ut=b(()=>x.value==="horizontal"?"vertical":x.value),qt=b(()=>He.value==="horizontal"?"vertical":He.value),it=()=>{var P,H;const ue=se.value,Fe=(P=e.icon)!==null&&P!==void 0?P:(H=t.icon)===null||H===void 0?void 0:H.call(t,e),at=e.expandIcon||t.expandIcon||ae.value,Ve=ge(Et(t,e,"title"),Fe);return $("div",{style:T.value,class:`${ue}-title`,tabindex:G.value?null:-1,ref:de,title:typeof Ve=="string"?Ve:null,"data-menu-id":d,"aria-expanded":X.value,"aria-haspopup":!0,"aria-controls":A,"aria-disabled":G.value,onClick:ne,onFocus:j},[Ve,x.value!=="horizontal"&&at?at(y(y({},e),{isOpen:X.value})):$("i",{class:`${ue}-arrow`},null)])};return()=>{var P;if(i)return ee?(P=t.default)===null||P===void 0?void 0:P.call(t):null;const H=se.value;let ue=()=>null;if(!L.value&&x.value!=="inline"){const Fe=x.value==="horizontal"?[0,8]:[10,0];ue=()=>$($t,{mode:He.value,prefixCls:H,visible:!e.internalPopupClose&&X.value,popupClassName:Q.value,popupOffset:e.popupOffset||Fe,disabled:G.value,onVisibleChange:z},{default:()=>[it()],popup:()=>$(Re,{mode:qt.value},{default:()=>[$(lt,{id:A,ref:O},{default:t.default})]})})}else ue=()=>$($t,null,{default:it});return $(Re,{mode:Ut.value},{default:()=>[$(Ie.Item,M(M({component:"li"},o),{},{role:"none",class:le(H,`${H}-${x.value}`,o.class,{[`${H}-open`]:X.value,[`${H}-active`]:J.value,[`${H}-selected`]:q.value,[`${H}-disabled`]:G.value}),onMouseenter:p,onMouseleave:I,"data-submenu-id":d}),{default:()=>$(Oe,null,[ue(),!L.value&&$(fo,{id:A,open:X.value,keyPath:C.value},{default:t.default})])})]})}}}),bo=()=>({title:N.any,originItemValue:me()}),ze=Y({compatConfig:{MODE:3},name:"AMenuItemGroup",inheritAttrs:!1,props:bo(),slots:Object,setup(e,n){let{slots:t,attrs:o}=n;const{prefixCls:l}=ie(),r=b(()=>`${l.value}-item-group`),c=ot();return()=>{var i,s;return c?(i=t.default)===null||i===void 0?void 0:i.call(t):$("li",M(M({},o),{},{onClick:g=>g.stopPropagation(),class:r.value}),[$("div",{title:typeof e.title=="string"?e.title:void 0,class:`${r.value}-title`},[Et(t,e,"title")]),$("ul",{class:`${r.value}-list`},[(s=t.default)===null||s===void 0?void 0:s.call(t)])])}}}),$o=()=>({prefixCls:String,dashed:Boolean}),Ne=Y({compatConfig:{MODE:3},name:"AMenuDivider",props:$o(),setup(e){const{prefixCls:n}=ie(),t=b(()=>({[`${n.value}-item-divider`]:!0,[`${n.value}-item-divider-dashed`]:!!e.dashed}));return()=>$("li",{class:t.value},null)}});var yo=function(e,n){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.indexOf(o)<0&&(t[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)n.indexOf(o[l])<0&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(t[o[l]]=e[o[l]]);return t};function Ue(e,n,t){return(e||[]).map((o,l)=>{if(o&&typeof o=="object"){const r=o,{label:c,children:i,key:s,type:g}=r,d=yo(r,["label","children","key","type"]),a=s??`tmp-${l}`,m=t?t.parentKeys.slice():[],u=[],v={eventKey:a,key:a,parentEventKeys:F(m),parentKeys:F(m),childrenEventKeys:F(u),isLeaf:!1};if(i||g==="group"){if(g==="group"){const f=Ue(i,n,t);return $(ze,M(M({key:a},d),{},{title:c,originItemValue:o}),{default:()=>[f]})}n.set(a,v),t&&t.childrenEventKeys.push(a);const C=Ue(i,n,{childrenEventKeys:u,parentKeys:[].concat(m,a)});return $(we,M(M({key:a},d),{},{title:c,originItemValue:o}),{default:()=>[C]})}return g==="divider"?$(Ne,M({key:a},d),null):(v.isLeaf=!0,n.set(a,v),$(Pe,M(M({key:a},d),{},{originItemValue:o}),{default:()=>[c]}))}return null}).filter(o=>o)}function ho(e){const n=R([]),t=R(!1),o=R(new Map);return Z(()=>e.items,()=>{const l=new Map;t.value=!1,e.items?(t.value=!0,n.value=Ue(e.items,l)):n.value=void 0,o.value=l},{immediate:!0,deep:!0}),{itemsNodes:n,store:o,hasItmes:t}}const Co=e=>{const{componentCls:n,motionDurationSlow:t,menuHorizontalHeight:o,colorSplit:l,lineWidth:r,lineType:c,menuItemPaddingInline:i}=e;return{[`${n}-horizontal`]:{lineHeight:`${o}px`,border:0,borderBottom:`${r}px ${c} ${l}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${n}-item, ${n}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},[`> ${n}-item:hover,
        > ${n}-item-active,
        > ${n}-submenu ${n}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${n}-item, ${n}-submenu-title`]:{transition:[`border-color ${t}`,`background ${t}`].join(",")},[`${n}-submenu-arrow`]:{display:"none"}}}},wo=e=>{let{componentCls:n,menuArrowOffset:t}=e;return{[`${n}-rtl`]:{direction:"rtl"},[`${n}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${n}-rtl${n}-vertical,
    ${n}-submenu-rtl ${n}-vertical`]:{[`${n}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(-${t})`},"&::after":{transform:`rotate(45deg) translateY(${t})`}}}}},ht=e=>y({},wn(e)),Ct=(e,n)=>{const{componentCls:t,colorItemText:o,colorItemTextSelected:l,colorGroupTitle:r,colorItemBg:c,colorSubItemBg:i,colorItemBgSelected:s,colorActiveBarHeight:g,colorActiveBarWidth:d,colorActiveBarBorderSize:a,motionDurationSlow:m,motionEaseInOut:u,motionEaseOut:v,menuItemPaddingInline:C,motionDurationMid:f,colorItemTextHover:h,lineType:S,colorSplit:K,colorItemTextDisabled:E,colorDangerItemText:w,colorDangerItemTextHover:x,colorDangerItemTextSelected:D,colorDangerItemBgActive:_,colorDangerItemBgSelected:L,colorItemBgHover:B,menuSubMenuBg:V,colorItemTextSelectedHorizontal:W,colorItemBgSelectedHorizontal:U}=e;return{[`${t}-${n}`]:{color:o,background:c,[`&${t}-root:focus-visible`]:y({},ht(e)),[`${t}-item-group-title`]:{color:r},[`${t}-submenu-selected`]:{[`> ${t}-submenu-title`]:{color:l}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{color:`${E} !important`},[`${t}-item:hover, ${t}-submenu-title:hover`]:{[`&:not(${t}-item-selected):not(${t}-submenu-selected)`]:{color:h}},[`&:not(${t}-horizontal)`]:{[`${t}-item:not(${t}-item-selected)`]:{"&:hover":{backgroundColor:B},"&:active":{backgroundColor:s}},[`${t}-submenu-title`]:{"&:hover":{backgroundColor:B},"&:active":{backgroundColor:s}}},[`${t}-item-danger`]:{color:w,[`&${t}-item:hover`]:{[`&:not(${t}-item-selected):not(${t}-submenu-selected)`]:{color:x}},[`&${t}-item:active`]:{background:_}},[`${t}-item a`]:{"&, &:hover":{color:"inherit"}},[`${t}-item-selected`]:{color:l,[`&${t}-item-danger`]:{color:D},"a, a:hover":{color:"inherit"}},[`& ${t}-item-selected`]:{backgroundColor:s,[`&${t}-item-danger`]:{backgroundColor:L}},[`${t}-item, ${t}-submenu-title`]:{[`&:not(${t}-item-disabled):focus-visible`]:y({},ht(e))},[`&${t}-submenu > ${t}`]:{backgroundColor:V},[`&${t}-popup > ${t}`]:{backgroundColor:c},[`&${t}-horizontal`]:y(y({},n==="dark"?{borderBottom:0}:{}),{[`> ${t}-item, > ${t}-submenu`]:{top:a,marginTop:-a,marginBottom:0,borderRadius:0,"&::after":{position:"absolute",insetInline:C,bottom:0,borderBottom:`${g}px solid transparent`,transition:`border-color ${m} ${u}`,content:'""'},"&:hover, &-active, &-open":{"&::after":{borderBottomWidth:g,borderBottomColor:W}},"&-selected":{color:W,backgroundColor:U,"&::after":{borderBottomWidth:g,borderBottomColor:W}}}}),[`&${t}-root`]:{[`&${t}-inline, &${t}-vertical`]:{borderInlineEnd:`${a}px ${S} ${K}`}},[`&${t}-inline`]:{[`${t}-sub${t}-inline`]:{background:i},[`${t}-item, ${t}-submenu-title`]:a&&d?{width:`calc(100% + ${a}px)`}:{},[`${t}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${d}px solid ${l}`,transform:"scaleY(0.0001)",opacity:0,transition:[`transform ${f} ${v}`,`opacity ${f} ${v}`].join(","),content:'""'},[`&${t}-item-danger`]:{"&::after":{borderInlineEndColor:D}}},[`${t}-selected, ${t}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:[`transform ${f} ${u}`,`opacity ${f} ${u}`].join(",")}}}}}},wt=e=>{const{componentCls:n,menuItemHeight:t,itemMarginInline:o,padding:l,menuArrowSize:r,marginXS:c,marginXXS:i}=e,s=l+r+c;return{[`${n}-item`]:{position:"relative"},[`${n}-item, ${n}-submenu-title`]:{height:t,lineHeight:`${t}px`,paddingInline:l,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:i,width:`calc(100% - ${o*2}px)`},[`${n}-submenu`]:{paddingBottom:.02},[`> ${n}-item,
            > ${n}-submenu > ${n}-submenu-title`]:{height:t,lineHeight:`${t}px`},[`${n}-item-group-list ${n}-submenu-title,
            ${n}-submenu-title`]:{paddingInlineEnd:s}}},So=e=>{const{componentCls:n,iconCls:t,menuItemHeight:o,colorTextLightSolid:l,dropdownWidth:r,controlHeightLG:c,motionDurationMid:i,motionEaseOut:s,paddingXL:g,fontSizeSM:d,fontSizeLG:a,motionDurationSlow:m,paddingXS:u,boxShadowSecondary:v}=e,C={height:o,lineHeight:`${o}px`,listStylePosition:"inside",listStyleType:"disc"};return[{[n]:{"&-inline, &-vertical":y({[`&${n}-root`]:{boxShadow:"none"}},wt(e))},[`${n}-submenu-popup`]:{[`${n}-vertical`]:y(y({},wt(e)),{boxShadow:v})}},{[`${n}-submenu-popup ${n}-vertical${n}-sub`]:{minWidth:r,maxHeight:`calc(100vh - ${c*2.5}px)`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${n}-inline`]:{width:"100%",[`&${n}-root`]:{[`${n}-item, ${n}-submenu-title`]:{display:"flex",alignItems:"center",transition:[`border-color ${m}`,`background ${m}`,`padding ${i} ${s}`].join(","),[`> ${n}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${n}-sub${n}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${n}-submenu > ${n}-submenu-title`]:C,[`& ${n}-item-group-title`]:{paddingInlineStart:g}},[`${n}-item`]:C}},{[`${n}-inline-collapsed`]:{width:o*2,[`&${n}-root`]:{[`${n}-item, ${n}-submenu ${n}-submenu-title`]:{[`> ${n}-inline-collapsed-noicon`]:{fontSize:a,textAlign:"center"}}},[`> ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-item,
          > ${n}-item-group > ${n}-item-group-list > ${n}-submenu > ${n}-submenu-title,
          > ${n}-submenu > ${n}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${d}px)`,textOverflow:"clip",[`
            ${n}-submenu-arrow,
            ${n}-submenu-expand-icon
          `]:{opacity:0},[`${n}-item-icon, ${t}`]:{margin:0,fontSize:a,lineHeight:`${o}px`,"+ span":{display:"inline-block",opacity:0}}},[`${n}-item-icon, ${t}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${n}-item-icon, ${t}`]:{display:"none"},"a, a:hover":{color:l}},[`${n}-item-group-title`]:y(y({},Sn),{paddingInline:u})}}]},St=e=>{const{componentCls:n,fontSize:t,motionDurationSlow:o,motionDurationMid:l,motionEaseInOut:r,motionEaseOut:c,iconCls:i,controlHeightSM:s}=e;return{[`${n}-item, ${n}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:[`border-color ${o}`,`background ${o}`,`padding ${o} ${r}`].join(","),[`${n}-item-icon, ${i}`]:{minWidth:t,fontSize:t,transition:[`font-size ${l} ${c}`,`margin ${o} ${r}`,`color ${o}`].join(","),"+ span":{marginInlineStart:s-t,opacity:1,transition:[`opacity ${o} ${r}`,`margin ${o}`,`color ${o}`].join(",")}},[`${n}-item-icon`]:y({},xn()),[`&${n}-item-only-child`]:{[`> ${i}, > ${n}-item-icon`]:{marginInlineEnd:0}}},[`${n}-item-disabled, ${n}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important"},[`> ${n}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},xt=e=>{const{componentCls:n,motionDurationSlow:t,motionEaseInOut:o,borderRadius:l,menuArrowSize:r,menuArrowOffset:c}=e;return{[`${n}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:r,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${t} ${o}, opacity ${t}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:r*.6,height:r*.15,backgroundColor:"currentcolor",borderRadius:l,transition:[`background ${t} ${o}`,`transform ${t} ${o}`,`top ${t} ${o}`,`color ${t} ${o}`].join(","),content:'""'},"&::before":{transform:`rotate(45deg) translateY(-${c})`},"&::after":{transform:`rotate(-45deg) translateY(${c})`}}}}},xo=e=>{const{antCls:n,componentCls:t,fontSize:o,motionDurationSlow:l,motionDurationMid:r,motionEaseInOut:c,lineHeight:i,paddingXS:s,padding:g,colorSplit:d,lineWidth:a,zIndexPopup:m,borderRadiusLG:u,radiusSubMenuItem:v,menuArrowSize:C,menuArrowOffset:f,lineType:h,menuPanelMaskInset:S}=e;return[{"":{[`${t}`]:y(y({},pt()),{"&-hidden":{display:"none"}})},[`${t}-submenu-hidden`]:{display:"none"}},{[t]:y(y(y(y(y(y(y({},Qe(e)),pt()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${l} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${t}-item`]:{flex:"none"}},[`${t}-item, ${t}-submenu, ${t}-submenu-title`]:{borderRadius:e.radiusItem},[`${t}-item-group-title`]:{padding:`${s}px ${g}px`,fontSize:o,lineHeight:i,transition:`all ${l}`},[`&-horizontal ${t}-submenu`]:{transition:[`border-color ${l} ${c}`,`background ${l} ${c}`].join(",")},[`${t}-submenu, ${t}-submenu-inline`]:{transition:[`border-color ${l} ${c}`,`background ${l} ${c}`,`padding ${r} ${c}`].join(",")},[`${t}-submenu ${t}-sub`]:{cursor:"initial",transition:[`background ${l} ${c}`,`padding ${l} ${c}`].join(",")},[`${t}-title-content`]:{transition:`color ${l}`},[`${t}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${t}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:d,borderStyle:h,borderWidth:0,borderTopWidth:a,marginBlock:a,padding:0,"&-dashed":{borderStyle:"dashed"}}}),St(e)),{[`${t}-item-group`]:{[`${t}-item-group-list`]:{margin:0,padding:0,[`${t}-item, ${t}-submenu-title`]:{paddingInline:`${o*2}px ${g}px`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:m,background:"transparent",borderRadius:u,boxShadow:"none",transformOrigin:"0 0","&::before":{position:"absolute",inset:`${S}px 0 0`,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'}},"&-placement-rightTop::before":{top:0,insetInlineStart:S},[`> ${t}`]:y(y(y({borderRadius:u},St(e)),xt(e)),{[`${t}-item, ${t}-submenu > ${t}-submenu-title`]:{borderRadius:v},[`${t}-submenu-title::after`]:{transition:`transform ${l} ${c}`}})}}),xt(e)),{[`&-inline-collapsed ${t}-submenu-arrow,
        &-inline ${t}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${f})`},"&::after":{transform:`rotate(45deg) translateX(-${f})`}},[`${t}-submenu-open${t}-submenu-inline > ${t}-submenu-title > ${t}-submenu-arrow`]:{transform:`translateY(-${C*.2}px)`,"&::after":{transform:`rotate(-45deg) translateX(-${f})`},"&::before":{transform:`rotate(45deg) translateX(${f})`}}})},{[`${n}-layout-header`]:{[t]:{lineHeight:"inherit"}}}]},Io=(e,n)=>Je("Menu",(o,l)=>{let{overrideComponentToken:r}=l;if((n==null?void 0:n.value)===!1)return[];const{colorBgElevated:c,colorPrimary:i,colorError:s,colorErrorHover:g,colorTextLightSolid:d}=o,{controlHeightLG:a,fontSize:m}=o,u=m/7*5,v=Te(o,{menuItemHeight:a,menuItemPaddingInline:o.margin,menuArrowSize:u,menuHorizontalHeight:a*1.15,menuArrowOffset:`${u*.25}px`,menuPanelMaskInset:-7,menuSubMenuBg:c}),C=new mt(d).setAlpha(.65).toRgbString(),f=Te(v,{colorItemText:C,colorItemTextHover:d,colorGroupTitle:C,colorItemTextSelected:d,colorItemBg:"#001529",colorSubItemBg:"#000c17",colorItemBgActive:"transparent",colorItemBgSelected:i,colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemTextDisabled:new mt(d).setAlpha(.25).toRgbString(),colorDangerItemText:s,colorDangerItemTextHover:g,colorDangerItemTextSelected:d,colorDangerItemBgActive:s,colorDangerItemBgSelected:s,menuSubMenuBg:"#001529",colorItemTextSelectedHorizontal:d,colorItemBgSelectedHorizontal:i},y({},r));return[xo(v),Co(v),So(v),Ct(v,"light"),Ct(f,"dark"),wo(v),un(v),De(v,"slide-up"),De(v,"slide-down"),qe(v,"zoom-big")]},o=>{const{colorPrimary:l,colorError:r,colorTextDisabled:c,colorErrorBg:i,colorText:s,colorTextDescription:g,colorBgContainer:d,colorFillAlter:a,colorFillContent:m,lineWidth:u,lineWidthBold:v,controlItemBgActive:C,colorBgTextHover:f}=o;return{dropdownWidth:160,zIndexPopup:o.zIndexPopupBase+50,radiusItem:o.borderRadiusLG,radiusSubMenuItem:o.borderRadiusSM,colorItemText:s,colorItemTextHover:s,colorItemTextHoverHorizontal:l,colorGroupTitle:g,colorItemTextSelected:l,colorItemTextSelectedHorizontal:l,colorItemBg:d,colorItemBgHover:f,colorItemBgActive:m,colorSubItemBg:a,colorItemBgSelected:C,colorItemBgSelectedHorizontal:"transparent",colorActiveBarWidth:0,colorActiveBarHeight:v,colorActiveBarBorderSize:u,colorItemTextDisabled:c,colorDangerItemText:r,colorDangerItemTextHover:r,colorDangerItemTextSelected:r,colorDangerItemBgActive:i,colorDangerItemBgSelected:i,itemMarginInline:o.marginXXS}})(e),Oo=()=>({id:String,prefixCls:String,items:Array,disabled:Boolean,inlineCollapsed:Boolean,disabledOverflow:Boolean,forceSubMenuRender:Boolean,openKeys:Array,selectedKeys:Array,activeKey:String,selectable:{type:Boolean,default:!0},multiple:{type:Boolean,default:!1},tabindex:{type:[Number,String]},motion:Object,role:String,theme:{type:String,default:"light"},mode:{type:String,default:"vertical"},inlineIndent:{type:Number,default:24},subMenuOpenDelay:{type:Number,default:0},subMenuCloseDelay:{type:Number,default:.1},builtinPlacements:{type:Object},triggerSubMenuAction:{type:String,default:"hover"},getPopupContainer:Function,expandIcon:Function,onOpenChange:Function,onSelect:Function,onDeselect:Function,onClick:[Function,Array],onFocus:Function,onBlur:Function,onMousedown:Function,"onUpdate:openKeys":Function,"onUpdate:selectedKeys":Function,"onUpdate:activeKey":Function}),It=[],pe=Y({compatConfig:{MODE:3},name:"AMenu",inheritAttrs:!1,props:Oo(),slots:Object,setup(e,n){let{slots:t,emit:o,attrs:l}=n;const{direction:r,getPrefixCls:c}=Le("menu",e),i=zt(),s=b(()=>{var p;return c("menu",e.prefixCls||((p=i==null?void 0:i.prefixCls)===null||p===void 0?void 0:p.value))}),[g,d]=Io(s,b(()=>!i)),a=R(new Map),m=fe(ro,F(void 0)),u=b(()=>m.value!==void 0?m.value:e.inlineCollapsed),{itemsNodes:v}=ho(e),C=R(!1);Ze(()=>{C.value=!0}),Be(()=>{Ce(!(e.inlineCollapsed===!0&&e.mode!=="inline"),"Menu","`inlineCollapsed` should only be used when `mode` is inline."),Ce(!(m.value!==void 0&&e.inlineCollapsed===!0),"Menu","`inlineCollapsed` not control Menu under Sider. Should set `collapsed` on Sider instead.")});const f=F([]),h=F([]),S=F({});Z(a,()=>{const p={};for(const I of a.value.values())p[I.key]=I;S.value=p},{flush:"post"}),Be(()=>{if(e.activeKey!==void 0){let p=[];const I=e.activeKey?S.value[e.activeKey]:void 0;I&&e.activeKey!==void 0?p=Ge([].concat(Me(I.parentKeys),e.activeKey)):p=[],xe(f.value,p)||(f.value=p)}}),Z(()=>e.selectedKeys,p=>{p&&(h.value=p.slice())},{immediate:!0,deep:!0});const K=F([]);Z([S,h],()=>{let p=[];h.value.forEach(I=>{const T=S.value[I];T&&(p=p.concat(Me(T.parentKeys)))}),p=Ge(p),xe(K.value,p)||(K.value=p)},{immediate:!0});const E=p=>{if(e.selectable){const{key:I}=p,T=h.value.includes(I);let z;e.multiple?T?z=h.value.filter(A=>A!==I):z=[...h.value,I]:z=[I];const j=y(y({},p),{selectedKeys:z});xe(z,h.value)||(e.selectedKeys===void 0&&(h.value=z),o("update:selectedKeys",z),T&&e.multiple?o("deselect",j):o("select",j))}B.value!=="inline"&&!e.multiple&&w.value.length&&U(It)},w=F([]);Z(()=>e.openKeys,function(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:w.value;xe(w.value,p)||(w.value=p.slice())},{immediate:!0,deep:!0});let x;const D=p=>{clearTimeout(x),x=setTimeout(()=>{e.activeKey===void 0&&(f.value=p),o("update:activeKey",p[p.length-1])})},_=b(()=>!!e.disabled),L=b(()=>r.value==="rtl"),B=F("vertical"),V=R(!1);Be(()=>{var p;(e.mode==="inline"||e.mode==="vertical")&&u.value?(B.value="vertical",V.value=u.value):(B.value=e.mode,V.value=!1),!((p=i==null?void 0:i.mode)===null||p===void 0)&&p.value&&(B.value=i.mode.value)});const W=b(()=>B.value==="inline"),U=p=>{w.value=p,o("update:openKeys",p),o("openChange",p)},ae=F(w.value),re=R(!1);Z(w,()=>{W.value&&(ae.value=w.value)},{immediate:!0}),Z(W,()=>{if(!re.value){re.value=!0;return}W.value?w.value=ae.value:U(It)},{immediate:!0});const ee=b(()=>({[`${s.value}`]:!0,[`${s.value}-root`]:!0,[`${s.value}-${B.value}`]:!0,[`${s.value}-inline-collapsed`]:V.value,[`${s.value}-rtl`]:L.value,[`${s.value}-${e.theme}`]:!0})),te=b(()=>c()),se=b(()=>({horizontal:{name:`${te.value}-slide-up`},inline:cn(`${te.value}-motion-collapse`),other:{name:`${te.value}-zoom-big`}}));Vt(!0);const G=function(){let p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const I=[],T=a.value;return p.forEach(z=>{const{key:j,childrenEventKeys:A}=T.get(z);I.push(j,...G(Me(A)))}),I},de=p=>{var I;o("click",p),E(p),(I=i==null?void 0:i.onClick)===null||I===void 0||I.call(i)},O=(p,I)=>{var T;const z=((T=S.value[p])===null||T===void 0?void 0:T.childrenEventKeys)||[];let j=w.value.filter(A=>A!==p);if(I)j.push(p);else if(B.value!=="inline"){const A=G(Me(z));j=Ge(j.filter(Q=>!A.includes(Q)))}xe(w,j)||U(j)},k=(p,I)=>{a.value.set(p,I),a.value=new Map(a.value)},X=p=>{a.value.delete(p),a.value=new Map(a.value)},q=F(0),J=b(()=>{var p;return e.expandIcon||t.expandIcon||!((p=i==null?void 0:i.expandIcon)===null||p===void 0)&&p.value?I=>{let T=e.expandIcon||t.expandIcon;return T=typeof T=="function"?T(I):T,ve(T,{class:`${s.value}-submenu-expand-icon`},!1)}:null});Lt({prefixCls:s,activeKeys:f,openKeys:w,selectedKeys:h,changeActiveKeys:D,disabled:_,rtl:L,mode:B,inlineIndent:b(()=>e.inlineIndent),subMenuCloseDelay:b(()=>e.subMenuCloseDelay),subMenuOpenDelay:b(()=>e.subMenuOpenDelay),builtinPlacements:b(()=>e.builtinPlacements),triggerSubMenuAction:b(()=>e.triggerSubMenuAction),getPopupContainer:b(()=>e.getPopupContainer),inlineCollapsed:V,theme:b(()=>e.theme),siderCollapsed:m,defaultMotions:b(()=>C.value?se.value:null),motion:b(()=>C.value?e.motion:null),overflowDisabled:R(void 0),onOpenChange:O,onItemClick:de,registerMenuInfo:k,unRegisterMenuInfo:X,selectedSubMenuKeys:K,expandIcon:J,forceSubMenuRender:b(()=>e.forceSubMenuRender),rootClassName:d});const ne=()=>{var p;return v.value||Dt((p=t.default)===null||p===void 0?void 0:p.call(t))};return()=>{var p;const I=ne(),T=q.value>=I.length-1||B.value!=="horizontal"||e.disabledOverflow,z=A=>B.value!=="horizontal"||e.disabledOverflow?A:A.map((Q,ge)=>$(Re,{key:Q.key,overflowDisabled:ge>q.value},{default:()=>Q})),j=((p=t.overflowedIndicator)===null||p===void 0?void 0:p.call(t))||$(je,null,null);return g($(Ie,M(M({},l),{},{onMousedown:e.onMousedown,prefixCls:`${s.value}-overflow`,component:"ul",itemComponent:Pe,class:[ee.value,l.class,d.value],role:"menu",id:e.id,data:z(I),renderRawItem:A=>A,renderRawRest:A=>{const Q=A.length,ge=Q?I.slice(-Q):null;return $(Oe,null,[$(we,{eventKey:_e,key:_e,title:j,disabled:T,internalPopupClose:Q===0},{default:()=>ge}),$(bt,null,{default:()=>[$(we,{eventKey:_e,key:_e,title:j,disabled:T,internalPopupClose:Q===0},{default:()=>ge})]})])},maxCount:B.value!=="horizontal"||e.disabledOverflow?Ie.INVALIDATE:Ie.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:A=>{q.value=A}}),{default:()=>[$(In,{to:"body"},{default:()=>[$("div",{style:{display:"none"},"aria-hidden":!0},[$(bt,null,{default:()=>[z(ne())]})])]})]}))}}});pe.install=function(e){return e.component(pe.name,pe),e.component(Pe.name,Pe),e.component(we.name,we),e.component(Ne.name,Ne),e.component(ze.name,ze),e};pe.Item=Pe;pe.Divider=Ne;pe.SubMenu=we;pe.ItemGroup=ze;var Po={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};function Ot(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?Object(arguments[n]):{},o=Object.keys(t);typeof Object.getOwnPropertySymbols=="function"&&(o=o.concat(Object.getOwnPropertySymbols(t).filter(function(l){return Object.getOwnPropertyDescriptor(t,l).enumerable}))),o.forEach(function(l){Mo(e,l,t[l])})}return e}function Mo(e,n,t){return n in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var Yt=function(n,t){var o=Ot({},n,t.attrs);return $(et,Ot({},o,{icon:Po}),null)};Yt.displayName="LeftOutlined";Yt.inheritAttrs=!1;ye.Button=Ke;ye.install=function(e){return e.component(ye.name,ye),e.component(Ke.name,Ke),e};const _o=()=>{const e=R(!1);return he(()=>{e.value=!0}),e},Bo={type:{type:String},actionFn:Function,close:Function,autofocus:Boolean,prefixCls:String,buttonProps:me(),emitEvent:Boolean,quitOnNullishReturnValue:Boolean};function Pt(e){return!!(e&&e.then)}const Lo=Y({compatConfig:{MODE:3},name:"ActionButton",props:Bo,setup(e,n){let{slots:t}=n;const o=R(!1),l=R(),r=R(!1);let c;const i=_o();Ze(()=>{e.autofocus&&(c=setTimeout(()=>{var a,m;return(m=(a=On(l.value))===null||a===void 0?void 0:a.focus)===null||m===void 0?void 0:m.call(a)}))}),he(()=>{clearTimeout(c)});const s=function(){for(var a,m=arguments.length,u=new Array(m),v=0;v<m;v++)u[v]=arguments[v];(a=e.close)===null||a===void 0||a.call(e,...u)},g=a=>{Pt(a)&&(r.value=!0,a.then(function(){i.value||(r.value=!1),s(...arguments),o.value=!1},m=>(i.value||(r.value=!1),o.value=!1,Promise.reject(m))))},d=a=>{const{actionFn:m}=e;if(o.value)return;if(o.value=!0,!m){s();return}let u;if(e.emitEvent){if(u=m(a),e.quitOnNullishReturnValue&&!Pt(u)){o.value=!1,s(a);return}}else if(m.length)u=m(e.close),o.value=!1;else if(u=m(),!u){s();return}g(u)};return()=>{const{type:a,prefixCls:m,buttonProps:u}=e;return $(Ee,M(M(M({},Dn(a)),{},{onClick:d,loading:r.value,prefixCls:m},u),{},{ref:l}),t)}}});export{Lo as A,Gn as D,je as E,Yt as L,pe as M,ke as R,No as S,Pe as _,zo as a,ko as b,ro as c,ye as d,Ro as e,lo as u};
