import{c as ot,W as N}from"./table-CWAoXMlp.js";import{O as B}from"./index-QAVfwe37.js";import{L as it}from"./index-C6CjS8Dl.js";import{z as o,r as a,u as rt,y as st,w as ut,f as b,d as m,o as r,i as s,b as D,c as _,F as O,e as H,q as ct,t as W,g as mt,A as g}from"./index-ClUwy-U2.js";import{u as dt}from"./collectionUnitConfig-Ol15KzRq.js";import{u as Dt}from"./devTree-BfAcuD8A.js";import{u as pt}from"./collectionUnitMonitor-DVpkXr3x.js";import{S as ht,b as S}from"./tools-BpvUNSHS.js";import{_ as ft}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as vt,a as bt}from"./index-p1I43ZwN.js";import{M as _t}from"./index-DkT7RQa1.js";import"./ActionButton-p4-l6Zij.js";import"./styleChecker-D2z1GQZd.js";import"./index-ByAZPsB5.js";import"./initDefaultProps-CbhiVpW4.js";import"./shallowequal-DorGB6RW.js";import"./index-CUxYOaAq.js";import"./index-Cg9xkC5X.js";import"./index-B9pLYs_L.js";import"./index-BUWlNxdP.js";import"./index-CgagFv0b.js";const gt={class:"btnGroups"},It={class:"clearfix"},Yt=["onClick"],Mt={class:"border"},Ct=["onClick"],yt={class:"formHeader"},kt={class:"chartBox"},xt={key:1,class:"nodata"},St={__name:"status",setup(Tt){const p=dt(),z=Dt(),I=pt();let u=[o().subtract(3,"month"),o()];const T=a(""),Y=a(!1),M=a(!1),A=rt(),n=a({}),L=a(),d=a(A.params.id),U=a([]),G=a({}),v=a(u),C=a(u),V=a({timeRange:u}),h=a({time:u}),c=a({}),i=st({dauList:[],tableData1:[],tableData2:[],chartInformation:{title:"",legendArr:[],unit:["时间","电压(V)"]}}),P=()=>{let t=z.findAncestorsWithNodes(d.value);t&&t.length&&t.length>1&&(L.value=t[t.length-2].id)},y=t=>{G.value=t,U.value=[{label:"采集单元名称",value:t.dauName},{label:"IP地址",value:t.ip},{label:"采集间隔(分钟)",value:t.dataAcquisitionInterval},{label:"状态",value:t.isAvailable?"开启":"禁用"},{label:"端口",value:t.port}]},$=async t=>{Y.value=!0,i.dauList=await p.fetchGetDAUList({WindTurbineID:d.value,WindParkId:L.value}),Y.value=!1,p.dAUList&&p.dAUList.length?(n.value=p.dAUList[0],y(n.value),w(),k()):(n.value={},y({}))},w=async()=>{const t=await I.fetchGetDauStatusInfoByTurbineIDAndDauID({turbineID:d.value,DAUID:n.value.dauID});i.tableData1=t.channelStatusList||[]},k=async()=>{const t=await I.fetchGetDSearchDAULog({turbineID:d.value,dauID:n.value.dauID,beginTime:C.value[0],endTime:C.value[1]});i.tableData2=t||[]};ut(()=>A.params.id,t=>{t&&(p.reset(),d.value=t,P(),$())},{immediate:!0});const q=[{title:"选择时间",dataIndex:"timeRange",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],E=[{title:"通道",dataIndex:"channelNumber"},{title:"测量位置",dataIndex:"measLocationName"},{title:"偏置电压(V)",dataIndex:"dcDataValue"},{title:"状态",dataIndex:"alarmState",customRender:({record:t})=>t.alarmState?g("span",{style:{color:S(t.alarmState).color}},S(t.alarmState).text):""},{title:"更新时间",dataIndex:"statusUpdateTime",headerOperations:{sorter:!0,date:!0},customRender:({record:t})=>t.statusUpdateTime?g("span",{},o(t.statusUpdateTime).format("YYYY-MM-DD HH:mm:ss")):""},{title:"操作",dataIndex:"otherColumn",columnHidden:!0,width:160}],j=[{title:"采集单元状态",dataIndex:"alarmStateDescription",headerOperations:{filters:[]},width:160,customRender:({text:t,record:e})=>e.alarmState?g("span",{style:{color:S(e.alarmState).color}},t):""},{title:"日志",dataIndex:"logTitle",customRender:({text:t,record:e})=>e.logTitle?g("p",{style:{textAlign:"left"}},t):""},{title:"更新时间",dataIndex:"eventTimeFormatted",headerOperations:{sorter:!0,date:!0},width:160}],J=t=>{!t.timeRange||!t.timeRange.length||(C.value=[o(t.timeRange[0]).format("YYYY-MM-DD"),o(t.timeRange[1]).format("YYYY-MM-DD")],k())},K=t=>{t.dauID!=n.value.dauID&&(n.value=t,y(t),V.value={timeRange:u},w(),k())},Q=t=>{T.value=`${t.measLocationName}`,X(),h.value={...h.value,...t},F()},F=async()=>{const t=h.value;let e=await I.fetchGetChannelDCTrendChart({turbineID:d.value,beginTime:o(v.value[0]).format("YYYY-MM-DD"),endTime:o(v.value[1]).format("YYYY-MM-DD"),channelID:t.channelNumber,DAUID:n.value.dauID});if(e&&e.timeValueData&&e.timeValueData.length){let f=[];e.waringValueData&&e.waringValueData.length&&(f.push(e.waringValueData[0]),e.errorValueData&&e.errorValueData.length&&f.push(e.errorValueData[0]));const x={time:e.timeValueData,lineData:[{line:e.eigenValueData}],markLine:f};c.value=x,i.chartInformation={title:`${e.titleName}`,legendArr:[e.eigenValue||"偏置电压"],unit:["时间","电压(V)"]}}else c.value={}},X=()=>{M.value=!0},Z=t=>{M.value=!1,h.value={time:u},v.value=u,c.value={}},tt=t=>{v.value=[o(t.time[0]).format("YYYY-MM-DD"),o(t.time[1]).format("YYYY-MM-DD")],F()},et=[{title:"选择时间",dataIndex:"time",inputType:"rangePicker",timeFormat:["YYYY-MM-DD","YYYY-MM-DD"],noMinLabelWidth:!0}],at={showToolbox:!0};return(t,e)=>{const f=bt,x=vt,nt=_t,lt=ht;return r(),b(lt,{spinning:Y.value,size:"large"},{default:m(()=>[s("div",null,[s("div",gt,[s("ul",It,[(r(!0),_(O,null,H(i.dauList,l=>(r(),_("li",{key:l.dauID,class:ct({active:n.value.dauID===l.dauID}),onClick:R=>K(l)},W(l.dauName),11,Yt))),128))])]),D(ot,{tableTitle:"采集单元信息",defaultCollapse:!0,batchApply:!1},{content:m(()=>[s("div",Mt,[D(x,{column:5,size:"small"},{default:m(()=>[(r(!0),_(O,null,H(U.value,l=>(r(),b(f,{label:l.label,key:l.label},{default:m(()=>[mt(W(l.value),1)]),_:2},1032,["label"]))),128))]),_:1})])]),_:1}),s("div",null,[D(N,{ref:"table",size:"default","table-key":"0","table-title":"传感器状态列表","table-columns":E,noPagination:!0,"record-key":"measLocationID","table-datas":i.tableData1,noBatchApply:!0},{otherColumn:m(({column:l,record:R,text:At})=>[s("span",{onClick:Lt=>Q(R),class:"editBtn"},"查看偏置电压趋势图",8,Ct)]),_:1},8,["table-datas"]),D(N,{ref:"table",size:"default","table-key":"1","table-title":"采集单元监测日志","table-columns":j,"record-key":"ModbusUnitID","table-datas":i.tableData2,noBatchApply:!0},{contentHeader:m(()=>[s("div",yt,[(r(),b(B,{titleCol:q,initFormData:V.value,key:n.value.dauID,onSubmit:J,formlayout:"inline"},null,8,["initFormData"]))])]),_:1},8,["table-datas"])]),D(nt,{maskClosable:!1,destroyOnClose:!0,width:"800px",open:M.value,title:T.value,footer:"",onCancel:Z},{default:m(()=>[D(B,{titleCol:et,ref:"formRef",initFormData:h.value,formlayout:"inline",onSubmit:tt},null,8,["initFormData"]),s("div",kt,[c.value&&c.value.time&&c.value.time.length?(r(),b(it,{key:0,boxId:"chart2",chartOptions:at,informations:i.chartInformation,chartData:c.value},null,8,["informations","chartData"])):(r(),_("div",xt,"暂无数据"))])]),_:1},8,["open","title"])])]),_:1},8,["spinning"])}}},Zt=ft(St,[["__scopeId","data-v-0c2291d5"]]);export{Zt as default};
