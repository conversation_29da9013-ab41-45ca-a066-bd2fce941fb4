﻿using Dapper;
using System.Data.Common;
using WTCMSLive.WebSite.Helpers;

namespace WTCMSLive.WebSite.Models
{
    public class DevWindParkGroupManager
    {
        DbConnection conn;
        public DevWindParkGroupManager(string dbstr)
        {
            //conn = new MySqlConnection(MySqlConnect._MysqlBaseDB);
            conn = MySqlConnect.GetMysqlConnection();
        }
        public DevWindParkGroup GetWindParkGroup(string parkid)
        {
            List<DevWindParkGroup> Data = conn.Query<DevWindParkGroup>($"SELECT * FROM devwindparkgroup WHERE WindParkID = '{parkid}';").ToList();

            return Data.FirstOrDefault();
        }

        public bool EditWindParkGroup(string parkid, int groupNum)
        {

            DevWindParkGroup group = GetWindParkGroup(parkid);
            string sql;
            if (group == null)
            {
                group = new DevWindParkGroup()
                {
                    WindParkID = parkid,
                    GroupNum = groupNum,
                };
                sql = "insert into devwindparkgroup values(@WindParkID,@GroupNum)";
            }
            else
            {
                group.GroupNum = groupNum;
                sql = "update devwindparkgroup set GroupNum=@GroupNum where WindParkID=@WindParkID";
            }
            var res = conn.Execute(sql,group);

            return Convert.ToBoolean(res) ;
        }
    }

    public class DevWindParkGroup
    {
        public string WindParkID { get; set; }
        public int GroupNum { get; set; }
    }
}