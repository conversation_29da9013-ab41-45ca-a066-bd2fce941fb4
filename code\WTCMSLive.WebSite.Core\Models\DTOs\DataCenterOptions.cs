namespace WTCMSLive.WebSite.Core.Models.DTOs
{
    /// <summary>
    /// 数据中心配置选项
    /// </summary>
    public class DataCenterOptions
    {
        /// <summary>
        /// 数据中心基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// 请求超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// 重试延迟时间（秒）
        /// </summary>
        public int RetryDelaySeconds { get; set; } = 2;
    }
}
