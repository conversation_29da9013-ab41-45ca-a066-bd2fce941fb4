﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using WTCMSLive.BusinessEntity;

namespace WTCMSLive.BusinessEntityConvert
{
     public class WaveDefParamConvert
    {


        public static string EncodWaveParam(WaveFormData _waveFormData)
        {
            string paramStr = string.Empty;
            if (_waveFormData.GetType() == typeof(WaveFormData_Time))
            {
                WaveFormData_Time wfTime = _waveFormData as WaveFormData_Time;
                paramStr = "";// wfTime.LowerLimitFreqency.ToString("F3") + "," + wfTime.UpperLimitFreqency.ToString("F3");
            }
            else if (_waveFormData.GetType() == typeof(WaveFormData_Envelope))
            {
                WaveFormData_Envelope wfEnv = _waveFormData as WaveFormData_Envelope;
                paramStr = GetWaveDefParam_EvpDesc(wfEnv.EnvFiterFreq);//wfEnv.EnvBandWidth.ToString("F3") + "," + wfEnv.EnvFiterFreq.ToString("F3");
            }
            return paramStr;
        }

        private static string GetWaveDefParam_EvpDesc(float filter)
        {
            StringBuilder sb = new StringBuilder();

            sb.Append("高通:");
            if (filter < 1000)
            {
                sb.Append(string.Format("{0:G4}", filter));
            }
            else
            {
                sb.Append(string.Format("{0:G4}", filter / 1000));
                sb.Append("K");
            }
            return sb.ToString();
        }

        private static float SplitFilterFreqency(string _descStr)
        {
            //  每个项目使用逗号分隔；
            //string[] items = _descStr.Split(',');

            // 单个项目格式 Name:value
            //foreach (string str in items)
            string str = _descStr;
            {
                if (str.Contains("高通"))
                {
                    System.Text.RegularExpressions.Match m =
                        System.Text.RegularExpressions.Regex.Match(str, @"[-]|[.]|[-.]|[0-9]*[.]*[0-9]+");

                    if (m.Success)
                    {
                        if (str.Contains("K"))
                        {
                            return float.Parse(m.Value) * 1000;
                        }

                        return float.Parse(m.Value);

                    }

                }
            }


            return 0;
        }


        public static void DecodeWaveDefParam(WaveFormData data, EnumMeasDefType WFType, string WaveDefParam)
        {
            //string[] waveParam = WaveDefParam.Split(',');
            //float[] param = new float[waveParam.Count()];
            //for (int i = 0; i < waveParam.Count(); i++)
            //{
            //    param[i] = float.Parse(waveParam[i]);
            //}
            switch (WFType)
            {
                case EnumMeasDefType.MDF_Time:
                    WaveFormData_Time data_Time = data as WaveFormData_Time;
                    data_Time.LowerLimitFreqency = 0;
                    data_Time.UpperLimitFreqency = (float)data_Time.SampleRate / 2.56F;
                    break;
                case EnumMeasDefType.MDF_Envelope:
                    WaveFormData_Envelope data_Envelope = data as WaveFormData_Envelope;
                    data_Envelope.EnvBandWidth = (float)data_Envelope.SampleRate / 2.56F;
                    data_Envelope.EnvFiterFreq = SplitFilterFreqency(WaveDefParam);
                    break;
                case EnumMeasDefType.MDF_Order:
                    break;
                case EnumMeasDefType.MDF_OrderEnvelope:
                    break;
                default:
                    break;
            }
        }
    }
}
