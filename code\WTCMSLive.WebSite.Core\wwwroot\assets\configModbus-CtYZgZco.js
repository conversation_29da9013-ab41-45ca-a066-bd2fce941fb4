import{C as c,ci as a,cj as n,ck as u,cl as i,cm as h,cn as d,co as l,cp as b,cq as p,cr as D}from"./index-ClUwy-U2.js";import{a as o}from"./tools-BpvUNSHS.js";const M=c("configModbus",{state:()=>({modbusDevTypes:[],modbusDeviceList:[],modbusLocaOptions:[],modbusMeasLocoptions:[]}),actions:{reset(){this.$reset()},async fetchGetModbusDevType(s){try{const e=await D(s);let t=o(e,{label:"value",value:"key"},{nother:!0});return t=t.map(r=>(r.value=r.value+"",r)),this.modbusDevTypes=t,t}catch(e){throw console.error("操作失败:",e),e}},async fetchAddModbusDevice(s){try{return await p(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusDeviceList(s){try{const e=await b(s);this.modbusDeviceList=e;let t=o(e,{label:"modbusDeviceName",value:"modbusDeviceID"});return this.modbusDeviceOptions=t,e}catch(e){throw console.error("操作失败:",e),e}},async fetchEditModbusDevice(s){try{return await l(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteModbusDevice(s){try{return await d(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchAddModbusChannel(s){try{return await h(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusChannelList(s){try{const e=await i(s);let t=o(e,{label:"measLocationName",value:"measLocationID"},{nother:!0});return this.modbusLocaOptions=t,e}catch(e){throw console.error("操作失败:",e),e}},async fetchEditModbusChannel(s){try{return await u(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchBatchDeleteModbusChannel(s){try{return await n(s)}catch(e){throw console.error("操作失败:",e),e}},async fetchGetModbusMeasLocByDeviceID(s){try{const e=await a(s);let t=o(e,{label:"measLocName",value:"measLocationID"},{nother:!0});return this.modbusMeasLocoptions=t,t}catch(e){throw console.error("操作失败:",e),e}}}});export{M as u};
